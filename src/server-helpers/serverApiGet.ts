import apiClient from '../config/apiClient'

function parseCookieString(cookieString: string): Record<string, string> {
  const result: Record<string, string> = {}
  cookieString.split(';').forEach((cookie) => {
    const [key, ...val] = cookie.trim().split('=')
    if (key) result[key] = decodeURIComponent(val.join('='))
  })
  return result
}

export async function getEntityHeadersFromCookies() {
  // Only works in server context with next/headers
  // Import here to avoid leaking to client bundle
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const { cookies } = require('next/headers')
  const cookieHeader = (await cookies()).toString()
  const parsed = parseCookieString(cookieHeader)
  const entityId = parsed['x-active-entity-id']
  const entityType = parsed['x-active-entity-type']
  const headers: Record<string, string> = {}
  if (entityId) headers['x-active-entity-id'] = entityId
  if (entityType) headers['x-active-entity-type'] = entityType
  return headers
}

export async function serverApiGet(url: string, config: any = {}) {
  const entityHeaders = await getEntityHeadersFromCookies()
  return apiClient.get(url, {
    ...config,
    headers: {
      ...config.headers,
      ...entityHeaders,
    },
  })
}
