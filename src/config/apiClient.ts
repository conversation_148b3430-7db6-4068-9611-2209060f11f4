import axios from 'axios'
import { DEFAULT_PROD_API_BASE_URL } from './defaultEnv'

const isServer = typeof window === 'undefined'

const API_BASE_URL: string = isServer
  ? DEFAULT_PROD_API_BASE_URL
  : process.env.NODE_ENV === 'development'
    ? '/api'
    : DEFAULT_PROD_API_BASE_URL

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
})

// Add a request interceptor to inject dynamic entity headers
if (typeof window !== 'undefined') {
  apiClient.interceptors.request.use(
    (config) => {
      // Get entity info from localStorage
      const entityId = localStorage.getItem('x-active-entity-id')
      const entityType = localStorage.getItem('x-active-entity-type')
      if (entityId) config.headers['x-active-entity-id'] = entityId
      if (entityType) config.headers['x-active-entity-type'] = entityType
      return config
    },
    (error) => Promise.reject(error),
  )
}

export default apiClient
