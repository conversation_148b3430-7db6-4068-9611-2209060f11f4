import { Salary } from '../models/Job'

/**
 * Formats a number as Indian currency using the 'en-IN' locale with zero decimal places.
 *
 * @param num - The number to be formatted.
 * @returns A formatted currency string if the input is a valid finite number; otherwise, returns 'Nil'.
 */
export const formatToIndianCurrency = (num: number): string => {
  if (typeof num === 'number' && isFinite(num))
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(num)
  else return 'Nil'
}

/**
 * Splits a headline string into job position and company based on the first comma.
 * If no comma is found, returns the entire headline as job position and an empty string for company.
 *
 * @param headline - The headline string (e.g., "Software Engineer, Lucres Private Limited")
 * @returns An object with `jobPosition` and `company` properties.
 */
export const splitHeadline = (headline: string): { jobPosition: string; company: string } => {
  const index = headline.indexOf(',')
  if (index === -1) {
    return { jobPosition: convertToTitleCase(headline.trim()), company: '' }
  }
  return {
    jobPosition: convertToTitleCase(headline.substring(0, index).trim()),
    company: convertToTitleCase(headline.substring(index + 1).trim()),
  }
}

/**
 * Converts a given string to Title Case while preserving spaces.
 * Each word's first character is capitalized. Additionally, if a word contains hyphens,
 * the first character of each hyphenated segment is also capitalized.
 *
 * For example:
 * - "Restful aPIs" becomes "Restful APIs"
 * - "Object-oriented Design" becomes "Object-Oriented Design"
 *
 * @param input - The string to be converted.
 * @returns The transformed string in Title Case with spaces preserved.
 */
export const convertToTitleCase = (input: string): string =>
  input.replace(/\w\S*/g, (word) =>
    word
      .split('-')
      .map((segment) => segment.charAt(0).toUpperCase() + segment.slice(1).toLowerCase())
      .join('-'),
  )

// List of degree acronyms that should be in all caps
export const acronymList = [
  'B.TECH',
  'BE',
  'BSC',
  'BA',
  'BCA',
  'BBA',
  'M.TECH',
  'ME',
  'MSC',
  'MA',
  'MCA',
  'MBA',
  'PHD',
]

// Map for correcting common degree name variations
export const degreeCorrections: { [key: string]: string } = {
  btech: 'B.Tech',
  mcom: 'M.Com',
  // Add more corrections as needed
}

/**
 * Formats a degree name based on the following rules:
 * - If the degree is in the acronymList, it is converted to uppercase.
 * - If the degree has a correction in degreeCorrections, it is applied first.
 * - Otherwise, it is converted to title case using convertToTitleCase.
 *
 * @param degreeName - The degree name to format (e.g., "btech", "MSc", "masters").
 * @returns The formatted degree name (e.g., "B.TECH", "MSC", "Masters").
 */
export const formatDegreeName = (degreeName: string): string => {
  const lowerDegree = degreeName.toLowerCase()
  // Apply corrections if available, otherwise use the original degreeName
  const correctedDegree = degreeCorrections[lowerDegree] || degreeName
  const isAcronym = acronymList.includes(correctedDegree.toUpperCase())
  return isAcronym ? correctedDegree.toUpperCase() : convertToTitleCase(correctedDegree)
}
/**
 * Abbreviates a number into a short Indian format:
 * - Below 1,000: as is.
 * - 1,000 to 99,999: in thousands (K).
 * - 1,00,000 to 9,999,999: in lakhs (L).
 * - 1,00,00,000 and above: in crores (Cr).
 *
 * The output is prefixed with the rupee symbol (₹).
 *
 * @param num - The number to abbreviate.
 * @returns The abbreviated currency string.
 */
export const formatSalaryAbbreviation = (num: number): string => {
  if (typeof num !== 'number' || !isFinite(num)) {
    return 'Nil'
  }

  let abbreviated: string
  if (num < 1000) {
    abbreviated = num.toString()
  } else if (num < 100000) {
    // Thousands (K)
    const value = num / 1000
    abbreviated = (value % 1 === 0 ? value.toString() : value.toFixed(1)) + 'K'
  } else if (num < 10000000) {
    // Lakhs (L)
    const value = num / 100000
    abbreviated = (value % 1 === 0 ? value.toString() : value.toFixed(1)) + 'L'
  } else {
    // Crores (Cr)
    const value = num / 10000000
    abbreviated = (value % 1 === 0 ? value.toString() : value.toFixed(1)) + 'Cr'
  }

  return `₹${abbreviated}`
}

/**
 * Calculates and formats a salary range using abbreviated currency values.
 *
 * The function displays the range in the format:
 * "₹50K - ₹70K"
 *
 * @param input - A Salary object containing value, min, max, and period.
 * @returns A formatted salary range string.
 */
export const calculateSalaryRange = (input: Salary): string => {
  const { min, max } = input
  const formattedMin = formatSalaryAbbreviation(min)
  const formattedMax = formatSalaryAbbreviation(max)

  return `${formattedMin} - ${formattedMax}`
}

/**
 * Converts a given string so that each word's first letter is capitalized and the remaining letters are lower case.
 * Spaces and hyphens are preserved.
 *
 * For example:
 * - "Restful aPIs" becomes "Restful Apis"
 * - "object-oriented design" becomes "Object-Oriented Design"
 *
 * @param input - The string to be converted.
 * @returns The transformed string with each word properly capitalized.
 */
export const capitalizeWords = (input: string): string =>
  input.replace(/\w\S*/g, (word) =>
    word
      .split('-')
      .map((segment) => segment.charAt(0).toUpperCase() + segment.slice(1).toLowerCase())
      .join('-'),
  )

/**
 * Truncates a given string to a specified maximum length and appends ellipsis ("...") if it exceeds that length.
 *
 * @param input - The string to be truncated.
 * @param maxLength - The maximum number of characters allowed before truncating.
 * @returns An object containing the possibly truncated text and a flag indicating if truncation occurred.
 */
export const truncateText = (
  input: string,
  maxLength: number,
): { text: string; wasTruncated: boolean } => {
  const isTooLong = input?.length > maxLength;
  const result = {
    text: isTooLong ? input?.slice(0, maxLength) + '...' : input,
    wasTruncated: isTooLong,
  };
  return result;
};

/**
 * Converts an ISO date string to a human-readable relative time string.
 *
 * This utility returns values like:
 * - "a few seconds ago"
 * - "2 minutes ago"
 * - "1 hour ago"
 * - "a day ago"
 * - "3 months ago"
 *
 * @param {string} dateString - An ISO 8601 date string (e.g., "2025-04-18T10:07:02.048Z").
 * @returns {string} A relative time string representing how long ago the date was.
 *
 * @example
 * getRelativeTime("2025-04-18T10:07:02.048Z");
 * // Returns: "a few seconds ago" (or depending on the current time)
 */
export function getRelativeTime(dateString: string): string {
  const now = new Date()
  const date = new Date(dateString)
  const diff = Math.floor((now.getTime() - date.getTime()) / 1000) // difference in seconds

  if (diff < 60) return 'a few seconds ago'
  if (diff < 120) return '1 minute ago'
  if (diff < 3600) return `${Math.floor(diff / 60)} minutes ago`
  if (diff < 7200) return '1 hour ago'
  if (diff < 86400) return `${Math.floor(diff / 3600)} hours ago`
  if (diff < 172800) return 'a day ago'
  if (diff < 2592000) return `${Math.floor(diff / 86400)} days ago`
  if (diff < 5184000) return 'a month ago'
  if (diff < 31536000) return `${Math.floor(diff / 2592000)} months ago`

  return `${Math.floor(diff / 31536000)} years ago`
}

/**
 * Combines `givenName` and `familyName` into a full name,
 * then truncates it to 15 characters and capitalizes each word.
 *
 * @param givenName - The user's given (first) name.
 * @param familyName - The user's family (last) name.
 * @returns A capitalized and truncated full name string.
 */
export const getFullName = (givenName: string, familyName: string): string => {
  const fullName = `${givenName ?? ''} ${familyName ?? ''}`.trim()
  const truncated = truncateText(fullName, 15).text
  return capitalizeWords(truncated)
}

export const parseLocationInput = (input: string): any => {
  const parts = input.split(',').map((part) => part.trim())
  const country = parts[parts.length - 1] || ''
  const state = parts.length > 1 ? parts[parts.length - 2] : ''
  const city = parts.length > 2 ? parts[parts.length - 3] : ''
  const areaParts = parts.length > 3 ? parts.slice(0, parts.length - 3) : ['']
  const area = areaParts.join(', ').trim()

  return {
    address: {
      area: area || '',
      city: city || '',
      state: state || '',
      country: country || '',
    },
    latitude: 0,
    longitude: 0,
  }
}

export function formatToReadableDate(
  dateString: string,
  timeRequired: boolean,
  fullDate: boolean = true,
): string {
  const date = new Date(dateString)

  if (!fullDate) {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
    }) // e.g., "Jan 2022"
  }

  const datePart = date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })

  if (timeRequired) {
    const timePart = date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })
    return `${datePart}, ${timePart}`
  }

  return datePart
}

export function getTimeDifference(from: string, to: string): string {
  const fromDate = new Date(from)
  const toDate = new Date(to)

  let years = toDate.getFullYear() - fromDate.getFullYear()
  let months = toDate.getMonth() - fromDate.getMonth()

  if (months < 0) {
    years--
    months += 12
  }

  const parts = []
  if (years > 0) parts.push(`${years} year${years > 1 ? 's' : ''}`)
  if (months > 0) parts.push(`${months} month${months > 1 ? 's' : ''}`)

  return parts.length > 0 ? parts.join(' ') : 'Less than a month'
}
