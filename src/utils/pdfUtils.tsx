import React from 'react'
import { View, Text, StyleSheet } from '@react-pdf/renderer'
import type { Styles } from '@react-pdf/renderer'

// Default styles for the parser - can be overridden
const defaultParserStyles = StyleSheet.create({
  paragraph: {
    marginBottom: 4,
    fontSize: 12,
    lineHeight: 1.2,
    textAlign: 'justify',
  },
  bold: {
    fontWeight: 700,
    fontSize: 12,
  },
  italic: {
    fontStyle: 'italic',
    fontSize: 12,
  },
  underline: {
    textDecoration: 'underline',
    fontSize: 12,
  },
  normal: {
    fontSize: 12,
  },
  listItem: {
    flexDirection: 'row',
    marginBottom: 3,
    alignItems: 'flex-start',
    width: '100%',
  },
  bullet: {
    width: 12,
    fontSize: 12,
    paddingRight: 4,
  },
  listText: {
    flex: 1,
    fontSize: 12,
    lineHeight: 1.2,
  },
})

interface ParseDescriptionOptions {
  customStyles?: Styles
  bulletSymbol?: string
}

/**
 * Parses HTML string and converts it to React PDF compatible JSX elements
 * @param html - The HTML string to parse
 * @param options - Optional configuration object
 * @returns JSX.Element compatible with react-pdf
 */
export function parseDescription(
  html: string,
  options: ParseDescriptionOptions = {},
): React.ReactElement {
  const { customStyles, bulletSymbol = '• ' } = options
  const parserStyles = customStyles || defaultParserStyles

  // Clean up the HTML structure but preserve formatting tags
  const structureCleaned = html
    .replace(/<div[^>]*>/g, '')
    .replace(/<\/div>/g, '')
    .replace(/<p[^>]*>\s*<ul>/g, '<ul>')
    .replace(/<\/ul>\s*<\/p>/g, '</ul>')
    .replace(/<p[^>]*>\s*<\/p>/g, '')
    // Normalize whitespace but preserve paragraph breaks
    .replace(/\n\s*\n/g, '\n')
    .trim()

  // Parse the cleaned HTML
  const parser = new DOMParser()
  const doc = parser.parseFromString(structureCleaned, 'text/html')

  // Helper function to parse text with inline formatting
  const parseInlineFormatting = (element: Element): (string | React.ReactElement)[] => {
    const result: (string | React.ReactElement)[] = []

    Array.from(element.childNodes).forEach((node, index) => {
      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent || ''
        if (text) {
          result.push(text)
        }
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const el = node as HTMLElement
        const tag = el.tagName.toLowerCase()
        const innerContent = parseInlineFormatting(el)

        switch (tag) {
          case 'b':
          case 'strong':
            result.push(
              <Text key={`bold-${index}`} style={parserStyles.bold}>
                {innerContent}
              </Text>,
            )
            break
          case 'i':
          case 'em':
            result.push(
              <Text key={`italic-${index}`} style={parserStyles.italic}>
                {innerContent}
              </Text>,
            )
            break
          case 'u':
            result.push(
              <Text key={`underline-${index}`} style={parserStyles.underline}>
                {innerContent}
              </Text>,
            )
            break
          case 'br':
            // Handle line breaks
            result.push('\n')
            break
          default:
            result.push(...innerContent)
            break
        }
      }
    })

    return result
  }

  // Process the document structure
  const elements: React.ReactElement[] = []
  let elementIndex = 0

  Array.from(doc.body.childNodes).forEach((node) => {
    if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as HTMLElement
      const tag = element.tagName.toLowerCase()

      if (tag === 'ul') {
        // Process list items
        const listItems = element.querySelectorAll('li')
        listItems.forEach((li, index) => {
          const content = parseInlineFormatting(li)
          if (content.length > 0) {
            elements.push(
              <View key={`list-${elementIndex}-${index}`} style={parserStyles.listItem}>
                <Text style={parserStyles.bullet}>{bulletSymbol}</Text>
                <Text style={parserStyles.listText}>{content}</Text>
              </View>,
            )
          }
        })
        elementIndex++
      } else if (tag === 'p') {
        // Process paragraph - handle both content and empty paragraphs for spacing
        const content = parseInlineFormatting(element)

        // Check if paragraph has any meaningful content or if it's meant for spacing
        const hasContent = content.some((item) =>
          typeof item === 'string' ? item.trim().length > 0 : true,
        )

        if (hasContent) {
          elements.push(
            <View key={`para-${elementIndex}`} style={parserStyles.paragraph}>
              <Text style={parserStyles.normal}>{content}</Text>
            </View>,
          )
        } else {
          // Empty paragraph - add spacing
          elements.push(
            <View
              key={`para-empty-${elementIndex}`}
              style={{ ...parserStyles.paragraph, minHeight: 12 }}
            >
              <Text style={parserStyles.normal}> </Text>
            </View>,
          )
        }
        elementIndex++
      } else if (tag === 'br') {
        // Handle standalone line breaks
        elements.push(
          <View key={`br-${elementIndex}`} style={{ ...parserStyles.paragraph, minHeight: 6 }}>
            <Text style={parserStyles.normal}> </Text>
          </View>,
        )
        elementIndex++
      } else {
        // Handle other block elements as paragraphs
        const content = parseInlineFormatting(element)
        if (content.length > 0) {
          elements.push(
            <View key={`block-${elementIndex}`} style={parserStyles.paragraph}>
              <Text style={parserStyles.normal}>{content}</Text>
            </View>,
          )
          elementIndex++
        }
      }
    } else if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent?.trim()
      if (text) {
        elements.push(
          <View key={`text-${elementIndex}`} style={parserStyles.paragraph}>
            <Text style={parserStyles.normal}>{text}</Text>
          </View>,
        )
        elementIndex++
      }
    }
  })

  // If no elements were parsed, try to handle as plain text
  if (elements.length === 0 && html.trim()) {
    // Split by line breaks and create paragraphs
    const lines = html.split(/\n|<br\s*\/?>/i)
    lines.forEach((line, index) => {
      const trimmedLine = line.replace(/<[^>]*>/g, '').trim()
      if (trimmedLine) {
        elements.push(
          <View key={`fallback-${index}`} style={parserStyles.paragraph}>
            <Text style={parserStyles.normal}>{trimmedLine}</Text>
          </View>,
        )
      }
    })
  }

  return <View>{elements}</View>
}

/**
 * Creates custom parser styles that can be used with parseDescription
 * @param baseStyles - Base styles to extend from
 * @param overrides - Style overrides
 * @returns StyleSheet object compatible with parseDescription
 */
export function createParserStyles(baseStyles?: Styles, overrides?: Styles) {
  return StyleSheet.create({
    ...defaultParserStyles,
    ...baseStyles,
    ...overrides,
  })
}
