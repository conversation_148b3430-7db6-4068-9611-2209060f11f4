// Route types definition
export const ROUTE_TYPES = {
  OPEN: 'open', // Available to everyone
  PUBLIC: 'public', // Only for non-authenticated users
  PRIVATE: 'private', // Only for authenticated users
} as const

// Route configuration - add your routes here
export const ROUTES = {
  '/': ROUTE_TYPES.OPEN,
  '/jobs': ROUTE_TYPES.OPEN,
  '/talent': ROUTE_TYPES.OPEN,
  '/about': ROUTE_TYPES.PUBLIC,
  '/contact': ROUTE_TYPES.PUBLIC,
  '/blogs': ROUTE_TYPES.PUBLIC,
  '/sign-in': ROUTE_TYPES.PUBLIC,
  '/sign-up': ROUTE_TYPES.PUBLIC,
  '/forgot-password': ROUTE_TYPES.PUBLIC,
  '/feed': ROUTE_TYPES.PRIVATE,
  '/resume': ROUTE_TYPES.PRIVATE,
  '/wallet': ROUTE_TYPES.PRIVATE,
  '/pricing': ROUTE_TYPES.PRIVATE,
  '/personal-information': ROUTE_TYPES.PRIVATE,
  '/orders': ROUTE_TYPES.PRIVATE,
  '/postjobs': ROUTE_TYPES.PRIVATE,
  '/postjob': ROUTE_TYPES.PRIVATE,
  '/alert': ROUTE_TYPES.PRIVATE,
  '/job': ROUTE_TYPES.PRIVATE,
  '/applicants': ROUTE_TYPES.PRIVATE,
} as const

// Dynamic route patterns - use regex patterns for dynamic routes
export const DYNAMIC_ROUTES = {
  // Username profile pages - public to everyone
  '^/[^/]+$': ROUTE_TYPES.OPEN, // Matches /username

  // Job detail pages - public to everyone
  '^/job/[^/]+$': ROUTE_TYPES.OPEN, // Matches /job/job-id

  // User-specific private routes
  //   "^/[^/]+/resume$": ROUTE_TYPES.PRIVATE, // Matches /username/resume
  //   "^/[^/]+/settings$": ROUTE_TYPES.PRIVATE, // Matches /username/settings

  // Add more dynamic patterns as needed
  '^/jobs/[^/]+$': ROUTE_TYPES.OPEN, // Matches /jobs/job-id
  '^/blogs/[^/]+$': ROUTE_TYPES.OPEN, // Matches /blogs/blog-id
} as const

// Helper function to check if pathname matches a pattern
const matchesPattern = (pathname: string, pattern: string): boolean => {
  const regex = new RegExp(pattern)
  return regex.test(pathname)
}

// Get route type for a given pathname (checks both static and dynamic routes)
const getRouteType = (pathname: string) => {
  // First check static routes
  const staticRouteType = ROUTES[pathname as keyof typeof ROUTES]
  if (staticRouteType) return staticRouteType

  // Then check dynamic routes
  for (const [pattern, routeType] of Object.entries(DYNAMIC_ROUTES)) {
    if (matchesPattern(pathname, pattern)) {
      return routeType
    }
  }

  return null // No matching route found
}

// Check if route is accessible based on authentication status
export const isRouteAccessible = (pathname: string, isAuthenticated: boolean): boolean => {
  const routeType = getRouteType(pathname)

  if (!routeType) return true // Default to accessible for unknown routes

  switch (routeType) {
    case ROUTE_TYPES.OPEN:
      return true
    case ROUTE_TYPES.PUBLIC:
      return !isAuthenticated
    case ROUTE_TYPES.PRIVATE:
      return isAuthenticated
    default:
      return true
  }
}

// Get redirect path based on route type and auth status
export const getRedirectPath = (pathname: string, isAuthenticated: boolean): string | null => {
  const routeType = getRouteType(pathname)

  if (!routeType) return null

  switch (routeType) {
    case ROUTE_TYPES.PUBLIC:
      return isAuthenticated ? '/feed' : null
    case ROUTE_TYPES.PRIVATE:
      return !isAuthenticated ? '/sign-in' : null
    default:
      return null
  }
}

// Helper function to get route type for debugging
export const getRouteTypeForPath = (pathname: string) => {
  return getRouteType(pathname)
}
