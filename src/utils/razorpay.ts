import { UserService } from '../services/UserService'
import type {
  RazorpayOptions,
  RazorpayResponse,
  RazorpayPaymentFailedResponse,
  RazorpayConstructor,
  RazorpayInstance,
} from '../models/Razorpay'
import type { ToastFunction } from '../components/ToastX'
import { redirect } from 'next/navigation'

declare global {
  interface Window {
    Razorpay: RazorpayConstructor
  }
}

export const loadRazorpayScript = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const script = document.createElement('script')
    script.src = 'https://checkout.razorpay.com/v1/checkout.js'
    script.onload = () => resolve(true)
    script.onerror = () => resolve(false)
    document.body.appendChild(script)
  })
}

export const initiateRazorpayPayment = async (
  orderId: string,
  id: string,
  toast: ToastFunction,
  handleClose?: () => void,
  handleSuccess?: (plan?: string) => void | Promise<void>,
  plan?: string,
) => {
  const res = await loadRazorpayScript()
  if (!res) {
    toast.warning('Razorpay SDK failed to load. Are you online?')
    return
  }

  const options: RazorpayOptions = {
    key: process.env.NEXT_PUBLIC_RAZORPAY_KEY || '',
    currency: 'INR',
    name: 'Lucres',
    order_id: orderId,
    handler: async function (response: RazorpayResponse) {
      const formData = {
        id: id,
        processor: {
          name: 'razorpay',
          orderId: response.razorpay_order_id,
          paymentId: response.razorpay_payment_id,
          signature: response.razorpay_signature,
        },
      }

      try {
        await UserService.verifyOrder(formData)
        toast.success('Payment successful!')
        handleClose?.()
        await handleSuccess?.(plan)
      } catch (error) {
        console.error('Verification error:', error)
        toast.error('Payment succeeded but verification failed. Please contact support.')
      }
    },
    theme: {
      color: '#2D4232',
    },
  }

  const rzp: RazorpayInstance = new window.Razorpay(options)

  rzp.on('payment.failed', function (response: RazorpayPaymentFailedResponse) {
    console.error('Payment failed:', response.error)
    toast.error('Payment failed. Please try again.')
  })

  rzp.open()
}
