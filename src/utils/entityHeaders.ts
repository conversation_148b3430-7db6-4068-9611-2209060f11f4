import { cookies } from 'next/headers'

function parseCookieString(cookieString: string): Record<string, string> {
  const result: Record<string, string> = {}
  cookieString.split(';').forEach((cookie) => {
    const [key, ...val] = cookie.trim().split('=')
    if (key) result[key] = decodeURIComponent(val.join('='))
  })
  return result
}

export async function getEntityHeadersFromCookies() {
  const cookieHeader = (await cookies()).toString()
  const parsed = parseCookieString(cookieHeader)
  const entityId = parsed['x-active-entity-id']
  const entityType = parsed['x-active-entity-type']
  const headers: Record<string, string> = {}
  if (entityId) headers['x-active-entity-id'] = entityId
  if (entityType) headers['x-active-entity-type'] = entityType
  return headers
}
