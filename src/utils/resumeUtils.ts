import { parseISO, format } from 'date-fns'
import DOMPurify from 'isomorphic-dompurify'
import React from 'react'

export const addItem = <T>(array: T[], newItem: T): T[] => {
  return [...array, newItem]
}

export const removeItem = <T>(array: T[], index: number): T[] => {
  return array.filter((_, idx) => idx !== index)
}

export const updateItem = <T>(array: T[], index: number, newValue: Partial<T>): T[] => {
  return array.map((item, idx) => (idx === index ? { ...item, ...newValue } : item))
}

export const isValidDateFormat = (date: string): boolean => {
  return /^(0[1-9]|1[0-2])\/\d{2,4}$/.test(date)
}

export const isDateValid = (date: string): boolean => {
  if (date === 'Present') return true
  const [month, year] = date.split('/')
  const monthNum = parseInt(month, 10)
  const yearNum = parseInt(year, 10)
  return !isNaN(monthNum) && monthNum >= 1 && monthNum <= 12 && !isNaN(yearNum)
}

export const isEndAfterStart = (start: string, end: string): boolean => {
  if (end === 'Present') return true
  if (!start || !end) return true

  const parseDate = (date: string) => {
    const [month, year] = date.split('/')
    return {
      month: parseInt(month, 10),
      year: year.length === 2 ? 2000 + parseInt(year, 10) : parseInt(year, 10),
    }
  }

  try {
    const startDate = parseDate(start)
    const endDate = parseDate(end)
    return (
      endDate.year > startDate.year ||
      (endDate.year === startDate.year && endDate.month >= startDate.month)
    )
  } catch {
    return false
  }
}

export const validateDates = (start: string, end: string | undefined, isCurrent: boolean) => {
  const errors = {
    start: '',
    end: '',
  }

  // Validate start date
  if (start && !isValidDateFormat(start)) {
    errors.start = 'Invalid format (MM/YY or MM/YYYY)'
  }
  if (start && !isDateValid(start)) {
    errors.start = 'Invalid date (01-12 for month)'
  }

  // Validate end date based on isCurrent
  if (!isCurrent) {
    if (!end) {
      errors.end = 'End date is required when not currently working here.'
    } else if (end !== 'Present' && !isValidDateFormat(end)) {
      errors.end = 'Invalid format (MM/YY or MM/YYYY)'
    } else if (end !== 'Present' && !isDateValid(end)) {
      errors.end = 'Invalid date (01-12 for month)'
    } else if (start && end && end !== 'Present' && !isEndAfterStart(start, end)) {
      errors.end = 'End date cannot be before start date'
    }
  } else {
    if (end !== 'Present') {
      errors.end = 'End date should be "Present" if currently working here.'
    }
  }

  return errors
}

export const formatDate = (date: string): string => {
  const monthNames = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ]

  if (date === 'Month Year') return 'Month Year'
  if (date === 'Present') return 'Present'

  // Try to parse as "Month yyyy" (e.g., "Mar 2018")
  const spaceParts = date.split(' ')
  if (spaceParts.length === 2) {
    const monthAbbr = spaceParts[0]
    const yearStr = spaceParts[1]
    if (monthNames.includes(monthAbbr) && /^\d{4}$/.test(yearStr)) {
      return date
    }
  }

  // Try to parse as "mm/yyyy" (e.g., "03/2018")
  const slashParts = date.split('/')
  if (slashParts.length === 2) {
    const monthStr = slashParts[0]
    const yearStr = slashParts[1]
    const month = parseInt(monthStr, 10)

    if (!isNaN(month) && month >= 1 && month <= 12) {
      const year = parseInt(yearStr, 10)
      if (!isNaN(year)) {
        let fullYear
        if (yearStr.length === 2) {
          fullYear = 2000 + year
        } else if (yearStr.length === 4) {
          fullYear = year
        } else {
          return 'Invalid Date'
        }
        return `${monthNames[month - 1]} ${fullYear}`
      }
    }
  }

  return 'Invalid Date'
}

/**
 * Convert a "MM/YY" or "MM/YYYY" string into an ISO timestamp
 * pointing at the very start of that month (UTC).
 */

export const convertToISODate = (dateStr: string): string | undefined => {
  if (!dateStr || dateStr === 'Present') return undefined

  const parts = dateStr.split('/')
  if (parts.length !== 2) {
    return undefined // Return undefined for incomplete dates
  }

  const [monthStr, yearStr] = parts
  const month = parseInt(monthStr, 10)
  let year = parseInt(yearStr, 10)

  if (yearStr.length === 2) {
    year += 2000 // Assume 20xx for two-digit years
  }

  if (!isNaN(month) && !isNaN(year) && month >= 1 && month <= 12) {
    return `${year}-${month.toString().padStart(2, '0')}-01`
  }

  return undefined
}

export const formatMonthYear = (date: string | undefined): string => {
  if (!date) return ''
  const d = new Date(date)
  return `${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`
}

/**
 * Formats an ISO date string to DD/MM/YYYY.
 * @param {string} isoDate - ISO date string (e.g., '1981-02-21T00:00:00.000Z')
 * @returns {string} - Formatted date (e.g., '21/02/1981') or empty string if invalid
 */
export const formatToDDMMYYYY = (isoDate: string | undefined): string => {
  if (!isoDate) return ''
  try {
    const date = parseISO(isoDate)
    return format(date, 'dd/MM/yyyy')
  } catch (error) {
    console.error('Invalid date:', isoDate)
    return ''
  }
}

/**
 * Formats an ISO date string to "EEE MMM dd yyyy" format (e.g., "Sat Jan 18 2025").
 * @param {string} isoDate - ISO date string (e.g., '2022-10-22T06:34:14.467Z')
 * @returns {string} - Formatted date (e.g., 'Sat Oct 22 2022') or empty string if invalid
 */
export const formatToWeekdayMonthDay = (isoDate: string | undefined): string => {
  if (!isoDate) return ''
  try {
    const date = parseISO(isoDate)
    return format(date, 'EEE MMM dd yyyy')
  } catch (error) {
    console.error('Invalid date:', isoDate)
    return ''
  }
}

// Add this type definition near the top of your file
export type FormErrors = {
  aboutMe?: string
  experiences: Array<Record<string, string>>
  educations: Array<Record<string, string>>
  achievements: Array<Record<string, string>>
  skills: Array<Record<string, string>>
  languages: Array<Record<string, string>>
  socialLinks: Array<Record<string, string>>
  references: Array<Record<string, string>>
}

export function debounce<T extends (...args: any[]) => void>(
  func: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout>

  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

//Html Santization
// Base configuration for resume templates
const RESUME_SANITIZE_CONFIG = {
  // Allow only safe HTML tags commonly used in resumes
  ALLOWED_TAGS: [
    'p',
    'br',
    'strong',
    'b',
    'em',
    'i',
    'u',
    'ul',
    'ol',
    'li',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    'blockquote',
    'span',
    'div',
  ],
  // Allow only safe attributes (be cautious with 'style')
  ALLOWED_ATTR: ['style'],
  // Explicitly forbid dangerous attributes
  FORBID_ATTR: [
    'onerror',
    'onload',
    'onclick',
    'onmouseover',
    'onmouseout',
    'onfocus',
    'onblur',
    'onkeydown',
    'onkeyup',
    'onsubmit',
    'onchange',
    'onselect',
    'onreset',
    'onabort',
    'onunload',
  ],
  // Forbid dangerous tags
  FORBID_TAGS: [
    'script',
    'object',
    'embed',
    'link',
    'style',
    'iframe',
    'frame',
    'frameset',
    'meta',
    'base',
    'form',
    'input',
    'button',
    'select',
    'textarea',
    'applet',
  ],
  // Keep whitespace formatting
  KEEP_CONTENT: true,
  // Return DOM instead of string for better performance
  RETURN_DOM_FRAGMENT: false,
  // Remove comments
  ALLOW_COMMENTS: false,
}

// Main sanitization function for resume content
export const sanitizeHtml = (html: string): string => {
  if (!html || typeof html !== 'string') {
    return ''
  }

  return DOMPurify.sanitize(html, RESUME_SANITIZE_CONFIG)
}

// More restrictive version for basic text formatting only
export const sanitizeBasicHtml = (html: string): string => {
  if (!html || typeof html !== 'string') {
    return ''
  }

  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'b', 'em', 'i', 'u', 'ul', 'ol', 'li'],
    ALLOWED_ATTR: [],
    FORBID_ATTR: ['style', 'class', 'id'],
    FORBID_TAGS: [...RESUME_SANITIZE_CONFIG.FORBID_TAGS],
    KEEP_CONTENT: true,
  })
}

// Hook for sanitizing HTML (useful for other scenarios)
export const useSanitizedHtml = (html: string, restrictive = false) => {
  return React.useMemo(() => {
    return restrictive ? sanitizeBasicHtml(html) : sanitizeHtml(html)
  }, [html, restrictive])
}
