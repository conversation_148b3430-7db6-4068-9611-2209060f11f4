export const getCroppedImg = async (
  imageSrc: string,
  pixelCrop: { x: number; y: number; width: number; height: number },
): Promise<Blob> => {
  const image = new Image()
  image.src = imageSrc

  await new Promise((resolve) => {
    image.onload = resolve
  })

  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')

  if (!ctx) throw new Error('Canvas context is not available')

  canvas.width = pixelCrop.width
  canvas.height = pixelCrop.height

  ctx.drawImage(
    image,
    pixelCrop.x,
    pixelCrop.y,
    pixelCrop.width,
    pixelCrop.height,
    0,
    0,
    canvas.width,
    canvas.height,
  )

  return new Promise((resolve, reject) => {
    canvas.toBlob((blob) => {
      if (blob) {
        resolve(blob)
      } else {
        reject(new Error('Failed to create blob'))
      }
    }, 'image/png')
  })
}

export const handleFileChange = (
  event: React.ChangeEvent<HTMLInputElement>,
  onFileSelected: (imageSrc: string) => void,
  handleShowToast: (type: 'success' | 'info' | 'error' | 'loading', message: string) => void,
) => {
  const file = event.target.files?.[0]
  if (!file) return

  const validTypes = ['image/jpeg', 'image/png', 'image/jpg']
  if (!validTypes.includes(file.type)) {
    handleShowToast('error', 'Please upload a valid image (JPEG, JPG, PNG).')
    return
  }

  if (file.size > 5 * 1024 * 1024) {
    handleShowToast('error', 'File size must be less than 5MB.')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    if (e.target?.result) {
      onFileSelected(e.target.result as string)
    }
  }
  reader.readAsDataURL(file)
}

// Helper type for uploadService response
interface UploadServiceResponse {
  status: string
  data: { secureUrl?: string; url?: string }[] | { secureUrl?: string; url?: string }
}

export const handleCropComplete = async (
  croppedFile: File,
  uploadService: (formData: FormData) => Promise<UploadServiceResponse>,
  onSuccess: () => void,
  onError: (error: string) => void,
  fileType: 'profile' | 'cover',
  handleShowToast: (
    type: 'success' | 'info' | 'error' | 'loading',
    message: string,
    timeout?: number,
  ) => number,
  removeToast: (id: number) => void,
  onUrlReceived?: (url: string) => void,
) => {
  const formData = new FormData()
  formData.append('images', croppedFile)
  formData.append('type', fileType)

  // Step 1: Show loading toast and capture the ID
  const toastId = handleShowToast('loading', 'Uploading image...')

  try {
    const response = await uploadService(formData)
    if (response.status === 'success') {
      const uploadedUrl = Array.isArray(response.data)
        ? response.data[0]?.secureUrl || response.data[0]?.url
        : response.data?.secureUrl || response.data?.url
      onUrlReceived?.(uploadedUrl || '')
      removeToast(toastId)
      handleShowToast('success', 'Image uploaded successfully', 3000)
      onSuccess()
    }
  } catch (error) {
    const err = error as { message?: string }
    console.error('Error uploading image:', error)
    removeToast(toastId)
    handleShowToast('error', err.message || 'Error uploading image.', 3000)
    onError(err.message || 'Error uploading image.')
  }
}
