'use client'
import { useState, useEffect } from 'react'

function useTabVisibility(): boolean {
  const [isTabVisible, setIsTabVisible] = useState<boolean>(true)

  const handleVisibilityChange = () => {
    setIsTabVisible(!document.hidden)
  }

  useEffect(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [])

  return isTabVisible
}

export default useTabVisibility
