import { Resume } from '../models/Resume'

export const useResumeProgress = (
  resume: Resume,
  removeSummary: boolean,
  isProTemplate: boolean,
): number => {
  const sections = [
    {
      name: 'Professional summary',
      isFilled: resume.aboutMe.trim() !== '',
      include: !removeSummary,
    },
    {
      name: 'Experience',
      isFilled: resume.experiences.some((exp) => exp.designation.name && exp.company.name),
    },
    {
      name: 'Education',
      isFilled: resume.educations.some((edu) => edu.institution.name && edu.degree.name),
    },
    {
      name: 'Achievement',
      isFilled: resume.achievements.some((ach) => ach.title),
    },
    {
      name: 'Skills',
      isFilled: resume.skills.some((skill) => skill.name),
    },
    {
      name: 'Languages',
      isFilled: resume.languages.some((lang) => lang.name),
    },
    {
      name: 'Social links',
      isFilled: resume.socialLinks.some((sl) => sl.title && sl.url),
      include: isProTemplate,
    },
    {
      name: 'References',
      isFilled: resume.references.some((ref) => ref.name && ref.email),
      include: isProTemplate,
    },
  ]

  const includedSections = sections.filter((section) => section.include !== false)
  const filledSections = includedSections.filter((section) => section.isFilled).length
  const totalSections = includedSections.length
  return totalSections > 0 ? Math.round((filledSections / totalSections) * 100) : 0
}
