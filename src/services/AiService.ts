import apiClient from '../config/apiClient'
import { TokenService } from './TokenService'

/**
 *  AiResumeService provides static methods to interact with AI chat endpoints for the resume builder.
 */
export class AiService {
  /**
   * Fetches job description suggestions from the AI endpoint.
   * @param context - The context string containing job details
   * @param jobSessionId - Optional job session ID for continuing a session
   * @returns A promise resolving to the AI generated suggestions
   * @throws An error if the request fails.
   */
  public static async getSuggestions(
    context: string,
    section?: 'jobDesc' | 'jobSkills',
  ): Promise<any> {
    try {
      // Always read jobSessionId from localStorage

      const jobSessionId = localStorage.getItem('jobSessionId')
      const endpoint = section
        ? `v2/job/temporary/aigenerate?context=${context}&jobSessionId=${jobSessionId}&section=${section}`
        : `v2/job/temporary/aigenerate?context=${context}`

      const token = TokenService.getAccessToken()
      const headers = token ? { Authorization: `Bearer ${token}` } : undefined

      const response = await apiClient.get(endpoint, { headers })

      // Store new jobSessionId from response if provided
      if (!('sessionExpired' in response.data) || response.data.sessionExpired) {
        const newJobSessionId = response.data?.data?.jobSessionId
        if (newJobSessionId) {
          localStorage.setItem('jobSessionId', newJobSessionId)
        }
      }

      return response.data
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message ||
          'Failed to get job description suggestions. Please try again.',
      )
    }
  }

  // public static async getSuggestion(
  //   entity: string,
  //   id: string,
  //   section: string,
  //   context?: string,
  //   action?: string,
  //   lastResponse?: string,
  //   existingSkills?: string[],
  // ): Promise<any> {
  //   try {
  //     // Construct the base endpoint
  //     let endpoint = `v2/${entity}/aigenerate/${id}?section=${section}`

  //     // Add action and lastResponse if both are provided
  //     if (action && lastResponse) {
  //       endpoint += `&action=${action}&lastResponse=${encodeURIComponent(lastResponse)}`
  //     }
  //     // Otherwise, add context if provided
  //     else if (context) {
  //       endpoint += `&context=${encodeURIComponent(context)}`
  //     }

  //     // Add existing skills as repeated query parameters if provided
  //     if (existingSkills && existingSkills.length > 0) {
  //       const skillsQuery = existingSkills
  //         .map((skill) => `skills=${encodeURIComponent(skill)}`)
  //         .join('&')
  //       endpoint += `&${skillsQuery}`
  //     }
  //     // Get the access token for authentication
  //     const token = TokenService.getAccessToken()
  //     const headers = token ? { Authorization: `Bearer ${token}` } : undefined

  //     // Make the API request
  //     const response = await apiClient.get(endpoint, { headers })
  //     return response.data
  //   } catch (error: any) {
  //     throw new Error(
  //       error.response?.data?.message || 'Failed to fetch AI suggestion. Please try again.',
  //     )
  //   }
  // }
}
