import apiClient from '../config/apiClient'
import { TokenService } from './TokenService'

/**
 * SrpService provides static methods to fetch both job and talent listings
 * from the SRP endpoints using the v2 API.
 *
 * The token stored in localStorage is validated using TokenService and then included
 * in the request headers.
 */
export class SrpService {
  /**
   * Fetches a list of job listings from the SRP jobs endpoint.
   *
   * @param page - The current page number (default is 1).
   * @param limit - The number of items per page (default is 25).
   * @param filters - Optional filters object to refine the job search.
   *                  Example: { workExperience: [{ min: 10, max: 30 }] }
   * @returns A promise that resolves to the jobs data.
   */
  public static async getJobs(page: number = 1, filters?: any): Promise<any> {
    try {
      let endpoint = `v2/srp/jobs/?page=${page}`
      if (filters) {
        endpoint += `&filters=${encodeURIComponent(JSON.stringify(filters))}`
      }
      const token = TokenService.getAccessToken()
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: token ? `Bearer ${token}` : '',
        },
      })
      return response.data
    } catch (error: any) {
      throw error.response
      // throw new Error(
      //   error.response?.data?.message || 'Failed to fetch SRP jobs data. Please try again.',
      // )
    }
  }

  /**
   * Fetches a list of job listings from the SRP jobs endpoint.
   *
   * @param page - The current page number (default is 1).
   * @param limit - The number of items per page (default is 25).
   * @param filters - Optional filters object to refine the job search.
   *                  Example: { workExperience: [{ min: 10, max: 30 }] }
   * @returns A promise that resolves to the jobs data.
   */
  public static async getSavedBookmark(type: 'JOBS_SRP' | 'TALENT_SRP'): Promise<any> {
    try {
      let endpoint = `v2/bookmark?type=${type}`
      const token = TokenService.getAccessToken()
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: token ? `Bearer ${token}` : '',
        },
      })
      return response.data
    } catch (error: any) {
      console.log('error')
      throw error
      // throw new Error(
      //   error.response?.data?.message || 'Failed to fetch SRP jobs data. Please try again.',
      // )
    }
  }

  public static async getAppliedJobs(): Promise<any> {
    try {
      let endpoint = `v2/users/jobs/applications`
      const token = TokenService.getAccessToken()
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: token ? `Bearer ${token}` : '',
        },
      })
      return response.data
    } catch (error: any) {
      throw error.response
      // throw new Error(
      //   error.response?.data?.message || 'Failed to fetch SRP jobs data. Please try again.',
      // )
    }
  }

  /**
   * Fetches a list of talent listings from the SRP talent endpoint.
   *
   * @param page - The current page number (default is 1).
   * @param limit - The number of items per page (default is 25).
   * @param filters - Optional filters object to refine the talent search.
   *                  Example: { workExperience: [{ min: 0, max: 1 }] }
   * @returns A promise that resolves to the talent data.
   */
  public static async getTalents(
    page: number = 1,
    limit: number = 25,
    filters?: any,
  ): Promise<any> {
    try {
      let endpoint = `v2/srp/talent?page=${page}&limit=${limit}`
      if (filters) {
        endpoint += `&filters=${encodeURIComponent(JSON.stringify(filters))}`
      }
      const token = TokenService.getAccessToken()
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: token ? `Bearer ${token}` : '',
        },
      })
      // console.log("GET TALENTS", response.data);
      return response.data
    } catch (error: any) {
      throw error.response
      // throw new Error(
      //   error.response?.data?.message || 'Failed to fetch SRP talent data. Please try again.',
      // )
    }
  }

  /**
   * Performs a global search for both users and jobs based on a query string.
   *
   * @param query - The search term entered by the user.
   * @returns A promise that resolves to an object containing `users` and `jobs`.
   */
  public static async searchGlobal(query: string): Promise<any> {
    try {
      const endpoint = `v2/srp/global?query=${encodeURIComponent(query)}`
      const token = TokenService.getAccessToken()
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: token ? `Bearer ${token}` : '',
        },
      })
      return response.data
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 'Failed to perform global search. Please try again.',
      )
    }
  }

  /**
   * Performs a global search for both users and jobs based on a query string.
   *
   * @param query - The search term entered by the user.
   * @returns A promise that resolves to an object containing `users` and `jobs`.
   */
  public static async repostJob(id: string): Promise<any> {
    try {
      const endpoint = `v2/jobs/${id}/repost`
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.post(
        endpoint,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )
      return response.data
    } catch (error: any) {
      throw new Error(error.message || 'Failed to repost')
    }
  }
  /**
   * Undoes a repost of a specific job.
   *
   * @param quoteId - The ID of the job whose repost should be undone
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async undoRePostJob(jobId: string): Promise<any> {
    const endpoint = `v2/repost/${jobId}/undo`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.delete(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (response.data.status === 'error') {
        throw new Error(response.data.message || 'Unable to undo repost job')
      }
      return response.data
    } catch (error: any) {
      throw new Error(
        error?.message || error?.response?.data?.message || 'Unable to undo repost job',
      )
    }
  }

  /**
   * Undoes a repost of a specific job.
   *
   * @param quoteId - The ID of the job whose repost should be undone
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async WithdrawApplication(applicationId: string, userId: string): Promise<any> {
    const endpoint = `v2/jobs/${applicationId}/withdraw`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.put(
        endpoint,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
            // 'x-active-entity-id': userId,
            // 'x-active-entity-type': 'USER',
          },
        },
      )

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }
}
