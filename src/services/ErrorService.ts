type ErrorVariant =
  | 'no-data'
  | 'offline'
  | 'something-went-wrong'
  | 'downtime'
  | 'permission-denied'
  | 'error-404'

type ErrorHandlerResult = {
  variant: ErrorVariant
  message?: string
} | null

export class ErrorService {
  static handleHttpError(error: any): ErrorHandlerResult {
    const status = error?.status
    const message = error?.data?.message

    switch (status) {
      case 400:
        return {
          variant: 'something-went-wrong',
          message: message || 'Bad request.',
        }
      case 401:
        if (message === 'Your session has expired. Please log in again.') {
          return { variant: 'permission-denied', message }
        } else {
          return {
            variant: 'permission-denied',
            message: message || 'Unauthorized access.',
          }
        }
      case 403:
        return { variant: 'permission-denied', message: 'Access denied.' }
      case 404:
        return {
          variant: 'error-404',
          message: 'The requested resource was not found.',
        }
      default:
        if (status >= 500) {
          return {
            variant: 'downtime',
            message: 'Server error. Please try again later.',
          }
        }
        return null
    }
  }

  static handleApiError(error: any): { message: string; isDefault: boolean } | ErrorHandlerResult {
    const message = error?.message || error?.data?.message || 'Something went wrong.'

    switch (message) {
      case 'Bookmark already exists.':
        return { message: 'Bookmark already exists.', isDefault: false }

      case "Oops, we couldn't find this in our records!":
        return ErrorService.handleHttpError({
          status: 404,
          data: { message },
        })

      case 'No Jobs available with provided details.':
        return ErrorService.handleHttpError({
          status: 404,
          data: { message },
        })

      /* Post Service  */
      // uploadPostImages
      case 'Image file format webp not allowed':
        return { message, isDefault: false }

      //image size greater than 10MB
      case 'Network Error':
        return { message, isDefault: false }

      //delete post, update post and edit post
      case 'does not meet minimum length of 24':
        return { message, isDefault: false }

      //ike post
      case 'Article already.':
        return { message: 'Article Already Liked', isDefault: false }
      // dislike post
      case 'No likes found.':
        return { message: 'Like Failed', isDefault: false }

      //view post
      case 'does not meet minimum length of 7':
        return { message, isDefault: false }

      case 'does not meet maximum length of 8':
        return { message, isDefault: false }
      //job dashboard  - contact unlock
      case 'This application is not eligible for free unlock.':
        return { message, isDefault: false }

      // repost post
      case 'No articles available with the provided details.':
        return { message, isDefault: false }
      // undo repost
      case 'Failed to remove rePost. The rePost may no longer exist.':
        return { message: 'Undo Repost Failed', isDefault: false }

      //download resume
      case 'Access denied: User has opted out of recruitment contacts.':
        return { message: ' User does not want to share contact info.', isDefault: false }

      // like quote
      case 'No Quote found with the provided details.':
        return { message, isDefault: false }

      // subscription
      case 'DOWNGRADE_NOT_ALLOWED_ERROR':
        return { message: 'You cannot downgrade your plan', isDefault: false }

      case 'JOB_EITHER_CLOSED_OR_NOT_FOUND':
        return { message: 'This job is closed', isDefault: false }

      default:
        /* Job Service */
        // applicantStatus
        if (message.includes('is/are already bought.')) {
          return { message: 'This applicant is already bought', isDefault: false }
        }

        return { message, isDefault: true }
    }
  }
}
