import apiClient from '../config/apiClient'
import { CreateCompanyRequest } from '../models/Company'
import { TokenService } from './TokenService'

/**
 * AuthService provides static methods to perform authentication-related operations,
 * such as signing in, signing up, logging out, resetting passwords, and signing in via Google.
 *
 * Note: All token management is now handled separately by the TokenService.
 */
export class CompanyService {
  /**
   * Validates the existing email while creating company
   *
   * @returns A promise that resolves when validation is completed.
   */
  public static async validatePermalink(permalink: string): Promise<any> {
    try {
      const response = await apiClient.get(`v2/company-pages/${permalink}/validate`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Logout failed. Please try again.')
    }
  }

  /**
   * Validates the existing email while sign-up
   *
   * @returns A promise that resolves when validation is completed.
   */
  public static async validateEmail(email: string): Promise<any> {
    try {
      const response = await apiClient.get(`v2/users/contact/${email}/validate`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Logout failed. Please try again.')
    }
  }

  /**
   * Registers a new user with the provided signup data.
   *
   * @param userData - An object containing the user's signup details.
   * @returns A promise that resolves to the sign-up response.
   */
  public static async createCompany(
    companyData: CreateCompanyRequest,
    userId: string,
  ): Promise<any> {
    try {
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.post('v2/company-pages', companyData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'x-active-entity-id': userId,
          'x-active-entity-type': 'USER',
        },
      })
      return response
    } catch (error: any) {
      throw error.response
    }
  }

  /**
   * Fetches industry suggestions based on query
   *
   * @param query - The search string
   * @returns A promise resolving to the suggestions list
   */
  public static async searchIndustry(query: string): Promise<any> {
    try {
      const response = await apiClient.get(`v2/career/search/industry/suggestions`, {
        params: { query },
      })
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch industry suggestions')
    }
  }

  /**
   * Uploads a banner image.
   *
   * @param formData - The FormData containing the image file.
   * @returns A promise resolving to the upload response.
   */
  public static async uploadPhoto(formData: FormData): Promise<any> {
    try {
      const endpoint = `v2/jobs/images/upload`
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.post(endpoint, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      })

      if (response.headers['x-access-token'] && token !== response.headers['x-access-token']) {
        TokenService.setAccessToken(response.headers['x-access-token'])
      }

      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to upload banner image.')
    }
  }
  /**
   * Unlocks contact information of a free applicant.
   *
   * @param {any} formData - The form data required to unlock the applicant's contact info.
   * @returns {Promise<any>} The response data from the server.
   * @throws {Error} If the request fails or the token is missing.
   */
  public static async unlockFreeAapplicantContact(formData: any) {
    const endpoint = `v2/users/unlock-free-applicant-contact`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.post(endpoint, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      if (response.data.status === 'error') {
        throw response
      }

      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Unlocks contact information of an applicant by their application ID.
   *
   * @param {any} applicationId - The ID of the application for which to unlock contact info.
   * @returns {Promise<any>} The response data from the server.
   * @throws {Error} If the request fails or the token is missing.
   */
  public static async unlockPaidAapplicantContact(formData: any) {
    const endpoint = `v2/users/unlock-paid-applicant-contact`
    // users/applicant-contact?applicationId=683984e3eefbfcf41d3fc10c
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.post(endpoint, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      if (response.data.status === 'error') {
        throw response
      }

      return response.data
    } catch (error: any) {
      throw error
    }
  }
}
