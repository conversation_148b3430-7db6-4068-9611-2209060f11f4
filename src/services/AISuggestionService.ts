import apiClient from '../config/apiClient'
import { TokenService } from './TokenService'

/**
 * AI Resume SuggestionService provides methods to interact with AI suggestion endpoints for resume sections.
 */
export class AISuggestionService {
  /**
   * Fetches AI-generated suggestions for a specific resume section.
   * @param entity - The entity type (e.g., 'user').
   * @param id - The 24-character MongoDB ObjectId of the entity.
   * @param section - The resume section to generate suggestions for (e.g., 'aboutMe').
   * @param context - Optional context for the AI generation (e.g., job profile).
   * @param action - Optional action to perform (e.g., 'improve', 'longer', 'shorter').
   * @param lastResponse - Optional previous response to modify.
   *  * @param existingSkills - Optional array of existing skills to exclude from suggestions (e.g., ['python', 'sql']).
   * @returns A promise resolving to the AI-generated content.
   * @throws An error if the request fails.
   */
  public static async getSuggestion(
    entity: string,
    id: string,
    section: string,
    context?: string,
    action?: string,
    lastResponse?: string,
    existingSkills?: string[],
  ): Promise<any> {
    try {
      // Validate entity and id
      if (entity !== 'user') {
        throw new Error('Invalid entity. Must be "user" .')
      }
      if (!/^[0-9a-fA-F]{24}$/.test(id)) {
        throw new Error('Invalid ID. It must be a 24-character MongoDB ObjectId.')
      }

      // Construct the base endpoint
      let endpoint = `v2/${entity}/aigenerate/${id}?section=${section}`

      // Add action and lastResponse if both are provided
      if (action && lastResponse) {
        endpoint += `&action=${action}&lastResponse=${encodeURIComponent(lastResponse)}`
      }
      // Otherwise, add context if provided
      else if (context) {
        endpoint += `&context=${encodeURIComponent(context)}`
      }

      // Add existing skills as repeated query parameters if provided
      if (existingSkills && existingSkills.length > 0) {
        const skillsQuery = existingSkills
          .map((skill) => `skills=${encodeURIComponent(skill)}`)
          .join('&')
        endpoint += `&${skillsQuery}`
      }
      // Get the access token for authentication
      const token = TokenService.getAccessToken()
      const headers = token ? { Authorization: `Bearer ${token}` } : undefined

      // Make the API request
      const response = await apiClient.get(endpoint, { headers })
      return response.data
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 'Failed to fetch AI suggestion. Please try again.',
      )
    }
  }
}
