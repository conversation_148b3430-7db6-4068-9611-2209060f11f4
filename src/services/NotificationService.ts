import apiClient from '../config/apiClient'
import { TokenService } from './TokenService'

// Notification interface defining the structure of a single notification object
export interface Notification {
  _id: string
  action: string
  actionId: string
  createdAt: string
  status: 'SENT' | 'READ' | 'UNREAD'
  updatedAt: string
  permalink: string
  postType: string
  otherUser: {
    givenName: string
    familyName: string
    permalink: string
    profileImage: {
      url: string
    }
  }
  user: {
    givenName: string
    familyName: string
    permalink: string
    profileImage: {
      url: string
    }
  }
}
// Interface for the response returned when fetching notifications
export interface NotificationsResponse {
  status: string
  message: string
  messageType: string
  data: {
    items: Notification[]
    paginator: {
      total: number
      limit: number
      page: number
      pageCount: number
      pagingCounter: number
      hasNextPage: boolean
      hasPrevPage: boolean
    }
  }
}

export class NotificationService {
  /**
   * Fetches notifications based on provided parameters.
   *
   * @param status - Filter notifications by status (e.g., 'ALL', 'UNREAD')
   * @param page - Page number for pagination
   * @param limit - Number of notifications per page
   * @returns A promise resolving to the notifications data
   */

  // get all the notifications
  // public static async getNotifications(
  //   status: string = 'ALL',
  //   page?: number,
  //   limit?: number,
  // ): Promise<NotificationsResponse> {
  //   try {
  //     const endpoint = page
  //       ? `v2/notifications?status=${status}&page=${page}&limit=${limit}`
  //       : `v2/notifications?status=${status}`
  //     const token = TokenService.getAccessToken()
  //     if (!token) {
  //       throw new Error('No token found. Please log in.')
  //     }
  //     const response = await apiClient.get(endpoint, {
  //       headers: {
  //         Authorization: `Bearer ${token}`,
  //       },
  //     })
  //     return response.data
  //   } catch (error: any) {
  //     throw new Error(error.response?.data?.message || 'Failed to fetch notifications.')
  //   }
  // }

  public static async getNotifications(
    status: string = 'ALL',
    page?: number,
    limit?: number,
  ): Promise<NotificationsResponse> {
    try {
      const params = new URLSearchParams({ status })
      if (page !== undefined) params.append('page', String(page))
      if (limit !== undefined) params.append('limit', String(limit))

      const token = TokenService.getAccessToken()
      // if (!token) {
      //   throw new Error('No token found. Please log in.')
      // }

      const response = await apiClient.get(`v2/notifications?${params.toString()}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      return response.data
    } catch (error: any) {
      throw error.response
      // throw new Error(error.response?.data?.message || 'Failed to fetch notifications.')
    }
  }

  //  Marks all notifications as read.
  public static async markAllAsRead(): Promise<any> {
    try {
      const endpoint = 'v2/notifications/status?mark=ALL'
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.put(
        endpoint,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to mark notifications as read.')
    }
  }

  // Marks a single notification as read using its ID.
  public static async markAsRead(notificationId: string): Promise<any> {
    try {
      const endpoint = 'v2/notifications/status'
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.put(
        endpoint,
        {
          notifications: [
            {
              id: notificationId,
              status: 'READ',
            },
          ],
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to mark notification as read.')
    }
  }
}
