import apiClient from '../config/apiClient'
import { PostJobRequest } from '../models/PostJobs'
import { TokenService } from './TokenService'

export class JobService {
  /**
   * Post the job
   *
   *
   * @param companyData - An object containing the job details.
   * @returns A promise that resolves when job post is completed.
   */
  public static async postJob(companyData: PostJobRequest, userId: string): Promise<any> {
    try {
      console.log("POSTJOB API IS CALLED");
      console.log("COMPANYDATA", companyData);
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.post('v2/jobs', companyData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'x-active-entity-id': userId,
          'x-active-entity-type': 'USER',
        },
      })
      console.log("RESPONSE", response.data);
      if (response.data.status === 'error') throw response
      return response
    } catch (error: any) {
      throw error
    }
  }
  // APPLY JOB
  public static async applyJob(JobId: string, userId: string): Promise<any> {
    try {
      const token = TokenService.getAccessToken()
      //   if (!token) {
      //     throw new Error('No token found. Please log in.')
      //   }
      const endpoint = `v2/jobs/${JobId}/applications`
      const response = await apiClient.post(
        endpoint,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'x-active-entity-id': userId,
            'x-active-entity-type': 'USER',
          },
        },
      )
      if (response.data.status === 'error') throw response
      return response
    } catch (error: any) {
      throw error
    }
  }

  public static async getGobByUser(username: string, status?: 'open' | 'close'): Promise<any> {
    try {
      const token = TokenService.getAccessToken()
      const entityType = localStorage.getItem("x-active-entity-type")?.toLowerCase();
      
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.get(
        `v2/${entityType}/${username}/jobs${status ? `?status=${status}` : ''}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )
      console.log("RESPONSE", response.data);
      if (response.data.status === 'error') throw response
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  public static async updateJob(jobData: PostJobRequest, userId: string): Promise<any> {
    try {
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.put(`v2/jobs/`, jobData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'x-active-entity-id': userId,
          'x-active-entity-type': 'USER',
        },
      })
      if (response.data.status === 'error') throw response
      return response
    } catch (error: any) {
      throw error
    }
  }

  public static async getJobApplicationsCount(
    jobId: string,
    status?: string,
    sort?: string,
  ): Promise<any> {
    try {
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      if (!status) status = 'ALL'
      if (!sort) sort = 'desc'
      const endpoint = `v2/jobs/${jobId}/applications?status=${status}&sort=${sort}`
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') throw response
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  public static async getApplicantDetails(applicantId: string): Promise<any> {
    try {
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.get(`v2/career/applicant/${applicantId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') throw response
      return response.data
    } catch (error: any) {
      throw error
    }
  }
  public static async getApplicantContact(applicantId: string): Promise<any> {
    try {
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.get(
        `v2/users/applicant-contact?applicationId=${applicantId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )
      if (response.data.status === 'error') throw response
      return response.data
    } catch (error: any) {
      throw error
    }
  }
  public static async downloadApplicantResume(applicantId: string): Promise<any> {
    try {
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.get(`v2/career/download/${applicantId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') throw response
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  public static async applicantStatus(
    JobId: string,
    applicationId: string,
    action: string,
  ): Promise<any> {
    try {
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const endpoint = `v2/jobs/${JobId}/applications`
      const response = await apiClient.put(
        endpoint,
        {
          applications: [
            {
              id: applicationId,
              status: action,
            },
          ],
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )
      if (response.data.status === 'error') throw response
      return response
    } catch (error: any) {
      throw error
    }
  }
  public static async deleteJob(jobId: string): Promise<any> {
    try {
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.delete(`v2/jobs/${jobId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') throw response
      return response.data
    } catch (error: any) {
      throw error
    }
  }
}
