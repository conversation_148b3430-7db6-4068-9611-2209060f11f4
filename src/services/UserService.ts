import apiClient from '../config/apiClient'
import { UserContact, UserProfile } from '../models/User'
import { TokenService } from './TokenService'

/**
 * UserService provides static methods to interact with user-related endpoints.
 * The token stored in localStorage (and validated via TokenService) is included in the request headers.
 */
export class UserService {
  /**
   * Fetches user details by permalink.
   *
   * @param permalink - The user's unique permalink.
   * @returns A promise resolving to the user data.
   * @throws An error if the request fails.
   */
  public static async getUserProfileByPermalink(permalink: string): Promise<any> {
    try {
      const endpoint = `v2/user/profile/${permalink}`
      const token = TokenService.getAccessToken()
      const headers = token
        ? {
            Authorization: `Bearer ${token}`,
          }
        : undefined

      const response = await apiClient.get(endpoint, { headers })
      if (response.data.status === 'success') {
        return response.data
      } else throw response
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Fetches the current authenticated user profile.
   *
   * @returns A promise resolving to the user profile data.
   * @throws An error if the request fails.
   */
  public static async getUserProfile(): Promise<any> {
    try {
      const endpoint = `v2/users`
      // console.log("GETUSERPROFILEAPI", endpoint)
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      return response.data
    } catch (error: any) {
      // throw error
      throw new Error(error.response?.data?.message || 'Failed to fetch profile. Please try again.')
    }
  }

  /**
   * Updates the current user profile.
   *
   * @param data - The updated user profile data.
   * @returns A promise resolving to the server response.
   */
  public static async updateUserProfile(profile: UserProfile): Promise<any> {
    try {
      const endpoint = 'v2/users'
      const token = TokenService.getAccessToken()
      if (!token) throw new Error('No token found. Please log in.')

      // ✅ Request body constructed inside the service
      const requestBody = {
        givenName: profile.givenName,
        familyName: profile.familyName,
        headline: profile.headline,
        title: profile.title,
        address: {
          line1: profile.address?.line1 || '',
          line2: profile.address?.line2 || '',
        },
        location: {
          latitude: profile.location?.latitude || 32.23,
          longitude: profile.location?.longitude || 32.23,
          address: {
            country: profile.location?.address?.country || '',
            state: profile.location?.address?.state || '',
            city: profile.location?.address?.city || '',
            area: profile.location?.address?.area || '',
          },
        },
        gender: profile.gender,
        nationality: profile.nationality || '',
        birthDate: profile?.birthDate?.includes('T')
          ? profile.birthDate.split('T')[0] // Normalize date format
          : profile.birthDate,
      }

      const response = await apiClient.put(endpoint, requestBody, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      // ✅ Handle token renewal
      if (response.headers['x-access-token'] && token !== response.headers['x-access-token']) {
        TokenService.setAccessToken(response.headers['x-access-token'])
      }

      return response.data
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 'Failed to update profile. Please try again.',
      )
    }
  }

  /**
   * Fetches the user's contact details.
   *
   * @returns A promise resolving to the contact details data.
   * @throws An error if the request fails.
   */
  public static async getUserContact(): Promise<any> {
    try {
      const endpoint = `v2/users/contact`
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      return response.data
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 'Failed to fetch contact details. Please try again.',
      )
    }
  }

  /**
   * Sends an OTP to the specified email address.
   *
   * @param email - The target email address for the OTP.
   * @param type - The type of OTP request, either 'send' or 'resend'.
   * @param flow - The context of the OTP request (e.g., 'signup' or 'other').
   * @returns A promise that resolves to the response data from the OTP send request.
   */
  public static async updatePrimaryPhone(
    primaryPhone: string,
    verificationCode: string,
    countryCode?: string,
  ): Promise<any> {
    const token = TokenService.getAccessToken()
    if (!token) throw new Error('No token found. Please log in.')
    try {
      const response = await apiClient.put(
        'v2/users/contact/phone',
        {
          primaryPhone,
          verificationCode,
          countryCode: countryCode || '+91',
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )
      if (response.data.status === 'error') throw response
      else return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Uploads a banner image.
   *
   * @param formData - The FormData containing the image file.
   * @returns A promise resolving to the upload response.
   */
  public static async uploadPhoto(formData: FormData): Promise<any> {
    try {
      const endpoint = `v2/users/images/upload`
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.post(endpoint, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      })

      if (response.headers['x-access-token'] && token !== response.headers['x-access-token']) {
        TokenService.setAccessToken(response.headers['x-access-token'])
      }

      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to upload banner image.')
    }
  }

  /**
   * Updates the user's contact details (email and secondary phone).
   *
   * @param data - The contact information to update.
   * @returns A promise resolving to the server response.
   */
  public static async updateUserContact(contact: UserContact): Promise<any> {
    try {
      const endpoint = `v2/users/contact`
      const token = TokenService.getAccessToken()
      if (!token) throw new Error('No token found. Please log in.')

      // ✅ Request body constructed inside the service
      const requestBody = {
        primaryEmail: contact.primaryEmail || '',
        secondaryEmail: contact.secondaryEmail || '',
        secondaryPhone: contact.secondaryPhone || '',
      }

      const response = await apiClient.put(endpoint, requestBody, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      return response.data
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 'Failed to update contact details. Please try again.',
      )
    }
  }

  /**
   * Unfollows a user based on their ID.
   *
   * @param id - The unique identifier of the user to unfollow.
   * @returns A promise resolving to the unfollow action response.
   * @throws An error if the request fails.
   */
  public static async getIntrestingTalent(permalink: string): Promise<any> {
    try {
      const endpoint = `v2/users/${permalink}/suggestions/follow-users?limit=4`
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to unfollow user. Please try again.')
    }
  }

  public static async getTrendingJobs(permalink: string): Promise<any> {
    try {
      const endpoint = `v2/users/${permalink}/suggestions/apply-jobs?limit=5`
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to unfollow user. Please try again.')
    }
  }
  //! GET OWN FOLLOWERS
  /**
   * Fetches the list of users the current authenticated user is following.
   *
   * @returns A promise resolving to the following details.
   * @throws An error if the request fails.
   */
  public static async getUserFollowers(page: number = 1, limit: number = 15): Promise<any> {
    try {
      const endpoint = `v2/followers?page=${page}&limit=${limit}`
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') throw response
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  //! GET OWN FOLLOING
  public static async getUserFollowing(page: number = 1, limit: number = 15): Promise<any> {
    try {
      const endpoint = `v2/following?page=${page}&limit=${limit}`
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') throw response
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Fetches the list of users another user is following, based on their permalink.
   *
   * @param permalink - The unique identifier (permalink) of the user.
   * @returns A promise resolving to the following details of the specified user.
   * @throws An error if the request fails.
   */
  public static async getOtherUsersFollowing(
    permalink: string,
    page: number = 1,
    limit: number = 15,
  ): Promise<any> {
    try {
      //! This api call is done from talent page so the entity type is hardcoded (user)
      // TODO: Make the URL dynamic take values from local storage x-active-entityType
      const endpoint = `v2/user/${permalink}/following?page=${page}&limit=${limit}`;
      console.log("OTHERFOLLOWING API", endpoint);
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      // console.log("RESPONSENOW", response.data);
      if (response.data.status === 'error') throw response
      return response.data
    } catch (error: any) {
      console.log("ERROR", error);
      throw error
    }
  }

  /**
   * Fetches the list of users who are following another user, based on their permalink.
   *
   * @param permalink - The unique identifier (permalink) of the user.
   * @returns A promise resolving to the follower details of the specified user.
   * @throws An error if the request fails.
   */
  public static async getOtherUsersFollowers(
    permalink: string,
    page: number = 1,
    limit: number = 15,
  ): Promise<any> {
    try {
      // const endpoint = `v2/users/${permalink}/followers?page=${page}&limit=${limit}`
      //! This api call is done from talent page so the entity type is hardcoded (user)
      // TODO: Make this dynamic take value from localstorage
      const endpoint = `v2/user/${permalink}/followers?page=${page}&limit=${limit}`
      console.log("LATEST API IS CALLED", endpoint);
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      console.log("RESPONSEOFOTHERFOLLOWER", response.data);
      if (response.data.status !== 'success') throw response
      return response.data
    } catch (error: any) {
      throw error
    }
  }
  // Handle Follow User
  /**
   * Follows a user based on their ID.
   *
   * @param id - The unique identifier of the user to follow.
   * @returns A promise resolving to the follow action response.
   * @throws An error if the request fails.
   */
  
  public static async handleFollowUser(id: number | string, entityType: string): Promise<any> {
    try {
      //? The entityType is dynamic now
      // console.log("THIS API CALLED")
      const entityTypeLowerCase = entityType?.toLowerCase();
      const endpoint = `v2/${entityTypeLowerCase}/follow/${id}`
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.put(
        endpoint,
        { id: id },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )
      if (response.data.status === 'error') {
        throw new Error(response.data.message || 'Follow request failed due to a server error.')
      }

      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to follow user.')
    }
  }

  /**
   * Unfollows a user based on their ID.
   *
   * @param id - The unique identifier of the user to unfollow.
   * @returns A promise resolving to the unfollow action response.
   * @throws An error if the request fails.
   */
  public static async handleUnFollowUser(id: number | string, entityType: string): Promise<any> {
    try {
      // endpoint = `v2/{entityType}/follow/{entityid}`
      //? The entityType is dynamic now
      const entityTypeLowerCase = entityType?.toLowerCase();
      // console.log(entityTypeLowerCase, entityType);
      const endpoint = `v2/${entityTypeLowerCase}/follow/${id}`
      // console.log("USER FOLLOW API", endpoint);
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.delete(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw new Error(response.data.message || 'unfollow request failed due to a server error.')
      }
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to unfollow user. Please try again.')
    }
  }

  /**
   * API Service to handle all order, resume, subscription, and wallet related requests.
   * Fetches paginated order details with optional filters.
   * @param {number} [page=1] - The page number.
   * @param {string} [title=''] - Title search filter.
   * @param {string} [status=''] - Order status filter.
   * @param {string} [fromDate] - Start date filter.
   * @param {string} [toDate] - End date filter.
   * @returns {Promise<any>} - The response data.
   */
  public static async getOrderDetails(
    page: number = 1,
    title: string = '',
    status: string = '',
    fromDate?: string,
    toDate?: string,
  ): Promise<any> {
    try {
      let endpoint = `v2/orders?page=${page}`

      if (title) {
        endpoint += `&title=${encodeURIComponent(title)}`
      }

      if (status && status !== 'ALL') {
        endpoint += `&status=${status}`
      }

      if (fromDate) {
        endpoint += `&from=${fromDate}`
      }

      if (toDate) {
        endpoint += `&to=${toDate}`
      }

      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }

      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') {
        throw response
      }

      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Fetches details of a specific order by ID.
   * @param {string} orderId - The order ID.
   * @returns {Promise<any>} - The order details.
   */
  public static async getOrderById(orderId: string): Promise<any> {
    try {
      let endpoint = `v2/orders/${orderId}`

      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }

      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      return response.data
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 'Failed to fetch order details. Please try again.',
      )
    }
  }
  /**
   * Downloads a resume for a specific talent.
   * @param {string} talentId - The talent ID.
   * @returns {Promise<any>} - The resume download response.
   */
  public static async downloadResume(talentId: string): Promise<any> {
    try {
      const endpoint = `v2/career/download/${talentId}`

      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }

      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Downloads the resume of the currently authenticated user.
   * @returns {Promise<any>} - The resume download response.
   */

  public static async downloadMyResume(): Promise<any> {
    try {
      const endpoint = `v2/career/download`

      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }

      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Initiates the purchase of a resume for a given talent.
   * @param {string} talentId - The talent ID.
   * @returns {Promise<any>} - The purchase response.
   */
  public static async purchaseResume(talentId: string): Promise<any> {
    try {
      const endpoint = `v2/career/download/purchase/${talentId}`

      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }

      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Creates a new order.
   * @param {any} formData - The order data to submit.
   * @returns {Promise<any>} - The order creation response.
   */
  public static async createOrder(formData: any) {
    const endpoint = `v2/orders`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.post(endpoint, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      if (response.data.status === 'error') {
        throw response
      }

      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Verifies an order after payment.
   * @param {any} formData - The verification payload.
   * @returns {Promise<any>} - The verification response.
   */
  public static async verifyOrder(formData: any) {
    const endpoint = `v2/orders/verify`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.post(endpoint, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      if (response.data.status === 'error') {
        throw response
      }

      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Creates a subscription plan for the user.
   * @param {any} plan - The subscription plan details.
   * @returns {Promise<any>} - The subscription creation response.
   */

  public static async createSubscription(plan: any) {
    const endpoint = `v2/subscription/create`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.post(
        endpoint,
        { plan },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      )
      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Fetches the list of applicants under a specific subscription.
   * @param {string} id - The subscription ID.
   * @param {number} page - Page number for pagination.
   * @returns {Promise<any>} - The applicants list.
   */
  public static async getApplicantList(id: string, page: number): Promise<any> {
    try {
      const endpoint = `v2/subscription/history/${id}?page=${page}&limit=15`

      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }

      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Retrieves all available subscription plans.
   * @returns {Promise<any>} - The list of subscriptions.
   */
  public static async getSubscriptions(): Promise<any> {
    try {
      const endpoint = `v2/subscriptions`

      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }

      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Retrieves wallet transaction history for the current user.
   * @returns {Promise<any>} - Wallet transactions.
   */
  public static async getWalletTransactions(page: number, type: string): Promise<any> {
    try {
      const endpoint = `v2/wallet/transactions?page=${page}&limit=15${type ? `&type=${type}` : ''}`

      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }

      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Gets the current UPI address linked to the wallet.
   * @returns {Promise<any>} - The UPI address.
   */
  public static async getUpiAddress(): Promise<any> {
    try {
      const endpoint = `v2/wallet/upi`
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }

      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Updates the UPI address associated with the wallet.
   * @param {string} upiAddress - The new UPI address.
   * @returns {Promise<any>} - The update response.
   */
  public static async updateUpiAddress(upiAddress: string): Promise<any> {
    try {
      const endpoint = `v2/wallet/upi`
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }

      const response = await apiClient.post(
        endpoint,
        { upi_address: upiAddress },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Initiates a wallet withdrawal request.
   * @param {any} formData - Withdrawal data including amount and UPI info.
   * @returns {Promise<any>} - The payout response.
   */
  public static async withdrawWalletAmmount(formData: any) {
    const endpoint = `v2/wallet/payout`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.post(endpoint, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  public static async recruiterContact(allowrecruiterContact: boolean) {
    const endpoint = `v2/career/preferences/recruitment`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.put(
        endpoint,
        { allowRecruiterContact: allowrecruiterContact },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )
      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  public static async resetPassword(password: string, otp: string): Promise<any> {
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }
    try {
      const response = await apiClient.post(
        'v2/auth/reset-password',
        {
          verificationCode: otp,
          newPassword: password,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )
      return response.data
    } catch (error: any) {
      throw error
    }
  }
}
