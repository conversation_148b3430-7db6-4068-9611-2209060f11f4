import apiClient from '../config/apiClient'
import { TokenService } from './TokenService'

/**
 * CareerService provides static methods to interact with career-related endpoints.
 */
export class CareerService {
  /**
   * Fetches career data by permalink.
   * @param permalink - The user's unique permalink.
   * @returns A promise resolving to the career data.
   * @throws An error if the request fails.
   */
  public static async getCareerByPermalink(permalink: string): Promise<any> {
    try {
      const endpoint = `v2/career?permalink=${permalink}`
      const token = TokenService.getAccessToken()
      const headers = token ? { Authorization: `Bearer ${token}` } : undefined

      const response = await apiClient.get(endpoint, { headers })
      return response.data
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 'Failed to fetch career data. Please try again.',
      )
    }
  }
  /**
   * Updates career data by permalink.
   * @param permalink - The user's unique permalink.
   * @param updateData - The updated resume data to send to the backend.
   * @returns A promise resolving to the response data.
   * @throws An error if the request fails.
   */
  public static async updateCareerByPermalink(permalink: string, updateData: any): Promise<any> {
    try {
      const endpoint = `v2/career?permalink=${permalink}`
      const token = TokenService.getAccessToken()
      const headers = token ? { Authorization: `Bearer ${token}` } : undefined

      const response = await apiClient.put(endpoint, updateData, { headers })
      return response.data
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 'Failed to update career data. Please try again.',
      )
    }
  }

  /**
   * Deletes the user's career data.
   * @param permalink - The user's unique permalink.
   * @returns A promise resolving to the server response.
   * @throws An error if the request fails.
   */
  public static async deleteCareerByPermalink(permalink: string): Promise<any> {
    try {
      const endpoint = `v2/career?permalink=${permalink}`
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }

      const response = await apiClient.delete(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      return response.data
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 'Failed to delete career data. Please try again.',
      )
    }
  }

  /**
   * Downloads the resume of a specific user by profile ID.
   *
   * @param {string} id - The ID of the user profile whose resume needs to be downloaded.
   * @returns {Promise<any>} The response data containing the resume information.
   * @throws {Error} If the token is missing or the request fails.
   */

  public static async getProfieResumeDownload(id: string): Promise<any> {
    try {
      const endpoint = `v2/career/download/${id}`

      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }

      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }
  public static async downLoadProResume(): Promise<any> {
    try {
      const endpoint = `v2/career/download/purchase/pro-resume/pro-resume-orange`

      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }

      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }
}
