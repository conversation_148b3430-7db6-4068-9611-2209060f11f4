import apiClient from '../config/apiClient'
import { TokenService } from './TokenService'

/**
 *  AiResumeService provides static methods to interact with AI chat endpoints for the resume builder.
 */
export class AiResumeService {
  /**
   * Fetches the AI chat history from the backend.
   * @returns A promise resolving to the chat history data.
   * @throws An error if the request fails.
   */
  public static async getChatHistory(): Promise<any> {
    try {
      const endpoint = 'v2/career/ai/chat'
      const token = TokenService.getAccessToken()
      const headers = token ? { Authorization: `Bearer ${token}` } : undefined

      const response = await apiClient.get(endpoint, { headers })
      return response.data
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 'Failed to fetch chat history. Please try again.',
      )
    }
  }

  /**
   * Sends a user input to the AI chat endpoint and retrieves the bot's response.
   * @param input - The user's input message.
   * @returns A promise resolving to the bot's response data.
   * @throws An error if the request fails.
   */
  public static async sendUserInput(input: string): Promise<any> {
    try {
      const endpoint = 'v2/career/ai/chat'
      const token = TokenService.getAccessToken()
      const headers = token ? { Authorization: `Bearer ${token}` } : undefined

      const response = await apiClient.post(endpoint, { message: input }, { headers })
      return response.data
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 'Failed to send user input. Please try again.',
      )
    }
  }
}
