import { GOOGLE_API_KEY } from '../config/defaultEnv'

export interface Location {
  address: LocationAddress
  latitude: number
  longitude: number
}

interface LocationAddress {
  country: string
  state: string
  city: string
  area: string
}

export class LocationService {
  /**
   * Gets location details from coordinates using Google Geocoding API
   *
   * @param latitude - The latitude coordinate
   * @param longitude - The longitude coordinate
   * @returns A promise resolving to the location details
   */
  public static async getLocationFromCoords(
    latitude: number,
    longitude: number,
  ): Promise<Location | null> {
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${GOOGLE_API_KEY}`,
      )
      const data = await response.json()

      if (data.status === 'OK' && data.results.length > 0) {
        const result = data.results[0]

        // Parse address components
        let country = ''
        let state = ''
        let city = ''
        let area = ''

        for (const component of result.address_components) {
          const types = component.types

          if (types.includes('country')) {
            country = component.long_name
          } else if (types.includes('administrative_area_level_1')) {
            state = component.long_name
          } else if (types.includes('locality') || types.includes('administrative_area_level_2')) {
            city = component.long_name
          } else if (types.includes('sublocality') || types.includes('neighborhood')) {
            area = component.long_name
          }
        }

        return {
          address: {
            country,
            state,
            city,
            area,
          },
          latitude,
          longitude,
        }
      }
      return null
    } catch (error: any) {
      throw new Error(
        error.response?.data?.error_message || 'Failed to get location from coordinates',
      )
    }
  }

  /**
   * Gets the current location using browser's geolocation API
   *
   * @returns A promise resolving to the location details
   * @throws Error if geolocation is not supported or permission is denied
   */
  public static async getCurrentLocation(): Promise<Location> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser.'))
        return
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            const { latitude, longitude } = position.coords
            const location = await this.getLocationFromCoords(latitude, longitude)
            if (location) {
              resolve(location)
            } else {
              reject(new Error('Could not get location details'))
            }
          } catch (error) {
            reject(error)
          }
        },
        (error) => {
          switch (error.code) {
            case error.PERMISSION_DENIED:
              reject(new Error('Location access denied by user.'))
              break
            case error.POSITION_UNAVAILABLE:
              reject(new Error('Location information is unavailable.'))
              break
            case error.TIMEOUT:
              reject(new Error('Location request timed out.'))
              break
            default:
              reject(new Error('An unknown error occurred while getting location.'))
              break
          }
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000, // 5 minutes
        },
      )
    })
  }
}
