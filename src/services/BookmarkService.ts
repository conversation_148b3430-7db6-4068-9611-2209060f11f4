import apiClient from '../config/apiClient'
import { TokenService } from './TokenService'

class BookmarkError extends Error {
  constructor(message: string) {
    super(message)
  }
}

export class BookmarkService {
  /**
   * Adds bookmarks of talent or jobs
   *
   * @body entityId: the id which is needed to bookmark, type: whether it is <PERSON><PERSON> or Talent
   * @returns A promise that resolves to the sign-in response.
   */
  public static async addBookmark(itemId: string, itemType: 'JOBS_SRP' | 'TALENT_SRP'): Promise<any> {
    try {
      console.log(typeof itemId);
      // check the type of entiry ID
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.post(
        'v2/bookmark',
        { itemId, itemType },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )
      // console.log("RESPONSE", response);
      return response.data
    } catch (error: any) {
      throw new BookmarkError(error.response?.data?.message || 'Failed to add bookmark.')
    }
  }

  /**
   * Adds bookmarks of talent or jobs
   *
   * @body entityId: the id which is needed to bookmark, type: whether it is Jobs or Talent
   * @returns A promise that resolves to the sign-in response.
   */
  public static async deleteBookmark(
    entityId: string | undefined,
    type: 'JOBS_SRP' | 'TALENT_SRP',
  ): Promise<any> {
    if (!entityId) return
    try {
      const token = TokenService.getAccessToken()
      if (!token) {
        throw new Error('No token found. Please log in.')
      }
      const response = await apiClient.delete(`v2/bookmark/${entityId}/${type}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      return response.data
    } catch (error: any) {
      throw new BookmarkError(error.response?.data?.message || 'Failed to add bookmark.')
    }
  }
}
