import axios from 'axios'
import apiClient from '../config/apiClient'
// import { googleOauthTest } from "../config/defaultEnv";
import { SignupRequest } from '../models/AuthModels'

/**
 * AuthService provides static methods to perform authentication-related operations,
 * such as signing in, signing up, logging out, resetting passwords, and signing in via Google.
 *
 * Note: All token management is now handled separately by the TokenService.
 */
export class AuthService {
  /**
   * Validates the existing email while sign-up
   *
   * @returns A promise that resolves when validation is completed.
   */
  public static async validateEmail(email: string): Promise<any> {
    try {
      const response = await apiClient.get(`v2/users/contact/${email}/validate`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Logout failed. Please try again.')
    }
  }

  /**
   * Validates the existing email while creating company
   *
   * @returns A promise that resolves when validation is completed.
   */
  public static async validateUsername(username: string): Promise<any> {
    try {
      const response = await apiClient.get(`v2/users/${username}/validate`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Logout failed. Please try again.')
    }
  }

  /**
   * Signs in a user using an email/username and password.
   *
   * @param emailOrUsername - The user's email address or username.
   * @param password - The user's password.
   * @returns A promise that resolves to the sign-in response.
   */
  public static async signIn(emailOrUsername: string, password: string): Promise<any> {
    try {
      const response = await apiClient.post('v2/auth/signin', {
        emailOrUsername,
        password,
      })
      return response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Logout failed. Please try again.')
    }
  }

  /**
   * Signs in a user via Google OAuth.
   *
   * @param code - The authorization code received from Google.
   * @returns A promise that resolves to the Google sign-in response data.
   */
  public static async googleSignIn(code: string): Promise<any> {
    try {
      const response = await apiClient.post('v2/auth/google-login', { code })
      return response
    } catch (error: any) {
      throw new Error('Google sign-in failed. Please try again.')
      // throw new Error(error.response?.data?.message || 'Google sign-in failed. Please try again.')
    }
  }

  /**
   * Registers a new user with the provided signup data.
   *
   * @param userData - An object containing the user's signup details.
   * @returns A promise that resolves to the sign-up response.
   */
  public static async signUp(userData: SignupRequest): Promise<any> {
    try {
      const response = await apiClient.post('v2/auth/sign-up', userData)
      return response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Sign-up failed. Please try again.')
    }
  }
  /**
   * Registers a new user with the provided signup data.
   *
   * @param userData - An object containing the user's signup details.
   * @returns A promise that resolves to the sign-up response.
   */
  public static async googleSignUp(userData: SignupRequest): Promise<any> {
    try {
      const response = await apiClient.post('v2/auth/sign-up', userData)
      return response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Sign-up failed. Please try again.')
    }
  }

  // /**
  //  * Logs out the current user by invalidating their session/token.
  //  *
  //  * @param token - The authentication token for the current user.
  //  * @returns A promise that resolves when the logout process is complete.
  //  */
  // public static async logout(token: string): Promise<void> {
  //   try {
  //     await apiClient.get('v2/auth/logout', {
  //       headers: {
  //         Authorization: `Bearer ${token}`,
  //       },
  //     })
  //   } catch (error: any) {
  //     throw new Error(error.response?.data?.message || 'Logout failed. Please try again.')
  //   }
  // }

  /**
   * Logs out the current user by invalidating their session/token.
   *
   * @param token - The authentication token for the current user.
   * @param source - (Optional) A source identifier to be added as a query parameter.
   * @returns A promise that resolves when the logout process is complete.
   */
  public static async logout(token: string, global?: boolean): Promise<void> {
    try {
      const endpoint = global ? `v2/auth/logout?global=true` : 'v2/auth/logout'

      if (global) {
        await apiClient.get(endpoint)
      } else {
        await apiClient.get(endpoint, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Logout failed. Please try again.')
    }
  }

  /**
   * Initiates a forgot-password process by sending a request to reset the user's password.
   *
   * @param email - The user's email address.
   * @param otp - The one-time password (or verification code) received by the user.
   * @param password - The new password to set.
   * @returns A promise that resolves when the password reset request is complete.
   */
  public static async forgotPassword(email: string, otp: string, password: string): Promise<void> {
    try {
      await apiClient.post('v2/auth/forgot-password', {
        email,
        verificationCode: otp,
        password,
      })
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Password reset failed. Please try again.')
    }
  }

  // public static async googleSignIn(authCode: any) {
  //   try {
  //     // Step 1: Exchange authorization code for access token
  //     const tokenResponse = await axios.post('https://oauth2.googleapis.com/token', null, {
  //       params: {
  //         code: authCode,
  //         client_id: googleOauthTest.clientId,
  //         client_secret: googleOauthTest.clientSecret,
  //         redirect_uri: googleOauthTest.redirectUri,
  //         grant_type: 'authorization_code',
  //       },
  //       headers: {
  //         'Content-Type': 'application/x-www-form-urlencoded',
  //       },
  //     })

  //     const { access_token } = tokenResponse.data

  //     if (!access_token) {
  //       throw new Error('Access token not received from Google.')
  //     }

  //     // Step 2: Use the access token to get user info
  //     const userInfoResponse = await axios.get('https://www.googleapis.com/oauth2/v2/userinfo', {
  //       headers: {
  //         Authorization: `Bearer ${access_token}`,
  //       },
  //     })

  //     const user = userInfoResponse.data

  //     // Step 3: Return the user data
  //     return {
  //       status: 'success',
  //       profile: {
  //         email: user.email,
  //         givenName: user.given_name,
  //         familyName: user.family_name,
  //         fullName: user.name,
  //         picture: user.picture,
  //         googleId: user.sub,
  //       },
  //     }
  //   } catch (err: any) {
  //     console.error('Google Sign-In Error:', err.response?.data || err.message)
  //     return { status: 'error', message: 'Google Sign-in failed.' }
  //   }
  // }
}
