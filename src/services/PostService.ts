import apiClient from '../config/apiClient'
import { TokenService } from './TokenService'
export class PostService {
  /**
   * Gets the job details by the permalink
   *
   * @returns A promise that resolves when fetching job details is completed.
   */
  public static async getJobByPermalink(permalink: string, token?: string): Promise<any> {
    let accessToken = token
    if (typeof window !== 'undefined' && !token) {
      const clientToken = TokenService.getAccessToken()
      accessToken = clientToken === null ? undefined : clientToken
    }
    try {
      if (typeof window === 'undefined') {
        // Server-side: dynamically import serverApiGet from server-helpers
        const { serverApiGet } = await import('../server-helpers/serverApiGet')
        const response = await serverApiGet(`/v2/jobs/${permalink}`, {
          headers: {
            Authorization: accessToken ? `Bearer ${accessToken}` : '',
          },
        })
        console.log("RESPONSEDATAJOBPERMALINK SERVER SIDE: ", response.data);
        if (response.data.status === 'success') {
          return response.data
        } else throw response
      } else {
        // Client-side: use apiClient as before
        const response = await apiClient.get(`v2/jobs/${permalink}`, {
          headers: {
            Authorization: accessToken ? `Bearer ${accessToken}` : '',
          },
        })
        console.log("RESPONSEDATAJOBPERMALINK CLIENT SIDE: ", response.data);
        if (response.data.status === 'success') {
          return response.data
        } else throw response
      }
    } catch (error: any) {
      throw error
    }
  }
  /**
   * Gets the job details by the id
   *
   * @returns A promise that resolves when fetching job details is completed.
   */
  public static async getJobById(id: string): Promise<any> {
    const endpoint = `v2/jobs?id=${id}`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Likes a job by job ID
   *
   * @param jobId - The ID of the job to like
   * @returns A promise that resolves with the response from the server
   */
  public static async likeJob(jobId: string): Promise<any> {
    const endpoint = `v2/jobs/${jobId}/likes`
    console.log("THIS END POINT IS CALLED", endpoint);
    const token = TokenService.getAccessToken()
    if (!token) {
      throw new Error('No token found. Please log in.')
    }
    try {
      const response = await apiClient.put(
        endpoint,
        { jobId: jobId },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )
      console.log("JOB LIKED BY", response.data);
      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * dislikes a job by job ID
   *
   * @param jobId - The ID of the job to like
   * @returns A promise that resolves with the response from the server
   */
  public static async dislikeJob(jobId: string): Promise<any> {
    const endpoint = `v2/jobs/${jobId}/likes`
    const token = TokenService.getAccessToken()
    if (!token) {
      throw new Error('No token found. Please log in.')
    }
    try {
      const response = await apiClient.delete(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Creates a new post with optional content and images.
   *
   * @param postPayload - The content and image data for the post
   * @returns A promise that resolves with the created post's response
   */
  public static async createPost(postPayload: {
    content?: string
    images?: {
      mimetype: string
      publicId: string
      width: number
      height: number
      secureUrl: string
    }[]
  }): Promise<any> {
    const endpoint = `v2/articles`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.post(endpoint, postPayload, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Updates an existing post with new content and/or images.
   *
   * @param postPayload - The updated content and image data for the post
   * @param id - The ID of the post to update
   * @returns A promise that resolves with the updated post's response
   */
  public static async updatePost(
    postPayload: {
      content?: string
      images?: {
        mimetype: string
        publicId: string
        width: number
        height: number
        secureUrl: string
      }[]
    },
    id: string,
  ): Promise<any> {
    const endpoint = `v2/articles/${id}`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.put(endpoint, postPayload, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }
  /**
   * Deletes an existing post by its ID.
   *
   * @param id - The ID of the post to delete
   * @returns A promise that resolves with the deletion response
   */
  public static async deletePost(id: string): Promise<any> {
    const endpoint = `v2/articles/${id}`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.delete(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }
  /**
   * Fetches post for a given artice code.
   *
   * @param permalink - The article code of the post
   * @returns A promise that resolves with the  post
   */
  public static async getPostById(articleCode: string): Promise<any> {
    const endpoint = `v2/articles/${articleCode}`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Uploads images for a post.
   *
   * @param formData - FormData containing image files to upload
   * @returns A promise that resolves with the uploaded image metadata
   */

  public static async uploadPostImages(formData: any): Promise<any> {
    const endpoint = `v2/articles/images/upload`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.post(endpoint, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      })
      if (response.data.status === 'error') {
        throw response
      }

      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Removes one or more uploaded post images by their IDs.
   *
   * @param imageIds - An array of image public IDs to remove
   * @returns A promise that resolves when the images are successfully removed
   */
  public static async removePostImages(imageIds: any): Promise<any> {
    const endpoint = `v2/articles/images/remove`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.delete(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        data: {
          imageIds,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }

      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Fetches the board data for a user by their permalink.
   *
   * @param permalink - The username or permalink of the user
   * @returns A promise that resolves with the user's board data
   */

  public static async getBoardData(permalink: string, page: number): Promise<any> {
    const endpoint = `v2/user/${permalink}/board?page=${page}&limit=${10}`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') {
        throw response
      }
      // console.log("api called", response.data);
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Fetches the feed data for a user by their permalink.
   *
   * @param permalink - The username or permalink of the user
   * @returns A promise that resolves with the user's feed data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async getFeedData(permalink: string, page: number): Promise<any> {
    const endpoint = `v2/users/${permalink}/feeds?page=${page}&limit=${20}`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Sends a like request for a post (article) by its ID.
   *
   * @param articleId - The ID of the article to like
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async likePost(articleId: string): Promise<any> {
    const endpoint = `v2/articles/${articleId}/likes`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.put(
        endpoint,
        { articleId: articleId },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )
      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Sends a dislike request to remove a like from a post (article) by its ID.
   *
   * @param articleId - The ID of the article to dislike
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async dislikePost(articleId: string): Promise<any> {
    const endpoint = `v2/articles/${articleId}/likes`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.delete(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') {
        throw response
      }

      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Fetches all comments for a given article.
   *
   * @param articleId - The ID of the article to fetch comments for
   * @returns A promise that resolves with the comments data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async getPostComments(articleId: string, page: number): Promise<any> {
    const endpoint = `v2/articles/${articleId}/comments?page=${page}&limit=5`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Adds a new comment to a specific article.
   *
   * @param formdata - Contains the articleId and the text of the comment
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async addPostComment(formdata: { articleId: string; text: string }): Promise<any> {
    const { articleId, text } = formdata
    const endpoint = `v2/articles/${articleId}/comments`

    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.post(
        endpoint,
        { text },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      )
      if (response.data.status === 'error') {
        throw response
      }

      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Edits an existing comment for a specific article.
   *
   * @param formdata - Contains articleId, commentId, and the updated text
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async editPostComment(formdata: {
    articleId: string
    text: string
    commentId: string
  }): Promise<any> {
    const { articleId, text, commentId } = formdata
    const endpoint = `v2/articles/${articleId}/comments/${commentId}`

    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }
    try {
      const response = await apiClient.put(
        endpoint,
        { text },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      )

      if (response.data.status === 'error') {
        throw response
      }

      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Deletes a comment from a specific article.
   *
   * @param formdata - Contains articleId, commentId, and the text (optional, if required by API)
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async deletePostComment(formdata: {
    articleId: string
    commentId: string
  }): Promise<any> {
    const { articleId, commentId } = formdata
    const endpoint = `v2/articles/${articleId}/comments/${commentId}`

    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }
    try {
      const response = await apiClient.delete(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      if (response.data.status === 'error') {
        throw response
      }

      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Reposts a specific article.
   *
   * @param articleId - The ID of the article to repost
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async rePostPost(articleId: string): Promise<any> {
    const endpoint = `v2/articles/${articleId}/repost`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.post(
        endpoint,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      )

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Undoes a repost of a specific article.
   *
   * @param articleId - The ID of the article to undo the repost for
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async undoRepost(Id: string): Promise<any> {
    const endpoint = `v2/repost/${Id}/undo`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.delete(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Creates a quote for a specific article.
   *
   * @param articleId - The ID of the article being quoted
   * @param quoteContent - The content of the quote to be created
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async createQuote(articleId: string, quoteContent: string): Promise<any> {
    const endpoint = `v2/articles/${articleId}/quotes`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.post(
        endpoint,
        {
          content: quoteContent,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      )

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Creates a nested quote for a specific quote.
   *
   * @param quoteId - The ID of the quote to nest the new quote under
   * @param quoteContent - The content of the nested quote
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async createNestedQuote(quoteId: string, quoteContent: string): Promise<any> {
    const endpoint = `v2/quotes/${quoteId}/nested`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.post(
        endpoint,
        {
          content: quoteContent,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      )

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Sends a dislike request for a quote by its ID.
   *
   * @param quoteId - The ID of the quote to dislike
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async dislikeQuote(quoteId: string): Promise<any> {
    const endpoint = `v2/quotes/${quoteId}/likes`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.delete(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }
  /**
   * Sends a like request for a quote by its ID.
   *
   * @param quoteId - The ID of the quote to like
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async likeQuote(quoteId: string): Promise<any> {
    const endpoint = `v2/quotes/${quoteId}/likes`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.put(
        endpoint,
        { articleId: quoteId },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )
      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Deletes a specific quote by ID.
   *
   * @param id - The ID of the quote to be deleted
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async deleteQuote(id: string): Promise<any> {
    const endpoint = `v2/quotes/${id}`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.delete(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Reposts a specific quote.
   *
   * @param quoteId - The ID of the quote to repost
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async rePostQuote(quoteId: string): Promise<any> {
    const endpoint = `v2/quotes/${quoteId}/repost`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.post(
        endpoint,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      )

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  public static async getQuoteById(quoteCode: string): Promise<any> {
    const endpoint = `v2/quotes/${quoteCode}`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Adds a new comment to a specific quote.
   *
   * @param formdata - Contains the quoteId and the text of the comment
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async addQuoteComment(formdata: { quoteId: string; text: string }): Promise<any> {
    const { quoteId, text } = formdata
    const endpoint = `v2/quotes/${quoteId}/comments`

    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.post(
        endpoint,
        { text },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      )
      if (response.data.status === 'error') {
        throw response
      }

      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Fetches all comments for a given article.
   *
   * @param articleId - The ID of the article to fetch comments for
   * @returns A promise that resolves with the comments data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async getQuoteComments(quoteId: string, page: number): Promise<any> {
    const endpoint = `v2/quotes/${quoteId}/comments?page=${page}&limit=5`
    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }

    try {
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data.status === 'error') {
        throw response
      }

      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Deletes a comment from a specific article.
   *
   * @param formdata - Contains articleId, commentId, and the text (optional, if required by API)
   * @returns A promise that resolves with the response data
   * @throws Will throw an error if the request fails or if no token is found
   */
  public static async deleteQuoteComment(formdata: {
    quoteId: string
    commentId: string
  }): Promise<any> {
    const { quoteId, commentId } = formdata
    const endpoint = `v2/quotes/${quoteId}/comments/${commentId}`

    const token = TokenService.getAccessToken()

    if (!token) {
      throw new Error('No token found. Please log in.')
    }
    try {
      const response = await apiClient.delete(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      if (response.data.status === 'error') {
        throw response
      }

      return response.data
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Fetches a list of users matching the given username query for mention suggestions.
   *
   * @param query - The partial or full username to search for.
   * @returns A promise that resolves with the response data containing the list of matching users.
   * @throws Will throw an error if the request fails or if the API returns an error status.
   */
  public static async mentionUsers(query: string): Promise<any> {
    try {
      const endpoint = `v2/users/search/mentions?username=${encodeURIComponent(query)}`
      const token = TokenService.getAccessToken()
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (response.data.status === 'error') {
        throw response
      }
      return response.data
    } catch (error: any) {
      throw error
    }
  }
}
