import apiClient from '../config/apiClient'

/**
 * OtpService provides static methods to handle OTP (One-Time Password)
 * operations such as sending and verifying OTPs.
 *
 * Note: Errors are handled silently by throwing an error with a user-friendly message.
 * No error details are logged to the browser console.
 */
export class OtpService {
  /**
   * Sends an OTP to the specified email address.
   *
   * @param email - The target email address for the OTP.
   * @param type - The type of OTP request, either 'send' or 'resend'.
   * @param flow - The context of the OTP request (e.g., 'signup' or 'other').
   * @returns A promise that resolves to the response data from the OTP send request.
   */
  public static async sendOtp(
    email: string,
    type: 'send' | 'resend',
    flow: 'signup' | 'other',
  ): Promise<any> {
    try {
      const response = await apiClient.post('v2/email/otp/send', {
        email,
        type,
        wantOTP: false,
        checkEmail: false,
        flow,
      })
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to send OTP. Please try again.')
    }
  }

  /**
   * Sends an OTP to the specified email address.
   *
   * @param email - The target email address for the OTP.
   * @param type - The type of OTP request, either 'send' or 'resend'.
   * @param flow - The context of the OTP request (e.g., 'signup' or 'other').
   * @returns A promise that resolves to the response data from the OTP send request.
   */
  public static async sendMobileOtp(
    primaryPhone: string,
    type: 'send' | 'resend',
    flow: 'signup' | 'other',
    countryCode?: string,
  ): Promise<any> {
    try {
      const response = await apiClient.post('v2/sms/otp/send', {
        primaryPhone,
        type,
        countryCode: '+91',
        wantOTP: 'false',
        checkPhone: 'false',
        flow,
      })
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to send OTP. Please try again.')
    }
  }

  /**
   * Verifies the provided OTP for the given email address.
   *
   * @param email - The email address associated with the OTP.
   * @param verificationCode - The OTP provided by the user.
   * @param flow - The context of the OTP verification (e.g., 'signup' or 'other').
   * @returns A promise that resolves to the response data from the OTP verification request.
   */
  public static async verifyMobileOtp(
    primaryPhone: string,
    verificationCode: string,
    flow: 'signup' | 'other',
    countryCode?: string,
  ): Promise<any> {
    try {
      const response = await apiClient.post('v2/sms/otp/verify', {
        primaryPhone,
        verificationCode,
        flow,
        countryCode: countryCode || '+91',
      })
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to verify OTP. Please try again.')
    }
  }
  /**
   * Verifies the provided OTP for the given email address.
   *
   * @param email - The email address associated with the OTP.
   * @param verificationCode - The OTP provided by the user.
   * @param flow - The context of the OTP verification (e.g., 'signup' or 'other').
   * @returns A promise that resolves to the response data from the OTP verification request.
   */
  public static async verifyOtp(
    email: string,
    verificationCode: string,
    flow: 'signup' | 'other',
  ): Promise<any> {
    try {
      const response = await apiClient.post('v2/email/otp/verify', {
        email,
        verificationCode,
        flow,
      })
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to verify OTP. Please try again.')
    }
  }
}
