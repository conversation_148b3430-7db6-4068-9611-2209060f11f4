'use client'
import { createContext, useContext, useEffect, useState } from 'react'

interface ThemeContextProps {
  theme: 'default' | 'dark' | 'auto' // Include "auto" as a valid type
  setTheme: (theme: 'default' | 'dark' | 'auto') => void
}

interface ThemeProviderProps {
  children: React.ReactNode
}

const ThemeContext = createContext<ThemeContextProps>({
  theme: 'default', // Default theme value
  setTheme: () => {}, // Placeholder function
})

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setTheme] = useState<'default' | 'dark' | 'auto'>('default')
  // const [mounted, setMounted] = useState(false)
  // const isBrowser = typeof window !== 'undefined'

  useEffect(() => {
    // if (!isBrowser) return

    // setMounted(true)
    // Check for saved theme in localStorage or use media query for "auto"
    const storedTheme =
      (localStorage.getItem('lucresTheme') as 'default' | 'dark' | 'auto') || 'default'

    if (storedTheme === 'auto') {
      const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
      setTheme(prefersDarkMode ? 'dark' : 'default')
    } else {
      setTheme(storedTheme)
    }
  }, [])

  useEffect(() => {
    // if (!isBrowser || !mounted) return
    // if (theme === 'auto') return

    // Save theme to localStorage
    localStorage.setItem('lucresTheme', theme)

    // Update document's theme classes
    document.documentElement.classList.remove('dark', 'default')
    document.documentElement.classList.add(theme)

    // Dispatch custom event for theme change
    window.dispatchEvent(new CustomEvent('on-hs-appearance-change', { detail: theme }))
  }, [theme])

  // During SSR, render with default theme
  // if (!isBrowser) {
  //   return (
  //     <ThemeContext.Provider value={{ theme: 'default', setTheme }}>
  //       {children}
  //     </ThemeContext.Provider>
  //   )
  // }

  // Prevent flash of wrong theme by not rendering until after hydration
  // if (!mounted) {
  // return (
  //   <ThemeContext.Provider value={{ theme: 'default', setTheme }}>
  //     {children}
  //   </ThemeContext.Provider>
  // )
  // }

  return <ThemeContext.Provider value={{ theme, setTheme }}>{children}</ThemeContext.Provider>
}

export const useTheme = () => useContext(ThemeContext)
