'use client'
// context/FeedContext.tsx
import { createContext, useContext, useState } from 'react'
import { PostCard as PostCardType } from '../models/Post'

type FeedContextType = {
  feedData: PostCardType[]
  setFeedData: React.Dispatch<React.SetStateAction<PostCardType[]>>
  page: number
  setPage: React.Dispatch<React.SetStateAction<number>>
  hasNextPage: boolean
  setHasNextPage: React.Dispatch<React.SetStateAction<boolean>>
  hasLoadedFirstPage: boolean
  setHasLoadedFirstPage: React.Dispatch<React.SetStateAction<boolean>>
  fetchedPages: Set<number>
  setFetchedPages: React.Dispatch<React.SetStateAction<Set<number>>>
  loading: boolean
  setLoading: React.Dispatch<React.SetStateAction<boolean>>
}

const FeedContext = createContext<FeedContextType | undefined>(undefined)

export const FeedProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [feedData, setFeedData] = useState<PostCardType[]>([])
  const [page, setPage] = useState(1)
  const [hasNextPage, setHasNextPage] = useState(false)
  const [hasLoadedFirstPage, setHasLoadedFirstPage] = useState(false)
  const [fetchedPages, setFetchedPages] = useState<Set<number>>(new Set())
  const [loading, setLoading] = useState<boolean>(true)

  return (
    <FeedContext.Provider
      value={{
        feedData,
        setFeedData,
        page,
        setPage,
        hasNextPage,
        setHasNextPage,
        hasLoadedFirstPage,
        fetchedPages,
        setFetchedPages,
        setHasLoadedFirstPage,
        loading,
        setLoading,
      }}
    >
      {children}
    </FeedContext.Provider>
  )
}

export const useFeed = () => {
  const context = useContext(FeedContext)
  if (!context) throw new Error('useFeed must be used within a FeedProvider')
  return context
}
