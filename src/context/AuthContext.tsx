'use client'
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { SignupRequest } from '../models/AuthModels'
import { AuthService } from '../services/AuthService'
import { UserService } from '../services/UserService'
import { PermalinkUser } from '../models/User'
import { TokenService } from '../services/TokenService'
import { NotificationService, Notification } from '../services/NotificationService'
import { useRouter } from 'next/navigation'
import { CareerService } from '../services/CareerService'
import { useResumeProgress } from '@/utils/useResumeProgress'

export interface AlertData {
  image?: string
  name?: string
  permalink?: string
  time?: string
  action?: string
  id?: string
  status: 'SENT' | 'READ' | 'UNREAD'
}

interface AuthContextType {
  isAuthenticated: boolean
  loading: boolean
  authUserProfile: PermalinkUser | null
  setAuthUserProfile: (profile: PermalinkUser | null) => void
  refreshUserProfile: () => Promise<void>
  refreshNotificationCount: () => Promise<void>
  setUnreadNotificatonCount: React.Dispatch<React.SetStateAction<number>>
  unreadNotificationCount: number

  login: (username: string, password: string) => Promise<any>
  googleLogin: (code: string) => Promise<any>
  googleSignUp: (code: string) => Promise<any>
  logout: (global?: boolean) => Promise<void>
  signUp: (userdata: SignupRequest) => Promise<any>
  // googleSignUp: (code: string) => Promise<any>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  // const isBrowser = typeof window !== 'undefined'
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loading, setLoading] = useState(true) // Only show loading state in browser
  const [authUserProfile, setAuthUserProfile] = useState<PermalinkUser | null>(null)
  const router = useRouter()
  const [notifications, setNotifications] = useState<AlertData[]>([])
  const [unreadNotificationCount, setUnreadNotificatonCount] = useState<number>(0)

  const fetchUserProfile = useCallback(async () => {
    const token = TokenService.getAccessToken()
    if (!token) {
      setAuthUserProfile(null)
      setLoading(false)
      return
    }
    try {
      const userResponse = await UserService.getUserProfile()
      const profileRes = await UserService.getUserProfileByPermalink(userResponse.data.permalink)
      if (profileRes.status === 'success' && profileRes.data) {
        setAuthUserProfile(profileRes.data)
        // Fetch resume data and calculate progressAdd commentMore actions
        if (profileRes.data.permalink) {
          try {
            const resumeResponse = await CareerService.getCareerByPermalink(
              profileRes.data.permalink,
            )
            if (resumeResponse.status === 'success' && resumeResponse.data) {
              const resumeData = resumeResponse.data
              const progress = useResumeProgress(resumeData, false, false)
              const key = `resumeProgress_${profileRes.data.id}`
              localStorage.setItem(key, progress.toString())
            } else {
              // Handle case where resume doesn't exist (e.g., new user)
              const key = `resumeProgress_${profileRes.data.id}`
              localStorage.setItem(key, '11')
            }
          } catch (error) {
            console.error('Failed to fetch resume data:', error)
            const key = `resumeProgress_${profileRes.data.id}`
            localStorage.setItem(key, '0') // Default to 0 on error
          }
        }
      } else {
        throw new Error(profileRes.message || 'Failed to fetch user profile')
      }
    } catch (error) {
      console.error('Failed to fetch user profile:', error)
      logout()
    } finally {
      setLoading(false)
    }
  }, [])

  const fetchNotifications = useCallback(async () => {
    // if (!isBrowser) return // Skip in SSR
    const token = TokenService.getAccessToken()
    if (!token) return
    try {
      const response = await NotificationService.getNotifications('UNREAD')
      const totalCount = response.data?.paginator?.total
      setUnreadNotificatonCount(totalCount)
    } catch (error: any) {
      setUnreadNotificatonCount(0)
    }
  }, [])

  const refreshUserProfile = async () => {
    // if (!isBrowser) return // Skip in SSR
    await fetchUserProfile()
  }

  const refreshNotificationCount = async () => {
    // if (!isBrowser) return // Skip in SSR
    await fetchNotifications()
  }

  const login = async (username: string, password: string) => {
    try {
      const response = await AuthService.signIn(username, password)

      if (response.data.status === 'error')
        throw new Error(response.data.message || 'Sign in Failed')

      const accessToken = response.headers['x-access-token']
      if (!accessToken) {
        throw new Error('No access token received')
      }

      TokenService.setAccessToken(accessToken)
      setIsAuthenticated(true)

      // Extract entity info
      let entityId = null
      let entityType = null

      if (response.data.user) {
        console.log(response.data.user)
        entityId = response.data.user.id || 'asdfghjkl;'
        entityType = 'USER'
      } else if (response.data.data.user && response.data.data.user.id) {
        console.log(response.data.data.user)
        entityId = response.data.data.user.id
        entityType = response.data.data.type || 'USER'
      }

      // Set cookies for entity id and type (valid for 7 days)
      const expires = new Date()
      expires.setDate(expires.getDate() + 7)

      if (entityId) {
        document.cookie = `x-active-entity-id=${entityId}; path=/; expires=${expires.toUTCString()}; secure`
      }
      if (entityType) {
        document.cookie = `x-active-entity-type=${entityType}; path=/; expires=${expires.toUTCString()}; secure`
      }

      // Fetch profile & notifications
      await Promise.all([fetchUserProfile(), fetchNotifications()])

      return response.data
    } catch (e: any) {
      throw new Error(e.message)
    }
  }

  const googleLogin = async (code: string) => {
    // if (!isBrowser) return // Skip in SSR
    try {
      const response = await AuthService.googleSignIn(code)
      if (response.data.status === 'error')
        throw new Error(response.data.message || 'Sign in Failed')

      const accessToken = response.headers['x-access-token']

      if (!accessToken) {
        throw new Error('No access token received')
      }
      TokenService.setAccessToken(accessToken)
      setIsAuthenticated(true)
      await Promise.all([fetchUserProfile(), fetchNotifications()])
      return response.data
    } catch (e: any) {
      throw new Error(e.message)
    }
  }

  const signUp = async (userdata: SignupRequest) => {
    // if (!isBrowser) return // Skip in SSR
    try {
      const response = await AuthService.signUp(userdata)
      const accessToken = response.headers['x-access-token']
      if (!accessToken) {
        throw new Error('No access token received')
      }
      TokenService.setAccessToken(accessToken)
      setIsAuthenticated(true)
      await Promise.all([fetchUserProfile(), fetchNotifications()])
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Sign-up failed. Please try again.')
    }
  }

  const googleSignUp = async (code: string) => {
    try {
      const response = await AuthService.googleSignIn(code)
      if (response.data.status === 'error') {
        throw new Error(response.data.message || 'Signup Failed')
      }
      return response.data
    } catch (e: any) {
      throw new Error(e.message)
    }
  }

  // const googleSignUp = async (code: string) => {
  //   // if (!isBrowser) return // Skip in SSR
  //   try {
  //     const response = await AuthService.googleSignIn(code)
  //     if (response.data.status === 'error')
  //       throw new Error(response.data.message || 'Signup Failed')
  //     return response
  //   } catch (e: any) {
  //     throw new Error(e.message)
  //   }
  // }

  const logout = async (global?: boolean) => {
    // if (!isBrowser) return // Skip in SSR
    try {
      const token = TokenService.getAccessToken()
      if (token) {
        await AuthService.logout(token, global)
      }
    } catch (error) {
      console.error('Logout failed:', error)
    } finally {
      setIsAuthenticated(false)
      setAuthUserProfile(null)
      setNotifications([])
      TokenService.removeAccessToken()
      router.push('/sign-in')
    }
  }

  useEffect(() => {
    // if (!isBrowser) return // Skip in SSR

    const token = TokenService.getAccessToken()
    if (token) {
      setIsAuthenticated(true)
    }
    fetchUserProfile()
    fetchNotifications()
  }, [fetchUserProfile, fetchNotifications])

  // During SSR, render with default values
  // if (!isBrowser) {
  //   return (
  //     <AuthContext.Provider
  //       value={{
  //         isAuthenticated: false,
  //         loading: false,
  //         authUserProfile: null,
  //         setAuthUserProfile: () => {},
  //         refreshUserProfile: async () => {},
  //         refreshNotificationCount: async () => {},
  //         setUnreadNotificatonCount: () => {},
  //         unreadNotificationCount: 0,
  //         login: async () => {},
  //         logout: async () => {},
  //         signUp: async () => {},
  //       }}
  //     >
  //       {children}
  //     </AuthContext.Provider>
  //   )
  // }

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        loading,
        authUserProfile,
        setAuthUserProfile,
        login,
        googleLogin,
        googleSignUp,
        logout,
        signUp,
        // googleSignUp,
        refreshUserProfile,
        refreshNotificationCount,
        unreadNotificationCount,
        setUnreadNotificatonCount,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
