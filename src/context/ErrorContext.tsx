'use client'
import { createContext, useContext, useState } from 'react'
import ErrorPage from '../components/ErrorPage/ErrorPage'
import Overlay from '../components/Overlay'
import { ErrorService } from '../services/ErrorService'
import { useToast } from '../components/ToastX'

type ErrorVariant =
  | 'no-data'
  | 'offline'
  | 'something-went-wrong'
  | 'downtime'
  | 'permission-denied'
  | 'error-404'

interface ErrorContextType {
  showError: (variant: ErrorVariant, message?: string) => void
  hideError: () => void
  handleError: (error: any) => void
  error: { variant: ErrorVariant; message?: string } | null
}

const ErrorContext = createContext<ErrorContextType | undefined>(undefined)

export const ErrorProvider = ({ children }: { children: React.ReactNode }) => {
  const [error, setError] = useState<ErrorContextType['error']>(null)
  const toast = useToast()

  const showError = (variant: ErrorVariant, message?: string) => {
    setError({ variant, message })
  }

  const hideError = () => {
    setError(null)
  }

  const handleError = (error: any) => {
    const status = error?.status
    if (status >= 400) {
      const result = ErrorService.handleHttpError(error)
      if (result) {
        showError(result.variant, result.message)
      }
      return
    }

    const result = ErrorService.handleApiError(error)
    if (!result) return

    if ('isDefault' in result) {
      if (result.isDefault) {
        alert(result.message)
      } else {
        toast.error(result.message)
      }
    } else {
      showError(result.variant, result.message)
    }
  }

  return (
    <ErrorContext.Provider value={{ error, showError, hideError, handleError }}>
      {children}

      {error && (
        <div className="z-9999 absolute inset-0 flex items-center justify-center bg-black/60">
          <div className="dark:bg-dark-lucres-black-900 relative w-full max-w-3xl rounded-xl p-6">
            <Overlay
              handleClose={hideError}
              heading="Error"
              crossRequired={
                error.variant !== 'permission-denied' &&
                error.variant !== 'downtime' &&
                error.variant !== 'error-404'
              }
              classes="w-full max-w-lg h-fit! relative z-50"
              closeOnOutsideClick={false}
            >
              <ErrorPage variant={error.variant} subtitle={error.message} />
            </Overlay>
          </div>
        </div>
      )}
    </ErrorContext.Provider>
  )
}

const useError = () => {
  const context = useContext(ErrorContext)
  if (!context) throw new Error('useError must be used within ErrorProvider')
  return context
}

export default useError
