'use client'
import { createContext, useState, useContext, FC, ReactNode } from 'react'

interface ColorContextType {
  selectedColor: string
  setSelectedColor: (color: string) => void
  secondaryColor: string
  setSecondaryColor: (color: string) => void
}

const ColorContext = createContext<ColorContextType | undefined>(undefined)

export const ColorProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [selectedColor, setSelectedColor] = useState<string>('#145349') // Default main color
  const [secondaryColor, setSecondaryColor] = useState<string>('white') // Default secondary color

  return (
    <ColorContext.Provider
      value={{
        selectedColor,
        setSelectedColor,
        secondaryColor,
        setSecondaryColor,
      }}
    >
      {children}
    </ColorContext.Provider>
  )
}

export const useColorContext = (): ColorContextType => {
  const context = useContext(ColorContext)
  if (!context) {
    throw new Error('useColorContext must be used within a ColorProvider')
  }
  return context
}
