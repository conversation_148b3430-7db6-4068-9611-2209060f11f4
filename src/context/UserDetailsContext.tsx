'use client'
import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react'
import { UserService } from '../services/UserService'
import { TokenService } from '@/services/TokenService'

interface PersonalInformation {
  firstName: string
  lastName: string
  headline?: string
  photo?: string
  dob: string
  address?: string
  location: string
  gender: string
}

interface ContactDetails {
  primaryPhone: string
  password: string
  email: string
  secondaryPhone: string
  secondaryEmail: string
}

interface AccountContextType {
  fetchUserData: () => Promise<any>
  personalInformation: PersonalInformation
  setPersonalInformation: React.Dispatch<React.SetStateAction<PersonalInformation>>
  contactDetails: ContactDetails
  setContactDetails: React.Dispatch<React.SetStateAction<ContactDetails>>
  resumeProgress: number
  setResumeProgress: React.Dispatch<React.SetStateAction<number>>
}

const defaultPersonalInformation: PersonalInformation = {
  firstName: '',
  lastName: '',
  headline: '',
  dob: '',
  address: '',
  location: '',
  gender: '',
}

const defaultContactDetails: ContactDetails = {
  primaryPhone: '',
  password: '',
  email: '',
  secondaryPhone: '',
  secondaryEmail: '',
}

const AccountContext = createContext<AccountContextType | undefined>(undefined)

export const AccountProvider = ({ children }: { children: ReactNode }) => {
  // const isBrowser = typeof window !== 'undefined'
  const [personalInformation, setPersonalInformation] = useState<PersonalInformation>({
    firstName: '',
    lastName: '',
    headline: '',
    dob: '',
    address: '',
    location: '',
    gender: '',
  })
  const [resumeProgress, setResumeProgress] = useState<number>(11)
  const [contactDetails, setContactDetails] = useState<ContactDetails>({
    primaryPhone: '',
    password: '',
    email: '',
    secondaryPhone: '',
    secondaryEmail: '',
  })

  const fetchUserData = async () => {
    const token = TokenService.getAccessToken()
    if (!token) {
      return
    }
    try {
      const [profileData, contactData] = await Promise.all([
        UserService.getUserProfile(),
        UserService.getUserContact(),
      ])
      const profile = profileData.data
      const contact = contactData.data.contact
      setPersonalInformation(profile)
      setContactDetails(contact)
    } catch (error) {
      console.error('Error fetching user data:', error)
    }
  }
  useEffect(() => {
    fetchUserData()
  }, [])

  return (
    <AccountContext.Provider
      value={{
        fetchUserData,
        personalInformation,
        setPersonalInformation,
        contactDetails,
        setContactDetails,
        resumeProgress,
        setResumeProgress,
      }}
    >
      {children}
    </AccountContext.Provider>
  )
}

export const useAccount = () => {
  const context = useContext(AccountContext)
  if (!context) {
    throw new Error('useAccount must be used within an AccountProvider')
  }
  return context
}
