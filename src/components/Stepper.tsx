'use client'
import React, { useRef, useEffect } from 'react'
import { CaretRight } from '@phosphor-icons/react'

interface StepperProps {
  steps: { label: string; isNew?: boolean }[]
  currentStep: number
  onStepChange: (step: number) => void
}

const Stepper: React.FC<StepperProps> = ({ steps, currentStep, onStepChange }) => {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (containerRef.current) {
      const currentStepElement = containerRef.current.querySelector(
        `[data-step-index="${currentStep}"]`,
      )
      if (currentStepElement) {
        currentStepElement.scrollIntoView({
          behavior: 'smooth',
          inline: 'center',
          block: 'nearest',
        })
      }
    }
  }, [currentStep]) // Re-run when currentStep changes

  return (
    <div className="scrollbar-none m-auto overflow-x-scroll sm:m-0" ref={containerRef}>
      <div className="flex items-center gap-3 text-sm font-medium sm:gap-3 lg:text-base">
        {steps.map((step, index) => (
          <div
            key={index}
            className="flex shrink-0 cursor-pointer items-center gap-2"
            onClick={() => onStepChange(index)}
            data-step-index={index}
          >
            {/* Step Label */}
            <span
              className={`whitespace-nowrap font-medium ${
                index < currentStep ? 'text-lucres-600 dark:text-lucres-500 leading-5' : ''
              } ${
                index === currentStep
                  ? 'text-lucres-600 dark:text-lucres-500 font-semibold leading-5'
                  : ''
              } `}
            >
              {step.label}
            </span>

            {/* New Badge */}
            {step.isNew && (
              <span className="bg-lucres-400 rounded-lg bg-opacity-20 px-3 py-0.5 text-xs sm:text-xs lg:text-sm">
                NEW
              </span>
            )}

            {/* Divider Icon */}
            {index < steps.length - 1 && (
              <span className="shrink-0" onClick={(e) => e.stopPropagation()}>
                <div className="flex h-5 w-5 items-center justify-center">
                  <CaretRight
                    size={currentStep === index ? 20 : 18}
                    className="dark:text-dark-lucres-green-100 cursor-auto"
                  />
                </div>
              </span>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

export default Stepper
