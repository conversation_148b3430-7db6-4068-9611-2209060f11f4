import React, { ReactNode } from 'react'

interface TagProps {
  theme?: 'primary' | 'secondary' | 'tertiary' // Theme prop
  ClassName?: string
  TagText: ReactNode
}

const Tag: React.FC<TagProps> = ({
  theme = 'primary', // Default theme
  TagText,
  ClassName,
}) => {
  // Default styles for each theme
  const themeStyles = {
    primary: {
      borderColor: ' border border-[#b6e777]! dark:border-dark-lucres-black-100!',
      backgroundColor: 'bg-transparent',
      textColor: '',
    },
    // <Tag BackgroundColor="#9bc95f4d" TagText="Empower your career" TextColor="#B6E777" Margin="0"/>
    secondary: {
      borderColor: 'border border-[#b6e777] dark:border-dark-lucres-black-100!',
      backgroundColor: 'bg-[#9bc95f4d] dark:bg-transparent',
      textColor: 'text-[#B6E777] dark:text-white',
    },
    tertiary: {
      borderColor:
        'border-transparent dark:border rounded-full dark:rounded-full dark:border-dark-lucres-black-100! border-[#b6e777]!',
      backgroundColor: 'bg-[#b6e77766] dark:bg-transparent',
      textColor: '',
    },
  }

  const styles = themeStyles[theme]
  return (
    <div
      className={`m-auto w-max whitespace-nowrap rounded-3xl px-5 py-2 text-sm font-medium ${styles.borderColor} ${styles.backgroundColor} ${styles.textColor} ${ClassName} `}
    >
      {TagText}
    </div>
  )
}

export default Tag
