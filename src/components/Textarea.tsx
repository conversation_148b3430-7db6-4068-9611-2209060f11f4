'use client'
import React, { forwardRef, useEffect, useRef, useState, useCallback } from 'react'
import { useTheme } from '../context/ThemeProvider'
import { PostService } from '../services/PostService'
import Avatar from './Avatar/Avatar'
import { capitalizeWords, truncateText } from '../utils/commonUtils'

interface MentionUser {
  id: string
  username: string
  profileImage: {
    url: string
    name: string
  }
  givenName: string
  familyName: string
}

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string
  className?: string
  labelClassName?: string
  error?: string
  height?: string
  theme?: 'transparent' | 'gray'
  mentionUsers?: MentionUser[] // List of all mentionable users
  onMentionSelect?: (user: MentionUser) => void
}

const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      label,
      className = '',
      labelClassName = '',
      error,
      theme = 'transparent',
      height = '80px',
      value = '',
      onMentionSelect,
      ...props
    },
    ref,
  ) => {
    const { theme: currentTheme } = useTheme()
    const mode = currentTheme === 'dark' ? 'dark' : 'light'
    const textareaRef = useRef<HTMLTextAreaElement | null>(null)
    const overlayRef = useRef<HTMLDivElement | null>(null)

    const [mentionQuery, setMentionQuery] = useState('')
    const [showMentions, setShowMentions] = useState(false)
    const [mentionUsers, setMentionUsers] = useState<MentionUser[]>([])
    const [isLoadingMentions, setIsLoadingMentions] = useState(false)
    const [mentionTriggerPosition, setMentionTriggerPosition] = useState<{
      top: number
      left: number
    } | null>(null)

    // const [filteredMentions, setFilteredMentions] = useState<MentionUser[]>([])
    const [selectedMentionIndex, setSelectedMentionIndex] = useState(0)

    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (showMentions && mentionUsers.length > 0) {
        if (e.key === 'ArrowDown') {
          e.preventDefault()
          setSelectedMentionIndex((prev) => (prev + 1) % mentionUsers.length)
        } else if (e.key === 'ArrowUp') {
          e.preventDefault()
          setSelectedMentionIndex((prev) => (prev === 0 ? mentionUsers.length - 1 : prev - 1))
        } else if (e.key === 'Enter') {
          e.preventDefault()
          insertMention(mentionUsers[selectedMentionIndex])
        }
      }

      props.onKeyDown?.(e) // if user has a custom onKeyDown
    }

    // Merge ref
    useEffect(() => {
      if (typeof ref === 'function') {
        ref(textareaRef.current)
      } else if (ref) {
        ;(ref as React.MutableRefObject<HTMLTextAreaElement | null>).current = textareaRef.current
      }
    }, [ref])

    const latestQueryRef = useRef('')

    function debounce<T extends (...args: any[]) => void>(
      fn: T,
      delay: number,
    ): (...args: Parameters<T>) => void {
      let timeoutId: ReturnType<typeof setTimeout>
      return (...args: Parameters<T>) => {
        clearTimeout(timeoutId)
        timeoutId = setTimeout(() => fn(...args), delay)
      }
    }

    const fetchMentionUsers = useCallback(async (query: string) => {
      setIsLoadingMentions(true)
      latestQueryRef.current = query

      try {
        const result = await PostService.mentionUsers(query)
        if (latestQueryRef.current === query) {
          const users = result.data
          setMentionUsers(users)
        }
      } catch (err) {
        console.error('Failed to fetch mention users', err)
      } finally {
        setIsLoadingMentions(false)
      }
    }, [])

    const debouncedFetchMentions = useCallback(debounce(fetchMentionUsers, 300), [
      fetchMentionUsers,
    ])

    // Resize
    // useEffect(() => {
    //   if (textareaRef.current) {
    //     textareaRef.current.style.height = height!
    //     textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    //   }
    // }, [value, height])

    // Handle input + mention trigger
    const handleInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const el = e.target
      el.style.height = height!
      el.style.height = `${el.scrollHeight}px`
      props.onInput?.(e)

      const caretPos = el.selectionStart
      const textBefore = el.value.slice(0, caretPos)

      // Regex to match `@` mention only if:
      // - @ is at start of text OR
      // - preceded by whitespace
      // And captures the word characters after @
      const mentionMatch = /(?:^|\s)@(\w*)$/.exec(textBefore)

      if (mentionMatch) {
        const query = mentionMatch[1] || ''

        setMentionQuery(query)
        if (query.length >= 1) {
          setShowMentions(true)
          latestQueryRef.current = query
          debouncedFetchMentions(query)

          if (!mentionTriggerPosition) {
            requestAnimationFrame(() => {
              // position of '@' character
              const atPosition = caretPos - query.length - 1
              const coords = getCaretCoordinates(el, atPosition)
              setMentionTriggerPosition(coords)
            })
          }
        } else {
          setShowMentions(false)
          setMentionUsers([])
          setMentionTriggerPosition(null)
        }
      } else {
        // No mention trigger found
        setMentionQuery('')
        setMentionUsers([])
        setShowMentions(false)
        setMentionTriggerPosition(null)
      }
    }

    useEffect(() => {
      const el = textareaRef.current
      if (!el) return

      // Reset height to base and then set scrollHeight
      el.style.height = height!
      el.style.height = `${el.scrollHeight}px`
    }, [value, height])

    const insertMention = (user: MentionUser) => {
      const el = textareaRef.current
      if (!el) return

      const caretPos = el.selectionStart
      const textBefore = el.value.slice(0, caretPos)
      const textAfter = el.value.slice(caretPos)

      const newTextBefore = textBefore.replace(/@(\w*)$/, `@${user.username} `) // add space
      const newText = newTextBefore + textAfter

      props.onChange?.({ ...({ target: { value: newText } } as any) })
      setShowMentions(false)
      setMentionQuery('')
      el.focus()
      requestAnimationFrame(() => {
        el.selectionStart = el.selectionEnd = newTextBefore.length
      })

      onMentionSelect?.(user)
    }

    const getCaretCoordinates = (textarea: HTMLTextAreaElement, position: number) => {
      const div = document.createElement('div')
      const style = window.getComputedStyle(textarea)

      // Copy essential styles
      const properties = [
        'direction',
        'boxSizing',
        'width',
        'height',
        'overflowX',
        'overflowY',
        'borderTopWidth',
        'borderRightWidth',
        'borderBottomWidth',
        'borderLeftWidth',
        'paddingTop',
        'paddingRight',
        'paddingBottom',
        'paddingLeft',
        'fontStyle',
        'fontVariant',
        'fontWeight',
        'fontStretch',
        'fontSize',
        'fontSizeAdjust',
        'lineHeight',
        'fontFamily',
        'textAlign',
        'textTransform',
        'textIndent',
        'textDecoration',
        'letterSpacing',
        'wordSpacing',
      ]

      div.style.position = 'absolute'
      div.style.visibility = 'hidden'
      div.style.whiteSpace = 'pre-wrap'

      properties.forEach((prop) => {
        // @ts-ignore
        div.style[prop] = style[prop]
      })

      div.style.width = textarea.offsetWidth + 'px'

      // Set the text and span
      const textBefore = textarea.value.substring(0, position)
      const textAfter = textarea.value.substring(position)

      div.textContent = textBefore

      const span = document.createElement('span')
      span.textContent = textAfter || '.' // dummy if caret is at the end

      div.appendChild(span)
      document.body.appendChild(div)

      const { offsetTop: top, offsetLeft: left } = span
      document.body.removeChild(div)

      return { top, left }
    }

    useEffect(() => {
      const handleClick = (event: MouseEvent) => {
        const isClickInsideOverlay = overlayRef.current?.contains(event.target as Node)

        // Close the overlay if clicked anywhere (even inside textarea) except the overlay itself
        if (!isClickInsideOverlay) {
          setShowMentions(false)
          setMentionQuery('')
          setMentionUsers([])
          setMentionTriggerPosition(null)
        }
      }

      document.addEventListener('mousedown', handleClick)
      return () => {
        document.removeEventListener('mousedown', handleClick)
      }
    }, [])

    const inputClasses = {
      light: {
        transparent:
          'border border-lucres-300 border-opacity-40 placeholder-lucres-300 bg-transparent',
        gray: 'bg-lucres-gray-100',
      },
      dark: {
        transparent:
          'border border-dark-lucres-black-200 placeholder-lucres-800 text-dark-lucres-green-100! bg-transparent',
        gray: 'bg-lucres-gray-100 dark:bg-dark-lucres-black-300',
      },
    }

    const errorClasses = {
      light: 'text-red-600',
      dark: 'text-red-400',
    }

    return (
      <div className="relative flex h-full w-full flex-col justify-end gap-2">
        {label && (
          <label
            className={`text-lucres-900 dark:text-dark-lucres-green-300 whitespace-nowrap text-sm font-semibold text-opacity-75 ${labelClassName}`}
          >
            {label}
            {props.required && <span className="text-red-400"> *</span>}
          </label>
        )}
        <textarea
          {...props}
          value={value}
          ref={textareaRef}
          onInput={handleInput}
          onKeyDown={handleKeyDown}
          style={{ height }}
          className={`scrollbar-none focus:outline-hidden max-h-96 w-full resize-none rounded-lg px-4 py-3 placeholder:text-sm ${inputClasses[mode][theme]} ${className}`}
        />

        {showMentions && mentionUsers.length > 0 && mentionTriggerPosition && (
          <div
            ref={overlayRef}
            className="dark:bg-dark-lucres-black-300 absolute z-50 flex h-fit max-h-60 w-60 max-w-sm flex-col gap-2 overflow-y-auto rounded-md border bg-white p-2"
            style={{
              top: mentionTriggerPosition.top + 30, // adjust to move below @
              left: mentionTriggerPosition.left - 8,
            }}
          >
            {mentionUsers.map((user, index) => (
              <div
                key={user.id}
                className={`flex cursor-pointer items-center gap-x-2 rounded-md p-1 ps-2 font-medium ${
                  index === selectedMentionIndex
                    ? 'dark:bg-dark-lucres-black-400 bg-[#f1f1f1]'
                    : 'text-lucres-gray-700 dark:text-lucres-green-100'
                } dark:hover:bg-dark-lucres-black-400 hover:bg-[#f1f1f1]`}
                onClick={(e) => {
                  e.preventDefault()
                  insertMention(user)
                }}
              >
                <Avatar
                  src={user?.profileImage?.url || ''}
                  alt={`${user?.profileImage?.name}'s Avatar`}
                  size={10}
                  className="min-w-10 cursor-pointer object-cover"
                />
                <div className="flex flex-col justify-center">
                  <div className="flex flex-wrap items-center">
                    <span className="text-lucres-black dark:text-dark-lucres-green-100 cursor-pointer font-medium">
                      {capitalizeWords(
                        truncateText(`${user.givenName ?? ''} ${user.familyName ?? ''}`, 15).text,
                      )}
                    </span>
                  </div>
                  <div className="text-lucres-gray-700 dark:text-dark-lucres-green-300 -mt-1 flex items-center text-sm">
                    @{user?.username}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {error && <p className={`mt-1 pl-4 text-xs capitalize ${errorClasses[mode]}`}>{error}</p>}
      </div>
    )
  },
)

export default Textarea
