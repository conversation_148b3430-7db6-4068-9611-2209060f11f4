import { ArrowRightIcon, BriefcaseIcon, MapPinIcon, WarningIcon } from '@phosphor-icons/react'
import { useEffect, useRef, useState } from 'react'

import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, Pagination, FreeMode, Navigation } from 'swiper/modules'
// @ts-ignore
import 'swiper/css'
// @ts-ignore
import 'swiper/css/free-mode'
// @ts-ignore
import 'swiper/css/pagination'
import IconButton from './IconButton'
import Button from './Button'
import { UserService } from '../services/UserService'
import { useAuth } from '../context/AuthContext'
import { useToast } from './ToastX'
import Avatar from './Avatar/Avatar'
import { TrendingJob } from '../models/Job'
import CopyLinkIcon from './CopyLinkIcon'
import { JobService } from '../services/JobService'
import useError from '../context/ErrorContext'
import { CareerService } from '../services/CareerService'
import { useAccount } from '../context/UserDetailsContext'
import Overlay from './Overlay'
import BookmarkIcon from './BookmarkIcon'
import { BookmarkService } from '../services/BookmarkService'
import { truncateText } from '../utils/commonUtils'
import { useRouter } from 'next/navigation'
// import { Job } from "./Jobs";
const TrendingJobs = ({
  className = '',
  breakpoints,
  // onApply,
  // jobApplied,
  // savedJobs,
  // onSave,
}: {
  className?: string
  breakpoints?: {}
  // onApply?: (job: Job, e: React.MouseEvent) => void
  // onSave?: (job: Job, e: React.MouseEvent) => void
  // jobApplied: Job[]
  // savedJobs: Job[]
}) => {
  const swiperRef = useRef<any>(null)
  const [isAtEnd, setIsAtEnd] = useState<boolean>(false)
  // const { trendingJobs } = useJob()
  const [trendingJobs, setTrendingJobs] = useState<TrendingJob[]>([])
  const { authUserProfile } = useAuth()
  const [isApplied, setIsApplied] = useState(false)
  const [jobId, setJobId] = useState('')
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [appliedJobs, setAppliedJobs] = useState<Set<string>>(new Set())
  const { isAuthenticated } = useAuth()
  const [showModal, setShowModal] = useState(false)
  const [missingDetails, setMissingDetails] = useState<string[]>([])
  const { handleError } = useError()
  const router = useRouter()
  const { contactDetails } = useAccount()
  const toast = useToast()
  // applied={jobApplied.some((appliedJob) => appliedJob.id === job.id)}
  const handleSlideChange = (swiper: any) => {
    setIsAtEnd(swiper.isEnd)
  }
  useEffect(() => {
    try {
      if (!isAuthenticated) return
      const fetchTrendingJobs = async () => {
        if (authUserProfile) {
          const res = await UserService.getTrendingJobs(authUserProfile?.permalink)
          setTrendingJobs(res.data)
        }
      }
      fetchTrendingJobs()
    } catch (error: any) {
      toast.error(error)
    }
  }, [])

  const handleApplyJob = () => {}
  const validateResume = async () => {
    if (!authUserProfile?.permalink) {
      toast.error('Please complete your profile before applying')
      return false
    }

    try {
      const resumeResponse = await CareerService.getCareerByPermalink(authUserProfile.permalink)
      if (resumeResponse.status !== 'success' || !resumeResponse.data) {
        toast.error('Please complete your resume before applying')
        return false
      }

      const resumeData = resumeResponse.data
      const missing = []

      // Check education
      if (!resumeData.educations || resumeData.educations.length === 0) {
        missing.push('education details')
      }

      // Check skills
      if (!resumeData.skills || resumeData.skills.length === 0) {
        missing.push('skills')
      }

      // Check phone number
      if (!contactDetails.primaryPhone) {
        missing.push('phone number')
      }

      if (missing.length > 0) {
        setMissingDetails(missing)
        setShowModal(true)
        return false
      }

      return true
    } catch (error) {
      console.error('Error validating resume:', error)
      toast.error('Failed to validate resume')
      return false
    }
  }

  const handleApply = async (id: string) => {
    if (!id || !authUserProfile?.id) return
    try {
      const isValid = await validateResume()
      if (!isValid) return

      const response = await JobService.applyJob(id, authUserProfile.id)

      if (response.data.status === 'success') {
        toast.success('Job Applied Successfully')
        setAppliedJobs((prev) => new Set(prev).add(id)) // mark this job as applied
      } else {
        throw new Error(response.data.message || 'Cannot apply to the job at this moment')
      }
    } catch (error: any) {
      handleError(error)
    }
  }

  // const handleBookmarkToggle = async (newBookmarked: boolean, id?: string) => {
  //   if (!isAuthenticated && !id) {
  //     toast.error('Please log in to bookmark.')
  //     router.push('/login')
  //     return
  //   }

  //   try {
  //     if (newBookmarked && id) {
  //       const res = await BookmarkService.addBookmark(id, 'JOBS_SRP')
  //       if (res.status === 'success') {
  //         toast.success(res.message || 'Bookmark added.')
  //       } else {
  //         throw new Error(res.message)
  //       }
  //     } else {
  //       const res = await BookmarkService.deleteBookmark(id, 'JOBS_SRP')
  //       if (res.status === 'success') {
  //         toast.success(res.message || 'Bookmark removed.')
  //       } else {
  //         throw new Error(res.message)
  //       }
  //     }
  //   } catch (error: any) {
  //     handleError(error)
  //     throw error // Re-throw to trigger UI rollback
  //   }
  // }
  return (
    <div
      className={`dark:border-dark-lucres-black-300 flex h-full flex-col gap-y-3 overflow-hidden border-y py-4 ${className}`}
    >
      <div className="flex w-full items-center justify-between pe-4 md:ps-4">
        <span className="text-lucres-black dark:text-dark-lucres-green-100 text-xl font-medium">
          Trending Jobs
        </span>

        <ArrowRightIcon
          size={24}
          className={`${isAtEnd && 'cursor-default text-gray-300'} cursor-pointer`}
          onClick={() => swiperRef.current?.swiper.slideNext()}
        />
      </div>
      <div className="w-full md:px-4">
        <Swiper
          ref={swiperRef}
          slidesPerView={1}
          slidesPerGroup={2}
          spaceBetween={10}
          speed={1000}
          modules={[Autoplay, FreeMode, Pagination, Navigation]}
          onSlideChange={handleSlideChange}
          breakpoints={
            breakpoints
              ? breakpoints
              : {
                  768: { slidesPerView: 1.7 },
                  1024: { slidesPerView: 1.8 },
                  1440: { slidesPerView: 1.8 },
                }
          }
          pagination={{
            clickable: true,
            el: '.custom-trending-job-pagination',
            bulletClass: 'swiper-trending-job-pagination-bullet',
            bulletActiveClass: 'swiper-trending-job-pagination-bullet-active',
          }}
        >
          {trendingJobs.map((job) => {
            // const applied = jobApplied.some((appliedJob) => appliedJob.id === job.id)
            // const saved = savedJobs.some((savedJobs) => savedJobs.id === job.id)
            return (
              <SwiperSlide key={job.id}>
                <div className="dark:border-dark-lucres-black-300 flex cursor-pointer flex-col rounded-xl border px-4 py-3">
                  <div className="flex flex-col">
                    <div className="flex items-center gap-x-2">
                      <Avatar
                        src={job?.companyLogo?.url}
                        alt={`${job?.companyLogo?.name}'s Avatar`}
                        size={10}
                        type="company"
                        // onClick={() => navigate('/profile')}
                        className="cursor-pointer object-cover"
                      />
                      <div className="flex flex-col">
                        <div className="flex items-center">
                          <span className="text-lucres-black dark:text-dark-lucres-green-100 text-lg font-semibold">
                            {truncateText(job.title, 20).text}
                          </span>
                        </div>
                        <span className="text-lucres-950 dark:text-dark-lucres-green-100 whitespace-normal">
                          {truncateText(job.company.name, 20).text}
                        </span>
                      </div>
                    </div>
                    <div className="mt-2 flex items-center gap-x-4 whitespace-nowrap">
                      <span className="text-lucres-gray-700 dark:text-dark-lucres-green-100 flex items-center gap-x-2">
                        <MapPinIcon size={18} weight="bold" />
                        {job.location.address.city}, {job.location.address.country}
                      </span>
                      <span className="text-lucres-gray-700 dark:text-dark-lucres-green-100 flex items-center gap-x-2">
                        <BriefcaseIcon weight="bold" size={18} />
                        {job.employmentType === 'PART_TIME'
                          ? 'Part Time'
                          : job.employmentType === 'Full_TIME'
                            ? 'Full Time'
                            : 'Contract'}
                      </span>
                    </div>
                  </div>
                  <div className="dark:border-t-dark-lucres-black-300 mt-4 flex items-center justify-between border-t pt-1">
                    <div className="flex items-center gap-x-2">
                      <CopyLinkIcon toCopy={`${location.origin}/job/${job?.permalink}`} />
                      {/* <BookmarkIcon
                        onBookmarkToggle={handleBookmarkToggle}
                        isBookmarked={isBookmarked}
                        tooltipText="Save Job"
                        jobId={job.id}
                      /> */}
                    </div>

                    <Button
                      theme={appliedJobs.has(job.id) ? 'transparent' : 'translucent'}
                      size="small"
                      disabled={appliedJobs.has(job.id)}
                      className={`px-6! py-2! whitespace-nowrap ${
                        appliedJobs.has(job.id)
                          ? 'text-[#980000]! hover:bg-transparent! hover:text-[#980000]! dark:bg-transparent! dark:text-[#E39D90]! dark:hover:text-[#E39D90]'
                          : ''
                      }`}
                      onClick={(e) => {
                        e.stopPropagation()
                        handleApply(job.id)
                      }}
                    >
                      {appliedJobs.has(job.id) ? 'Applied' : 'Apply'}
                    </Button>
                  </div>
                </div>
              </SwiperSlide>
            )
          })}
          <div className="custom-trending-job-pagination mt-4 flex justify-center"></div>
        </Swiper>
      </div>
      {showModal && (
        <Overlay
          heading={'Complete Your Resume'}
          handleClose={() => setShowModal(false)}
          size="min-h-fit mt-20"
          classes="p-0!"
        >
          <div className="dark:bg-dark-lucres-black-500 w-full rounded-lg bg-white p-4 shadow-lg">
            <div className="flex flex-col items-center">
              <WarningIcon size={32} className="text-yellow-500" />
              <p
                className={`mt-3 text-center text-sm ${'text-lucres-800 dark:text-dark-lucres-black-100'}`}
              >
                Please add the following details:
              </p>

              <div className="mt-1 flex flex-col items-center text-sm">
                {missingDetails.map((detail, index) => (
                  <div key={index} className="capitalize">
                    {detail}
                  </div>
                ))}
              </div>
            </div>
            <div className="dark:border-t-dark-lucres-black-200 mt-6 flex justify-between gap-4 border-t pt-4">
              <Button
                size="small"
                theme="transparent"
                className="px-4! py-1.5! !border"
                onClick={() => setShowModal(false)}
              >
                Cancel
              </Button>
              <Button
                size="small"
                theme="dark"
                className="px-4! py-1.5! text-white!"
                onClick={() => {
                  setShowModal(false)
                  router.push('/resume')
                }}
              >
                {'Go to Resume'}
              </Button>
            </div>
          </div>
        </Overlay>
      )}
    </div>
  )
}

export default TrendingJobs
