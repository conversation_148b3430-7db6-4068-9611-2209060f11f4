import { sanitizeBasicHtml, sanitizeHtml } from '../utils/resumeUtils'

interface SafeHtmlProps {
  html: string
  className?: string
  restrictive?: boolean
  children?: never // Prevent children since we're using dangerouslySetInnerHTML
}

const SafeHtml: React.FC<SafeHtmlProps> = ({ html, className = '', restrictive = false }) => {
  const sanitizedHtml = restrictive ? sanitizeBasicHtml(html) : sanitizeHtml(html)

  if (!sanitizedHtml.trim()) {
    return null
  }

  return <div className={className} dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />
}

export default SafeHtml
