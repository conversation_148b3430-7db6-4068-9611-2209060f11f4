'use client'
import React, { ReactNode } from 'react'

type SlideUpViewProps = {
  isOpen: boolean
  children: ReactNode
}

const SlideUpView: React.FC<SlideUpViewProps> = ({ isOpen, children }) => {
  return (
    <div
      className={`z-999 fixed inset-0 bottom-0 flex h-screen items-end justify-center overflow-hidden bg-black bg-opacity-50 transition-opacity duration-300 ${
        isOpen ? 'opacity-100' : 'pointer-events-none opacity-0'
      }`}
    >
      <div
        className={`dark:bg-dark-lucres-black-500 w-full max-w-md transform rounded-t-2xl bg-white p-4 transition-transform duration-300 ${
          isOpen ? 'translate-y-0' : 'translate-y-full'
        }`}
        style={{ height: '95%' }}
        onClick={(e) => e.stopPropagation()} // prevent close on modal click
      >
        {/* Drag handle */}
        {/* <div className="mx-auto mb-4 h-1.5 w-12 rounded-full bg-gray-300" /> */}

        {/* Content */}
        <div className="!scrollbar-none h-full w-full overflow-y-auto">{children}</div>
      </div>
    </div>
  )
}

export default SlideUpView
