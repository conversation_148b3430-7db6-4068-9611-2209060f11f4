import { X } from '@phosphor-icons/react'
import React, { useEffect } from 'react'
import IconButton from './IconButton'

interface ModalProps {
  onClose: (e: React.MouseEvent<HTMLElement>) => void
  children: React.ReactNode
  classes?: string
  crossRequired?: boolean
  crossClass?: string
}

const Modal: React.FC<ModalProps> = ({
  onClose,
  children,
  classes,
  crossRequired = true,
  crossClass,
}) => {
  useEffect(() => {
    // Add 'no-scroll' class to the body when the modal is open
    document.body.classList.add('no-scroll')
    return () => {
      // Remove 'no-scroll' class when the modal is closed
      document.body.classList.remove('no-scroll')
    }
  }, [])

  const handleStopPropagation = (e: any) => {
    e.stopPropagation()
  }

  return (
    <div
      className="z-999! relative flex h-full w-full items-center justify-center transition-transform duration-700"
      onClick={handleStopPropagation}
    >
      <div className="z-50! fixed inset-0 mx-auto flex h-full w-full items-center justify-center bg-black bg-opacity-50">
        <div
          className={`scrollbar-none dark:bg-dark-lucres-black-500 relative z-40 h-full max-h-[90vh] w-11/12 max-w-[1440px] overflow-y-auto rounded-lg bg-white px-4 pt-2 shadow-lg lg:w-full ${classes}`}
        >
          {crossRequired && (
            <span
              className={`text-lucres-900 z-50! absolute right-2 top-5 rounded-full lg:right-6 ${crossClass}`}
              onClick={(e) => {
                e.stopPropagation()
                onClose(e)
              }}
            >
              <IconButton>
                <X
                  size={24}
                  className={`md:text-lucres-900 dark:text-dark-lucres-green-100 z-50! ${crossClass}`}
                />
              </IconButton>
            </span>
          )}
          <div className="-translate-y-4">{children}</div>
        </div>
      </div>
    </div>
    // document.getElementById("modal-root") as HTMLElement
  )
}

export default Modal
