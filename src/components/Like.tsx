'use client'
import React, { useEffect, useRef, useState } from 'react'
import { HeartIcon } from '@phosphor-icons/react'
import Tooltip from './Tooltip'
import IconButton from './IconButton'

interface LikeProps {
  isLiked: boolean
  likeCount: number
  tooltipText?: string
  onToggleLike: (newLiked: boolean) => Promise<void> | void
}

const Like: React.FC<LikeProps> = ({ isLiked, likeCount, tooltipText = 'Like', onToggleLike }) => {
  const [liked, setLiked] = useState(isLiked)
  const [count, setCount] = useState(likeCount)
  const debounceRef = useRef<NodeJS.Timeout | null>(null)
  const committedLikedRef = useRef(isLiked)
  const committedCountRef = useRef(likeCount)

  useEffect(() => {
    setLiked(isLiked)
    setCount(likeCount)
    committedLikedRef.current = isLiked
    committedCountRef.current = likeCount
  }, [isLiked, likeCount])

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()

    const prevLiked = liked
    const prevCount = count
    const newLiked = !liked
    const newCount = newLiked ? count + 1 : Math.max(count - 1, 0)

    setLiked(newLiked)
    setCount(newCount)

    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    debounceRef.current = setTimeout(() => {
      if (newLiked !== committedLikedRef.current) {
        try {
          onToggleLike(newLiked)
          committedLikedRef.current = newLiked
          committedCountRef.current = newCount
        } catch (error) {
          // Rollback UI state
          setLiked(prevLiked)
          setCount(prevCount)
        }
      }
    }, 500)
  }

  return (
    <div className="flex items-center">
      <Tooltip text={tooltipText} direction="bottom">
        <IconButton onClick={handleClick}>
          <HeartIcon
            size={19}
            weight={liked ? 'fill' : 'bold'}
            className={`${
              liked
                ? 'animate-pop text-red-500'
                : 'animate-shrink text-lucres-gray-700 dark:text-dark-lucres-green-100'
            }`}
          />
        </IconButton>
      </Tooltip>
      <span className="text-lucres-gray-700 dark:text-dark-lucres-green-100">{count}</span>
    </div>
  )
}

export default React.memo(Like)
