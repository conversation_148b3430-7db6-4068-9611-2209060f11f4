'use client'

import React, { useEffect, useRef, useState } from 'react'
import { useAuth } from '../context/AuthContext'
// import { useUser } from "../context/UserProvider";
import Logo from './Logo'
import Tooltip from './Tooltip'
import IconButton from './IconButton'
import {
  ArrowsCounterClockwiseIcon,
  BellIcon,
  BriefcaseIcon,
  CaretDownIcon,
  CheckIcon,
  CheckCircleIcon,
  GearIcon,
  HouseIcon,
  ListIcon,
  MagnifyingGlassIcon,
  PencilCircleIcon,
  PlusIcon,
  SignInIcon,
  SignOutIcon,
  UserIcon,
  UsersThreeIcon,
  WarningIcon,
  UserPlusIcon,
} from '@phosphor-icons/react'
import SearchPage from './SearchPage'
import ProfileMenu from './ProfileMenu'
import { useRouter, usePathname } from 'next/navigation'
import Link from 'next/link'
import ThemeToggle from './ThemeToggle'
import { useTheme } from '../context/ThemeProvider'
import SearchBar from './SearchBar'
import Overlay from './Overlay'
import { capitalizeWords } from '../utils/commonUtils'
import { useAccount } from '../context/UserDetailsContext'
import Button from './Button'
import BottomNavigation from './BottomNavigation'
import Avatar from './Avatar/Avatar'

export interface LinkObj {
  Id: number
  Title: string
  Link: string
  IconPath: React.JSX.Element
}

const NavigationBar = () => {
  const {
    isAuthenticated,
    logout,
    authUserProfile: userProfile,
    unreadNotificationCount,
  } = useAuth()
  const { contactDetails, fetchUserData } = useAccount()
  const [open, setOpen] = useState<boolean>(false)
  const [activeMenu, setActiveMenu] = useState(false)
  const [activeLink, setActiveLink] = useState<number | null>(1)
  const pathname = usePathname()
  const router = useRouter()
  const [isDropdownOpen, setDropdownOpen] = useState(false)
  const [searchPage, setSearchPage] = useState(false)
  const { theme } = useTheme()
  const dropdownRef = useRef<HTMLDivElement | null>(null)
  const [switchAccount, setSwitchAccount] = useState<boolean>(false)
  const [showModal, setShowModal] = useState<boolean>(false)

  const toggleDropdown = () => {
    setDropdownOpen((prev) => !prev)
  }

  const handleMenu = () => {
    setActiveMenu(!activeMenu)
    setTimeout(() => setOpen(!open), 300)
  }

  const links: LinkObj[] = [
    {
      Id: 1,
      Title: 'Jobs',
      Link: '/jobs',
      IconPath: <BriefcaseIcon size={26} />,
    },
    {
      Id: 2,
      Title: 'Talent',
      Link: '/talent',
      IconPath: <UsersThreeIcon size={26} />,
    },
    {
      Id: 3,
      Title: 'Sign In',
      Link: '/sign-in',
      IconPath: <SignInIcon size={26} />,
    },
    {
      Id: 4,
      Title: 'Create Account',
      Link: '/sign-up',
      IconPath: <UserPlusIcon size={26} />,
    },
  ]

  const DashboardLinks: LinkObj[] = [
    { Id: 1, Title: 'Home', Link: '/feed', IconPath: <HouseIcon size={26} /> },
    {
      Id: 2,
      Title: 'Jobs',
      Link: '/jobs',
      IconPath: <BriefcaseIcon size={26} />,
    },
    {
      Id: 3,
      Title: 'Talent',
      Link: '/talent',
      IconPath: <UsersThreeIcon size={26} />,
    },
    {
      Id: 4,
      Title: 'Alerts',
      Link: '/alert',
      IconPath: <BellIcon size={26} />,
    },
  ]

  useEffect(() => {
    fetchUserData()
  }, [])

  const handlePostJobNavigation = async () => {
    if (isAuthenticated) {
      router.push('/post-job')
    } else {
      router.push('/sign-in')
      return
    }
    if (!contactDetails.primaryPhone) {
      setShowModal(true)
      return
    }
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  useEffect(() => {
    if (activeMenu) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }
  }, [activeMenu])

  useEffect(() => {
    const active = DashboardLinks.find((link) => link.Link === pathname)
    setActiveLink(active ? active.Id : null)
  }, [pathname])

  const handleLogout = () => {
    logout()
    router.push('/')
  }

  const handleActiveLink = (id: number) => {
    setActiveLink(id)
  }

  const profilePagesSet = new Set<string>([
    `/${userProfile?.username}`,
    '/wallet',
    '/personal-information',
    '/orders',
  ])

  if (pathname === '/sign-in' || pathname === '/sign-up') {
    return null
  }

  const name = userProfile ? `${userProfile.givenName}` : 'Guest'
  const userLocation = userProfile?.location?.address
    ? `${userProfile.location.address.city}, ${userProfile.location.address.country}`
    : 'Unknown'

  return (
    <>
      <div className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 fixed left-0 top-0 z-50 w-full border-b bg-white">
        <div
          className={`z-30 flex h-16 w-full items-center justify-center transition-transform duration-700`}
        >
          <div
            className={`dark:bg-dark-lucres-black-500 dark:text-lucres-50 flex h-full w-full max-w-[1440px] items-center justify-between bg-white px-4 lg:gap-36 lg:px-10 xl:rounded-b-2xl`}
          >
            {theme === 'dark' ? (
              <div>
                <Logo
                  width={103}
                  height={20.1}
                  src="/common/whitelogo.svg"
                  className="hidden md:block"
                />
                <Logo width={85} height={20.1} src="/common/whitelogo2.svg" className="md:hidden" />
              </div>
            ) : (
              <div>
                <Logo
                  width={103}
                  height={20.1}
                  src="/common/logo2.svg"
                  className="hidden md:block"
                />
                <Logo width={85} height={20.1} src="/common/logo3.svg" className="md:hidden" />
              </div>
            )}

            <div className="flex flex-1 gap-10">
              <div className={`hidden w-fit flex-1 items-center justify-end gap-x-4 lg:flex`}>
                {isAuthenticated ? (
                  <div className="flex w-full flex-1 items-center justify-end gap-x-5">
                    <SearchBar setSearchPage={setSearchPage} searchPage={searchPage} />
                    <div className="flex gap-2">
                      {DashboardLinks.map((link) => (
                        <Link href={link.Link} key={link.Id} prefetch={false}>
                          <span className="hidden lg:flex">
                            <Tooltip
                              text={link.Title}
                              classes="whitespace-nowrap text-center"
                              direction="bottom"
                            >
                              <IconButton onClick={() => handleActiveLink(link.Id)}>
                                <div
                                  className="group relative flex cursor-pointer flex-col items-center justify-center text-base font-medium md:text-sm"
                                  style={{
                                    color:
                                      activeLink === link.Id
                                        ? theme === 'dark'
                                          ? '#B6E777'
                                          : '#3F8D51'
                                        : theme === 'dark'
                                          ? '#F2F7F3'
                                          : '#2D4232',
                                  }}
                                >
                                  {React.cloneElement(link.IconPath, {
                                    weight: activeLink === link.Id ? 'duotone' : 'regular',
                                  })}
                                  {link.Id === 4 && unreadNotificationCount > 0 && (
                                    <div className="absolute -top-1 left-3 flex min-h-4 min-w-4 items-center justify-center rounded-full bg-red-500 px-1 text-xs text-white">
                                      {unreadNotificationCount > 99
                                        ? '99+'
                                        : unreadNotificationCount}
                                    </div>
                                  )}
                                </div>
                              </IconButton>
                            </Tooltip>
                          </span>
                        </Link>
                      ))}
                    </div>
                  </div>
                ) : (
                  links.map((link) => (
                    <div
                      onClick={() => router.push(link.Link)}
                      className="group relative hidden cursor-pointer text-base font-medium md:block md:text-sm"
                      key={link.Id}
                    >
                      {link.Title}
                    </div>
                  ))
                )}
                <div className={`hidden ${isAuthenticated ? 'lg:flex' : 'md:flex'}`}>
                  {isAuthenticated && (
                    <div className="relative mr-2 hidden gap-3 lg:flex" ref={dropdownRef}>
                      <div className="h-10 w-10 overflow-hidden rounded-full">
                        <Avatar
                          src={userProfile?.profileImage}
                          alt={`${userProfile?.givenName}'s Avatar`}
                          size={10}
                          className="cursor-pointer object-cover"
                          onClick={() => router.push(`/${userProfile?.permalink}`)}
                        />
                      </div>
                      <span
                        className="mr-2 flex cursor-pointer items-center whitespace-nowrap"
                        onClick={toggleDropdown}
                      >
                        {capitalizeWords(name)}
                        <CaretDownIcon size={20} className="ml-1" />
                      </span>
                      {isDropdownOpen && (
                        <div className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 absolute left-[52%] top-[105%] z-50 w-60 -translate-x-1/2 transform rounded-md border bg-white">
                          <ul className="flex flex-col gap-y-2 p-3">
                            <Link href={`/${userProfile?.username}`}>
                              <li
                                onClick={() => setDropdownOpen(false)}
                                className="text-lucres-gray-700 dark:text-lucres-green-100 dark:hover:bg-dark-lucres-black-400 flex cursor-pointer items-center gap-x-3 rounded-md p-1 ps-2 font-medium hover:bg-[#f1f1f1]"
                              >
                                <UserIcon size={20} weight="bold" /> Profile
                              </li>
                            </Link>

                            <Link href={'/personal-information'}>
                              <li
                                onClick={() => setDropdownOpen(false)}
                                className="text-lucres-gray-700 dark:text-lucres-green-100 dark:hover:bg-dark-lucres-black-400 flex cursor-pointer items-center gap-x-3 rounded-md p-1 ps-2 font-medium hover:bg-[#f1f1f1]"
                              >
                                <GearIcon size={20} weight="bold" /> Settings
                              </li>
                            </Link>
                            <hr className="dark:border-dark-lucres-black-200" />

                            <li
                              // onClick={() => {
                              //   setDropdownOpen(false)
                              //   setSwitchAccount(true)
                              // }}
                              className="text-lucres-gray-700/40 dark:text-lucres-green-100/40 dark:hover:bg-dark-lucres-black-400 flex  cursor-default  items-center gap-x-3 rounded-md p-1 ps-2 font-medium hover:bg-[#f1f1f1]"
                            >
                              <ArrowsCounterClockwiseIcon size={20} weight="bold" />
                              Switch Account
                            </li>

                            {/* <Link href={'/company'}> */}
                            <li
                              // onClick={() => setDropdownOpen(false)}
                              className="text-lucres-gray-700/40 dark:text-lucres-green-100/40 dark:hover:bg-dark-lucres-black-400 flex  cursor-default  items-center gap-x-3 whitespace-nowrap rounded-md p-1 ps-2 font-medium hover:bg-[#f1f1f1]"
                            >
                              <PlusIcon size={20} weight="bold" /> Create Company Page
                            </li>
                            {/* </Link> */}
                            <hr className="dark:border-dark-lucres-black-200" />
                            <li
                              className="dark:text-lucres-green-100 dark:hover:bg-dark-lucres-black-400 flex cursor-pointer items-center gap-x-3 rounded-md p-1 ps-2 font-medium text-red-500 hover:bg-[#f1f1f1]"
                              onClick={() => {
                                setDropdownOpen(false)
                                handleLogout()
                              }}
                            >
                              <SignOutIcon size={20} weight="bold" />
                              Logout
                            </li>
                          </ul>
                        </div>
                      )}
                    </div>
                  )}
                  <Button size={'small'} onClick={handlePostJobNavigation} className="">
                    <span className="mr-1">
                      <PlusIcon weight="bold" size={16} />
                    </span>
                    Create a Job
                  </Button>
                </div>
              </div>
              <ThemeToggle classname="!hidden lg:!inline-flex" />
            </div>

            <div className={`${isAuthenticated ? 'lg:hidden' : 'lg:hidden'}`}>
              {!isAuthenticated && <ListIcon size={26} onClick={handleMenu} />}
            </div>
            {isAuthenticated && (
              <div className="flex cursor-pointer items-center gap-2 lg:hidden">
                <MagnifyingGlassIcon size={26} onClick={() => setSearchPage(!searchPage)} />
                <div>
                  {pathname && profilePagesSet.has(pathname) && (
                    <ListIcon size={26} onClick={handleMenu} />
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
        {activeMenu && (
          <div
            className={`dark:bg-dark-lucres-black-500 absolute right-0 top-0 z-50 min-h-screen w-11/12 transform overflow-hidden bg-white px-4 transition-all duration-700 ${
              open ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
            }`}
          >
            <ProfileMenu
              isAuthenticated={isAuthenticated}
              handleLogout={handleLogout}
              handleMenu={handleMenu}
              handlePostJobNavigation={handlePostJobNavigation}
              links={links}
              isOpen={activeMenu}
            />
          </div>
        )}
        {!open && <BottomNavigation />}
        {searchPage && (
          <div className="dark:bg-dark-lucres-black-300/40 z-50! w-full! absolute left-0 top-0 h-screen bg-black/80">
            <SearchPage
              onClick={() => setSearchPage(false)}
              searchPage={searchPage}
              setSearchPage={setSearchPage}
            />
          </div>
        )}
      </div>
      {switchAccount && (
        <Overlay
          handleClose={() => setSwitchAccount(false)}
          heading="Switch Account"
          size="w-84! min-h-fit!"
        >
          <div className="dark:border-b-dark-lucres-black-200 flex w-full items-center justify-between border-b pb-3">
            <div className="flex items-center gap-x-2">
              <Avatar
                src={userProfile?.profileImage}
                alt={`${userProfile?.givenName}'s Avatar`}
                size={12}
                className="cursor-pointer object-cover"
              />
              <div className="flex flex-col">
                <div className="flex items-center gap-x-2">
                  <span className="text-lucres-black dark:text-dark-lucres-green-100 font-semibold">
                    {capitalizeWords(name)}
                  </span>
                  <span
                    className={`bg-linear-to-b flex h-4 w-4 items-center justify-center rounded-full from-[#FBBC05] to-[#e8c049]`}
                  >
                    <CheckIcon size={10} className="text-white" weight="bold" />
                  </span>
                </div>
                <span className="text-lucres-gray-700 dark:text-lucres-gray-400 text-sm">
                  {userLocation}
                </span>
              </div>
            </div>
            <CheckCircleIcon size={24} className="text-lucres-600" />
          </div>
          <Button
            size="small"
            theme="translucent"
            isRectangle
            className="px-3! py-1.5! mx-auto w-fit"
          >
            <PlusIcon weight="bold" className="me-2" /> Add New Account
          </Button>
        </Overlay>
      )}
      {showModal && (
        <Overlay
          heading={'Verify you Phone'}
          handleClose={() => setShowModal(false)}
          size="min-h-fit mt-20"
          classes="p-0!"
        >
          <div className="dark:bg-dark-lucres-black-500 w-full rounded-lg bg-white p-4 shadow-lg">
            <div className="flex flex-col items-center">
              <WarningIcon size={32} className="text-yellow-500" />
              <p
                className={`'text-lucres-800 dark:text-dark-lucres-black-100 mt-3 text-center text-sm`}
              >
                <>
                  You need to verify your Phone Number to
                  <br />
                  Post a Job
                </>
              </p>
            </div>
            <div className="dark:border-t-dark-lucres-black-200 mt-6 flex justify-between gap-4 border-t pt-4">
              <Button
                size="small"
                theme="transparent"
                className="px-4! py-1.5! !border"
                onClick={() => setShowModal(false)}
              >
                Cancel
              </Button>
              <Button
                size="small"
                theme="dark"
                className="px-4! py-1.5! text-white!"
                onClick={() => {
                  router.push('/personal-information')
                  setShowModal(false)
                }}
              >
                Update Phone
              </Button>
            </div>
          </div>
        </Overlay>
      )}
    </>
  )
}

export default NavigationBar
