import { MagnifyingGlassIcon } from '@phosphor-icons/react'
import { usePathname } from 'next/navigation'

interface SearchBarProps {
  setSearchPage?: (value: boolean) => void
  searchPage?: boolean
}

const SearchBar: React.FC<SearchBarProps> = ({ setSearchPage, searchPage }) => {
  const pathname = usePathname()
  return (
    <div
      className={`dark:border-dark-lucres-black-200 hidden flex-1 items-center justify-between rounded-full border py-2 pl-1 pr-3 lg:flex`}
      onClick={() => {
        if (setSearchPage && typeof searchPage === 'boolean') {
          setSearchPage(!searchPage)
        }
      }}
    >
      <div
        // type="text"
        // placeholder={
        //   location.pathname === '/talent'
        //     ? 'Search Talent'
        //     : location.pathname === '/jobs'
        //       ? 'Search Jobs'
        //       : 'Search'
        // }
        className={`text-lucres-gray-800 outline-hidden w-full grow cursor-text whitespace-nowrap bg-transparent lg:px-4`}
      >
        {pathname === '/talent' ? 'Search Talent' : pathname === '/jobs' ? 'Search Jobs' : 'Search'}
      </div>
      <div className="">
        <MagnifyingGlassIcon size={20} />
      </div>
    </div>
  )
}

export default SearchBar
