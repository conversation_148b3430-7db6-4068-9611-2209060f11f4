// BookmarkIcon.tsx
import React, { useEffect, useRef, useState } from 'react'
import Tooltip from './Tooltip'
import IconButton from './IconButton'
import { BookmarkSimple, BookmarkSimpleIcon } from '@phosphor-icons/react'
import { usePathname, useRouter } from 'next/navigation'
import { useAuth } from '../context/AuthContext'

interface BookmarkIconProps {
  isBookmarked?: boolean
  onBookmarkToggle: (newBookmarked: boolean, jobId?: string) => Promise<void>
  tooltipText?: string
  jobId?: string
}

const BookmarkIcon: React.FC<BookmarkIconProps> = ({
  onBookmarkToggle,
  isBookmarked = false,
  tooltipText = 'Save',
  jobId,
}) => {
  const pathname = usePathname()
  const router = useRouter()
  const [bookmarked, setBookmarked] = useState(isBookmarked)
  const debounceRef = useRef<NodeJS.Timeout | null>(null)
  const committedBookmarkedRef = useRef(isBookmarked)
  const { isAuthenticated } = useAuth()

  useEffect(() => {
    setBookmarked(isBookmarked)
    committedBookmarkedRef.current = isBookmarked
  }, [isBookmarked])

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()
    if (!isAuthenticated) {
      router.push('/sign-in')
      return
    }

    const prevBookmarked = bookmarked
    const newBookmarked = !bookmarked

    setBookmarked(newBookmarked)

    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    debounceRef.current = setTimeout(() => {
      if (newBookmarked !== committedBookmarkedRef.current) {
        try {
          onBookmarkToggle(newBookmarked, jobId)
          committedBookmarkedRef.current = newBookmarked
        } catch (error) {
          // Rollback UI state
          setBookmarked(prevBookmarked)
        }
      }
    }, 500)
  }

  return (
    <div className="flex items-center gap-x-2">
      <Tooltip
        text={pathname.includes('talent') ? 'Save Talent' : tooltipText}
        classes="whitespace-nowrap text-center"
        direction="bottom"
      >
        <IconButton onClick={handleClick}>
          <BookmarkSimpleIcon
            size={19}
            weight={bookmarked ? 'fill' : 'bold'}
            className={`text-lucres-gray-700 dark:text-dark-lucres-green-100 font-bold ${
              bookmarked ? 'animate-pop' : 'animate-shrink'
            }`}
          />
        </IconButton>
      </Tooltip>
    </div>
  )
}

export default React.memo(BookmarkIcon)
