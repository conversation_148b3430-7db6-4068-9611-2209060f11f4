import { Moon, MoonIcon, Sun, SunIcon } from '@phosphor-icons/react'
import { useTheme } from '../context/ThemeProvider'

const ThemeToggleButton = ({ classname = '' }) => {
  const { theme, setTheme } = useTheme()

  const toggleTheme = ({}) => {
    setTheme(theme === 'dark' ? 'default' : 'dark')
  }

  return (
    <button
      type="button"
      className={`text-lucres-black lg:text-lucres-900 inline-flex w-fit items-center justify-center gap-x-3 rounded-full border border-transparent text-sm font-medium dark:text-white ${classname}`}
      onClick={toggleTheme}
    >
      {theme === 'dark' ? <SunIcon size={25} /> : <MoonIcon size={25} />}
      <span className="text-base lg:hidden">{theme === 'dark' ? 'Light' : 'Dark'} </span>
    </button>
  )
}

export default ThemeToggleButton
