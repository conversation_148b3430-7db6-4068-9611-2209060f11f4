import { X, CheckCircle, Info, WarningCircle, Spinner } from '@phosphor-icons/react'
import { convertToTitleCase } from '../utils/commonUtils'

export interface ToastData {
  id: number
  type: 'success' | 'info' | 'loading' | 'error'
  message: string
  timeout?: number
}

export interface ToastProps {
  toasts: ToastData[]
  position?:
    | 'top-right'
    | 'top-left'
    | 'top-center'
    | 'bottom-right'
    | 'bottom-left'
    | 'bottom-center'
  removeToast: (id: number) => void
}

const Toast = ({ toasts, position = 'bottom-center', removeToast }: ToastProps) => {
  return (
    <div
      className={`pointer-events-none fixed z-50 flex flex-col gap-2 ${
        position.includes('top') ? 'top-5' : 'bottom-5'
      } ${
        position.includes('right')
          ? 'right-5'
          : position.includes('left')
            ? 'left-5'
            : 'left-1/2 -translate-x-1/2 transform'
      }`}
    >
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className="pointer-events-auto relative flex min-w-64 max-w-sm items-center justify-between rounded-sm bg-white p-3 shadow-lg dark:bg-gray-800"
        >
          <div className="flex items-center gap-2">
            {toast.type === 'success' && (
              <CheckCircle size={20} className="text-green-600" weight="fill" />
            )}
            {toast.type === 'info' && <Info size={20} className="text-blue-500" weight="fill" />}
            {toast.type === 'error' && (
              <WarningCircle size={20} className="text-[#FF2C2C]" weight="fill" />
            )}
            {toast.type === 'loading' && <Spinner size={20} className="animate-spin" />}
            <span>{convertToTitleCase(toast.message)}</span>
          </div>
          <button onClick={() => removeToast(toast.id)} className="ml-3">
            <X size={20} />
          </button>

          {/* Progress Bar */}
          {/* <div className="h-1 absolute bottom-0 left-0 w-full bg-white/30">
            <div
              className="h-1 transition-all bg-blue-500"
              style={{ width: `${progressMap[toast.id] }%` }}
            ></div>
          </div> */}
        </div>
      ))}
    </div>
  )
}

export default Toast
