// Define the props type for the Radio component
interface RadioProps {
  value: string
  label: string
  selected: string
  setSelected: (value: string) => void
  name: string
  classname?: string
  labelClassName?: string
}
const Radio: React.FC<RadioProps> = ({
  value,
  label,
  selected,
  setSelected,
  name,
  classname,
  labelClassName,
}) => {
  return (
    <div>
      <label key={value} className="flex cursor-pointer items-center space-x-2">
        <input
          type="radio"
          name={name}
          value={value}
          checked={selected === value}
          onChange={(e) => setSelected(e.target.value)}
          className={`form-radio hidden ${labelClassName}`}
        />
        {/* <span
                    className={`px-6 py-2 rounded-lg border text-xs font-semibold ${
                      selected === value
                        ? `bg-lime-300 dark:bg-dark-lucres-green-100 dark:hover:bg-dark-lucres-green-100 dark:border-dark-lucres-black-200 border border-lime-300 dark:text-dark-lucres-black-500  bg-opacity-30 hover:text-black dark:hover:text-dark-lucres-green-300 hover:bg-opacity-45`
                        : `bg-gray-100 dark:bg-dark-lucres-black-300 dark:border-dark-lucres-black-200 dark:text-dark-lucres-green-100 hover:bg-lucres-200 dark:hover:bg-dark-lucres-green-100 dark:hover:text-dark-lucres-green-300 whitespace-nowrap`
                    }`}
                  >
                    {label}
                  </span> */}
        <span
          className={`rounded-lg border px-6 py-2 text-xs font-semibold ${
            selected === value
              ? `dark:border-dark-lucres-black-200 dark:text-dark-lucres-black-500 dark:hover:text-dark-lucres-black-400 border border-lime-300 bg-lime-300 bg-opacity-30 hover:bg-opacity-45 hover:text-black dark:bg-white dark:hover:bg-opacity-75`
              : `hover:bg-lucres-200 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-300 dark:text-dark-lucres-green-100 dark:hover:bg-dark-lucres-black-200 whitespace-nowrap bg-gray-100 dark:hover:text-white`
          } ${classname} ${labelClassName}`}
        >
          {label}
        </span>
      </label>
    </div>
  )
}
export default Radio
