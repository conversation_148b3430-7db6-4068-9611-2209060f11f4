'use client'
import React, { useEffect, useRef } from 'react'
import { X } from '@phosphor-icons/react'
import IconButton from './IconButton'

interface OverlayProps {
  handleClose: () => void // Function to handle closing the modal
  heading: string // The heading of the modal
  children: React.ReactNode // The dynamic content of the modal
  classes?: string
  size?: string
  crossRequired?: boolean
  closeOnOutsideClick?: boolean // New prop
}

const Overlay: React.FC<OverlayProps> = ({
  handleClose,
  heading,
  children,
  classes,
  size,
  crossRequired = true,
  closeOnOutsideClick = true, // Default to true
}) => {
  const modalRef = useRef<HTMLDivElement>(null)
  useEffect(() => {
    // Disable body scroll on mount
    document.body.style.overflow = 'hidden'

    // Enable body scroll on unmount
    return () => {
      document.body.style.overflow = ''
    }
  }, [])

  // Close modal when clicking outside
  const handleClickOutside = (event: React.MouseEvent<HTMLDivElement>) => {
    event.stopPropagation()
    if (!closeOnOutsideClick) return // Only close if allowed
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      handleClose()
    }
  }

  return (
    <div
      className="bg-dark-lucres-black-300/80 fixed inset-0 z-50 flex items-start justify-center pt-32"
      onClick={handleClickOutside}
    >
      <div
        ref={modalRef}
        className={`dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 flex min-h-96 w-11/12 max-w-md flex-col rounded-lg border bg-white ${size}`}
      >
        <div className="dark:bg-dark-lucres-black-500 flex w-full items-center justify-between rounded-t-lg bg-[#ECFFD5] p-3 px-6">
          <h2 className="font-medium">{heading}</h2>
          {crossRequired && (
            <IconButton onClick={handleClose}>
              <X
                size={20}
                weight="bold"
                className="dark:text-dark-lucres-green-100 cursor-pointer"
              />
            </IconButton>
          )}
        </div>

        <div className={`flex flex-col gap-4 p-4 px-6 ${classes}`}>{children}</div>
      </div>
    </div>
  )
}

export default Overlay
