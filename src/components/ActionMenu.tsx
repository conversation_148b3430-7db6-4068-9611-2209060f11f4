'use client'
import React, { useState, useRef, useEffect, ReactNode } from 'react'

interface MenuItem {
  label: string
  icon?: ReactNode
  onClick: (close: () => void, e: React.MouseEvent) => void
  className?: string
}

interface ActionMenuProps {
  trigger: ReactNode
  menuItems: MenuItem[]
  classes?: string
}

const ActionMenu: React.FC<ActionMenuProps> = ({ trigger, menuItems, classes }) => {
  const [showMenu, setShowMenu] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)

  const closeMenu = () => setShowMenu(false)

  const toggleMenu = (e: React.MouseEvent) => {
    e.stopPropagation()
    setShowMenu((prev) => !prev)
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        closeMenu()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className="relative" ref={menuRef}>
      <div onClick={toggleMenu} className={`${menuItems.length > 0 && 'cursor-pointer'} `}>
        {trigger}
      </div>

      {showMenu && menuItems.length > 0 && (
        <div
          className={`dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 absolute right-2 z-50 rounded-2xl border bg-white px-1 py-2 shadow-lg ${classes}`}
        >
          <ul className="text-lucres-gray-700 dark:text-dark-lucres-green-100 w-full whitespace-nowrap text-sm">
            {menuItems.map((item, idx) => (
              <li
                key={idx}
                onClick={(e) => {
                  e.stopPropagation()
                  item.onClick(closeMenu, e)
                }}
                className={`dark:hover:bg-dark-lucres-black-400 flex cursor-pointer items-center gap-3 rounded-xl px-4 py-2 font-medium hover:bg-gray-100 ${
                  item.className || ''
                }`}
              >
                {item.icon}
                {item.label}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}

export default ActionMenu
