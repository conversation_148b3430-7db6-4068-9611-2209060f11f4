'use client'
import { CheckCircleIcon, CheckIcon, DotIcon } from '@phosphor-icons/react'
import { useEffect, useMemo, useState } from 'react'
import { useRouter, usePathname, useParams } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '../context/AuthContext'
import { convertToTitleCase, truncateText } from '../utils/commonUtils'
import Avatar from './Avatar/Avatar'

function SidebarN({ data }: { data?: any }) {
  const router = useRouter()
  const pathname = usePathname()
  const params = useParams()
  const username = params.username as string
  const { authUserProfile: userProfile } = useAuth()
  const profileData = useMemo(() => {
    return username && username !== userProfile?.username
      ? [
          {
            name:
              data?.givenName && data?.familyName
                ? `${data.givenName} ${data.familyName}`
                : 'Guest User',
            location: data?.location?.address
              ? `${data.location.address.city}, ${data.location.address.country}`
              : '',
            avatar: data?.profileImage?.url || null,
            id: data?.username ? `@${data.username}` : '@guest',
            tag: data?.headline || 'Keep it Real.',
            link: `/${data?.permalink}`,
            stats: [
              { label: 'Posts', value: data?.postsCount || 0, href: './posts' },
              {
                label: 'Followers',
                value: data?.followersCount || 0,
                href: `/${data?.username}/followers?page=1&limit=15`,
              },
              {
                label: 'Following',
                value: data?.followingCount || 0,
                href: `/${data?.username}/following?page=1&limit=15`,
              },
            ],
          },
        ]
      : [
          {
            name: userProfile ? `${userProfile.givenName} ${userProfile.familyName}` : 'Guest User',
            location: userProfile?.location?.address
              ? `${userProfile.location.address.city}, ${userProfile.location.address.country}`
              : '',
            avatar: userProfile?.profileImage?.url || null,
            id: userProfile ? `@${userProfile.username}` : '@guest',
            tag: userProfile?.headline || 'Keep it Real.',
            link: `/${userProfile?.permalink}`,
            stats: [
              {
                label: 'Posts',
                value: userProfile?.postsCount || 0,
                href: `/${userProfile?.username}`,
              },
              {
                label: 'Followers',
                value: userProfile?.followersCount || 0,
                href: `/${userProfile?.username}/followers?page=1&limit=15`,
              },
              {
                label: 'Following',
                value: userProfile?.followingCount || 0,
                href: `/${userProfile?.username}/following?page=1&limit=15`,
              },
            ],
          },
          {
            name: 'Acme Corp Ltd',
            location: 'Bangalore, India',
            avatar: '/company/CompanyLogo.svg',
            id: '@acmecorp',
            tag: 'Keep it Real.',
            link: '/companyprofile',
            stats: [
              { label: 'Posts', value: 200, href: './posts' },
              { label: 'Followers', value: 380, href: './followers' },
              { label: 'Following', value: 1003, href: './following' },
            ],
          },
        ]
  }, [userProfile, username, data])

  const [profile, setProfile] = useState(profileData[0])

  useEffect(() => {
    setProfile(profileData[0])
  }, [username, userProfile, profile, profileData, data])

  // useEffect(() => {
  //   if (username)
  //     setProfile({
  //       name: data
  //         ? `${data?.data?.givenName} ${data.familyName}`
  //         : "Guest User",
  //       location: data
  //         ? `${data?.location?.address?.city}, ${data?.location?.address?.country}`
  //         : "Unknown",
  //       // Use the profile image if available, otherwise null (to trigger default images)
  //       avatar: data && data.profileImage?.url ? data.profileImage.url : null,
  //       id: data ? `@${data.username}` : "@guest",
  //       tag: data ? data.headline : "Keep it Real.",
  //       link: `/${data?.permalink}`,
  //       stats: [
  //         { label: "Posts", value: 18, href: "./posts" },
  //         { label: "Followers", value: 230, href: "./follow" },
  //         { label: "Following", value: 127, href: "./follow" },
  //       ],
  //     });
  // });

  // Update profile on location pathname or userProfile change
  useEffect(() => {
    if (pathname === '/companyprofile' || pathname === '/viewcompanyprofile') {
      setProfile(profileData[1])
    } else {
      setProfile(profileData[0])
    }
  }, [pathname, userProfile])

  return (
    <div
      className={`md:dark:border-r-dark-lucres-black-300 z-1 fixed top-20 mt-2 flex w-60 flex-col items-start justify-start pb-10 pe-6 md:h-5/6 md:pt-5`}
    >
      {/* Profile Section */}
      <div className="block w-full justify-between pl-2 pr-6">
        <div className="flex flex-col items-start gap-x-4">
          <Avatar
            src={profile.avatar}
            alt={`${profile.name}'s Avatar`}
            size={25}
            {...(!username || pathname !== `/${username}`
              ? { onClick: () => router.push(profile.link) }
              : {})}
            className="w-28 cursor-pointer object-cover"
          />
          <div className="relative w-56">
            <h2 className="text-lucres-black mt-4 flex items-center gap-1.5  text-xl  font-medium dark:text-white">
              {truncateText(convertToTitleCase(profile.name), 15).text}
              <div>
                {data?.isUserVerified && (
                  <span className="bg-linear-to-b flex min-h-4 min-w-4 items-center justify-center rounded-full  from-[#FBBC05] to-[#9570035a]">
                    <CheckIcon size={10} className="text-white" weight="bold" />
                  </span>
                )}
              </div>
            </h2>

            {/* {showAccounts ? (
                <CaretUp
                  size={20}
                  className={`cursor-pointer ${username !== userProfile?.username ? 'hidden' : ''}`}
                  onClick={() => setShowAccounts(!showAccounts)}
                />
              ) : (
                <CaretDown
                  size={20}
                  className={`cursor-pointer ${
                    username && username !== userProfile?.username ? 'hidden' : ''
                  }`}
                  onClick={() => setShowAccounts(!showAccounts)}
                />
              )} */}
            <p className="dark:text-dark-lucres-black-100 mb-2 flex items-center text-sm font-medium text-gray-600 opacity-90">
              {profile.id}
              {profile.location !== '' && <DotIcon size={20} weight="bold" />}
              {truncateText(profile.location, 10).text}
            </p>
            <span className="text-sm font-medium">{profile.tag}</span>
            {/* {showAccounts && (
              <div className="absolute -left-0.5 top-12 h-fit w-60 rounded-lg border bg-white p-2 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500">
                {profileData.map((user, index) => (
                  <div
                    key={index}
                    className="flex cursor-pointer items-start gap-x-2 border-b p-2 dark:border-b-dark-lucres-black-200"
                    onClick={() => router.push(user.link)}
                  >
                    <Avatar
                      src={user.avatar}
                      alt={`${user.name}'s Avatar`}
                      size={10}
                      className="h-auto"
                    />
                    <div className="flex flex-col">
                    
                      <span className="text-sm font-medium text-lucres-800">{user.location}</span>
                    </div>
                  </div>
                ))}
                <div className="mt-4 flex w-full justify-center">
                  <Button size="small" theme="translucent">
                    <Plus className="me-1" weight="bold" />
                    Add New Account
                  </Button>
                </div>
              </div>
            )} */}
          </div>
        </div>
        <div className="text-lucres-black dark:text-lucres-green-100 mt-4 flex gap-0 space-x-4">
          {profile.stats.map((stat) => (
            <Link key={stat.label} href={stat.href} className="cursor-pointer text-center">
              <p className="text-sm font-semibold">{stat.value}</p>
              <p className="text-sm font-medium">{stat.label}</p>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}

export default SidebarN
