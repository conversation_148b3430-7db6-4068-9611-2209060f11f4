'use client'
import React, { useState, useEffect, useRef, ReactNode } from 'react'
import { useTheme } from '../context/ThemeProvider'
import { CaretDown, CaretUp, X } from '@phosphor-icons/react'
import { truncateText } from '../utils/commonUtils'

interface Option {
  label: string
  value: string
  icon?: ReactNode
}

interface CustomSelectProps {
  options?: Option[]
  value?: string[]
  onChange: (values: string[]) => void
  placeholder?: string
  label?: string
  required?: boolean
  disabled?: boolean
  parentClassName?: string
  labelClassName?: string
  classes?: string
  type?: 'dropdown' | 'text'
}

const MultiSelect: React.FC<CustomSelectProps> = ({
  options = [],
  value = [],
  onChange,
  placeholder = 'Select options',
  label,
  required,
  disabled,
  parentClassName = '',
  classes = '',
  labelClassName = '',
  type = 'dropdown',
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const selectRef = useRef<HTMLDivElement>(null)
  const { theme: currentTheme } = useTheme()
  const mode = currentTheme === 'dark' ? 'dark' : 'light'

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen((prev) => !prev)
    }
  }

  const handleRemoveOption = (optionValue: string) => {
    onChange(value.filter((val) => val !== optionValue))
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && inputValue.trim()) {
      event.preventDefault()
      if (!value.includes(inputValue)) {
        onChange([...value, inputValue])
      }
      setInputValue('')
    }
  }

  const selectClasses = {
    light: 'border-lucres-300 border-opacity-40 placeholder-lucres-300',
    dark: 'border-dark-lucres-black-200 placeholder-lucres-800',
  }

  const dropdownClasses = {
    light: 'bg-white border-lucres-300',
    dark: 'bg-dark-lucres-black-500 border-dark-lucres-black-300',
  }

  return (
    <div
      className={`relative flex w-full flex-col justify-end gap-2 md:max-w-52 ${parentClassName}`}
      ref={selectRef}
    >
      {label && (
        <label
          className={`text-lucres-900 dark:text-dark-lucres-green-300 text-sm font-semibold text-opacity-75 ${labelClassName}`}
        >
          {label}
          {required && <span className="text-red-400"> *</span>}
        </label>
      )}

      <div
        className={`flex w-full rounded-lg border px-4 py-3 ${
          value.length === 0 ? 'h-10' : 'min-h-10'
        } items-center bg-transparent ${selectClasses[mode]} ${classes} ${
          disabled ? 'pointer-events-none opacity-60' : ''
        }`}
        onClick={toggleDropdown}
      >
        <div
          className={`scrollbar-none flex w-full flex-wrap gap-2 overflow-hidden rounded-sm px-2 ${
            type === 'dropdown' && 'cursor-pointer'
          }`}
        >
          <div className="flex w-full flex-wrap items-start gap-1">
            {value.length > 0 ? (
              value.map((selectedValue) => (
                <div
                  key={selectedValue}
                  className="bg-lucres-200 flex items-center gap-x-1 rounded-md p-0.5 px-2 dark:bg-gray-700"
                >
                  <span className="whitespace-nowrap text-xs">
                    {truncateText(selectedValue, 15).text}
                  </span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleRemoveOption(selectedValue)
                    }}
                  >
                    <X size={10} />
                  </button>
                </div>
              ))
            ) : (
              <span className="text-lucres-800 text-sm">{type === 'dropdown' && placeholder}</span>
            )}
            {type === 'text' && (
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={placeholder}
                className="placeholder:text-lucres-800 focus:outline-hidden w-full min-w-0 flex-1 bg-transparent text-sm md:min-w-10"
              />
            )}
          </div>
        </div>

        {type === 'dropdown' && (
          // <img
          //   src="/dashboard/dropdownIcon.svg"
          //   width={7}
          //   height={6}
          //   alt="dropdown icon"
          //   className="ml-auto cursor-pointer object-cover"
          // />
          <div className="opacity-50">{isOpen ? <CaretUp /> : <CaretDown />}</div>
        )}
      </div>

      {!disabled && isOpen && type === 'dropdown' && (
        <ul
          className={`absolute z-50 mt-1 w-full rounded-lg border shadow-lg ${dropdownClasses[mode]}`}
          style={{ top: '100%', left: 0 }}
        >
          {options.length > 0 ? (
            options.map((option) => (
              <li
                key={option.value}
                className={`dark:border-dark-lucres-black-300 flex items-center gap-x-2 border-b px-3 py-2 text-sm last:border-0 ${
                  value.includes(option.value)
                    ? 'text-gray-500 dark:text-gray-400'
                    : 'text-lucres-gray-700 dark:text-lucres-green-100 dark:hover:bg-dark-lucres-black-400 cursor-pointer hover:bg-[#f1f1f1]'
                }`}
                onClick={(e) => {
                  e.stopPropagation()
                  setIsOpen(false)
                  !value.includes(option.value) && onChange([...value, option.value])
                }}
              >
                {option.icon}
                {option.label}
              </li>
            ))
          ) : (
            <li className="text-lucres-800 px-3 py-2 text-sm">{placeholder}</li>
          )}
        </ul>
      )}
    </div>
  )
}

export default MultiSelect
