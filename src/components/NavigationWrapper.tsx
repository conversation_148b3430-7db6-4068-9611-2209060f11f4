'use client'

import { usePathname } from 'next/navigation'
import NavigationBar from './NavigationBar'
import BottomNavigation from './BottomNavigation'

const HIDE_NAV_ROUTES = ['/login', '/register', '/post-job', '/pricing']

export default function NavigationWrapper() {
  const pathname = usePathname()
  const hideNav = HIDE_NAV_ROUTES.some(
    (route) => pathname === route || pathname.startsWith(route + '/'),
  )

  if (hideNav) return null

  return (
    <>
      <NavigationBar />
      <BottomNavigation />
    </>
  )
}
