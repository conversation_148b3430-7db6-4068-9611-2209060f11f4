'use client'

import React, { useState } from 'react'
import { EyeI<PERSON>, EyeSlashIcon } from '@phosphor-icons/react'
import { useTheme } from '../context/ThemeProvider'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  className?: string
  labelClassName?: string
  componentClass?: string
  error?: string
  theme?: 'transparent' | 'gray'
  icon?: React.ReactNode
  iconClassName?: string
}

const Input: React.FC<InputProps> = ({
  label,
  className = '',
  labelClassName = '',
  componentClass = '',
  error,
  theme = 'transparent',
  icon,
  iconClassName = '',
  type = 'text',
  required,
  disabled,
  ...props
}) => {
  const { theme: currentTheme } = useTheme()
  const mode = currentTheme === 'dark' ? 'dark' : 'light'
  const [showPassword, setShowPassword] = useState(false)

  const inputType = type === 'password' && showPassword ? 'text' : type

  const inputClasses = {
    light: {
      transparent:
        'border border-lucres-300 border-opacity-40 placeholder-lucres-300 bg-transparent',
      gray: 'bg-lucres-gray-100',
    },
    dark: {
      transparent: 'border border-dark-lucres-black-200 placeholder-lucres-800 bg-transparent',
      gray: 'bg-dark-lucres-black-300',
    },
  }

  const errorClasses = {
    light: 'text-red-600',
    dark: 'text-red-400',
  }

  return (
    <div className={`relative mb-5 flex w-full flex-col justify-end gap-2 ${componentClass}`}>
      {label && (
        <label
          className={`text-lucres-900 dark:text-dark-lucres-green-300 whitespace-nowrap text-sm font-semibold text-opacity-75 ${labelClassName}`}
        >
          {label}
          {required && <span className="text-red-400"> *</span>}
        </label>
      )}

      <div className="relative w-full">
        <input
          {...props}
          type={inputType}
          className={`w-full rounded-lg px-4 py-3 placeholder:text-sm focus:outline-none ${inputClasses[mode][theme]} ${className}`}
          required={required}
          disabled={disabled}
        />

        {type === 'password' && (
          <button
            type="button"
            aria-label={showPassword ? 'Hide password' : 'Show password'}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            onClick={() => setShowPassword((prev) => !prev)}
          >
            {showPassword ? <EyeSlashIcon size={20} /> : <EyeIcon size={20} />}
          </button>
        )}

        {icon && type !== 'password' && (
          <div
            className={`absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400 ${iconClassName}`}
          >
            {icon}
          </div>
        )}
      </div>

      {error && <p className={`mt-1 pl-4 text-xs capitalize ${errorClasses[mode]}`}>{error}</p>}
    </div>
  )
}

export default Input
