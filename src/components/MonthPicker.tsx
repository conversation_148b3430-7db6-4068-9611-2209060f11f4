'use client'
import { CaretLeft, CaretRight } from '@phosphor-icons/react'
import {
  add,
  eachMonthOfInterval,
  endOfYear,
  format,
  isEqual,
  isFuture,
  parse,
  startOfMonth,
  startOfToday,
} from 'date-fns'
import { useEffect, useRef, useState } from 'react'

// Utility function to concatenate class names, filtering out falsy values
function cn(...classes: (string | undefined | null | false)[]) {
  return classes.filter(Boolean).join(' ')
}

function getStartOfCurrentMonth() {
  return startOfMonth(startOfToday())
}

interface MonthPickerProps {
  currentMonth: Date
  onMonthChange: (newMonth: Date) => void
  isOpen: boolean
  onClose: () => void
  onOpen?: () => void
}

export default function MonthPicker({
  currentMonth,
  onMonthChange,
  isOpen,
  onClose,
  onOpen,
}: MonthPickerProps) {
  const monthPickerRef = useRef<HTMLDivElement>(null)
  const [isSelectingYear, setIsSelectingYear] = useState(false)
  const [yearRangeStart, setYearRangeStart] = useState(0)
  const minYear = 1900
  const maxYear = parseInt(format(startOfToday(), 'yyyy'))

  // Call onOpen when the component mounts
  useEffect(() => {
    if (onOpen) {
      onOpen()
    }
  }, [onOpen])

  // Handle clicks outside to close the picker
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (monthPickerRef.current && !monthPickerRef.current.contains(event.target as Node)) {
        onClose()
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [onClose])

  if (!isOpen) return null

  const [currentYear, setCurrentYear] = useState(format(currentMonth, 'yyyy'))
  const firstDayCurrentYear = parse(currentYear, 'yyyy', new Date())
  const months = eachMonthOfInterval({
    start: firstDayCurrentYear,
    end: endOfYear(firstDayCurrentYear),
  })

  // Handle navigation based on mode
  function previous() {
    if (isSelectingYear) {
      setYearRangeStart(yearRangeStart - 12)
    } else {
      let firstDayNextYear = add(firstDayCurrentYear, { years: -1 })
      setCurrentYear(format(firstDayNextYear, 'yyyy'))
    }
  }

  function next() {
    if (isSelectingYear) {
      setYearRangeStart(yearRangeStart + 12)
    } else {
      let firstDayNextYear = add(firstDayCurrentYear, { years: 1 })
      setCurrentYear(format(firstDayNextYear, 'yyyy'))
    }
  }

  // Generate 12 years for the grid
  const years = Array.from({ length: 12 }, (_, i) => yearRangeStart + i)

  return (
    <div
      ref={monthPickerRef}
      className="border-lucres-300 font-grotesk dark:border-dark-lucres-black-200 focus:outline-hidden rounded-md border  bg-white p-3 dark:bg-[#181818]"
    >
      <div className="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
        <div className="space-y-4">
          <div className="relative flex items-center justify-center pt-1">
            <button
              className="text-sm font-medium text-black dark:text-white"
              aria-live="polite"
              id="month-picker"
              onClick={() => {
                if (isSelectingYear) {
                  setIsSelectingYear(false)
                } else {
                  setIsSelectingYear(true)
                  setYearRangeStart(Math.max(minYear, parseInt(currentYear) - 5))
                }
              }}
            >
              {isSelectingYear
                ? `${yearRangeStart} - ${yearRangeStart + 11}`
                : format(firstDayCurrentYear, 'yyyy')}
            </button>
            <div className="flex items-center space-x-1">
              <button
                name="previous"
                aria-label="Go to previous"
                className="focus-visible:ring-ring dark:border-dark-lucres-black-200 shadow-xs focus-visible:outline-hidden absolute left-1 inline-flex h-7 w-7 items-center justify-center gap-2 whitespace-nowrap rounded-md border p-0 text-sm font-medium transition-colors hover:bg-slate-100 hover:text-slate-900 focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"
                type="button"
                onClick={previous}
              >
                <CaretLeft className="h-4 w-4" />
              </button>
              <button
                name="next"
                aria-label="Go to next"
                className="focus-visible:ring-ring dark:border-dark-lucres-black-200 shadow-xs focus-visible:outline-hidden absolute right-1 inline-flex h-7 w-7 items-center justify-center gap-2 whitespace-nowrap rounded-md border p-0 text-sm font-medium transition-colors hover:bg-slate-100 hover:text-slate-900 focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"
                type="button"
                disabled={
                  (isSelectingYear && yearRangeStart + 11 >= maxYear) ||
                  (!isSelectingYear && parseInt(currentYear) >= maxYear)
                }
                onClick={next}
              >
                <CaretRight size={16} />
              </button>
            </div>
          </div>
          <div
            className="grid w-full grid-cols-4 gap-2 font-medium"
            role="grid"
            aria-labelledby="month-picker"
          >
            {isSelectingYear
              ? years.map((year) => (
                  <div
                    key={year}
                    className="relative p-0 text-center text-sm font-medium text-black focus-within:relative focus-within:z-20 dark:text-white"
                  >
                    <button
                      className={cn(
                        'hover:bg-lucres-100 dark:hover:bg-dark-lucres-black-300 focus-visible:outline-hidden inline-flex h-9 w-16 items-center justify-center rounded-md p-0 text-sm font-normal ring-offset-white transition-colors focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:ring-offset-slate-950 dark:hover:text-slate-50 dark:focus-visible:ring-slate-800',
                        year === parseInt(format(currentMonth, 'yyyy')) &&
                          'bg-slate-900 text-slate-50 hover:bg-slate-900 hover:text-slate-50 focus:bg-slate-900 focus:text-slate-50 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50 dark:hover:text-slate-900 dark:focus:bg-slate-50 dark:focus:text-slate-900',
                      )}
                      disabled={year > maxYear}
                      onClick={() => {
                        setCurrentYear(year.toString())
                        setIsSelectingYear(false)
                      }}
                    >
                      {year}
                    </button>
                  </div>
                ))
              : months.map((month) => (
                  <div
                    key={month.toString()}
                    className="relative p-0 text-center text-sm font-medium text-black focus-within:relative focus-within:z-20 dark:text-white [&:has([aria-selected])]:bg-slate-100 first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md dark:[&:has([aria-selected])]:bg-slate-800"
                    role="presentation"
                  >
                    <button
                      name="day"
                      className={cn(
                        'hover:bg-lucres-100 dark:hover:bg-dark-lucres-black-300 focus-visible:outline-hidden inline-flex h-9 w-16 items-center justify-center rounded-md p-0 text-sm font-normal ring-offset-white transition-colors focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 aria-selected:opacity-100 dark:ring-offset-slate-950 dark:hover:text-slate-50 dark:focus-visible:ring-slate-800',
                        isEqual(month, currentMonth) &&
                          'bg-slate-900 text-slate-50 hover:bg-slate-900 hover:text-slate-50 focus:bg-slate-900 focus:text-slate-50 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50 dark:hover:text-slate-900 dark:focus:bg-slate-50 dark:focus:text-slate-900',
                        !isEqual(month, currentMonth) &&
                          isEqual(month, getStartOfCurrentMonth()) &&
                          'bg-slate-100 text-slate-900 dark:bg-slate-800 dark:text-slate-50',
                      )}
                      disabled={isFuture(month)}
                      role="gridcell"
                      tabIndex={-1}
                      type="button"
                      onClick={() => onMonthChange(month)}
                    >
                      <time dateTime={format(month, 'yyyy-MM-dd')}>{format(month, 'MMM')}</time>
                    </button>
                  </div>
                ))}
          </div>
        </div>
      </div>
    </div>
  )
}
