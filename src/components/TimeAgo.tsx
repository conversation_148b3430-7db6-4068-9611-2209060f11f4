'use client'
import { useEffect, useState } from 'react'
import { getRelativeTime } from '../utils/commonUtils'

const TimeAgo = ({ dateString, ClassName }: { dateString: string; ClassName?: string }) => {
  const [relativeTime, setRelativeTime] = useState(getRelativeTime(dateString))

  useEffect(() => {
    const interval = setInterval(() => {
      setRelativeTime(getRelativeTime(dateString))
    }, 60000) // update every 60 seconds

    return () => clearInterval(interval)
  }, [dateString])

  return (
    <span className={`text-lucres-gray-700 dark:text-dark-lucres-green-300 text-sm ${ClassName}`}>
      {relativeTime}
    </span>
  )
}
export default TimeAgo
