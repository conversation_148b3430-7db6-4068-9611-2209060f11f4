'use client'
import React, { useState, useEffect } from 'react'
import { usePathname, useRouter } from 'next/navigation'
// import { Sidebar } from '@phosphor-icons/react'
import SidebarN from './Sidebar'
import { useAuth } from '@/context/AuthContext'
// import Order from "../orders/Order";
// import Wallet from "./wallet/wallet";
// import Accounts from "./accounts/Accounts";
// import Order from "./orders/Order";

interface ProfileInformationProps {
  activeComponent?: React.ReactNode
}

const ProfileInformation: React.FC<ProfileInformationProps> = ({ activeComponent }) => {
  const pathname = usePathname()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState<string>('personalInfo')
  const { authUserProfile } = useAuth()
  // Map path to tab
  useEffect(() => {
    const pathToTab: Record<string, string> = {
      '/personal-information': 'personalInfo',
      '/orders': 'orderDetails',
      '/wallet': 'wallet',
      // '/subscription': 'subscription',
    }

    // Set active tab based on the current route
    const newTab = pathToTab[pathname] || 'personalInfo'
    setActiveTab(newTab)
  }, [pathname])

  // Handle tab navigation
  const handleTabClick = (tab: string, path: string) => {
    setActiveTab(tab)
    router.push(path) // Navigate to the corresponding route
  }

  return (
    <section className="font-inter flex h-full w-full items-center justify-center pb-12 pt-12 md:mt-16 md:pt-0 lg:py-0">
      <div className="mx-auto flex w-full max-w-[1260px] items-start justify-start md:flex md:p-0">
        <div className="relative hidden lg:block">
          <SidebarN data={authUserProfile} />
        </div>
        <div className="flex w-full flex-row items-start lg:ms-64">
          <div className="border-lucres-300 dark:border-dark-lucres-black-200 relative min-h-screen w-full overflow-hidden md:border-x lg:max-w-[650px] xl:w-9/12">
            {/* Tabs */}
            <div className="border-lucres-300 dark:border-dark-lucres-black-200 mx-auto hidden w-full items-center justify-start gap-4 border-b pt-6 md:flex">
              <span
                className={`cursor-pointer px-4 pb-2 ${
                  activeTab === 'personalInfo' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => handleTabClick('personalInfo', '/personal-information')}
              >
                Personal Info
              </span>
              <span
                className={`cursor-pointer px-4 pb-2 ${
                  activeTab === 'orderDetails' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => handleTabClick('orderDetails', '/orders')}
              >
                Order Details
              </span>
              <span
                className={`cursor-pointer px-4 pb-2 ${
                  activeTab === 'wallet' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => handleTabClick('wallet', '/wallet')}
              >
                Wallet
              </span>
              {/* <span
                className={`cursor-pointer px-4 pb-2 ${
                  activeTab === 'subscription' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => handleTabClick('subscription', '/subscription')}
              >
                Subscription
              </span> */}
            </div>

            {/* Content */}
            <div
              className={`${
                activeTab === 'orderDetails' || activeTab === 'wallet' ? 'sm:px-0' : 'sm:px-16'
              }`}
            >
              {activeComponent}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ProfileInformation
