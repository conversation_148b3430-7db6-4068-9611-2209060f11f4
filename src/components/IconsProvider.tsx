'use client'

import {
  IconWeight,
  SparkleIcon,
  FacebookLogoIcon,
  InstagramLogoIcon,
  TwitterLogoIcon,
} from '@phosphor-icons/react'
import { HeartIcon } from '@phosphor-icons/react'

interface IconProps {
  size?: number
  className?: string
  weight?: IconWeight
  color?: string
}

export const PhosphorSparkleIcon = ({ size = 16, className, weight, color }: IconProps) => {
  return <SparkleIcon size={size} className={className} weight={weight} color={color} />
}
export const PhosphorHeartIcon = ({ size = 16, className, weight, color }: IconProps) => {
  return <HeartIcon size={size} className={className} weight={weight} color={color} />
}
export const PhosphorFacebookIcon = ({ size = 16, className, weight, color }: IconProps) => {
  return <FacebookLogoIcon size={size} className={className} weight={weight} color={color} />
}
export const PhosphorInstagramIcon = ({ size = 16, className, weight, color }: IconProps) => {
  return <InstagramLogoIcon size={size} className={className} weight={weight} color={color} />
}
export const PhosphorTwitterIcon = ({ size = 16, className, weight, color }: IconProps) => {
  return <TwitterLogoIcon size={size} className={className} weight={weight} color={color} />
}
