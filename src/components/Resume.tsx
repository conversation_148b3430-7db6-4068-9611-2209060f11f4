'use client'
import {
  CheckIcon,
  DotIcon,
  NotePencilIcon,
  PlusIcon,
  MinusIcon,
  PhoneCallIcon,
  EnvelopeIcon,
  LockOpenIcon,
  XCircleIcon,
  PlanetIcon,
  LockIcon,
} from '@phosphor-icons/react'
import { useState, useEffect } from 'react'
import { useRouter, useParams, usePathname } from 'next/navigation'
import Link from 'next/link'
import { useTheme } from '../context/ThemeProvider'
import { convertToTitleCase, formatDegreeName } from '../utils/commonUtils'
import { CareerService } from '../services/CareerService'
import NoDataFound from './NoDataFound/NoDataFound'
import ProfileProgress from '../app/feed/ProfileProgress'
import { useAuth } from '../context/AuthContext'
import CopyLinkIcon from './CopyLinkIcon'
import Tooltip from './Tooltip'
import { CompanyService } from '../services/CompanyService'
import Pricing from './Pricing'
import { useToast } from './ToastX'
import SafeHtml from './SafeHtml'
import { templates } from '../app/resume/ResumeTemplates/templates'
import IconButton from './IconButton'
import ResumeDownload from './ResumeDownload'
import Avatar from './Avatar/Avatar'
import Button from './Button'
import { UserService } from '../services/UserService'
import { JobService } from '../services/JobService'
import useError from '../context/ErrorContext'

interface ResumeProps {
  className?: string
  data?: any
  jobId?: string
  application?: any
  isBought?: boolean
  setFreeUnlockCount?: React.Dispatch<React.SetStateAction<number>>
  setApplicantDetails?: any
  totalCredits?: number
  remainingCredits?: number
  freeUnlockCount?: number
  setFilteredApplications?: any
}

const Resume: React.FC<ResumeProps> = ({
  className,
  data,
  jobId,
  application,
  isBought,
  // fetchApplicationsCount,
  freeUnlockCount,
  setFreeUnlockCount,
  setApplicantDetails,
  // totalCredits,
  // remainingCredits,
  setFilteredApplications,
}) => {
  const router = useRouter()
  const pathname = usePathname()
  const { username } = useParams<{ username: string }>()
  const { theme } = useTheme()
  const [resumeData, setResumeData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isApplicantResume, setIsApplicantResume] = useState<boolean>(false)
  const [revealResume, setRevealResume] = useState(isBought)
  const [showAllExperiences, setShowAllExperiences] = useState(false)
  const [showAllEducations, setShowAllEducations] = useState(false)
  const [showAllAchievements, setShowAllAchievements] = useState(false)
  const [showPricing, setShowPricing] = useState(false)
  const [showResumeDownoadModal, setShowResumeDownloadModal] = useState(false)
  const [applicantStatus, setApplicantStatus] = useState<string>(application?.status)
  const [isApplicationExpired, setIsApplicationExpired] = useState(application?.isExpired || false)
  const toast = useToast()
  const { handleError } = useError()
  const { authUserProfile, setAuthUserProfile, isAuthenticated } = useAuth()
  const loggedInPermalink = authUserProfile?.permalink

  // Determine if it's the logged-in user's profile by comparing permalinks
  const isOwnProfile = username === loggedInPermalink

  useEffect(() => {
    if (pathname.startsWith('/applicants')) setIsApplicantResume(true)
  }, [pathname])
  useEffect(() => {
    if (data) {
      setResumeData(data)
      setIsLoading(false) // Set loading to false when we have data from props
    }
  }, [data])

  useEffect(() => {
    const fetchCareerData = async () => {
      if (pathname.startsWith('/applicants'))
        if (data) {
          setIsLoading(false) // Set loading to false if we have data from props
          return
        }

      // Only fetch career data if user is authenticated
      if (!isAuthenticated) {
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)
        const response = await CareerService.getCareerByPermalink(username!)
        setResumeData(response.data)
      } catch (error) {
      } finally {
        setIsLoading(false)
      }
    }
    fetchCareerData()
  }, [username, data, pathname, isAuthenticated]) // Add isAuthenticated to dependencies

  if (isLoading) {
    return <ResumeShimmer />
  }

  const curvedVector =
    theme === 'dark' ? '/dashboard/curvedVectorblack.svg' : '/dashboard/curvedVector.svg'

  const experiencesToShow = showAllExperiences
    ? resumeData?.experiences
    : resumeData?.experiences?.slice(0, 2)
  const educationsToShow = showAllEducations
    ? resumeData?.educations
    : resumeData?.educations?.slice(0, 2)
  const achievementsToShow = showAllAchievements
    ? resumeData?.achievements
    : resumeData?.achievements?.slice(0, 2)

  const hasResumeData =
    resumeData &&
    (resumeData.skills?.length > 0 ||
      resumeData.experiences?.length > 0 ||
      resumeData.educations?.length > 0 ||
      resumeData.achievements?.length > 0 ||
      resumeData.languages?.length > 0)

  const profile = data
    ? {
        name:
          data?.givenName && data?.familyName
            ? `${data.givenName} ${data.familyName}`
            : data.user
              ? `${data.user.givenName} ${data.user.familyName}`
              : resumeData?.user?.givenName
                ? `${resumeData.user.givenName} ${resumeData.user.familyName}`
                : 'Guest User',
        location: data?.location?.address
          ? `${data.location.address.city}, ${data.location.address.country}`
          : '',
        // workExperience: data?.workExperience,
        avatar:
          data?.profileImage?.url ||
          data?.user?.profileImage?.url ||
          resumeData?.user?.profileImage?.url ||
          null,
        // id: data?.username ? `@${data.username}` : '@guest',
        // tag: data?.headline || 'Keep it Real.',
        // stats: [
        //   { label: 'Posts', value: data?.postsCount || 0, href: './posts' },
        //   { label: 'Followers', value: data?.followersCount || 0, href: './follow' },
        //   { label: 'Following', value: data?.followingCount || 0, href: './follow' },
        // ],
      }
    : {
        name: 'Pritesh Srivastava',
        location: 'Bangalore, India',
        avatar: '/dashboard/pritesh-avatar.svg',
        id: '@pritesh12',
        tag: 'Keep it Real.',
        stats: [
          { label: 'Posts', value: 18, href: './posts' },
          { label: 'Followers', value: 230, href: './followers' },
          { label: 'Following', value: 127, href: './following' },
        ],
      }

  const handleUnlockFreeResume = async () => {
    try {
      const formData = {
        applicationId: application.id,
        jobId: jobId,
      }
      await CompanyService.unlockFreeAapplicantContact(formData)
      const contactResponse = await JobService.getApplicantContact(application.id)
      const name = `${contactResponse.data.user.givenName} ${contactResponse.data.user.familyName}`
      setRevealResume(true)
      // fetchApplicantDetails()
      setIsApplicationExpired(false)
      setResumeData({ ...data, ...contactResponse.data })
      setApplicantStatus('BOUGHT')
      const newData = { ...data, ...contactResponse.data }
      // {resumeData.phone || resumeData.contact?.primaryPhone || '9999999999'}

      if (typeof setFilteredApplications === 'function') {
        setFilteredApplications((prev: any[]) =>
          prev.map((app) =>
            app.id === application.id
              ? {
                  ...app,
                  status: 'BOUGHT',
                  isExpired: false,
                  applicant: {
                    ...app.applicant,
                    name: name,
                  },
                }
              : app,
          ),
        )
      }
      setRevealResume(true)
      setFreeUnlockCount?.((pre: number) => pre + 1)
    } catch (error: any) {
      handleError(error)
    }
  }

  const handleUnlockResume = async () => {
    try {
      const formData = {
        applicationId: application.id,
        jobId: jobId,
      }
      await CompanyService.unlockPaidAapplicantContact(formData)
      const contactResponse = await JobService.getApplicantContact(application.id)
      const name = `${contactResponse.data.user.givenName} ${contactResponse.data.user.familyName}`
      if (authUserProfile) {
        const authUser = await UserService.getUserProfileByPermalink(authUserProfile.username)
        setAuthUserProfile(authUser.data)
      }
      setRevealResume(true)
      setApplicantStatus('BOUGHT')
      setIsApplicationExpired(false)
      // setFreeUnlockCount?.((pre: number) => pre + 1)
      // fetchApplicantDetails()
      setResumeData({ ...data, ...contactResponse.data })

      if (typeof setFilteredApplications === 'function') {
        setFilteredApplications((prev: any[]) =>
          prev.map((app) =>
            app.id === application.id
              ? {
                  ...app,
                  status: 'BOUGHT',
                  isExpired: false,
                  applicant: {
                    ...app.applicant,
                    name: name,
                  },
                }
              : app,
          ),
        )
      }
    } catch (error: any) {
      toast.error(error.data.message)
    }
  }

  const handleSuccess = async (plan?: string) => {
    if (authUserProfile) {
      const authUser = await UserService.getUserProfileByPermalink(authUserProfile.username)
      setAuthUserProfile(authUser.data)
      if (plan === 'CLS_49') {
        const contactResponse = await JobService.getApplicantContact(application.id)
        const name = `${contactResponse.data.user.givenName} ${contactResponse.data.user.familyName}`
        setRevealResume(true)

        setApplicantStatus('BOUGHT')
        setIsApplicationExpired(false)
        setResumeData({ ...data, ...contactResponse.data })

        if (typeof setFilteredApplications === 'function') {
          setFilteredApplications((prev: any[]) =>
            prev.map((app) =>
              app.id === application.id
                ? {
                    ...app,
                    status: 'BOUGHT',
                    isExpired: false,
                    applicant: {
                      ...app.applicant,
                      name: name,
                    },
                  }
                : app,
            ),
          )
        }
      }
    }
  }

  const rejectApplicant = async () => {
    if (!jobId || !application?.id) return
    try {
      const response = await JobService.applicantStatus(jobId, application?.id, 'REJECTED')
      if (response.data.status === 'success') {
        toast.error('Applicant Rejected')
        setApplicantStatus('REJECTED')
      }
    } catch (error) {
      handleError(error)
    }
  }

  return (
    <div className={`dark:border-dark-lucres-black-200 m-4 rounded-lg border ${className}`}>
      <div
        className={`flex w-full items-center justify-between rounded-t-lg p-4 ${
          username
            ? 'bg-lucres-gray-100 dark:bg-dark-lucres-black-400'
            : 'dark:border-dark-lucres-black-200 border-b'
        }`}
      >
        <span className="font-medium">{convertToTitleCase('Resume')}</span>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <ResumeDownload
              userId={data.id || application.id}
              application={application}
              jobId={jobId}
              setShowPricing={setShowPricing}
              username={profile.name || application.applicant.username}
              showResumeDownloadModal={showResumeDownoadModal}
              setShowResumeDownloadModal={setShowResumeDownloadModal}
              isOwnProfile={data.username === authUserProfile?.username}
              isApplicantResume={isApplicantResume}
              revealResume={revealResume}
              freeUnlockCount={freeUnlockCount}
              isApplicationExpired={isApplicationExpired}
              isContactRestricted={data?.preferences?.allowRecruiterContact}
              handleUnlockResume={
                freeUnlockCount !== 5 ? handleUnlockFreeResume : handleUnlockResume
              }
              setApplicantDetails={setApplicantDetails}
            />
            {isApplicantResume && applicantStatus !== 'BOUGHT' && (
              <Button
                size="small"
                theme="transparent"
                disabled={applicantStatus === 'REJECTED'}
                className="border-lucres-300! min-1140:flex dark:border-dark-lucres-black-200! px-3! py-2! font-medium! text-[#980000]! hover:bg-transparent! hover:text-[#980000]! dark:bg-transparent! dark:text-[#E39D90]! hidden rounded-lg text-xs dark:hover:text-[#E39D90]"
                onClick={rejectApplicant}
              >
                {applicantStatus !== 'REJECTED' && <XCircleIcon size={22} className="mr-2" />}
                {applicantStatus === 'REJECTED' ? 'Rejected' : 'Not a Good Fit'}
              </Button>
            )}

            {((isApplicantResume && !revealResume) ||
              (isApplicantResume && isApplicationExpired && revealResume)) && (
              <Button
                size="small"
                theme="transparent"
                className="border-lucres-300! min-1140:flex dark:border-dark-lucres-black-200! px-3! py-2! font-medium! hidden rounded-lg text-xs"
                onClick={(e) => {
                  e.stopPropagation()
                  if (freeUnlockCount !== 5) {
                    handleUnlockFreeResume()
                  } else if (authUserProfile?.subscription.status === 'ACTIVE') {
                    setShowResumeDownloadModal(true)
                  } else {
                    setShowPricing(true)
                  }
                }}
              >
                <LockOpenIcon size={22} className="mr-2" />
                {freeUnlockCount !== 5 ? 'Free Unlock' : 'Unlock Contact'}
              </Button>
            )}
          </div>
          {isOwnProfile && (
            <Link href="/resume">
              <Tooltip
                text="Edit Resume"
                classes="whitespace-nowrap text-center "
                direction="bottom"
              >
                <div className="dark:bg-dark-lucres-black-500 rounded-full bg-white">
                  <IconButton spanClass="group-hover:bg-lucres-200">
                    <NotePencilIcon size={22} />
                  </IconButton>
                </div>
              </Tooltip>
            </Link>
          )}

          {isOwnProfile && (
            <div className="dark:bg-dark-lucres-black-500 rounded-full bg-white dark:hover:bg-opacity-45">
              <CopyLinkIcon toCopy={window.location.href} />
            </div>
          )}
        </div>
      </div>
      <div className="flex w-full items-center justify-between p-4">
        <div className="flex items-start gap-x-2">
          <Avatar
            src={profile.avatar}
            alt={`${profile.name}'s Avatar`}
            size={10}
            className={`cursor-pointer object-cover`}
          />

          <div className="ml-2 flex flex-col">
            <div className="flex items-center gap-x-2">
              <span className={`whitespace-nowrap text-lg font-medium`}>
                {(isApplicantResume && !revealResume) ||
                (isApplicantResume && revealResume && isApplicationExpired) ? (
                  <span
                    className={`bg-lucres-gray-50 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-400 -mb-1 -ml-1 flex w-fit items-center gap-2 whitespace-nowrap rounded-3xl border px-2 py-1 text-xs opacity-80`}
                  >
                    <LockIcon size={16} />
                    Profile Locked
                  </span>
                ) : (
                  convertToTitleCase(profile.name)
                )}
              </span>
              {!isApplicantResume && data.isUserVerified && (
                <span className="bg-linear-to-b flex h-4 w-4 items-center justify-center rounded-full  from-[#FBBC05] to-[#9570035a]">
                  <CheckIcon size={10} className="text-white" weight="bold" />
                </span>
              )}
            </div>
            <span className="flex items-center text-sm font-medium text-gray-600 opacity-90">
              {convertToTitleCase(profile.location)}
              {/* <Dot size={20} weight="bold" />{' '} */}
              {/* {convertToTitleCase(profile.workExperience || 'Add experience')} */}
            </span>
            {isApplicantResume && (
              <span className="mt-2 flex items-center text-sm font-medium opacity-90">
                <PhoneCallIcon size={16} weight="bold" className="mr-1" />
                <span>{resumeData.phone || resumeData.contact?.primaryPhone || 'xxxxxxxxxx'}</span>
                <DotIcon size={20} weight="bold" />{' '}
                <EnvelopeIcon size={16} weight="bold" className="mr-1" />
                <span>
                  {resumeData.phone || resumeData.contact?.primaryEmail || '<EMAIL>'}
                </span>
                {/* {convertToTitleCase(profile.workExperience || 'Add experience')} */}
              </span>
            )}
          </div>
        </div>
      </div>
      {hasResumeData ? (
        <div className="ms-2 mt-0 flex flex-col px-6">
          <div className="dark:border-l-dark-lucres-black-200 relative border-l p-1 py-4 ps-12">
            <img src={curvedVector} alt="" className="absolute -left-px top-2" />
            <span className="text-sm text-gray-500 opacity-60">{convertToTitleCase('Skills')}</span>
            <div className="mt-2 flex flex-wrap gap-2">
              {resumeData.skills.map((skill: { name: string; level: number }, index: number) => (
                <span
                  className="dark:bg-dark-lucres-black-200 rounded-lg bg-gray-100 px-3 py-1"
                  key={index}
                >
                  {convertToTitleCase(skill.name)}
                </span>
              ))}
            </div>
          </div>
          <div className="dark:border-l-dark-lucres-black-200 relative border-l p-1 py-4 ps-12">
            <img src={curvedVector} alt="" className="absolute -left-px top-2" />
            <span className="text-sm text-gray-500 opacity-60">
              {convertToTitleCase('Recent Experience')}
            </span>
            <div className="mt-2 flex flex-col flex-wrap gap-2">
              {experiencesToShow.map((exp: any, index: number) => (
                <div key={index} className="flex flex-col">
                  <h3 className="font-medium">
                    {convertToTitleCase(exp.designation.name)}{' '}
                    <span className="font-normal">{convertToTitleCase(exp.company.name)}</span>
                  </h3>
                  <div>
                    <span className="flex text-sm text-gray-500">
                      {convertToTitleCase(
                        `${new Date(exp.startDate).toLocaleString('default', {
                          month: 'short',
                        })} ${new Date(exp.startDate).getFullYear()} - ${
                          exp.isCurrent
                            ? 'Present'
                            : `${new Date(exp.endDate).toLocaleString('default', {
                                month: 'short',
                              })} ${new Date(exp.endDate).getFullYear()}`
                        }`,
                      )}
                    </span>
                  </div>
                  <span className="flex text-sm text-gray-500">
                    {convertToTitleCase(`${exp.location.address.city}`)}
                  </span>
                  <span>
                    {exp.description && (
                      <SafeHtml
                        html={exp.description}
                        className="mt-2 text-sm text-gray-500 [&_ol]:ml-6 [&_ol]:list-decimal [&_ul]:ml-6 [&_ul]:list-disc"
                      />
                    )}
                  </span>
                </div>
              ))}
              {resumeData.experiences.length > 2 && (
                <Button
                  theme="translucent"
                  size="extraSmall"
                  className="border-none! w-fit gap-1 rounded-lg"
                  onClick={() => setShowAllExperiences(!showAllExperiences)}
                >
                  {showAllExperiences ? (
                    <>
                      <MinusIcon size={20} color={theme === 'dark' ? '#F2F7F3' : '#2D4232'} />
                      <span>Show less</span>
                    </>
                  ) : (
                    <>
                      <PlusIcon size={14} color={theme === 'dark' ? '#F2F7F3' : '#2D4232'} />
                      <span>{`${resumeData.experiences.length - 2} more`}</span>
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
          <div className="dark:border-l-dark-lucres-black-200 relative border-l p-1 py-4 ps-12">
            <img src={curvedVector} alt="" className="absolute -left-px top-2" />
            <span className="text-sm text-gray-500 opacity-60">
              {convertToTitleCase('Education')}
            </span>
            <div className="mt-2 flex flex-col flex-wrap gap-2">
              {educationsToShow.map((edu: any, index: number) => (
                <div key={index} className="flex flex-col">
                  <h3 className="font-medium">
                    {formatDegreeName(edu.degree.name)}{' '}
                    <span className="font-normal">{convertToTitleCase(edu.institution.name)}</span>
                  </h3>
                  <div>
                    <span className="flex text-sm text-gray-500">
                      {convertToTitleCase(`${edu.startYear} - ${edu.endYear}`)}
                    </span>
                  </div>
                  <span className="flex text-sm text-gray-500">
                    {convertToTitleCase(`${edu.location.address.city}`)}
                  </span>
                  <span>
                    {edu.description && (
                      <SafeHtml
                        html={edu.description}
                        className="mt-2 text-sm text-gray-500 [&_ol]:ml-6 [&_ol]:list-decimal [&_ul]:ml-6 [&_ul]:list-disc"
                      />
                    )}
                  </span>
                </div>
              ))}
              {resumeData.educations.length > 2 && (
                <Button
                  theme="translucent"
                  size="extraSmall"
                  className="border-none! w-fit gap-1 rounded-lg"
                  onClick={() => setShowAllEducations(!showAllEducations)}
                >
                  {showAllEducations ? (
                    <>
                      <MinusIcon size={20} color={theme === 'dark' ? '#F2F7F3' : '#2D4232'} />
                      <span>Show less</span>
                    </>
                  ) : (
                    <>
                      <PlusIcon size={14} color={theme === 'dark' ? '#F2F7F3' : '#2D4232'} />
                      <span>{`${resumeData.educations.length - 2} more`}</span>
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
          <div className="dark:border-l-dark-lucres-black-200 relative border-l p-1 py-4 pb-8 ps-12">
            <img src={curvedVector} alt="" className="absolute -left-px top-2" />
            <span className="text-sm text-gray-500 opacity-60">
              {convertToTitleCase('Achievements')}
            </span>
            <div className="mt-2 flex flex-col flex-wrap gap-2">
              {achievementsToShow.map((achievement: any, index: number) => (
                <div key={index} className="flex flex-col">
                  <h3 className="font-medium">{convertToTitleCase(achievement.title)}</h3>
                  <div>
                    <span className="flex text-sm text-gray-500">
                      {convertToTitleCase(new Date(achievement.date).toLocaleDateString())}
                    </span>
                  </div>
                  <span className="mt-2 text-sm text-gray-500">{achievement.awardedBy}</span>
                </div>
              ))}
              {resumeData.achievements.length > 2 && (
                <Button
                  theme="translucent"
                  size="extraSmall"
                  className="border-none! w-fit gap-1 rounded-lg"
                  onClick={() => setShowAllAchievements(!showAllAchievements)}
                >
                  {showAllAchievements ? (
                    <>
                      <MinusIcon size={20} color={theme === 'dark' ? '#F2F7F3' : '#2D4232'} />
                      <span>Show less</span>
                    </>
                  ) : (
                    <>
                      <PlusIcon size={14} color={theme === 'dark' ? '#F2F7F3' : '#2D4232'} />
                      <span>{`${resumeData.achievements.length - 2} more`}</span>
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
          <div className="relative p-1 pb-4 ps-12">
            <img src={curvedVector} alt="" className="absolute -top-1 left-[0px]" />
            <span className="text-sm text-gray-500 opacity-60">
              {convertToTitleCase('Languages Known')}
            </span>
            <div className="mt-2 flex flex-wrap gap-2">
              {resumeData.languages.map(
                (language: { name: string; level: string }, index: number) => (
                  <span
                    className="dark:bg-dark-lucres-black-200 rounded-lg bg-gray-100 px-3 py-1"
                    key={index}
                  >
                    {convertToTitleCase(language.name)}
                  </span>
                ),
              )}
            </div>
          </div>
        </div>
      ) : isOwnProfile ? (
        <div className="p-4">
          <ProfileProgress percentage={0} />
        </div>
      ) : (
        <div className="p-4">
          <NoDataFound subtitle="No resume data available." />
        </div>
      )}
      {showPricing && (
        <div className="z-999! fixed left-0 top-0 h-screen w-screen">
          <Pricing
            handleClose={() => {
              setShowPricing(false)
            }}
            handleSuccess={handleSuccess}
            // flow="JOB_DASHBOARD"
            flow={isApplicantResume ? 'JOB_DASHBOARD' : 'PROFILE'}
            jobId={jobId}
            applications={[application?.id]}
          />
        </div>
      )}
    </div>
  )
}

const ResumeShimmer = () => {
  return (
    <div className="dark:border-dark-lucres-black-200 m-4 rounded-lg border">
      <div className="dark:bg-dark-lucres-black-200 flex w-full items-center justify-between rounded-t-lg bg-gray-100 p-4">
        <div className="shimmer h-6 w-24 bg-gray-200 dark:bg-gray-700"></div>
        <div className="flex items-center gap-2">
          <div className="shimmer h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700"></div>
          <div className="shimmer h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700"></div>
        </div>
      </div>
      <div className="flex w-full items-center justify-between p-4">
        <div className="flex items-start gap-x-2">
          <div className="shimmer h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700"></div>
          <div className="flex flex-col">
            <div className="shimmer h-6 w-32 bg-gray-200 dark:bg-gray-700"></div>
            <div className="shimmer mt-2 h-4 w-24 bg-gray-200 dark:bg-gray-700"></div>
          </div>
        </div>
      </div>
      <div className="ms-2 mt-0 flex flex-col px-6">
        <div className="dark:border-l-dark-lucres-black-200 relative border-l p-1 py-4 ps-12">
          <div className="shimmer h-4 w-16 bg-gray-200 dark:bg-gray-700"></div>
          <div className="mt-2 flex flex-wrap gap-2">
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                className="shimmer h-6 w-20 rounded-lg bg-gray-200 dark:bg-gray-700"
              ></div>
            ))}
          </div>
        </div>
        <div className="dark:border-l-dark-lucres-black-200 relative border-l p-1 py-4 ps-12">
          <div className="shimmer h-4 w-24 bg-gray-200 dark:bg-gray-700"></div>
          <div className="mt-2 space-y-4">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="shimmer h-5 w-32 bg-gray-200 dark:bg-gray-700"></div>
                <div className="shimmer h-4 w-48 bg-gray-200 dark:bg-gray-700"></div>
                <div className="shimmer h-4 w-40 bg-gray-200 dark:bg-gray-700"></div>
                <div className="shimmer h-4 w-full bg-gray-200 dark:bg-gray-700"></div>
              </div>
            ))}
          </div>
        </div>
        <div className="dark:border-l-dark-lucres-black-200 relative border-l p-1 py-4 ps-12">
          <div className="shimmer h-4 w-20 bg-gray-200 dark:bg-gray-700"></div>
          <div className="mt-2 space-y-4">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="shimmer h-5 w-32 bg-gray-200 dark:bg-gray-700"></div>
                <div className="shimmer h-4 w-48 bg-gray-200 dark:bg-gray-700"></div>
                <div className="shimmer h-4 w-40 bg-gray-200 dark:bg-gray-700"></div>
                <div className="shimmer h-4 w-full bg-gray-200 dark:bg-gray-700"></div>
              </div>
            ))}
          </div>
        </div>
        <div className="dark:border-l-dark-lucres-black-200 relative border-l p-1 py-4 pb-8 ps-12">
          <div className="shimmer h-4 w-24 bg-gray-200 dark:bg-gray-700"></div>
          <div className="mt-2 space-y-4">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="shimmer h-5 w-32 bg-gray-200 dark:bg-gray-700"></div>
                <div className="shimmer h-4 w-48 bg-gray-200 dark:bg-gray-700"></div>
                <div className="shimmer h-4 w-full bg-gray-200 dark:bg-gray-700"></div>
              </div>
            ))}
          </div>
        </div>
        <div className="relative p-1 pb-4 ps-12">
          <div className="shimmer h-4 w-24 bg-gray-200 dark:bg-gray-700"></div>
          <div className="mt-2 flex flex-wrap gap-2">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="shimmer h-6 w-20 rounded-lg bg-gray-200 dark:bg-gray-700"
              ></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Resume
