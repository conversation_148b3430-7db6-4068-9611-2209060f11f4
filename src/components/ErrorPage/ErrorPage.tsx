'use client'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import useError from '../../context/ErrorContext'

// Import placeholder images (you'll need to add these SVG files)
// For now, I'll use placeholder divs
const noDataLight = '/errors/nodata-light.svg'
const noDataDark = '/errors/nodata-dark.svg'
const somethingWentWrongLight = '/errors/something-went-wrong-light.svg'
const somethingWentWrongDark = '/errors/something-went-wrong-dark.svg'
const error404Light = '/errors/error404-light.svg'
const error404dark = '/errors/error404-dark.svg'
const offlineLight = '/errors/offline-light.svg'
const offlineDark = '/errors/offline-dark.svg'
const downtimeLight = '/errors/downtime-light.svg'
const downtimeDark = '/errors/downtime-dark.svg'
const permissionDeniedLight = '/errors/permission-denied-light.svg'
const permissionDeniedDark = '/errors/permission-denied-dark.svg'

interface NoDataFoundProps {
  variant?: string
  subtitle?: string
  className?: string
}

const ErrorPage: React.FC<NoDataFoundProps> = ({
  variant = 'default',
  subtitle = 'No results found. Try adjusting your filters or search for something else.',
  className,
}) => {
  const router = useRouter()
  const { hideError } = useError()
  const [countdown, setCountdown] = useState(10)

  useEffect(() => {
    let interval: NodeJS.Timeout | undefined
    let timeout: NodeJS.Timeout | undefined

    if (variant === 'permission-denied' || variant === 'error-404') {
      interval = setInterval(() => {
        setCountdown((prev) => prev - 1)
      }, 1000)

      timeout = setTimeout(() => {
        router.push('/sign-in')
        hideError()
      }, 10000)
    }

    if (variant === 'downtime') {
      interval = setInterval(() => {
        setCountdown((prev) => prev - 1)
      }, 1000)
      timeout = setTimeout(() => {
        window.location.reload()
        hideError()
      }, 10000) // retry after 10 seconds
    }

    return () => {
      if (interval) clearInterval(interval)
      if (timeout) clearTimeout(timeout)
    }
  }, [variant, router, hideError])

  let headline: string
  let lightImage: string
  let darkImage: string

  switch (variant) {
    case 'offline':
      headline = 'Network Error / Offline'
      subtitle = subtitle || "You're offline! Check your internet connection and try again."
      lightImage = offlineLight
      darkImage = offlineDark
      break
    case 'no-data':
      headline = 'No Data Found'
      subtitle =
        subtitle || 'No results found. Try adjusting your filters or search for something else.'
      lightImage = noDataLight
      darkImage = noDataDark
      break
    case 'something-went-wrong':
      headline = 'Something Went Wrong'
      subtitle = subtitle || 'We are working on fixing the Problem. Please try Again'
      lightImage = somethingWentWrongLight
      darkImage = somethingWentWrongDark
      break
    case 'downtime':
      headline = 'Maintenance / Downtime'
      subtitle =
        subtitle ||
        "We're making improvements! The site is temporarily unavailable but will be back soon."
      lightImage = downtimeLight
      darkImage = downtimeDark
      break
    case 'permission-denied':
      headline = 'Permission or Access Denied'
      subtitle =
        subtitle ||
        "You don't have permission to access this page. Contact support if you believe this is a mistake."
      lightImage = permissionDeniedLight
      darkImage = permissionDeniedDark
      break
    case 'error-404':
      headline = '404 Not Found / Page Not Found'
      subtitle =
        subtitle ||
        "Oops! The page you're looking for doesn't exist. Try checking the URL or head back to the homepage."
      lightImage = error404Light
      darkImage = error404dark
      break
    default:
      headline = 'No Data Found'
      subtitle =
        subtitle || 'No results found. Try adjusting your filters or search for something else.'
      lightImage = noDataLight
      darkImage = noDataDark
  }

  return (
    <div className={`flex flex-col items-center justify-start pb-4 ${className}`}>
      {/* Light mode image */}
      <img
        src={lightImage}
        alt={headline}
        className="mb-2 flex h-60 w-60 items-center justify-center rounded-lg bg-gray-100 dark:hidden"
      />
      {/* Dark mode image */}
      <img
        src={darkImage}
        alt={headline}
        className="hidden h-72 w-80 items-center justify-center rounded-lg bg-gray-800 dark:flex"
      />

      <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200">{headline}</h2>
      <p className="mt-2 text-center text-gray-600 dark:text-gray-400">{subtitle}</p>
      {variant === 'permission-denied' && (
        <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
          Redirecting to sign in {countdown}
        </p>
      )}
      {variant === 'error-404' && (
        <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">Going back in {countdown}</p>
      )}
      {variant === 'downtime' && (
        <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
          Retrying after {countdown} sec
        </p>
      )}
    </div>
  )
}

export default ErrorPage
