interface ColorSwatchProps {
  colors: string[]
  secondaryColors?: string[]
  selectedColor: string
  onSelect: (color: string) => void
  selectedTemplateId?: number
}

const ColorSwatch: React.FC<ColorSwatchProps> = ({
  colors,
  secondaryColors = [],
  selectedColor,
  onSelect,
  selectedTemplateId,
}) => {
  return (
    <div className="flex items-center gap-x-3">
      {colors.map((color, index) => {
        const pairedSecondaryColor =
          selectedTemplateId === 4 && secondaryColors?.[index] ? secondaryColors[index] : 'white'

        return (
          <div
            key={color}
            onClick={() => onSelect(color)}
            className={`flex h-6 w-6 -rotate-45 cursor-pointer flex-col overflow-hidden rounded-full transition-all duration-200 md:h-7 md:w-7 ${
              selectedColor === color ? 'border-2 border-lime-300' : ''
            }`}
          >
            <span className="h-1/2 w-full" style={{ backgroundColor: color }} />
            <span className="h-1/2 w-full" style={{ backgroundColor: pairedSecondaryColor }} />
          </div>
        )
      })}
    </div>
  )
}

export default ColorSwatch
