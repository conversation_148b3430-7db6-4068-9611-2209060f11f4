// import React, { useState, useEffect, useRef } from 'react'
// import {
//   ArrowBendUpRight,
//   Briefcase,
//   Globe,
//   Image,
//   MagnifyingGlass,
//   User,
//   X,
// } from '@phosphor-icons/react'
// import { User as UserType, useTalent } from '../context/TalentProvider'
// import { SrpService } from '../services/SrpService'

// interface JobType {
//   name: string
//   permalink: string
//   img: string
//   location: string
//   headline: string
// }

// interface SearchPageProps {
//   className?: string
//   placeHolder?: string
//   onClick: () => void
//   searchPage: boolean
// }

// const searchData = {
//   users: [
//     { name: '<PERSON><PERSON><PERSON>', username: 'rohandev', img: '', location: '', headline: '' },
//     {
//       name: '<PERSON><PERSON>a <PERSON>',
//       username: 'kavya',
//       img: '',
//       location: 'Bengaluru, India',
//       headline: '<PERSON><PERSON><PERSON>, <PERSON>',
//     },
//     {
//       name: '<PERSON><PERSON><PERSON>',
//       username: 'divya',
//       img: '',
//       location: '',
//       headline: '<PERSON>eloper, Lucres',
//     },
//     {
//       name: 'Suresh b',
//       username: 'suresha',
//       img: '',
//       location: 'Bhavnagar, India',
//       headline: 'Web Developer, Lucres',
//     },
//     {
//       name: 'Ashish S',
//       username: 'ashneonprime',
//       img: '',
//       location: '',
//       headline: 'Software Developer/Engineer, Accenture',
//     },
//   ],
//   jobs: [
//     {
//       name: 'Angular developer',
//       permalink: 'angular',
//       img: '',
//       location: 'Chennai, India',
//       headline: 'Web developer, R WEB',
//     },
//     {
//       name: 'NodeJs Developer',
//       permalink: 'nodejs',
//       img: '',
//       location: 'Bengaluru, India',
//       headline: 'Backend Developer, Wipro',
//     },
//     {
//       name: 'Web developer',
//       permalink: 'webdev',
//       img: '',
//       location: 'Bengaluru, India',
//       headline: 'web developer, Lucres',
//     },
//     {
//       name: 'java developer',
//       permalink: 'java',
//       img: '',
//       location: 'Chennai, India',
//       headline: 'Software Engineer, Lucres Private Limited',
//     },
//     {
//       name: 'PowerBI Developer',
//       permalink: 'powerbi',
//       img: '',
//       location: 'Bengaluru, India',
//       headline: 'Business Analyst, Oracle India',
//     },
//   ],
// }

// const SearchPage: React.FC<SearchPageProps> = ({
//   className,
//   placeHolder = 'Search Lucres',
//   onClick,
//   searchPage,
// }) => {
//   const searchRef = useRef<HTMLDivElement>(null)
//   const [selectedFirstStepFilter, setSelectedFirstStepFilter] = useState<string | null>(null)
//   const [selectedSecondStepFilter, setSelectedSecondStepFilter] = useState<string | null>(null)
//   const [selectedThirdStepFilter, setSelectedThirdStepFilter] = useState<string | null>(null)
//   const [search, setSearch] = useState<string>('')
//   const [filteredSuggestions, setFilteredSuggestions] = useState<Array<any>>([])
//   const [selectFromSuggestion, setSelectFromSuggestion] = useState<string>('')
//   const { talents } = useTalent()

//   useEffect(() => {
//     if (searchPage) {
//       document.body.classList.add('overflow-hidden')
//     } else {
//       document.body.classList.remove('overflow-hidden')
//     }
//     return () => {
//       document.body.classList.remove('overflow-hidden')
//     }
//   }, [searchPage])

//   useEffect(() => {
//     const handleClickOutside = (event: MouseEvent) => {
//       if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
//         onClick()
//       }
//     }
//     document.addEventListener('mousedown', handleClickOutside)
//     return () => {
//       document.removeEventListener('mousedown', handleClickOutside)
//     }
//   }, [onClick])

//   const firstStepFilters = [
//     { name: 'Posts', icon: <Globe /> },
//     { name: 'Jobs', icon: <Briefcase /> },
//     { name: 'Talent', icon: <User /> },
//     { name: 'Media', icon: <Image /> },
//   ]
//   const secondStepFilters = [
//     { name: 'To' },
//     { name: 'From' },
//     { name: 'Mentions' },
//     { name: 'Liked By' },
//   ]
//   const thirdStepFilters = [{ name: 'People I Follow' }, { name: 'People following me' }]

//   const selectFirstStepFilter = (filterName: string) => {
//     setSelectedFirstStepFilter(filterName)
//     setFilteredSuggestions([]) // Reset
//   }

//   const selectSecondStepFilter = (filterName: string) => {
//     setSelectedSecondStepFilter(filterName)
//   }

//   const selectThirdStepFilter = (filterName: string) => {
//     setSelectedThirdStepFilter(filterName)
//   }

//   const clearFilter = () => {
//     setSelectedFirstStepFilter(null)
//     setSelectedSecondStepFilter(null)
//     setSelectedThirdStepFilter(null)
//   }

//   // const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
//   //   const value = e.target.value.trim().toLowerCase()
//   //   setSearch(value)

//   //   if (!value) {
//   //     setFilteredSuggestions([])
//   //     return
//   //   }

//   //   if (selectedFirstStepFilter === 'Talent') {
//   //     setFilteredSuggestions(
//   //       searchData.users.filter((user) => user.name.toLowerCase().startsWith(value)),
//   //     )
//   //   } else if (selectedFirstStepFilter === 'Jobs') {
//   //     setFilteredSuggestions(
//   //       searchData.jobs.filter((job) => job.name.toLowerCase().startsWith(value)),
//   //     )
//   //   } else {
//   //     setFilteredSuggestions([]) // Can be extended for Posts/Media
//   //   }
//   // }'

//   const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
//     setSearch(e.target.value)
//     const value = e.target.value.trim()

//     if (!value) {
//       setFilteredSuggestions([])
//       return
//     }

//     try {
//       const result = await SrpService.searchGlobal(value)

//       if (Array.isArray(result)) {
//         setFilteredSuggestions(result)
//       } else {
//         setFilteredSuggestions([])
//       }
//     } catch (err) {
//       console.error('Search error:', err)
//       setFilteredSuggestions([])
//     }
//   }

//   useEffect(() => {
//   }, [filteredSuggestions])

//   const selectSuggestion = (item: UserType | JobType) => {
//     setSearch(item.name || '')
//     setSelectFromSuggestion(item.name || '')
//     setFilteredSuggestions([item])
//   }

//   const handleClearSelectedSuggestion = () => {
//     setSelectFromSuggestion('')
//     setSearch('')
//     setFilteredSuggestions([])
//   }

//   return (
//     <div
//       ref={searchRef}
//       className={`fixed left-1/2 top-0 z-51 mt-4 h-2/3 w-11/12 max-w-2xl -translate-x-1/2 rounded-lg bg-white md:w-8/12 xl:w-9/12 dark:border dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 ${className}`}
//     >
//       {/* Search bar */}
//       <div className="flex items-center gap-x-2 border-b p-4 ps-8 dark:border-b-dark-lucres-black-300">
//         {selectedFirstStepFilter ? (
//           <div className="flex items-center gap-x-2 rounded-2xl bg-gray-100 px-3 py-1 dark:border dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500">
//             <span>{selectedFirstStepFilter}</span>
//             <X size={16} className="cursor-pointer" onClick={clearFilter} />
//           </div>
//         ) : (
//           <MagnifyingGlass size={20} />
//         )}
//         {selectedSecondStepFilter && (
//           <span className="text-[#6A6F74] dark:text-dark-lucres-green-100 dark:text-opacity-80">
//             {selectedSecondStepFilter}:
//           </span>
//         )}
//         {selectFromSuggestion !== '' ? (
//           <div className="flex items-center gap-x-2 rounded-2xl bg-gray-100 px-3 py-1 dark:border dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500">
//             <span>{selectFromSuggestion}</span>
//             <X size={16} className="cursor-pointer" onClick={handleClearSelectedSuggestion} />
//           </div>
//         ) : (
//           <input
//             className="border-none p-0 placeholder:text-sm placeholder:text-lucres-gray-500 focus:outline-hidden dark:bg-dark-lucres-black-500"
//             value={search}
//             onChange={handleSearch}
//             autoFocus
//             placeholder={
//               selectedSecondStepFilter
//                 ? ''
//                 : selectedFirstStepFilter
//                   ? `Search ${selectedFirstStepFilter}`
//                   : 'Search Lucres'
//             }
//           />
//         )}
//       </div>

//       {/* Filtered Suggestions */}
//       <div className="flex flex-col gap-4 p-4 ps-8">
//         {search.trim() !== '' && filteredSuggestions.length > 0 && (
//           <ul className="flex flex-col gap-4">
//             {filteredSuggestions.map((item) => (
//               <div
//                 key={item.name}
//                 className="flex cursor-pointer items-center gap-4 text-lucres-gray-700"
//                 onClick={() => selectSuggestion(item)}
//               >
//                 <img
//                   src={item.img || '/default-avatar.svg'}
//                   alt="avatar"
//                   className="z-20 h-10 w-10 cursor-pointer rounded-full"
//                 />
//                 <div className="flex flex-col">
//                   <span className="text-lg font-bold text-lucres-black dark:text-dark-lucres-green-100">
//                     {item.name}
//                   </span>
//                   <span className="text-sm text-lucres-gray-700 dark:text-dark-lucres-green-100 dark:text-opacity-80">
//                     {item.headline}
//                   </span>
//                 </div>
//               </div>
//             ))}
//           </ul>
//         )}

//         {search.trim() === '' && !selectedFirstStepFilter && (
//           <div className="flex flex-wrap items-center gap-4 text-lucres-gray-700 dark:text-dark-lucres-green-100">
//             {firstStepFilters.map((filter) => (
//               <span
//                 key={filter.name}
//                 className="flex cursor-pointer items-center gap-x-1 rounded-lg bg-gray-100 px-2 py-1 dark:border dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500"
//                 onClick={() => selectFirstStepFilter(filter.name)}
//               >
//                 {filter.icon}
//                 {filter.name}
//               </span>
//             ))}
//           </div>
//         )}
//       </div>
//     </div>
//   )
// }

// export default SearchPage
'use client'
import React, { useState, useEffect, useRef } from 'react'
import {
  ArrowBendUpRight,
  Briefcase,
  Globe,
  Image,
  MagnifyingGlass,
  User,
  X,
} from '@phosphor-icons/react'
// import { User as UserType, useTalent } from "../context/TalentProvider";
import { SrpService } from '../services/SrpService'
import { useRouter } from 'next/navigation'
import Avatar from './Avatar/Avatar'
import { capitalizeWords, convertToTitleCase, truncateText } from '../utils/commonUtils'

interface JobType {
  name: string
  permalink: string
  img: string
  location: string
  headline: string
}

interface SearchPageProps {
  className?: string
  placeHolder?: string
  onClick: () => void
  searchPage: boolean
  setSearchPage: React.Dispatch<React.SetStateAction<boolean>>
}

const SearchPage: React.FC<SearchPageProps> = ({
  className,
  onClick,
  setSearchPage,
  searchPage,
}) => {
  const searchRef = useRef<HTMLDivElement>(null)
  const router = useRouter()
  // const [selectedFirstStepFilter, setSelectedFirstStepFilter] = useState<string | null>(null)
  // const [selectedSecondStepFilter, setSelectedSecondStepFilter] = useState<string | null>(null)
  // const [selectedThirdStepFilter, setSelectedThirdStepFilter] = useState<string | null>(null)
  const [search, setSearch] = useState<string>('')
  const [filter, setFilter] = useState('')
  const [selectFromSuggestion, setSelectFromSuggestion] = useState<string>('')
  const [searchedUsers, setSearchedUsers] = useState<Array<any>>([])
  const [searchedJobs, setSearchedJobs] = useState<Array<any>>([])
  // const { talents } = useTalent();

  useEffect(() => {
    if (searchPage) {
      document.body.classList.add('overflow-hidden')
    } else {
      document.body.classList.remove('overflow-hidden')
    }
    return () => {
      document.body.classList.remove('overflow-hidden')
    }
  }, [searchPage])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        onClick()
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [onClick])

  const filters = [
    { name: 'Jobs', icon: <Briefcase /> },
    { name: 'Talent', icon: <User /> },
  ]
  // const secondStepFilters = [
  //   { name: 'To' },
  //   { name: 'From' },
  //   { name: 'Mentions' },
  //   { name: 'Liked By' },
  // ]
  // const thirdStepFilters = [{ name: 'People I Follow' }, { name: 'People following me' }]

  // const selectFirstStepFilter = (filterName: string) => {
  //   setSelectedFirstStepFilter(filterName)
  //   setFilter('') // Reset
  // }

  // const selectSecondStepFilter = (filterName: string) => {
  //   setSelectedSecondStepFilter(filterName)
  // }

  // const selectThirdStepFilter = (filterName: string) => {
  //   setSelectedThirdStepFilter(filterName)
  // }

  const clearFilter = () => {
    setFilter('')
    // setSelectedFirstStepFilter(null)
    // setSelectedSecondStepFilter(null)
    // setSelectedThirdStepFilter(null)
  }

  // const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const value = e.target.value.trim().toLowerCase()
  //   setSearch(value)

  //   if (!value) {
  //     setFilteredSuggestions([])
  //     return
  //   }

  //   if (selectedFirstStepFilter === 'Talent') {
  //     setFilteredSuggestions(
  //       searchData.users.filter((user) => user.name.toLowerCase().startsWith(value)),
  //     )
  //   } else if (selectedFirstStepFilter === 'Jobs') {
  //     setFilteredSuggestions(
  //       searchData.jobs.filter((job) => job.name.toLowerCase().startsWith(value)),
  //     )
  //   } else {
  //     setFilteredSuggestions([]) // Can be extended for Posts/Media
  //   }
  // }'

  const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value)
    const value = e.target.value.trim()

    if (!value) {
      return
    }

    try {
      const result = await SrpService.searchGlobal(value)
      if (result.status === 'success') {
        setSearchedUsers(result.data.users)
        setSearchedJobs(result.data.jobs)
      }
    } catch (err) {
      console.error('Search error:', err)
      // setFilter([])
    }
  }

  // const selectSuggestion = (item: UserType | JobType) => {
  //   setSearch(item.name || '')
  //   setSelectFromSuggestion(item.name || '')
  //   setFilter([item])
  // }

  const handleClearFilter = () => {
    // setSelectFromSuggestion('')
    setSearch('')
    setFilter('')
  }

  return (
    <div
      ref={searchRef}
      className={`dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 z-51 fixed left-1/2 top-0 mt-4 h-2/3 w-11/12 max-w-2xl -translate-x-1/2 rounded-lg bg-white md:w-8/12 xl:w-9/12 dark:border ${className}`}
    >
      {/* Search bar */}
      <div className="dark:border-b-dark-lucres-black-300 flex items-center gap-x-2 border-b p-4 ps-8">
        {filter ? (
          <div className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 flex items-center gap-x-2 rounded-2xl bg-gray-100 px-3 py-1 dark:border">
            <span>{filter}</span>
            <X size={16} className="cursor-pointer" onClick={clearFilter} />
          </div>
        ) : (
          <MagnifyingGlass size={20} />
        )}
        {/* {selectedSecondStepFilter && (
          <span className="text-[#6A6F74] dark:text-dark-lucres-green-100 dark:text-opacity-80">
            {selectedSecondStepFilter}:
          </span>
        )} */}
        {selectFromSuggestion !== '' ? (
          <div className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 flex items-center gap-x-2 rounded-2xl bg-gray-100 px-3 py-1 dark:border">
            <span>{selectFromSuggestion}</span>
            <X size={16} className="cursor-pointer" onClick={handleClearFilter} />
          </div>
        ) : (
          <input
            className="placeholder:text-lucres-gray-500 dark:bg-dark-lucres-black-500 focus:outline-hidden border-none p-0 placeholder:text-sm"
            value={search}
            onChange={handleSearch}
            autoFocus
            placeholder={'Search Lucres'}
          />
        )}
      </div>

      {/* Filtered Suggestions */}
      <div className="flex flex-col gap-4 p-4 ps-8">
        {/* {search.trim() !== '' && filteredSuggestions.length > 0 && (
            <ul className="flex flex-col gap-4">
              {filteredSuggestions.map((item) => (
                <div
                  key={item.name}
                  className="flex cursor-pointer items-center gap-4 text-lucres-gray-700"
                  onClick={() => selectSuggestion(item)}
                >
                  <img
                    src={item.img || '/default-avatar.svg'}
                    alt="avatar"
                    className="z-20 h-10 w-10 cursor-pointer rounded-full"
                  />
                  <div className="flex flex-col">
                    <span className="text-lg font-bold text-lucres-black dark:text-dark-lucres-green-100">
                      {item.name}
                    </span>
                    <span className="text-sm text-lucres-gray-700 dark:text-dark-lucres-green-100 dark:text-opacity-80">
                      {item.headline}
                    </span>
                  </div>
                </div>
              ))}
            </ul>
          )} */}
        {
          <div className="text-lucres-gray-700 dark:text-dark-lucres-green-100 flex flex-wrap items-center gap-4">
            {filters.map((filter) => (
              <span
                key={filter.name}
                className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 flex cursor-pointer items-center gap-x-1 rounded-lg bg-gray-100 px-2 py-1 dark:border"
                onClick={() => setFilter(filter.name)}
              >
                {filter.icon}
                {filter.name}
              </span>
            ))}
          </div>
        }
      </div>
      <div className="scrollbar-none flex h-3/4 flex-col gap-6 overflow-auto p-4 ps-8">
        {search.trim() && (searchedUsers.length > 0 || searchedJobs.length > 0) ? (
          <>
            {searchedUsers.length > 0 && filter !== 'Jobs' && (
              <div className="flex flex-col gap-2">
                <span className="dark:text-dark-lucres-green-100 text-sm text-[#6A6F74] dark:text-opacity-80">
                  Talent
                </span>
                {searchedUsers.map((user) => (
                  <div
                    key={user.username}
                    className="hover:bg-lucres-gray-100 dark:hover:bg-dark-lucres-black-400 flex cursor-pointer items-center gap-4 rounded-md p-2"
                    onClick={() => {
                      router.push(`/${user.username}`)
                      setSearchPage(false)
                    }}
                  >
                    <Avatar
                      src={user.img}
                      alt={`${user.name}'s Avatar`}
                      size={10}
                      className="cursor-pointer object-cover"
                    />
                    <div className="flex flex-col">
                      <span className="text-lucres-black dark:text-dark-lucres-green-100 text-lg hover:underline">
                        {capitalizeWords(user.name)}
                      </span>
                      <span className="text-lucres-gray-700 dark:text-dark-lucres-green-100 text-sm dark:text-opacity-80">
                        {user.headline}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {searchedJobs.length > 0 && filter !== 'Talent' && (
              <div className="flex flex-col gap-2">
                <span className="dark:text-dark-lucres-green-100 text-sm text-[#6A6F74] dark:text-opacity-80">
                  Jobs
                </span>
                {searchedJobs.map((job) => (
                  <div
                    key={job.permalink}
                    className="hover:bg-lucres-gray-100 dark:hover:bg-dark-lucres-black-400 flex cursor-pointer items-center gap-4 rounded-md p-2"
                    onClick={() => {
                      router.push(`/job/${job.permalink}`)
                      setSearchPage(false)
                    }}
                  >
                    <Avatar
                      src={job.img}
                      alt={`${job.name}'s Avatar`}
                      size={10}
                      className="cursor-pointer object-cover"
                    />
                    <div className="flex flex-col">
                      <span className="text-lucres-black dark:text-dark-lucres-green-100 text-lg hover:underline">
                        {convertToTitleCase(truncateText(job.name, 20).text)}
                      </span>
                      <span className="text-lucres-gray-700 dark:text-dark-lucres-green-100 text-sm dark:text-opacity-80">
                        {job.location}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        ) : search.trim() ? (
          <div className="text-sm text-[#888]">No results found</div>
        ) : (
          <div className="text-sm text-[#888]">Type minimum 3 letters to search...</div>
        )}
      </div>
    </div>
  )
}

export default SearchPage
