import React from 'react'
import { useTheme } from '../context/ThemeProvider'

interface AISuggestionsButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  className?: string
  size?: 'small' | 'medium' | 'large'
  theme?: 'primary' | 'secondary' | 'tertiary'
  icon?: React.ReactNode
}

const AISuggestionsButton: React.FC<AISuggestionsButtonProps> = ({
  children,
  className,
  size = 'medium',
  theme = 'primary',
  icon,
  type = 'button',
  ...props
}) => {
  const { theme: currentTheme } = useTheme()
  const mode = currentTheme === 'dark' ? 'dark' : 'light'

  const sizeClasses = {
    small: 'py-1 px-2 text-sm',
    medium: 'py-2 px-3 text-sm',
    large: 'p-2 px-4 text-base',
  }

  const buttonStyleClasses = {
    light: {
      primary: 'bg-lucres-200 text-black flex items-center  rounded-lg',
      secondary: 'border border-lucres-900 flex items-center rounded-lg',
      tertiary: 'border border-lucres-300 text-black flex items-center  rounded-lg',
    },
    dark: {
      primary: 'bg-dark-lucres-black-200 text-white flex items-center rounded-lg',
      secondary: ' rounded-lg border shadow-xs  border-lucres-900 ',
      tertiary: 'border border-dark-lucres-300 text-white flex items-center rounded-lg',
    },
  }
  const combinedClassName = `inline-flex items-center gap-2 ${
    sizeClasses[size]
  } ${buttonStyleClasses[mode][theme]} ${className || ''}`

  return (
    <button type={type} {...props} className={combinedClassName}>
      {<span>{icon}</span>}
      <span>{children}</span>
    </button>
  )
}
export default AISuggestionsButton
