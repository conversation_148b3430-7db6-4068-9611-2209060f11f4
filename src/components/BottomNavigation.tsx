'use client'

import React, { useState } from 'react'
import { BriefcaseIcon, HouseIcon, UserIcon, UsersThreeIcon } from '@phosphor-icons/react'
import Tooltip from './Tooltip'
import IconButton from './IconButton'
import Link from 'next/link'
import <PERSON><PERSON>rawer from './PostDrawer'
import { useTheme } from '../context/ThemeProvider'
import { useAuth } from '../context/AuthContext'
import { usePathname } from 'next/navigation'

interface BottomNavigationProps {
  className?: string
}

interface LinkIcons {
  Id: number
  Title: string
  Link: string
  IconPath?: React.JSX.Element
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({ className }) => {
  const { theme } = useTheme()
  const { isAuthenticated, authUserProfile } = useAuth()
  const [toggleBottomSlider, setToggleBottomSlider] = useState(false)
  const pathname = usePathname()

  const isActive = (link: LinkIcons) => {
    if (link.Link === '/feed' && pathname === '/feed') return true
    if (link.Link === '/' && pathname === '/') return true
    if (link.Link === '/jobs' && pathname.startsWith('/jobs')) return true
    if (link.Link === '/talent' && pathname.startsWith('/talent')) return true
    if (link.Title === 'Profile' && pathname === `/${authUserProfile?.username}`) return true
    return false
  }

  const BottomTabLinks: LinkIcons[] = [
    {
      Id: 1,
      Title: 'Home',
      Link: `${isAuthenticated ? '/feed' : '/'}`,
      IconPath: <HouseIcon size={24} />,
    },
    { Id: 2, Title: 'Jobs', Link: '/jobs', IconPath: <BriefcaseIcon size={24} /> },
    {
      Id: 3,
      Title: 'Talent',
      Link: '/talent',
      IconPath: <UsersThreeIcon size={24} />,
    },
  ]

  // Add Profile tab only if authenticated
  if (isAuthenticated) {
    BottomTabLinks.push({
      Id: 5,
      Title: 'Profile',
      Link: `/${authUserProfile?.username}`,
      IconPath: <UserIcon size={24} />,
    })
  }

  return (
    <>
      <div
        className={`bg-lucres-50 dark:bg-dark-lucres-black-200 z-45 fixed bottom-0 flex h-16 w-full items-center justify-center lg:hidden ${className}`}
      >
        <div className="flex h-full w-full items-center justify-between px-6">
          {BottomTabLinks.map((link) => (
            <span key={link.Id}>
              <Tooltip text={link.Title} classes="whitespace-nowrap text-center" direction="top">
                {link.IconPath && (
                  <Link
                    href={link.Link}
                    className="text-lucres-900 group relative flex cursor-pointer flex-col items-center justify-center text-lg font-medium md:text-lg"
                  >
                    <IconButton>
                      {React.cloneElement(link.IconPath, {
                        weight: isActive(link) ? 'duotone' : 'regular',
                        color: isActive(link)
                          ? theme === 'dark'
                            ? '#B6E777'
                            : '#3F8D51'
                          : theme === 'dark'
                            ? '#F2F7F3'
                            : '#2D4232',
                      })}
                    </IconButton>
                  </Link>
                )}
              </Tooltip>
            </span>
          ))}
        </div>
      </div>
      <PostDrawer
        toggleBottomSlider={toggleBottomSlider}
        setToggleBottomSlider={setToggleBottomSlider}
      />
    </>
  )
}

export default BottomNavigation
