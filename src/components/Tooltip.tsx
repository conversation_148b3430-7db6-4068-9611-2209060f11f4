'use client'
import React, { useState, ReactNode } from 'react'

interface TooltipProps {
  text: string // Text to be displayed in the tooltip
  children: ReactNode // Child elements to hover over
  direction?: 'top' | 'bottom' | 'left' | 'topleft' | 'right' // Restrict direction values
  classes?: string // Optional additional classes for customization
}

const Tooltip: React.FC<TooltipProps> = ({
  text,
  children,
  direction = 'right', // Default direction
  classes,
}) => {
  const [isHovered, setIsHovered] = useState<boolean>(false)

  const handleMouseEnter = () => {
    setIsHovered(true)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
  }

  const getTooltipPositionClass = (): string => {
    switch (direction) {
      case 'right':
        return 'absolute left-full ml-2 top-1/2 transform -translate-y-1/2'
      case 'left':
        return 'absolute right-full mr-2 top-1/2 transform -translate-y-1/2'
      case 'topleft':
        return 'absolute right-full -mr-4 -top-8 transform -translate-y-1/2'
      case 'top':
        return 'absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2'
      case 'bottom':
        return 'absolute top-full mt-2 left-1/2 transform -translate-x-1/2'
      default:
        return ''
    }
  }

  const getArrowClass = (): string => {
    switch (direction) {
      case 'right':
        return 'absolute right-full top-1/2 transform -translate-y-1/2 dark:border-r-white border-r-gray-900 border-r-[5px] border-transparent border-y-[5px]'
      case 'left':
        return 'absolute left-full top-1/2 transform -translate-y-1/2 dark:border-l-white border-l-gray-900 border-l-[5px] border-transparent border-y-[5px]'
      case 'top':
        return 'absolute top-full left-1/2 transform -translate-x-1/2 dark:border-t-white border-t-gray-900 border-t-[5px] border-transparent border-x-[5px]'
      case 'bottom':
        return 'absolute bottom-full left-1/2 transform -translate-x-1/2 dark:border-b-white border-b-gray-900 border-b-[5px] border-transparent border-x-[5px]'
      default:
        return ''
    }
  }

  return (
    <div
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      aria-describedby="tooltip"
      className="relative overflow-visible"
    >
      {children}
      {isHovered && (
        <div
          className={`${classes} dark:text-dark-lucres-black-500 z-100 shadow-xs whitespace-nowrap rounded-sm bg-gray-900 px-2 py-1 text-xs font-medium text-white dark:bg-white ${getTooltipPositionClass()} min-w-fit`}
        >
          {text}
          <div className={`${getArrowClass()}`}></div>
        </div>
      )}
    </div>
  )
}

export default Tooltip
