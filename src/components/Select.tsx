'use client'
import React, { useState, useEffect, useRef, ReactNode, useMemo } from 'react'
import { useTheme } from '../context/ThemeProvider'
import { CaretDown, CaretUp } from '@phosphor-icons/react'
// Define the type for the options
interface Option {
  label: string // Display label for the option
  value: string // Value for the option
  icon?: ReactNode
}
interface CustomSelectProps {
  options: Option[]
  value?: string
  onChange: (value: string) => void
  placeholder?: string
  label?: string
  error?: string
  required?: boolean
  disabled?: boolean
  parentClassName?: string
  labelClassName?: string
  classes?: string
}
const Select: React.FC<CustomSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = 'Select an option',
  label,
  required,
  error,
  disabled,
  parentClassName = '',
  classes = '',
  labelClassName = '',
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const selectRef = useRef<HTMLDivElement>(null)
  const { theme: currentTheme } = useTheme()
  const mode = currentTheme === 'dark' ? 'dark' : 'light'
  // Memoize selected option to avoid repeated find operations
  const selectedOption = useMemo(
    () => options.find((option) => option.value === value),
    [options, value],
  )

  const toggleDropdown = () => {
    if (disabled) return
    setIsOpen((prev) => !prev)
    // If already open, close it (acts as a toggle)
  }
  const handleOptionClick = (option: Option) => {
    onChange(option.value) // Call the onChange prop with the selected value
    setIsOpen(false)
  }
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])
  const selectClasses = {
    light: 'border-lucres-300  placeholder-lucres-300',
    dark: 'border-dark-lucres-black-200 placeholder-lucres-800',
  }
  const dropdownClasses = {
    light: 'bg-white border-lucres-300',
    dark: 'bg-dark-lucres-black-500 border-dark-lucres-black-300',
  }
  const errorClasses = {
    light: 'text-red-600',
    dark: 'text-red-400',
  }
  return (
    <div className={`flex w-full flex-col justify-end gap-2 ${parentClassName}`} ref={selectRef}>
      {label && (
        <label
          className={`text-lucres-900 dark:text-dark-lucres-green-300 text-sm font-semibold text-opacity-75 ${labelClassName}`}
        >
          {label}
          {required && <span className="text-red-400"> *</span>}
        </label>
      )}
      <div
        className={`relative flex w-full cursor-pointer items-center justify-between rounded-lg border bg-transparent px-4 py-3 ${selectClasses[mode]} ${classes}`}
        onClick={(e) => {
          e.stopPropagation()
          toggleDropdown()
        }}
        style={{
          opacity: disabled ? 0.6 : 1,
          pointerEvents: disabled ? 'none' : 'auto',
        }}
      >
        {selectedOption ? (
          <span className="flex items-center gap-2 text-sm">
            {selectedOption.icon}
            {selectedOption.label}
          </span>
        ) : (
          <span className="text-lucres-800 text-sm">{placeholder}</span>
        )}
        <div className="opacity-50">{isOpen ? <CaretUp /> : <CaretDown />}</div>
        {!disabled && isOpen && (
          <ul
            className={`absolute left-0 top-full z-50 mt-1 w-full overflow-hidden rounded-lg border shadow-lg ${dropdownClasses[mode]}`}
          >
            {options.map((option) => (
              <li
                key={option.value}
                className="text-lucres-gray-700 dark:border-b-dark-lucres-black-300 dark:text-lucres-green-100 dark:hover:bg-dark-lucres-black-400 flex cursor-pointer items-center gap-x-2 border-b p-2 text-sm last:border-0 hover:border-none hover:bg-[#f1f1f1]"
                onClick={(e) => {
                  e.stopPropagation()
                  handleOptionClick(option)
                }}
              >
                {option?.icon}
                {option.label}
              </li>
            ))}
          </ul>
        )}
      </div>
      {error && <p className={`mt-1 pl-4 text-xs capitalize ${errorClasses[mode]}`}>{error}</p>}
    </div>
  )
}
export default React.memo(Select)
