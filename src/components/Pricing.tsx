'use client'
import Button from './Button'
// import { initiateRazorpayPayment } from '../../utils/zazorpay'
import { useState } from 'react'
import { UserService } from '../services/UserService'
import { useToast } from './ToastX'
import { initiateRazorpayPayment } from '../utils/razorpay'
import { AuthProvider, useAuth } from '../context/AuthContext'
import { useRouter } from 'next/navigation'
import { ArrowLeftIcon, CheckIcon } from '@phosphor-icons/react'
import useError from '@/context/ErrorContext'
type PricingPlan = {
  name: string
  price: string
  yearlyPrice?: string
  duration?: string
  tag?: string
  billingCycle?: string
  features: string[]
  postTrial?: string
  buttonText: string
  note?: string
  plan: string
}

type PricingProps = {
  handleClose?: any
  flow: string
  jobId?: string
  applications?: any
  handleSuccess?: (plan?: string) => void | Promise<void>
}

function Pricing({ handleClose, flow, jobId, applications, handleSuccess }: PricingProps) {
  const [isYearly, setIsYearly] = useState(false)
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null)
  const { authUserProfile } = useAuth()
  const toast = useToast()
  const router = useRouter()
  const { handleError } = useError()
  const pricingPlans: PricingPlan[] = [
    {
      name: 'Starter',
      price: flow === 'JOB_DASHBOARD' && authUserProfile?.recruiterTrial === true ? '₹49' : 'FREE',
      tag: '',
      features:
        flow === 'JOB_DASHBOARD' && authUserProfile?.recruiterTrial === true
          ? ['1 unlock / resume downloads']
          : ['5 Free candidate unlocks or resume downloads'],
      postTrial: 'Unlock @ ₹49 + GST',
      buttonText: 'Get Started',
      note:
        flow === 'JOB_DASHBOARD' && authUserProfile?.recruiterTrial === false
          ? 'After the trial, unlock at ₹49 + GST.'
          : undefined,
      plan:
        flow === 'JOB_DASHBOARD' && authUserProfile?.recruiterTrial === true ? 'CLS_49' : 'TRIAL_5',
    },
    {
      name: 'Startup',
      price: '₹350',
      tag: 'Most Popular',
      features: [
        '12 Unlocks / Resume Downloads',
        'Valid for 30 days',
        'Ideal for growing businesses',
        'Unlimited job postings',
      ],
      buttonText: 'Upgrade Plan',
      plan: 'CLS_100',
    },
    {
      name: 'Enterprise',
      price: '₹1700',
      yearlyPrice: '₹10,0000',
      tag: '',
      billingCycle: 'Yearly',
      features: [
        '100 Unlocks / Resume Downloads valid for 120 days',
        'Instant approval for your job postings.',
        'Priority support & seamless hiring experience',
        'Unlimited job postings',
        'Verified Tag',
      ],
      buttonText: 'Upgrade Plan',
      plan: 'ENTERPRISE',
    },
  ]

  const handleBuyContact = async (plan: string) => {
    try {
      const formData = {
        amount: {
          subtotal: 49,
          taxes: {
            gst: 8.82,
          },
          total: 57.82,
        },
        flow: 'JOB_DASHBOARD',
        jobId: jobId,
        applications: applications,
      }
      const response = await UserService.createOrder(formData)
      const result = await initiateRazorpayPayment(
        response.data.payment.processor.orderId,
        response.data._id,
        toast,
        handleClose,
        handleSuccess,
        plan,
      )
    } catch (error) {
      handleError(error)
    } finally {
      setLoadingPlan(null)
    }
  }

  const handleBuy = async (plan: string) => {
    setLoadingPlan(plan)
    if (plan === 'CLS_100') {
      try {
        const response = await UserService.createSubscription(plan)
        await initiateRazorpayPayment(
          response.data.order.payment.processor.orderId,
          response.data.order._id,
          toast,
          handleClose,
          handleSuccess,
          plan,
        )
      } catch (error: any) {
        handleError(error)
      }
    } else if (plan === 'ENTERPRISE') {
      try {
        const response = await UserService.createSubscription(plan)
        await initiateRazorpayPayment(
          response.data.order.payment.processor.orderId,
          response.data.order._id,
          toast,
          handleClose,
          handleSuccess,
          plan,
        )
      } catch (error: any) {
        handleError(error)
      }
    } else if (plan === 'CLS_49') {
      await handleBuyContact(plan)
    } else {
      try {
        await UserService.createSubscription(plan)
        handleSuccess?.()
        toast.success('Subscription created successfully')
      } catch (error: any) {
        handleError(error)
      }
    }
  }
  // const shouldHideCard =
  //   (flow === 'PROFILE' || flow === 'TALENT') &&
  //   plan.plan === 'TRIAL_5' &&
  //   authUserProfile?.recruiterTrial === true

  return (
    <div className="dark:bg-dark-lucres-black-500 flex h-screen w-full flex-col bg-white ">
      {/* Top Navbar Section */}
      <div className="dark:bg-dark-lucres-black-500 mx-auto flex h-20 w-11/12 items-center justify-between bg-white px-6 py-4">
        <Button
          size="small"
          theme="transparent"
          className="py-2!"
          onClick={() => {
            if (handleClose) {
              handleClose()
            } else {
              router.back()
            }
          }}
        >
          <span className="me-2">
            <ArrowLeftIcon size={20} weight="bold" />
          </span>
          Back
        </Button>
        {/* <div className="flex w-full items-center justify-center text-lg font-semibold">
          Plans & Pricing
        </div> */}
      </div>
      {/* Full-Screen Gradient Section */}
      <div className="dark:from-dark-lucres-black-300 dark:via-dark-lucres-black-400 dark:to-dark-lucres-black-300 bg-linear-to-b flex flex-1 flex-col from-[#f1fcf1] via-white to-white py-16">
        <div className="mx-auto flex w-9/12 flex-col items-center justify-center py-6 text-center lg:w-5/12">
          <h3 className="text-lucres-black dark:text-dark-lucres-green-100 text-xl font-semibold">
            Find the Right Talent with Lucres
          </h3>
          <p className="dark:text-lucres-gray-400 text-gray-500">
            Choose the perfect plan to unlock top candidates and download resumes effortlessly.
          </p>
        </div>
        {/* Pricing Cards Section */}
        <div className="mx-auto flex w-9/12 max-w-4xl flex-col justify-center gap-4 md:w-11/12 md:flex-row lg:w-10/12">
          {pricingPlans.map((plan) => (
            <div
              key={plan.name}
              className={`${
                (flow === 'PROFILE' || flow === 'TALENT') &&
                plan.plan === 'TRIAL_5' &&
                authUserProfile?.recruiterTrial === true
                  ? 'hidden'
                  : 'flex'
              } dark:border-dark-lucres-black-200 w-full max-w-xs flex-col rounded-md border p-6 lg:p-8`}
            >
              {/* {plan.billingCycle === 'Yearly' && (
                <div className="flex items-center justify-end gap-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-lucres-gray-400">
                    Yearly
                  </span>
                  <button
                    onClick={() => setIsYearly(!isYearly)}
                    className={`relative flex h-6 w-12 items-center rounded-full p-1 transition ${
                      isYearly
                        ? 'bg-lucres-400 dark:bg-dark-lucres-black-200'
                        : 'bg-gray-200 dark:bg-dark-lucres-black-500'
                    }`}
                  >
                    <div
                      className={`h-5 w-5 transform rounded-full bg-white shadow-md transition dark:bg-dark-lucres-black-100 ${
                        isYearly ? 'translate-x-5' : 'translate-x-0'
                      }`}
                    ></div>
                  </button>
                </div>
              )} */}
              {/* Title and Tag */}
              <div className={`mt-2 flex items-center gap-2`}>
                <h5 className="text-lucres-gray-700 dark:text-lucres-gray-400 font-semibold">
                  {plan.name}
                </h5>
                {plan.tag && !(isYearly && plan.name === 'Enterprise') && (
                  <span className="bg-lucres-400 dark:bg-dark-lucres-black-300 rounded-md bg-opacity-25 px-2 py-0.5 text-sm font-medium">
                    {plan.tag}
                  </span>
                )}
              </div>

              {/* Price */}
              <div className="dark:text-lucres-gray-300 mt-2 flex items-end gap-1 text-gray-900">
                <h3 className="text-xl font-bold">
                  {isYearly && plan.yearlyPrice ? plan.yearlyPrice : plan.price}
                </h3>
                {plan.price !== 'FREE' && <span className="text-sm">+ GST</span>}
              </div>

              {/* Middle Content - Make it grow */}
              <div className="flex flex-1 flex-col justify-between">
                {/* Features List */}
                <ul className="mt-4 flex flex-col gap-1">
                  {plan.features.map((feat, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <span className="mt-1">
                        <CheckIcon
                          size={16}
                          className="dark:text-dark-lucres-green-200 text-[#3f8d51]"
                        />
                      </span>
                      <span className="dark:text-lucres-gray-400 text-sm text-gray-500">
                        {feat}
                      </span>
                    </div>
                  ))}
                </ul>

                {/* Note */}
                {plan.note && (
                  <span className="dark:text-lucres-gray-400 mt-2 pe-16 text-sm text-gray-500">
                    {plan.note}
                  </span>
                )}
              </div>

              {/* Button - Stays at Bottom */}
              <Button
                size="small"
                theme="dark"
                isRectangle
                isLoading={loadingPlan === plan.plan}
                disabled={
                  authUserProfile?.subscription.status === 'ACTIVE' &&
                  authUserProfile?.subscription.plan === plan.plan
                }
                className="mt-6 w-full"
                onClick={() => handleBuy(plan.plan)}
              >
                {plan.buttonText}
              </Button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default Pricing
