import React, { useEffect } from 'react'
import { Paginator } from '../models/Paginator'
import { CaretLeft, CaretRight } from '@phosphor-icons/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface PaginationProps {
  paginator: Paginator
  onPageChange?: (page: number) => void
}

const Pagination: React.FC<PaginationProps> = ({ paginator, onPageChange }) => {
  const { page, pageCount, hasPrevPage, hasNextPage, prev, next } = paginator
  const router = useRouter()

  useEffect(() => {
    // If current page is greater than total pages, redirect to page 1
    if (page > pageCount) {
      const url = new URL(window.location.href)
      const pathSegments = url.pathname.split('/')
      const lastSegment = pathSegments[pathSegments.length - 1]

      if (!isNaN(Number(lastSegment))) {
        pathSegments[pathSegments.length - 1] = '1'
      } else {
        pathSegments.push('1')
      }

      const newPath = pathSegments.join('/')
      router.replace(newPath)
      if (onPageChange) {
        onPageChange(1)
      }
    }
  }, [page, pageCount, router, onPageChange])

  // Generate an array of page numbers with ellipsis if there are too many pages.
  let pagesToDisplay: (number | 'ellipsis')[] = []
  if (pageCount <= 5) {
    pagesToDisplay = Array.from({ length: pageCount }, (_, i) => i + 1)
  } else {
    if (page <= 3) {
      pagesToDisplay = [1, 2, 3, 4, 'ellipsis', pageCount]
    } else if (page >= pageCount - 2) {
      pagesToDisplay = [1, 'ellipsis', pageCount - 3, pageCount - 2, pageCount - 1, pageCount]
    } else {
      pagesToDisplay = [1, 'ellipsis', page - 1, page, page + 1, 'ellipsis', pageCount]
    }
  }

  const handlePageChange = (newPage: number) => {
    if (onPageChange && newPage !== page) {
      onPageChange(newPage)
    }
  }

  const getPreviewUrl = (pageNum: number) => {
    const url = new URL(window.location.href)
    const pathSegments = url.pathname.split('/')
    const lastSegment = pathSegments[pathSegments.length - 1]

    if (!isNaN(Number(lastSegment))) {
      pathSegments[pathSegments.length - 1] = pageNum.toString()
    } else {
      pathSegments.push(pageNum.toString())
    }

    return pathSegments.join('/')
  }

  return (
    <div className="flex justify-center">
      <nav className="flex items-center -space-x-px" aria-label="Pagination">
        {/* Previous Button */}
        {hasPrevPage && prev ? (
          <Link
            href={getPreviewUrl(prev)}
            title={getPreviewUrl(prev)}
            onClick={(e) => {
              e.preventDefault()
              handlePageChange(prev)
            }}
            className="text-lucres-900 min-h-9.5 min-w-9.5 focus:outline-hidden inline-flex items-center justify-center border border-gray-200 px-2.5 py-2 text-sm first:rounded-s-lg last:rounded-e-lg hover:bg-gray-100 dark:border-neutral-700 dark:text-white dark:hover:bg-white/10"
            aria-label="Previous"
          >
            <CaretLeft size={20} />
          </Link>
        ) : (
          <button
            disabled
            className="text-lucres-900 min-h-9.5 min-w-9.5 focus:outline-hidden inline-flex items-center justify-center border border-gray-200 px-2.5 py-2 text-sm first:rounded-s-lg last:rounded-e-lg hover:bg-gray-100 disabled:pointer-events-none disabled:opacity-50 dark:border-neutral-700 dark:text-white dark:hover:bg-white/10"
            aria-label="Previous"
          >
            <CaretLeft size={20} />
          </button>
        )}

        {/* Page Numbers */}
        {pagesToDisplay.map((p, index) => {
          if (p === 'ellipsis') {
            return (
              <div
                key={index}
                className="text-lucres-900 min-h-9.5 min-w-9.5 flex items-center justify-center border border-gray-200 px-3 py-2 text-sm dark:border-neutral-700"
              >
                •••
              </div>
            )
          }
          const pageNum = p as number
          return (
            <Link
              key={index}
              href={getPreviewUrl(pageNum)}
              title={getPreviewUrl(pageNum)}
              onClick={(e) => {
                e.preventDefault()
                handlePageChange(pageNum)
              }}
              className={`min-h-9.5 min-w-9.5 focus:outline-hidden flex items-center justify-center border border-gray-200 px-3 py-2 text-sm first:rounded-s-lg last:rounded-e-lg ${
                p === page
                  ? 'text-lucres-900 dark:bg-dark-lucres-black-200 bg-gray-200'
                  : 'text-lucres-900 hover:bg-gray-100'
              } dark:border-neutral-700 dark:text-white dark:hover:bg-white/10`}
              aria-current={p === page ? 'page' : undefined}
            >
              {p}
            </Link>
          )
        })}

        {/* Next Button */}
        {hasNextPage && next ? (
          <Link
            href={getPreviewUrl(next)}
            title={getPreviewUrl(next)}
            onClick={(e) => {
              e.preventDefault()
              handlePageChange(next)
            }}
            className="text-lucres-900 min-h-9.5 min-w-9.5 focus:outline-hidden inline-flex items-center justify-center border border-gray-200 px-2.5 py-2 text-sm first:rounded-s-lg last:rounded-e-lg hover:bg-gray-100 dark:border-neutral-700 dark:text-white dark:hover:bg-white/10"
            aria-label="Next"
          >
            <CaretRight size={20} />
          </Link>
        ) : (
          <button
            disabled
            className="text-lucres-900 min-h-9.5 min-w-9.5 focus:outline-hidden inline-flex items-center justify-center border border-gray-200 px-2.5 py-2 text-sm first:rounded-s-lg last:rounded-e-lg hover:bg-gray-100 disabled:pointer-events-none disabled:opacity-50 dark:border-neutral-700 dark:text-white dark:hover:bg-white/10"
            aria-label="Next"
          >
            <CaretRight size={20} />
          </button>
        )}
      </nav>
    </div>
  )
}

export default Pagination
