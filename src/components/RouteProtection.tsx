'use client'
import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '@/context/AuthContext'
import { isRouteAccessible, getRedirectPath } from '@/utils/routeProtection'

interface RouteProtectionProps {
  children: React.ReactNode
}

export default function RouteProtection({ children }: RouteProtectionProps) {
  const { isAuthenticated, loading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (loading) return // Wait for auth to load

    const redirectPath = getRedirectPath(pathname, isAuthenticated)
    if (redirectPath) {
      router.replace(redirectPath)
    }
  }, [pathname, isAuthenticated, loading, router])

  // Show loading while auth is being determined
  if (loading) {
    return <div>Loading...</div> // You can replace this with a proper loading component
  }

  // Check if route is accessible
  if (!isRouteAccessible(pathname, isAuthenticated)) {
    return null // Don't render anything while redirecting
  }

  return <>{children}</>
}
