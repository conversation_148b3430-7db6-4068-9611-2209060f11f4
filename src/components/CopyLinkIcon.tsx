'use client'
import { useState, MouseEvent } from 'react'
import { LinkIcon, CheckIcon } from '@phosphor-icons/react'
import Tooltip from './Tooltip'
import IconButton from './IconButton'
import { useToast } from './ToastX'

interface CopyLinkIconProps {
  toCopy: string
}

const CopyLinkIcon = ({ toCopy }: CopyLinkIconProps) => {
  const [copied, setCopied] = useState(false)
  const toast = useToast()

  const handleCopy = (e: MouseEvent) => {
    e.stopPropagation()

    navigator.clipboard
      .writeText(toCopy)
      .then(() => {
        setCopied(true)
        toast.success('Link copied successfully')
        setTimeout(() => setCopied(false), 1500)
      })
      .catch((err) => {
        console.error('Failed to copy: ', err)
      })
  }

  return (
    <Tooltip text="Share" classes="whitespace-nowrap text-center" direction="bottom">
      <IconButton onClick={handleCopy} spanClass="group-hover:bg-lucres-200">
        {copied ? (
          <CheckIcon
            size={19}
            weight="bold"
            className="animate-pop text-lucres-gray-700 dark:text-dark-lucres-green-100"
          />
        ) : (
          <LinkIcon
            size={19}
            weight="bold"
            className="text-lucres-gray-700 dark:text-dark-lucres-green-100"
          />
        )}
      </IconButton>
    </Tooltip>
  )
}

export default CopyLinkIcon
