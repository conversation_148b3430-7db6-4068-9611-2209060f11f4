'use client'
import React, { useEffect, useRef, useState } from 'react'
import SlideUpView from './SlideUpView'
import Button from './Button'
import IconButton from './IconButton'
import { CalendarMinus, Image as IconImage } from '@phosphor-icons/react'
interface PostDrawerProps {
  className?: string
  toggleBottomSlider: boolean
  setToggleBottomSlider: (value: boolean) => void
}
const PostDrawer: React.FC<PostDrawerProps> = ({
  toggleBottomSlider,
  setToggleBottomSlider,
  className,
}) => {
  const [post, setPost] = useState('')
  const textareaRef = useRef<HTMLTextAreaElement>(null) // Specify textarea type
  const [textAreaHeight, setTextAreaHeight] = useState('auto') // State for textarea height
  useEffect(() => {
    if (toggleBottomSlider) {
      // Focus on the textarea when the slider opens
      textareaRef.current?.focus()
      setTextAreaHeight('auto')
    }
  }, [toggleBottomSlider])
  // Automatically adjust height based on content
  const handleInputChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPost(event.target.value)
    const textarea = event.target
    if (textarea.scrollHeight !== textarea.offsetHeight) {
      setTextAreaHeight(`${textarea.scrollHeight}px`)
    }
  }
  return (
    <div
      className={`relative z-50 flex h-full w-full items-center justify-center transition-transform duration-700 lg:hidden ${className}`}
    >
      <SlideUpView isOpen={toggleBottomSlider}>
        <div className="flex w-full items-center justify-between border-b p-3">
          <span
            className="text-lucres-900 dark:text-dark-lucres-green-100 z-50! right-4 cursor-pointer rounded-full text-base font-semibold hover:text-gray-500"
            onClick={() => setToggleBottomSlider(false)}
          >
            Cancel
          </span>
          <h2 className="text-lucres-900 dark:text-dark-lucres-green-100 text-lg font-bold">
            Create a Post
          </h2>
          <Button theme="translucent" size="small">
            Post
          </Button>
        </div>
        <div className="flex items-start gap-x-2 p-4">
          <img
            src="/common/avatar.svg"
            alt="profile"
            className="h-10 w-10 rounded-full object-cover"
          />
          <div className="flex w-full flex-col gap-0.5">
            <span className="text-lucres-900 dark:text-dark-lucres-green-100 text-sm font-semibold">
              Vishwajeet
            </span>
            <textarea
              ref={textareaRef} // Attach ref to textarea
              style={{ height: textAreaHeight }} // Set dynamic height
              onChange={handleInputChange}
              value={post}
              className="text-lucres-900 dark:bg-dark-lucres-black-500 dark:text-dark-lucres-green-200 dark:placeholder:text-dark-lucres-green-200 outline-hidden focus:outline-hidden w-full min-w-full resize-none overflow-hidden text-sm placeholder:text-xs"
              placeholder="What's new?"
            ></textarea>
            <div className="flex items-center gap-x-2">
              <IconButton>
                <IconImage size={20} className="text-lucres-900 dark:text-dark-lucres-green-100" />
              </IconButton>
              <IconButton>
                <CalendarMinus
                  size={20}
                  className="text-lucres-900 dark:text-dark-lucres-green-100"
                />
              </IconButton>
            </div>
          </div>
        </div>
      </SlideUpView>
    </div>
  )
}
export default PostDrawer
