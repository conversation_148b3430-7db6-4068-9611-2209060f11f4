'use client'
import React, { useState, useEffect, useRef, ReactNode } from 'react'
import Button from './Button'

interface InputOtpProps {
  length: number
  onSubmit: (otp: string) => void
  resendTime?: number
  onResendOtp?: () => void
  inputClassName?: string
  resendButtonText?: ReactNode
  placeholder?: string
  isResend?: boolean
  className?: string
  isButton?: boolean
  error?: string
  onClick?: any
}

const InputOtp: React.FC<InputOtpProps> = ({
  length,
  onSubmit,
  inputClassName,
  resendTime = 60,
  onResendOtp,
  resendButtonText = 'Resend OTP',
  isResend,
  className,
  isButton,
  onClick,
  error = '',
  ...props
}) => {
  const [otp, setOtp] = useState<string[]>(new Array(length).fill(''))
  const [timeLeft, setTimeLeft] = useState<number>(resendTime)
  const inputRefs = useRef<Array<HTMLInputElement | null>>([])
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    inputRefs.current[0]?.focus()
    startTimer()
    return () => {
      if (timerRef.current) clearInterval(timerRef.current)
    }
  }, [])

  const startTimer = () => {
    setTimeLeft(resendTime)
    if (timerRef.current) clearInterval(timerRef.current)
    timerRef.current = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          if (timerRef.current) clearInterval(timerRef.current)
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }

  const handleResendOtp = () => {
    if (onResendOtp) onResendOtp()
    setOtp(new Array(length).fill(''))
    inputRefs.current[0]?.focus()
    startTimer()
  }

  const handleChange = (value: string, index: number) => {
    if (!/^\d*$/.test(value)) return
    const newOtp = [...otp]
    newOtp[index] = value
    setOtp(newOtp)

    if (value && index < length - 1) {
      inputRefs.current[index + 1]?.focus()
    }

    if (newOtp.every((val) => val !== '')) {
      onSubmit(newOtp.join(''))
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (event.key === 'Backspace') {
      // event.preventDefault()
      const newOtp = [...otp]

      if (newOtp[index]) {
        newOtp[index] = ''
        setOtp(newOtp)
      } else if (index > 0) {
        inputRefs.current[index - 1]?.focus()
        const updatedOtp = [...otp]
        updatedOtp[index - 1] = ''
        setOtp(updatedOtp)
      }
    }

    if (event.key === 'ArrowLeft' && index > 0) {
      event.preventDefault()
      inputRefs.current[index - 1]?.focus()
    }

    if (event.key === 'ArrowRight' && index < length - 1) {
      event.preventDefault()
      inputRefs.current[index + 1]?.focus()
    }

    if (event.key === 'Enter' && otp.every((val) => val !== '')) {
      onSubmit(otp.join(''))
    }
  }

  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault()
    // const pasteData = event.clipboardData.getData('text').slice(0, length)
    const pasteData = event.clipboardData.getData('text').replace(/\D/g, '').slice(0, length)
    const newOtp = pasteData.split('').concat(new Array(length - pasteData.length).fill(''))
    setOtp(newOtp)

    if (newOtp.every((val) => val !== '')) {
      onSubmit(newOtp.join(''))
    }
  }

  return (
    <div className={`flex w-full flex-col items-center justify-center gap-y-2 ${className}`}>
      <div className="flex space-x-3">
        {otp.map((digit, index) => (
          <input
            {...props}
            key={index}
            ref={(el) => {
              inputRefs.current[index] = el
            }}
            type="text"
            className={`border-lucres-300 text-lucres-900 dark:border-dark-lucres-black-200 dark:text-lucres-100 bg-transparent! focus:outline-hidden block h-8 rounded-lg border text-center text-xl disabled:pointer-events-none disabled:opacity-50 sm:w-8 ${
              inputClassName || ''
            }`}
            maxLength={1}
            value={digit}
            onChange={(e) => handleChange(e.target.value, index)}
            onKeyDown={(e) => handleKeyDown(e, index)}
            onPaste={handlePaste}
            autoFocus={index === 0}
            aria-label={`OTP input ${index + 1}`}
          />
        ))}
      </div>
      {timeLeft > 0 ? (
        <span className="text-sm text-gray-500" aria-live="polite">
          Resend OTP in {timeLeft} sec
        </span>
      ) : (
        <button
          onClick={handleResendOtp}
          className={`dark:text-dark-lucres-green-300 mt-2 text-sm text-gray-500 hover:underline`}
        >
          {resendButtonText}
        </button>
      )}
      <div className="mt-1 w-full">
        {error && <p className="mb-1 text-sm text-red-500">{error}</p>}
        {isButton && (
          <Button
            disabled={otp.some((digit) => digit === '')}
            size="small"
            // theme="translucent"
            className="w-full max-w-[300px] text-xs"
            isRectangle
            onClick={onClick}
          >
            Verify OTP
          </Button>
        )}
      </div>
    </div>
  )
}

export default InputOtp
