'use client'
import { FC, ButtonHTMLAttributes, ReactNode } from 'react'

interface IconButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children?: ReactNode
  linkText?: string
  theme?: 'primary' | 'secondary'
  spanClass?: string
  isDisabled?: boolean
}

const IconButton: FC<IconButtonProps> = ({
  children,
  linkText,
  theme = 'primary',
  spanClass,
  isDisabled = false,
  ...props
}) => {
  return (
    <button
      {...props}
      className={`text-lucres-900 dark:text-lucres-green-100 relative z-0 flex items-center gap-x-2 text-sm font-semibold ${props.className} ${isDisabled && 'cursor-auto'} group`}
    >
      {/* Apply hover effect on both icon and text when either is hovered */}
      <span
        className={`!z-1 flex items-center justify-center border border-transparent ${
          theme === 'primary'
            ? 'dark:group-hover:bg-lucres-800 h-9.5 w-9.5 rounded-full group-hover:bg-gray-100'
            : ''
        } ${spanClass}`}
      >
        {children}
      </span>
      {theme === 'secondary' && (
        <div className="dark:group-hover:bg-lucres-800 absolute left-1/2 top-1/2 z-0 h-9 w-9 -translate-x-1/2 -translate-y-1/2 rounded-full group-hover:bg-gray-100"></div>
      )}
      {linkText && <span className="group-hover:text-base">{linkText}</span>}
    </button>
  )
}

export default IconButton
