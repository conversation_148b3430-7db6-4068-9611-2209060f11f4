'use client'
import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react'
import { CheckCircleIcon, InfoIcon, WarningIcon, XCircleIcon, XIcon } from '@phosphor-icons/react'
import { capitalizeWords } from '../utils/commonUtils'

// Define types
type ToastType = 'success' | 'error' | 'warning' | 'info' | 'description'
type ToastStatus = 'entering' | 'visible' | 'exiting'
type ToastPosition =
  | 'top-left'
  | 'top-center'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-center'
  | 'bottom-right'

interface Toast {
  id: number
  message: string
  description?: string
  type: ToastType
  status: ToastStatus
  duration?: number
}

export type ToastFunction = ((message: string, options?: Partial<Toast>) => void) & {
  success: (message: string, options?: Partial<Toast>) => void
  error: (message: string, options?: Partial<Toast>) => void
  warning: (message: string, options?: Partial<Toast>) => void
  info: (message: string, options?: Partial<Toast>) => void
  description: (message: string, description: string, options?: Partial<Toast>) => void
}

interface ToastContextType {
  toast: ToastFunction
}

// Create context
const ToastContext = createContext<ToastContextType | undefined>(undefined)

// ToastProvider component
export const ToastProvider: React.FC<{
  children: React.ReactNode
  position?: ToastPosition
}> = ({ children, position }) => {
  const [toasts, setToasts] = useState<Toast[]>([])
  const [isHovered, setIsHovered] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const nextIdRef = useRef(0)

  useEffect(() => {
    setIsClient(true)
  }, [])

  const addToast = useCallback((message: string, options: Partial<Toast> = {}) => {
    const id = nextIdRef.current++
    const baseDuration = options.duration ?? 2000
    setToasts((prev) => {
      const visibleToasts = prev.filter((t) => t.status !== 'exiting').length
      const staggerOffset = visibleToasts * 100
      const newToast: Toast = {
        id,
        message,
        type: 'info',
        status: 'entering',
        duration: baseDuration + staggerOffset,
        ...options,
      }
      const updatedToasts = [...prev, newToast]
      return updatedToasts.slice(-3) // Keep only the latest 3
    })
    setTimeout(() => {
      setToasts((prev) =>
        prev.map((toast) => (toast.id === id ? { ...toast, status: 'visible' } : toast)),
      )
    }, 0)
  }, [])

  const dismissToast = useCallback((id: number) => {
    setToasts((prev) =>
      prev.map((toast) => (toast.id === id ? { ...toast, status: 'exiting' } : toast)),
    )
    setTimeout(() => {
      setToasts((prev) => prev.filter((toast) => toast.id !== id))
    }, 300)
  }, [])

  const toast = useCallback(
    (message: string, options: Partial<Toast> = {}) => {
      addToast(message, options)
    },
    [addToast],
  ) as ToastFunction

  toast.success = (message: string, options: Partial<Toast> = {}) =>
    toast(message, { ...options, type: 'success' })
  toast.error = (message: string, options: Partial<Toast> = {}) =>
    toast(message, { ...options, type: 'error' })
  toast.warning = (message: string, options: Partial<Toast> = {}) =>
    toast(message, { ...options, type: 'warning' })
  toast.info = (message: string, options?: Partial<Toast>) =>
    toast(message, { ...options, type: 'info' })
  toast.description = (message: string, description: string, options: Partial<Toast> = {}) =>
    toast(message, { ...options, description, type: 'description' })

  const contextValue: ToastContextType = { toast }

  // Default positioning: bottom-center on mobile, bottom-right on desktop, unless overridden
  const positionClasses = position
    ? `fixed z-[999] ${position === 'top-left' ? 'top-4 left-4' : ''} ${
        position === 'top-center' ? 'top-4 left-1/2 -translate-x-1/2' : ''
      } ${position === 'top-right' ? 'top-4 right-4' : ''} ${
        position === 'bottom-left' ? 'bottom-4 left-4' : ''
      } ${position === 'bottom-center' ? 'bottom-4 left-1/2 -translate-x-1/2' : ''} ${
        position === 'bottom-right' ? 'bottom-4 right-4' : ''
      }`
    : 'fixed z-[999] bottom-24 left-1/2 -translate-x-1/2 md:bottom-4 md:right-4 md:left-auto md:translate-x-0'

  // Only render toast container when there are toasts to show
  const hasToasts = toasts.length > 0

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      {isClient && hasToasts && (
        <div
          className={`${positionClasses} perspective-1000 max-w-[80vw] md:w-80 md:max-w-none`}
          style={{ transformStyle: 'preserve-3d' }}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          aria-live="polite"
        >
          <div className="relative">
            {toasts
              .slice(-3)
              .reverse()
              .map((toast, index) => (
                <ToastComponent
                  key={toast.id}
                  {...toast}
                  index={index}
                  totalToasts={toasts.length}
                  isHovered={isHovered}
                  dismiss={() => dismissToast(toast.id)}
                />
              ))}
          </div>
        </div>
      )}
    </ToastContext.Provider>
  )
}

// ToastComponent for individual toasts
const ToastComponent: React.FC<
  Toast & { index: number; totalToasts: number; isHovered: boolean; dismiss: () => void }
> = ({ message, description, type, status, duration, index, totalToasts, isHovered, dismiss }) => {
  const [isPaused, setIsPaused] = useState(false)

  // Ensure message is a string before passing to capitalizeWords
  const safeMessage = typeof message === 'string' ? message : String(message)

  useEffect(() => {
    if (status === 'visible' && duration && !isPaused) {
      const timer = setTimeout(() => {
        dismiss()
      }, duration)
      return () => clearTimeout(timer)
    }
  }, [status, duration, dismiss, isPaused])

  const typeClasses = {
    success:
      'bg-green-100 text-green-800 border-green-200 dark:bg-green-700 dark:text-white dark:border-green-600',
    error:
      'bg-red-100 text-red-800 border-red-200 dark:bg-red-700 dark:text-white dark:border-red-600',
    warning:
      'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-600 dark:text-white dark:border-yellow-500',
    info: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-700 dark:text-white dark:border-blue-600',
    description:
      'bg-white text-gray-800 border-gray-200 dark:bg-gray-700 dark:text-white dark:border-gray-600',
  }

  const getIcon = (type: ToastType) => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-green-600 dark:text-green-100" />
      case 'error':
        return <XCircleIcon className="h-5 w-5 text-red-600 dark:text-red-200" />
      case 'warning':
        return <WarningIcon className="h-5 w-5 text-yellow-600 dark:text-yellow-300" />
      case 'info':
        return <InfoIcon className="h-5 w-5 text-blue-600 dark:text-blue-200" />
      case 'description':
        return null
      default:
        return null
    }
  }

  const getTransform = () => {
    if (status === 'entering' || status === 'exiting') {
      return 'translateY(100%)'
    } else if (isHovered) {
      return 'translateY(0) scale(1)'
    } else {
      if (index === 0) return 'translateY(0) scale(1)'
      if (index === 1) return 'translateY(0) scale(0.95)'
      if (index === 2) return 'translateY(0) scale(0.90)'
      return 'translateY(0) scale(1)' // Fallback
    }
  }

  const getOpacity = () => {
    if (status === 'entering' || status === 'exiting') {
      return 0
    } else if (isHovered) {
      return 1
    } else {
      return index === 0 ? 1 : 0.9
    }
  }

  const getBottomOffset = () => {
    const baseOffset = description ? (isHovered ? 90 : 30) : isHovered ? 70 : 20
    return index * baseOffset
  }

  return (
    <div
      role="alert"
      className={`absolute right-0 transition-all duration-300 ${
        status === 'entering'
          ? 'ease-toast-enter'
          : status === 'exiting'
            ? 'ease-toast-exit'
            : 'ease-in-out'
      }`}
      style={{
        bottom: `${getBottomOffset()}px`,
        zIndex: totalToasts - index,
        transform: getTransform(),
        opacity: getOpacity(),
      }}
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
    >
      <div
        className={`flex items-center space-x-2 rounded-md border p-4 shadow-lg ${typeClasses[type]} w-auto`}
      >
        {getIcon(type)}
        <div className="flex-1">
          <p className="whitespace-normal text-sm font-medium">{capitalizeWords(safeMessage)}</p>
          {description && (
            <p className="mt-1 whitespace-normal text-sm text-gray-600 dark:text-gray-300">
              {description}
            </p>
          )}
        </div>
        <button
          onClick={dismiss}
          className="text-gray-500 hover:text-gray-700 focus:outline-none dark:text-white dark:hover:text-gray-200"
          aria-label="Close toast"
        >
          <XIcon className="h-4 w-4" />
        </button>
      </div>
    </div>
  )
}

// useToast hook
export const useToast = () => {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context.toast
}
