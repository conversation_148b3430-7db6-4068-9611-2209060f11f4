'use client'
import React, { useState } from 'react'
import { useTheme } from '../context/ThemeProvider'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean
  className?: string
  buttonTheme?: 'light' | 'dark'
  size?: 'extraSmall' | 'small' | 'medium' | 'large'
  theme?: 'transparent' | 'translucent' | 'opaque' | 'white' | 'dark'
  isRectangle?: boolean
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void | Promise<void>
  onAction?: () => void | Promise<void>
  loaderClasses?: string
}

const Button: React.FC<ButtonProps> = ({
  children,
  isLoading = false,
  className,
  loaderClasses,
  size = 'medium',
  theme = 'opaque',
  isRectangle = false,
  onClick,
  onAction,
  ...props
}) => {
  const { theme: currentTheme } = useTheme()
  const mode = currentTheme === 'dark' ? 'dark' : 'light'

  const [loading, setLoading] = useState(isLoading)

  const isDisabled = props.disabled || loading

  const sizeClasses = {
    extraSmall: 'py-2 px-3 text-sm',
    small: 'py-3 px-6 text-sm',
    medium: 'py-4 px-10 text-sm',
    large: 'py-5 px-[61px] text-base',
  }

  const buttonStyleClasses = {
    light: {
      translucent: 'bg-lucres-green-200/35  hover:text-black hover:bg-opacity-45',
      opaque: 'bg-lucres-green-200 hover:bg-lucres-400 text-lucres-green-400',
      transparent: 'bg-transparent border border-solid text-lucres-gray-700 hover:bg-gray-100',
      white: 'bg-white text-black hover:bg-lucres-gray-100',
      dark: 'bg-lucres-green-400 text-white',
    },
    dark: {
      translucent:
        'bg-dark-lucres-black-300 border border-dark-lucres-black-200 bg-opacity-30 hover:bg-opacity-45 hover:text-lime-300',
      opaque: 'bg-dark-lucres-black-300 hover:bg-dark-lucres-black-200 hover:text-lucres-500',
      transparent:
        'bg-transparent border border-solid border-dark-lucres-black-200 hover:bg-dark-lucres-black-200 hover:text-lucres-500',
      white: 'bg-white text-black hover:bg-lucres-gray-100',
      dark: 'bg-dark-lucres-black-300 hover:bg-dark-lucres-black-200 hover:text-lucres-500',
    },
  }

  const combinedClassName = `inline-flex justify-center group overflow-hidden relative whitespace-nowrap items-center box-border font-semibold disabled:opacity-50 disabled:pointer-events-none ${
    isRectangle ? 'rounded-lg' : 'rounded-full'
  } ${sizeClasses[size]} ${buttonStyleClasses[mode][theme]} ${
    isDisabled && theme !== 'transparent' ? 'text-white! bg-lucres-800!' : ''
  } ${className || ''}`

  // Handle click with promise detection
  const handleClick = async (e: React.MouseEvent<HTMLButtonElement>) => {
    if (onClick) {
      const result = onClick(e)

      // Detect if the click handler returns a promise
      if (result && typeof result === 'object' && 'then' in result) {
        setLoading(true)

        try {
          await result // Wait for the promise to resolve
        } catch (error) {
          console.error('Promise failed:', error)
        } finally {
          setLoading(false)
        }
      }
    }
  }

  // const handleClick = async (e: React.MouseEvent<HTMLButtonElement>) => {
  //   if (onClick) {
  //     const result = onClick(e)

  //     if (result && typeof result === 'object' && 'then' in result) {
  //       setLoading(true)
  //       try {
  //         await result
  //       } catch (error) {
  //         console.error('Promise failed:', error)
  //       } finally {
  //         setLoading(false)
  //       }
  //       return
  //     }
  //   }

  //   // Handle onAction separately (if onClick didn't exist or didn't return a promise)
  //   if (onAction) {
  //     const result = onAction()

  //     if (result && typeof result === 'object' && 'then' in result) {
  //       setLoading(true)
  //       try {
  //         await result
  //       } catch (error) {
  //         console.error('Promise failed:', error)
  //       } finally {
  //         setLoading(false)
  //       }
  //     }
  //   }
  // }

  return (
    <button {...props} className={combinedClassName} onClick={handleClick} disabled={isDisabled}>
      {loading ? (
        <span
          className={`inline-block h-3 w-3 animate-spin rounded-full border-[3px] border-current border-t-transparent p-2 ${
            mode === 'dark' ? 'text-white' : 'text-gray-500'
          } ${loaderClasses}`}
        ></span>
      ) : (
        <>
          <div
            className={`group-hover:animate-button-animation -skew-x-45 absolute -left-20 h-24 w-4 bg-white bg-opacity-25`}
          ></div>
          {children}
        </>
      )}
    </button>
  )
}

export default Button
