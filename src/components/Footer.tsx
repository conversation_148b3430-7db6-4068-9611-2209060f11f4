'use client'

import React from 'react'
import FooterLinks from './FooterLinks'
import Logo from './Logo'
import {
  CopyrightIcon,
  InstagramLogoIcon,
  TwitterLogoIcon,
  YoutubeLogoIcon,
  LinkedinLogoIcon,
} from '@phosphor-icons/react'
import { useTheme } from '../context/ThemeProvider'
import { useRouter } from 'next/navigation'

// Define the type for resource and site links
interface Link {
  Id: number
  Title: string
  Link: string
}

const Footer: React.FC = () => {
  const router = useRouter()
  const { theme } = useTheme()

  const ResourceLinks: Link[] = [
    { Id: 1, Title: 'Blogs', Link: '/blogs' },
    { Id: 2, Title: 'About Us', Link: '/about' },
    { Id: 3, Title: 'Contact Us', Link: '/contact' },
    // { Id: 4, Title: 'Frequently Asked Questions', Link: '' },
  ]

  const SiteLinks: Link[] = [
    { Id: 1, Title: 'Sign In', Link: '/sign-in' },
    { Id: 2, Title: 'Terms and Conditions', Link: '/terms' },
    { Id: 3, Title: 'Privacy Policy', Link: '/privacy' },
    { Id: 4, Title: 'FAQs', Link: '/faq' },
  ]

  const socialLinks = [
    {
      icon: <TwitterLogoIcon size={24} />,
      url: 'https://twitter.com/lucresnetwork',
      label: 'Twitter',
    },
    {
      icon: <InstagramLogoIcon size={24} />,
      url: 'https://www.instagram.com/lucresnetwork/',
      label: 'Instagram',
    },
    {
      icon: <YoutubeLogoIcon size={24} />,
      url: 'https://www.youtube.com/@lucresnetwork',
      label: 'YouTube',
    },
    {
      icon: <LinkedinLogoIcon size={24} className="h-6 w-6" />,
      url: 'https://www.linkedin.com/company/lucres/',
      label: 'LinkedIn',
    },
  ]

  return (
    <div>
      <div
        className={`bg-lucres-900 dark:bg-dark-lucres-black-500 flex min-h-full w-full flex-col items-center justify-start px-4 pb-6 pt-12 text-white`}
      >
        <div className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-300 flex h-full w-full max-w-[1440px] flex-col items-start justify-between gap-y-8 rounded-2xl bg-white px-1 py-8 xl:h-64 xl:w-10/12 xl:items-start xl:gap-y-0 xl:px-6">
          <div className="flex w-full flex-col items-start justify-between ps-8 lg:flex-row">
            <div className="flex flex-col gap-x-56 gap-y-4 md:flex-row">
              {theme === 'dark' ? (
                <Logo width={105.03} height={29.1} src="/common/whitelogo.svg" />
              ) : (
                <Logo width={105.03} height={29.1} src="/common/logo2.svg" />
              )}
              <div className="flex flex-col gap-x-20 gap-y-4 md:flex-row">
                <FooterLinks Heading="RESOURCES" Links={ResourceLinks} />
                <FooterLinks Heading="SITE" Links={SiteLinks} />
              </div>
            </div>
          </div>
          <div className="mt-6 flex w-full items-center justify-start gap-x-5 ps-8 md:mt-0 md:justify-end">
            {socialLinks.map((item, index) => (
              <a
                key={index}
                href={item.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-lucres-900 hover:text-lucres-600 dark:text-dark-lucres-green-300 dark:hover:text-lucres-400"
                aria-label={item.label}
              >
                {item.icon}
              </a>
            ))}
          </div>
        </div>

        <div className="font-inter mt-6 flex w-full items-center justify-start gap-x-1 pb-2 text-xs leading-[140%] opacity-50 md:ps-20 lg:w-10/12">
          <span className="flex h-full items-center justify-center">
            <CopyrightIcon className="dark:text-dark-lucres-green-100 flex h-full items-center justify-center" />
          </span>
          <span>{new Date().getFullYear()} Lucres. All Rights Reserved.</span>
        </div>
      </div>
    </div>
  )
}

export default Footer
