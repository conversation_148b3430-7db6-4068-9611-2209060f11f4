interface CustomCheckboxProps {
  onClick: () => void
  label: string
  classname?: string
  checked: boolean // Accept checked state as a prop
}

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({ onClick, label, classname, checked }) => {
  return (
    <label className={`flex cursor-pointer items-center font-normal ${classname}`}>
      {/* Custom Checkbox */}
      <div
        onClick={onClick} // Use the parent's handler directly
        className={`border-lucres-300 dark:border-dark-lucres-black-200 flex h-5 w-5 items-center justify-center rounded-md border-2 transition-colors duration-200 ${
          checked ? 'bg-lucres-500 dark:bg-dark-lucres-black-500' : 'bg-transparent'
        }`}
      >
        {checked && (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-3 w-3 text-white"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M5 12l5 5L20 7" />
          </svg>
        )}
      </div>
      <span className="ml-2" onClick={onClick}>
        {label}
      </span>
    </label>
  )
}

export default CustomCheckbox
