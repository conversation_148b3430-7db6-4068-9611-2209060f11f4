import React from 'react'
import Link from 'next/link'

// Define the types for props
interface FooterLinksProps {
  Heading: string
  Links: {
    Id: number
    Title: string
    Link: string
  }[]
}

const FooterLinks: React.FC<FooterLinksProps> = ({ Heading, Links }) => {
  return (
    <div className="font-inter text-lucres-950 dark:text-dark-lucres-green-300 flex flex-col gap-y-4 leading-[140%]">
      <span className="text-[10px] font-medium tracking-[4%] opacity-70 dark:opacity-60">
        {Heading}
      </span>
      <ul className="flex flex-col gap-y-1">
        {Links.map((link) => (
          <Link
            href={link.Link}
            className="hover:text-lucres-600 cursor-pointer text-xs font-medium"
            key={link.Id}
          >
            {link.Title}
          </Link>
        ))}
      </ul>
    </div>
  )
}

export default FooterLinks
