'use client'
import React, { useEffect, useRef, useState } from 'react'

interface AvatarProps extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, 'src'> {
  // Allow src to be a string, an object with a `url` property, or null.
  src?: string | { url: string } | null | undefined
  // Numeric size for square dimensions (e.g., 14 for h-14 w-14)
  size?: number
  // type determines which fallback images to use
  type?: 'company' | 'user'
}

const Avatar: React.FC<AvatarProps> = ({
  src,
  size = 14,
  type = 'user',
  alt = 'Avatar',
  className = '',
  onClick,
  ...props
}) => {
  // Use relative paths to the SVG files in the public directory
  const fallbackLight =
    type === 'company'
      ? '/avatars/company-default-avatar-light.svg'
      : '/avatars/user-default-avatar-light.svg'
  const fallbackDark =
    type === 'company'
      ? '/avatars/company-default-avatar-dark.svg'
      : '/avatars/user-default-avatar-dark.svg'

  const modalRef = useRef<HTMLDivElement>(null)

  const [showProfilePicture, setShowProfilePicture] = useState<boolean>(false)
  // Extract the URL if src is an object.
  const imageSrc = typeof src === 'string' ? src : src?.url

  const sizeClasses = `h-${size} w-${size}`
  const baseClasses = `rounded-full z-20 overflow-hidden ${sizeClasses} ${className}`.trim()

  // Default onClick handler if none is provided
  const handleProfileClick =
    onClick ||
    (() => {
      setShowProfilePicture(true)
    })

  // Close modal when clicking outside
  const handleClickOutside = (event: React.MouseEvent<HTMLDivElement>) => {
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      setShowProfilePicture(false)
    }
  }

  // Disable scrolling when modal is open
  useEffect(() => {
    if (showProfilePicture) {
      document.body.style.overflow = 'hidden' // Prevent scrolling
    } else {
      document.body.style.overflow = '' // Restore scrolling
    }

    return () => {
      document.body.style.overflow = '' // Cleanup on unmount
    }
  }, [showProfilePicture])
  return (
    <>
      <div
        onClick={handleProfileClick}
        className={`inline-block ${onClick ? 'cursor-pointer' : ''}`}
      >
        {imageSrc ? (
          <img src={imageSrc} alt={alt} className={baseClasses} {...props} />
        ) : (
          <>
            <img
              src={fallbackLight}
              alt={alt}
              className={`${baseClasses} dark:hidden`}
              {...props}
            />
            <img
              src={fallbackDark}
              alt={alt}
              className={`${baseClasses} hidden dark:block`}
              {...props}
            />
          </>
        )}
      </div>
      {showProfilePicture && (
        <div
          className="bg-dark-lucres-black-300/60 fixed inset-0 !z-50 flex items-center justify-center"
          onClick={handleClickOutside}
        >
          <div
            ref={modalRef}
            className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 z-50 flex h-auto w-80 flex-col rounded-full border bg-white"
          >
            {imageSrc ? (
              <img src={imageSrc} alt={alt} className="rounded-full" />
            ) : (
              <>
                <img
                  src={fallbackLight}
                  alt={alt}
                  className={`z-50 h-auto w-full rounded-full dark:hidden`}
                />
                <img src={fallbackDark} alt={alt} className={`hidden rounded-full dark:block`} />
              </>
            )}
          </div>
        </div>
      )}
    </>
  )
}

export default Avatar
