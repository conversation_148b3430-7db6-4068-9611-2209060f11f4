// components/ContentWithMentions.tsx
import React, { useState } from 'react'
import { useRouter } from 'next/navigation'

interface ContentWithMentionsProps {
  text: string
  truncateLength?: number
}

const ContentWithMentions: React.FC<ContentWithMentionsProps> = ({
  text,
  truncateLength = 150,
}) => {
  const router = useRouter()
  const [showFullText, setShowFullText] = useState(false)

  const toggleText = (e: any) => {
    e.stopPropagation()
    setShowFullText((prev) => !prev)
  }

  const renderText = (rawText: string) => {
    const parts = rawText.split(/(?<=^|\s)(@\w+\b)/g)

    return parts.map((part, index) => {
      const isMention = /^@\w+$/.test(part)

      if (isMention) {
        return (
          <span
            key={index}
            className="text-lucres-gray-700 dark:text-dark-lucres-green-300 cursor-pointer break-words hover:underline"
            onClick={(e) => {
              e.stopPropagation()
              router.push(`/${part.slice(1)}`)
            }}
          >
            {part}
          </span>
        )
      }

      return <span key={index}>{part}</span>
    })
  }

  const shouldTruncate = text.length > truncateLength
  const visibleText = showFullText || !shouldTruncate ? text : text.slice(0, truncateLength)

  return (
    <div className="w-full">
      <div className="text-lucres-950 dark:text-dark-lucres-green-100 break-words! w-full">
        {renderText(visibleText)}
        {shouldTruncate && (
          <button onClick={toggleText} className="">
            ...
            <span className="text-lucres-gray-700 dark:text-dark-lucres-green-100 hover:underline">
              {showFullText ? 'Read Less' : 'Read More'}
            </span>
          </button>
        )}
      </div>
    </div>
  )
}

export default ContentWithMentions
