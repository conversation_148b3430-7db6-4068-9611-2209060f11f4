'use client'
import React, { useState, useCallback } from 'react'
import <PERSON><PERSON>per from 'react-easy-crop'
import { getCroppedImg } from '../utils/imageUtils' // Ensure this returns Blob
import <PERSON>ton from './Button'
import Modal from './Modal'

interface ImageCropModalProps {
  image: string
  type: 'profile' | 'cover'
  onClose: () => void
  onCropComplete: (croppedFile: File) => void
}

const ImageCropModal: React.FC<ImageCropModalProps> = ({
  image,
  type,
  onClose,
  onCropComplete,
}) => {
  const [crop, setCrop] = useState({ x: 0, y: 0 })
  const [zoom, setZoom] = useState(1)
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<{
    x: number
    y: number
    width: number
    height: number
  } | null>(null)
  const [loading, setLoading] = useState(false)

  const onCropChange = (crop: { x: number; y: number }) => setCrop(crop)
  const onZoomChange = (zoom: number) => setZoom(zoom)

  const onCropAreaChange = useCallback(
    (
      _: { x: number; y: number; width: number; height: number },
      croppedAreaPixels: { x: number; y: number; width: number; height: number },
    ) => {
      setCroppedAreaPixels(croppedAreaPixels)
    },
    [],
  )

  const handleCrop = async () => {
    if (!croppedAreaPixels) return

    setLoading(true)
    try {
      const croppedBlob = await getCroppedImg(image, croppedAreaPixels)

      // Create File from Blob
      const croppedFile = new File([croppedBlob], 'cropped.png', {
        type: croppedBlob.type || 'image/png',
      })

      onCropComplete(croppedFile)
      onClose()
    } catch (error) {
      console.error('Error cropping image:', error)
      alert('Failed to crop the image. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Modal onClose={onClose} crossRequired={true} classes="w-full max-w-lg h-fit!">
      <div className="flex h-full w-full flex-col items-center justify-center">
        <div className="relative h-[400px] w-full">
          <Cropper
            image={image}
            crop={crop}
            zoom={zoom}
            aspect={type === 'profile' ? 1 : 10 / 3}
            cropShape={type === 'profile' ? 'round' : 'rect'}
            onCropChange={onCropChange}
            onZoomChange={onZoomChange}
            onCropComplete={onCropAreaChange}
          />
        </div>
        <div className="mt-4 flex justify-end">
          <Button onClick={handleCrop} size="small" disabled={loading}>
            {loading ? 'Processing...' : 'Crop & Upload'}
          </Button>
        </div>
      </div>
    </Modal>
  )
}

export default ImageCropModal
