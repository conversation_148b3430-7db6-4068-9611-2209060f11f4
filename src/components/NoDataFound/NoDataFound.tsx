interface NoDataFoundProps {
  subtitle?: string
}

const NoDataFound: React.FC<NoDataFoundProps> = ({
  subtitle = 'No results found. Try adjusting your filters or search for something else.',
}) => {
  return (
    <div className="flex flex-col items-center justify-center">
      <img src="/nodata-light.svg" alt="No Data Found" className="mb-2 h-72 dark:hidden" />
      <img src="/nodata-dark.svg" alt="No Data Found" className="mb-2 hidden h-72 dark:block" />
      <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200">No Data Found</h2>
      <p className="mb-14 mt-2 text-center text-gray-600 dark:text-gray-400">{subtitle}</p>
    </div>
  )
}

export default NoDataFound
