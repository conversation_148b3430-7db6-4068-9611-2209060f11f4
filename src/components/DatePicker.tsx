import { CaretLeft, CaretRight } from '@phosphor-icons/react'
import {
  add,
  eachDayOfInterval,
  eachMonthOfInterval,
  endOfMonth,
  endOfYear,
  format,
  isEqual,
  isFuture,
  parse,
  startOfMonth,
  startOfToday,
  startOfWeek,
  endOfWeek,
} from 'date-fns'
import { useEffect, useRef, useState } from 'react'

// Utility function to concatenate class names, filtering out falsy values
function cn(...classes: (string | undefined | null | false)[]) {
  return classes.filter(Boolean).join(' ')
}

type ViewMode = 'days' | 'months' | 'years'

interface DatePickerProps {
  label?: string
  selectedDate?: Date | null
  onDateChange: (newDate: Date) => void
  isOpen: boolean
  onClose: () => void
  onOpen?: () => void
  maxDate?: Date // For birth date, typically today's date
}

export default function DatePicker({
  selectedDate,
  onDateChange,
  isOpen,
  onClose,
  onOpen,
  maxDate = startOfToday(),
}: DatePickerProps) {
  const datePickerRef = useRef<HTMLDivElement>(null)
  const [viewMode, setViewMode] = useState<ViewMode>('days')
  const [currentMonth, setCurrentMonth] = useState(
    selectedDate ? startOfMonth(selectedDate) : startOfMonth(startOfToday()),
  )
  const [yearRangeStart, setYearRangeStart] = useState(0)

  const minYear = 1900
  const maxYear = parseInt(format(maxDate, 'yyyy'))
  const currentYear = parseInt(format(currentMonth, 'yyyy'))

  // Call onOpen when the component mounts
  useEffect(() => {
    if (onOpen) {
      onOpen()
    }
  }, [onOpen])

  // Handle clicks outside to close the picker
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target as Node)) {
        onClose()
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [onClose])

  if (!isOpen) return null

  // Generate calendar days
  const monthStart = startOfMonth(currentMonth)
  const monthEnd = endOfMonth(currentMonth)
  const startDate = startOfWeek(monthStart)
  const endDate = endOfWeek(monthEnd)

  const days = eachDayOfInterval({ start: startDate, end: endDate })

  // Generate months for year view
  const months = eachMonthOfInterval({
    start: parse(currentYear.toString(), 'yyyy', new Date()),
    end: endOfYear(parse(currentYear.toString(), 'yyyy', new Date())),
  })

  // Generate years for decade view
  const years = Array.from({ length: 12 }, (_, i) => yearRangeStart + i)

  // Handle navigation based on view mode
  function previous() {
    switch (viewMode) {
      case 'days':
        setCurrentMonth(add(currentMonth, { months: -1 }))
        break
      case 'months':
        setCurrentMonth(add(currentMonth, { years: -1 }))
        break
      case 'years':
        setYearRangeStart(yearRangeStart - 12)
        break
    }
  }

  function next() {
    switch (viewMode) {
      case 'days':
        setCurrentMonth(add(currentMonth, { months: 1 }))
        break
      case 'months':
        setCurrentMonth(add(currentMonth, { years: 1 }))
        break
      case 'years':
        setYearRangeStart(yearRangeStart + 12)
        break
    }
  }

  // Check if next button should be disabled
  function isNextDisabled() {
    switch (viewMode) {
      case 'days':
        return add(currentMonth, { months: 1 }) > maxDate
      case 'months':
        return currentYear >= maxYear
      case 'years':
        return yearRangeStart + 11 >= maxYear
      default:
        return false
    }
  }

  // Handle header click to change view mode
  function handleHeaderClick() {
    switch (viewMode) {
      case 'days':
        setViewMode('months')
        break
      case 'months':
        setViewMode('years')
        setYearRangeStart(Math.max(minYear, currentYear - 5))
        break
      case 'years':
        setViewMode('days')
        break
    }
  }

  // Get header text based on view mode
  function getHeaderText() {
    switch (viewMode) {
      case 'days':
        return format(currentMonth, 'MMMM yyyy')
      case 'months':
        return format(currentMonth, 'yyyy')
      case 'years':
        return `${yearRangeStart} - ${yearRangeStart + 11}`
      default:
        return ''
    }
  }

  // Handle date selection
  function handleDateSelect(date: Date) {
    onDateChange(date)
    onClose()
  }

  // Handle month selection
  function handleMonthSelect(month: Date) {
    setCurrentMonth(month)
    setViewMode('days')
  }

  // Handle year selection
  function handleYearSelect(year: number) {
    const newMonth = parse(year.toString(), 'yyyy', currentMonth)
    setCurrentMonth(newMonth)
    setViewMode('months')
  }

  return (
    <div
      ref={datePickerRef}
      className="border-lucres-300/40 font-grotesk dark:border-dark-lucres-black-200 focus:outline-hidden rounded-md border bg-white p-3 dark:bg-[#181818]"
    >
      <div className="flex flex-col space-y-4">
        {/* Header */}
        <div className="relative flex items-center justify-center pt-1">
          <button
            className="rounded px-2 py-1 text-sm font-medium text-black transition-colors hover:bg-slate-100 dark:text-white dark:hover:bg-slate-800"
            aria-live="polite"
            onClick={handleHeaderClick}
          >
            {getHeaderText()}
          </button>
          <div className="flex items-center space-x-1">
            <button
              name="previous"
              aria-label="Go to previous"
              className="focus-visible:ring-ring dark:border-dark-lucres-black-200 shadow-xs focus-visible:outline-hidden absolute left-1 inline-flex h-7 w-7 items-center justify-center gap-2 whitespace-nowrap rounded-md border p-0 text-sm font-medium transition-colors hover:bg-slate-100 hover:text-slate-900 focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"
              type="button"
              onClick={previous}
            >
              <CaretLeft className="h-4 w-4" />
            </button>
            <button
              name="next"
              aria-label="Go to next"
              className="focus-visible:ring-ring dark:border-dark-lucres-black-200 shadow-xs focus-visible:outline-hidden absolute right-1 inline-flex h-7 w-7 items-center justify-center gap-2 whitespace-nowrap rounded-md border p-0 text-sm font-medium transition-colors hover:bg-slate-100 hover:text-slate-900 focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"
              type="button"
              disabled={isNextDisabled()}
              onClick={next}
            >
              <CaretRight size={16} />
            </button>
          </div>
        </div>

        {/* Content based on view mode */}
        {viewMode === 'days' && (
          <div className="space-y-2">
            {/* Day headers */}
            <div className="grid grid-cols-7 gap-1 text-center text-xs font-medium text-slate-500 dark:text-slate-400">
              <div>Su</div>
              <div>Mo</div>
              <div>Tu</div>
              <div>We</div>
              <div>Th</div>
              <div>Fr</div>
              <div>Sa</div>
            </div>

            {/* Days grid */}
            <div className="grid grid-cols-7 gap-1">
              {days.map((day) => {
                const isCurrentMonth = format(day, 'M') === format(currentMonth, 'M')
                const isSelected = selectedDate && isEqual(day, selectedDate)
                const isToday = isEqual(day, startOfToday())
                const isFutureDate = isFuture(day) || day > maxDate

                return (
                  <div key={day.toString()} className="relative p-0 text-center">
                    <button
                      className={cn(
                        'focus-visible:outline-hidden inline-flex h-8 w-8 items-center justify-center rounded-md p-0 text-sm font-normal ring-offset-white transition-colors focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:ring-offset-slate-950',
                        isCurrentMonth
                          ? 'hover:bg-lucres-100 dark:hover:bg-dark-lucres-black-300 text-black dark:text-white'
                          : 'text-slate-400 hover:bg-slate-50 dark:text-slate-600 dark:hover:bg-slate-800',
                        isSelected &&
                          'bg-slate-900 text-slate-50 hover:bg-slate-900 hover:text-slate-50 focus:bg-slate-900 focus:text-slate-50 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50 dark:hover:text-slate-900',
                        !isSelected &&
                          isToday &&
                          'bg-slate-100 text-slate-900 dark:bg-slate-800 dark:text-slate-50',
                      )}
                      disabled={isFutureDate}
                      onClick={() => handleDateSelect(day)}
                    >
                      <time dateTime={format(day, 'yyyy-MM-dd')}>{format(day, 'd')}</time>
                    </button>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {viewMode === 'months' && (
          <div className="grid w-full grid-cols-4 gap-2 font-medium">
            {months.map((month) => {
              const isSelectedMonth =
                selectedDate && format(month, 'yyyy-MM') === format(selectedDate, 'yyyy-MM')
              const isCurrentMonth = format(month, 'yyyy-MM') === format(startOfToday(), 'yyyy-MM')

              return (
                <div
                  key={month.toString()}
                  className="relative p-0 text-center text-sm font-medium text-black focus-within:relative focus-within:z-20 dark:text-white"
                >
                  <button
                    className={cn(
                      'hover:bg-lucres-100 dark:hover:bg-dark-lucres-black-300 focus-visible:outline-hidden inline-flex h-9 w-16 items-center justify-center rounded-md p-0 text-sm font-normal ring-offset-white transition-colors focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:ring-offset-slate-950 dark:hover:text-slate-50 dark:focus-visible:ring-slate-800',
                      isSelectedMonth &&
                        'bg-slate-900 text-slate-50 hover:bg-slate-900 hover:text-slate-50 focus:bg-slate-900 focus:text-slate-50 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50 dark:hover:text-slate-900 dark:focus:bg-slate-50 dark:focus:text-slate-900',
                      !isSelectedMonth &&
                        isCurrentMonth &&
                        'bg-slate-100 text-slate-900 dark:bg-slate-800 dark:text-slate-50',
                    )}
                    disabled={month > maxDate}
                    onClick={() => handleMonthSelect(month)}
                  >
                    {format(month, 'MMM')}
                  </button>
                </div>
              )
            })}
          </div>
        )}

        {viewMode === 'years' && (
          <div className="grid w-full grid-cols-4 gap-2 font-medium">
            {years.map((year) => {
              const isSelectedYear = selectedDate && parseInt(format(selectedDate, 'yyyy')) === year
              const isCurrentYear = parseInt(format(startOfToday(), 'yyyy')) === year

              return (
                <div
                  key={year}
                  className="relative p-0 text-center text-sm font-medium text-black focus-within:relative focus-within:z-20 dark:text-white"
                >
                  <button
                    className={cn(
                      'hover:bg-lucres-100 dark:hover:bg-dark-lucres-black-300 focus-visible:outline-hidden inline-flex h-9 w-16 items-center justify-center rounded-md p-0 text-sm font-normal ring-offset-white transition-colors focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:ring-offset-slate-950 dark:hover:text-slate-50 dark:focus-visible:ring-slate-800',
                      isSelectedYear &&
                        'bg-slate-900 text-slate-50 hover:bg-slate-900 hover:text-slate-50 focus:bg-slate-900 focus:text-slate-50 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50 dark:hover:text-slate-900 dark:focus:bg-slate-50 dark:focus:text-slate-900',
                      !isSelectedYear &&
                        isCurrentYear &&
                        'bg-slate-100 text-slate-900 dark:bg-slate-800 dark:text-slate-50',
                    )}
                    disabled={year > maxYear}
                    onClick={() => handleYearSelect(year)}
                  >
                    {year}
                  </button>
                </div>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}
