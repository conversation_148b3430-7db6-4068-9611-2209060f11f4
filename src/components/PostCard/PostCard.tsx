'use client'
import React, { <PERSON><PERSON><PERSON>, ReactNode, useEffect, useRef, useState } from 'react'
import {
  BookmarkSimpleIcon,
  ChatCircleIcon,
  HeartIcon,
  RepeatIcon,
  TrashIcon,
  PencilSimpleLineIcon,
  DotIcon,
  DotsThreeIcon,
  WarningIcon,
  PencilLineIcon,
} from '@phosphor-icons/react'
import { useRouter, useParams } from 'next/navigation'

import IconButton from '../IconButton'
import Tooltip from '../Tooltip'
// import Input from '../Input'
import Button from '../Button'

import SlideUpView from '../SlideUpView'
import Avatar from '../Avatar/Avatar'
import { capitalizeWords, getFullName, truncateText } from '../../utils/commonUtils'
import { useAuth } from '../../context/AuthContext'
// import ImageFullScreenModal from './ImageFullScreenModal'
import type { PostCard as PostCardType, PostUser } from '../../models/Post'
import ImageCarousel from './ImageCarousel'
import ActionMenu from '../ActionMenu'
import CreatePost from '../../app/feed/CreatePost'
import { PostService } from '../../services/PostService'
import Comments from './Comments'
import TimeAgo from '../TimeAgo'
import { useToast } from '../ToastX'
import CopyLinkIcon from '../CopyLinkIcon'

import Overlay from '../Overlay'
import { PermalinkUser, UserProfile } from '../../models/User'
// import Like from '../LikeN'
import CreateQuoteModal from '../QuoteCard/CreateQuote'
import Like from '../Like'
import ContentWithMentions from '../ContentWithMentions'
import useError from '../../context/ErrorContext'

type PostCardProps = {
  postData: PostCardType
  setPostData?: React.Dispatch<React.SetStateAction<PostCardType | undefined>>
  fetchData?: any
  isCommentsOpen?: boolean
  isRepostedBy?: PostUser
  setData?: React.Dispatch<React.SetStateAction<PostCardType[]>>
  isViewPost?: boolean
  isProfilePage?: boolean
  data?: PostCardType[]
}

const PostCard = ({
  postData,
  fetchData,
  isCommentsOpen,
  isRepostedBy,
  setData,
  data,
  setPostData,
  isViewPost,
  isProfilePage,
}: PostCardProps) => {
  const { isLiked, article } = postData
  const [showFullDescription, setShowFullDescription] = useState<boolean>(false)
  const [confirmDeletePost, setConfirmDeletePost] = useState(false)
  const [editPost, setEditPost] = useState(false)
  const [showComments, setShowComments] = useState<boolean>(isCommentsOpen || false)
  const { authUserProfile: userProfile, isAuthenticated } = useAuth()
  const [commentsCount, setCommentsCount] = useState(postData.article?.commentsCount || 0)
  // const [repostCount, setRepostCount] = useState<number | undefined>(postData.article?.rePostCount)
  // const [isReposted, setIsReposted] = useState<boolean>(postData.isReposted)
  const [toggleBottomSlider, setToggleBottomSlider] = useState<boolean>(false)
  const [showQuoteModal, setShowQuoteModal] = useState(false)
  // const [showTag,setShowTag] = useState(false)
  // const [repostedBy, setRepostedBy] = useState(isRepostedBy)
  // Intersection Observer setup
  const postCardRef = useRef<HTMLDivElement | null>(null)
  const router = useRouter()
  const { handleError } = useError()
  const params = useParams()
  // const location = useLocation()
  const username = params.username as string
  const toast = useToast()
  const handleOpenComment = (e: any) => {
    e.stopPropagation()
    setShowComments(!showComments)
    setToggleBottomSlider(true)
  }

  const handleDeletePost = async () => {
    try {
      if (!postData.article?.id) return

      await PostService.deletePost(postData.article.id)

      setData?.((prev) =>
        prev.filter((post) => {
          // Safe access with optional chaining
          const isOriginalPost = post.article?.id === postData.article?.id
          const isRepostOfDeleted =
            post.type === 'REPOST' && post.repost?.repostedItem?.id === postData.article?.id

          return !(isOriginalPost || isRepostOfDeleted)
        }),
      )

      if (isViewPost) {
        router.back()
      }

      toast.success('Post Deleted')
      setConfirmDeletePost(false)
    } catch (err) {
      // toast.error('Delete post failed')
      handleError(err)
      setConfirmDeletePost(false)
    }
  }

  useEffect(() => {
    if (confirmDeletePost) {
      document.body.classList.add('overflow-hidden')
    } else {
      document.body.classList.remove('overflow-hidden')
    }

    // Cleanup in case component unmounts
    return () => {
      document.body.classList.remove('overflow-hidden')
    }
  }, [confirmDeletePost])

  const handleNavigateToViewPost = (id: number) => {
    if (!isAuthenticated) {
      router.push('/sign-in')
      return
    }
    if (isViewPost) return
    router.push(`/feed/post/${id}`)
  }
  const handleNavigateToProfile = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation()
    if (!isAuthenticated) {
      router.push('/sign-in')
      return
    }
    router.push(`/${postData.article?.user.username}`)
  }

  const handleRepostPost = async () => {
    try {
      if (!postData.article?.id) return

      await PostService.rePostPost(postData.article.id)

      const updatedData = {
        ...postData,
        isReposted: true,
        article: {
          ...postData.article,
          rePostCount: (postData.article.rePostCount ?? 0) + 1,
        },
      }

      // Update view post data
      if (isViewPost) {
        setPostData?.(updatedData)
      }

      // Update post list
      setData?.((prevPosts) =>
        prevPosts.map((post) => {
          // Original post update
          if (post.article?.id === postData.article?.id) {
            return {
              ...post,
              ...updatedData,
            }
          }

          // Repost update
          if (
            post.type === 'REPOST' &&
            post.repost &&
            post.repost?.repostedItem?.id === postData.article?.id
          ) {
            return {
              ...post,
              isReposted: true,
              repost: {
                ...post.repost,
                repostedItem: {
                  ...post.repost.repostedItem,
                  rePostCount: (post.repost.repostedItem?.rePostCount ?? 0) + 1,
                },
              },
            } as PostCardType
          }

          return post
        }),
      )

      toast.success('Post Reposted')
    } catch (error) {
      handleError(error)
    }
  }

  const handleUndoRepostPost = async () => {
    try {
      const id = postData.article?.id
      if (!id) return

      if (!isAuthenticated) {
        router.push('/sign-in')
        return
      }

      await PostService.undoRepost(id)

      // For view post only
      const updatedData = {
        ...postData,
        isReposted: false,
        article: {
          ...postData.article!,
          rePostCount: Math.max((postData.article?.rePostCount ?? 1) - 1, 0),
        },
      }

      if (isViewPost && postData) {
        setPostData?.(updatedData)
      }

      setData?.((prevPosts) => {
        const updatedPosts = prevPosts.map((post) => {
          // Update original post
          if (post.article?.id === id) {
            return {
              ...post,
              isReposted: false,
              article: {
                ...post.article,
                rePostCount: Math.max((post.article?.rePostCount ?? 1) - 1, 0),
              },
            } as PostCardType
          }

          // Update repost entries
          if (post.type === 'REPOST' && post.repost?.repostedItem?.id === id) {
            // ✅ Added ? after repostedItem
            return {
              ...post,
              isReposted: false,
              repost: {
                ...post.repost,
                repostedItem: {
                  ...post.repost.repostedItem,
                  rePostCount: Math.max((post.repost.repostedItem?.rePostCount ?? 1) - 1, 0),
                },
              },
            } as PostCardType
          }

          return post
        })

        // Filter out reposts on profile page
        if (isProfilePage && userProfile && userProfile.username === username) {
          return updatedPosts.filter((post) => {
            if (post.type !== 'REPOST') return true
            return post.repost?.repostedItem?.id !== id // ✅ Added ? after repostedItem
          })
        }

        return updatedPosts
      })

      toast.success('Repost Removed.')
    } catch (error) {
      toast.error('Failed to undo repost.')
      handleError(error)
    }
  }

  const getRepostTag = (
    postData: PostCardType,
    userProfile: PermalinkUser,
    allPosts: PostCardType[],
  ): ReactNode | null => {
    const repostedBy = new Set<string>()
    const currentUserFullName = `${userProfile.givenName} ${userProfile.familyName}`.trim()

    // Skip for ARTICLE reposts
    if (
      postData.type === 'ARTICLE' &&
      allPosts.some(
        (p) => p.type === 'REPOST' && p.repost?.repostedItem?.id === postData.article?.id,
      )
    ) {
      return null
    }

    // Collect other reposters
    allPosts.forEach((p) => {
      if (
        p.type === 'REPOST' &&
        p.repost?.repostedItem?.id === postData.article?.id &&
        p.repost?.repostedBy?.username
      ) {
        if (p.repost.repostedBy.username !== userProfile.username) {
          repostedBy.add(
            truncateText(
              capitalizeWords(
                `${p.repost.repostedBy.givenName} ${p.repost.repostedBy.familyName}`.trim(),
              ),
              15, // or any max length you prefer
            ).text,
          )
        }
      }
    })

    // Include current user if they reposted
    if (postData.isReposted) {
      repostedBy.add(currentUserFullName)
    }

    // Format output
    const usernames = Array.from(repostedBy)
    const youIncluded = usernames.includes(currentUserFullName)
    const others = usernames.filter((name) => name !== currentUserFullName)

    if (others.length > 0 && youIncluded) {
      return (
        <div
          className="flex items-center gap-x-1"
          //  onClick={() => navigate(`/${isRepostedBy?.username}`)}
        >
          <RepeatIcon /> {others[0]} and You reposted
        </div>
      )
    } else if (others.length > 0) {
      return (
        <div
          className="flex items-center gap-x-1"
          //  onClick={() => navigate(`/${isRepostedBy?.username}`)}
        >
          <RepeatIcon /> {others[0]} reposted
        </div>
      )
    } else if (youIncluded) {
      return (
        <div
          className="flex items-center gap-x-1"
          //  onClick={() => navigate(`/${userProfile.username}`)}
        >
          <RepeatIcon /> You reposted
        </div>
      )
    }

    return null
  }

  const handleSyncPostLike = (id: string, liked: boolean) => {
    setData?.((prev) =>
      prev.map((item) => {
        if (postData.type === 'REPOST' && item.type === 'ARTICLE' && item.article?.id === id) {
          const prevCount = item.article.likesCount ?? 0
          const newCount = liked ? prevCount + 1 : Math.max(prevCount - 1, 0)

          return {
            ...item,
            isLiked: liked,
            article: {
              ...item.article,
              likesCount: newCount,
            },
          }
        }
        if (
          postData.type === 'ARTICLE' &&
          item.type === 'REPOST' &&
          item.repost?.repostedItem?.id === id
        ) {
          const prevCount = item.repost.repostedItem?.likesCount ?? 0
          const newCount = liked ? prevCount + 1 : Math.max(prevCount - 1, 0)

          return {
            ...item,
            isLiked: liked,
            repost: {
              ...item.repost,
              repostedItem: {
                ...item.repost.repostedItem,
                likesCount: newCount,
              },
            },
          }
        }

        return item
      }),
    )
  }

  const rollbackPostLike = (id: string, prevLiked: boolean, prevCount: number) => {
    setData?.((prev) =>
      prev.map((item) => {
        if (item.type === 'ARTICLE' && item.article?.id === id) {
          return {
            ...item,
            isLiked: prevLiked,
            article: {
              ...item.article,
              likesCount: prevCount,
            },
          }
        }

        if (item.type === 'REPOST' && item.repost?.repostedItem?.id === id) {
          return {
            ...item,
            isLiked: prevLiked,
            repost: {
              ...item.repost,
              repostedItem: {
                ...item.repost.repostedItem,
                likesCount: prevCount,
              },
            },
          }
        }

        return item
      }),
    )
  }

  async function handleTogglePostLike(postData: PostCardType, newLiked: boolean) {
    const articleId = postData.article?.id
    if (!articleId) {
      return
    }

    const prevCount = postData.article?.likesCount ?? 0
    handleSyncPostLike(articleId, newLiked)

    try {
      if (newLiked) {
        await PostService.likePost(articleId)
      } else {
        await PostService.dislikePost(articleId)
      }
    } catch (error) {
      rollbackPostLike(articleId, !newLiked, prevCount)
      handleError(error)
    }
  }

  return (
    <div className="dark:border-dark-lucres-black-300 w-full rounded-lg border">
      <div
        ref={postCardRef}
        className={`flex w-full ${
          isViewPost ? '' : 'cursor-pointer'
        } flex-col items-start justify-start p-3 pb-1`}
        onClick={(e) => {
          if (postData.article?.articleCode) {
            e.stopPropagation()
            handleNavigateToViewPost(postData.article.articleCode)
          }
        }}
      >
        <div className="cursor-pointer ps-5">
          {isViewPost && (
            <div className="flex items-center gap-x-1 text-sm">
              {postData.isReposted && (
                <div className="text-lucres-gray-700 dark:text-dark-lucres-green-300 flex items-center gap-x-1 pb-0.5">
                  <RepeatIcon /> You reposted
                </div>
              )}
            </div>
          )}
          {data && userProfile && (
            <div className="text-lucres-gray-700 dark:text-dark-lucres-green-300 flex items-center gap-x-1 pb-0.5 text-sm">
              {getRepostTag(postData, userProfile, data)}
            </div>
          )}
        </div>

        <div className="flex w-full items-start justify-start">
          <div className="min-w-14">
            <Avatar
              src={article?.user?.profileImage?.url || ''}
              alt={`${article?.user?.profileImage?.name}'s Avatar`}
              size={12}
              className="cursor-pointer object-cover"
            />
          </div>
          <div className="flex w-10/12 md:w-11/12">
            <div className="flex w-full flex-col justify-between">
              <div className="flex w-full items-start justify-between">
                <div className="flex flex-col items-start md:flex-row md:items-center">
                  <span
                    className="text-lucres-black dark:text-dark-lucres-green-100 cursor-pointer  font-semibold hover:underline"
                    onClick={handleNavigateToProfile}
                  >
                    {article?.user && getFullName(article.user.givenName, article.user.familyName)}
                  </span>
                  <div className="text-lucres-gray-700 dark:text-dark-lucres-green-300 flex items-center text-sm">
                    <DotIcon
                      weight="bold"
                      size={14}
                      className="text-lucres-gray-700 dark:text-dark-lucres-green-100 hidden md:block"
                    />
                    @{article?.user?.username}
                    <DotIcon
                      weight="bold"
                      size={14}
                      className="text-lucres-gray-700 dark:text-dark-lucres-green-100"
                    />
                    {postData.article && <TimeAgo dateString={postData.article?.createdAt} />}
                  </div>
                </div>
                {userProfile?.username === article?.articlePoster?.username && (
                  <ActionMenu
                    classes="top-3 right-10!"
                    trigger={
                      <IconButton theme="secondary">
                        <DotsThreeIcon
                          size={19}
                          weight="bold"
                          className="text-lucres-gray-700 dark:text-dark-lucres-green-100"
                        />
                      </IconButton>
                    }
                    menuItems={[
                      {
                        label: 'Edit',
                        icon: <PencilSimpleLineIcon size={19} weight="bold" />,
                        onClick: (close: any) => {
                          setEditPost(true)
                          close()
                        },
                      },
                      {
                        label: 'Delete',
                        icon: <TrashIcon size={19} weight="bold" />,
                        className: 'text-red-500',
                        onClick: (close: any) => {
                          setConfirmDeletePost(true)
                          close()
                        },
                      },
                    ]}
                  />
                )}
              </div>
              <div>
                <div className="">
                  {article?.content && <ContentWithMentions text={article?.content} />}
                </div>
                <div className="w-full sm:w-11/12">
                  {article?.images &&
                    article.images.length > 0 &&
                    postData.article?.articleCode !== undefined && (
                      <div className="mt-4 w-full">
                        <ImageCarousel
                          images={article.images}
                          // id={postData.article.articleCode}
                          id={`carousel-${Math.random().toString(36).substring(2, 9)}`}
                        />
                      </div>
                    )}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-1 flex w-full items-start justify-between ps-12">
          <div className="flex items-start justify-start gap-6">
            {/* <div className="ms-0.5 hidden w-4  lg:flex "></div> */}
            <Like
              isLiked={postData.isLiked}
              likeCount={postData.article?.likesCount ?? 0}
              onToggleLike={(newLiked: boolean) => handleTogglePostLike(postData, newLiked)}
            />

            <div className="flex items-center">
              <IconButton onClick={handleOpenComment}>
                <ChatCircleIcon
                  size={19}
                  weight="bold"
                  className={`text-lucres-gray-700 dark:text-dark-lucres-green-100 cursor-pointer rounded-md`}
                />
              </IconButton>
              <span className="text-lucres-gray-700 dark:text-dark-lucres-green-100">
                {/* {commentsCount} */}
                {postData.article?.commentsCount}
              </span>
            </div>
            <div className="flex items-center">
              <ActionMenu
                classes="top-10 w-40 left-1/2! -translate-x-1/2"
                trigger={
                  <IconButton>
                    <RepeatIcon
                      size={19}
                      //  weight={isReposted ? 'fill' : 'regular'}
                      weight="bold"
                      className={`${
                        postData.isReposted
                          ? 'text-lucres-700'
                          : 'text-lucres-gray-700 dark:text-dark-lucres-green-100'
                      }`}
                    />
                  </IconButton>
                }
                menuItems={[
                  {
                    label: postData.isReposted ? 'Undo repost' : 'Repost',
                    icon: <RepeatIcon size={19} weight="bold" />,
                    onClick: (close: any) => {
                      close()
                      if (postData.isReposted) {
                        // Handle undo repost action here
                        handleUndoRepostPost() // You would need to define this function to undo the repost
                      } else {
                        // Handle repost action here
                        handleRepostPost() // Your existing repost function
                      }
                    },
                  },
                  {
                    label: 'Quote',
                    icon: <PencilLineIcon size={19} weight="bold" />,
                    onClick: (close: any) => {
                      close()
                      setShowQuoteModal(true)
                    },
                  },
                ]}
              />
              <span
                className={`${
                  postData.isReposted
                    ? 'text-lucres-700'
                    : 'text-lucres-gray-700 dark:text-dark-lucres-green-100'
                }`}
              >
                {(postData.article?.rePostCount ?? 0) + (postData.article?.quoteCount ?? 0)}
              </span>
            </div>
            <CopyLinkIcon
              toCopy={`${location.origin}/feed/post/${postData.article?.articleCode}`}
            />
          </div>
          {/* <div className="">
            <Tooltip text={'Save'} classes="whitespace-nowrap   text-center" direction="bottom">
              <IconButton>
                <BookmarkSimpleIcon
                  weight="bold"
                  size={19}
                  className="text-lucres-gray-700 dark:text-dark-lucres-green-100 cursor-pointer rounded-md"
                />
              </IconButton>
            </Tooltip>
          </div> */}
        </div>
      </div>
      {showComments && (
        <div className={`hidden w-full items-center md:flex`}>
          {postData.article?.id && (
            <Comments
              postId={postData.article?.id}
              // setCommentsCount={setCommentsCount}
              type="POST"
              setData={setData}
              setPostData={setPostData}
              isViewPost={isViewPost}
            />
          )}
        </div>
      )}
      {toggleBottomSlider && (
        <div className="z-999! relative flex w-full flex-col items-center justify-center transition-transform duration-700 md:hidden">
          <SlideUpView isOpen={toggleBottomSlider}>
            <div className="h-full w-full flex-1">
              {postData.article?.id && (
                <Comments
                  postId={postData.article?.id}
                  // setCommentsCount={setCommentsCount}
                  handleClose={() => setToggleBottomSlider(false)}
                  setData={setData}
                  setPostData={setPostData}
                  type="POST"
                  isViewPost={isViewPost}
                />
              )}
            </div>
          </SlideUpView>
        </div>
      )}
      {/* Edit a post */}
      {editPost && (
        <div className="fixed inset-0 bottom-0 left-0 z-50 flex h-screen w-full items-start justify-center bg-black bg-opacity-75">
          <CreatePost
            handleClose={() => setEditPost(false)}
            showPostModal={editPost}
            postData={postData}
            fetchFeedData={fetchData}
            setPostData={setPostData}
            setData={setData}
            // feedData={feed}
          />
        </div>
      )}

      {/* Quote a post */}
      {showQuoteModal && (
        <Overlay
          heading="Quote and Repost"
          size="min-w-xl! max-w-xl! min-h-fit!"
          handleClose={() => {}}
        >
          <CreateQuoteModal
            post={postData}
            setData={setData}
            handleClose={() => setShowQuoteModal(false)}
            type="ARTICLE"
            isViewPost={isViewPost}
            setPostData={setPostData}
          />
        </Overlay>
      )}

      {confirmDeletePost && (
        <Overlay
          heading="Delete Post"
          handleClose={() => setConfirmDeletePost(false)}
          size="min-h-fit mt-20"
          classes="p-0! "
        >
          <div className="dark:bg-dark-lucres-black-500 w-full rounded-lg bg-white p-4 shadow-lg">
            <div className="flex flex-col items-center">
              <WarningIcon size={32} />
              <h2 className="mt-3 text-center text-lg font-semibold">Delete This Post?</h2>
              <p className="text-center text-sm text-gray-500 dark:text-gray-300">
                Are you sure you want to delete this post?
              </p>
            </div>
            <div className="dark:border-t-dark-lucres-black-200 mt-4 flex justify-between gap-4 border-t pt-4">
              <Button
                size="small"
                theme="transparent"
                className="px-4! py-1.5! !border"
                onClick={() => setConfirmDeletePost(false)}
              >
                Cancel
              </Button>
              <Button
                size="small"
                theme="dark"
                className="px-4! py-1.5! text-white!"
                onClick={handleDeletePost}
              >
                Yes, Delete
              </Button>
            </div>
          </div>
        </Overlay>
      )}
    </div>
  )
}

export default React.memo(PostCard)
