import Modal from '../Modal'
// import IconButton from '../IconButton'
// import { useEffect, useState } from 'react'
// import { PostService } from '../../services/PostService'
// import { useNavigate, useParams } from 'react-router'
// import { PostCard } from '../../models/Post'
import ImageCarousel, { ImageType } from './ImageCarousel'
// import ImageFullScreenModalSkeleton from './ImageFullScreenModalSkeleton'
interface ImageFullScreenModalProps {
  activeIndex: number
  closeModal: (e: React.MouseEvent<HTMLElement>) => void
  images: ImageType[]
  id: number | string
}

const ImageFullScreenModal: React.FC<ImageFullScreenModalProps> = ({
  activeIndex,
  closeModal,
  images,
  id,
}) => {
  // const [postData, setPostData] = useState<PostCard>()
  // const navigate = useNavigate()
  // const [loading, setLoading] = useState(true)
  // const [commentsCount, setCommentsCount] = useState(0)
  // const { id } = useParams()
  // const closeModal = () => {
  //   // navigate('/feed')
  // }
  // useEffect(() => {
  //   const fetchPostData = async () => {
  //     try {
  //       if (!id) return // make sure id exists

  //       const response = await PostService.getPostById(id)
  //       if (response.status === 'success') {
  //         setPostData(response.data)
  //         // setCommentsCount(response.data?.article?.commentsCount)
  //         setLoading(false)
  //       } else {

  //       }
  //     } catch (error) {
  //       console.error('Error fetching post:', error)
  //     }
  //   }

  //   fetchPostData()
  // }, [id])
  // const name = `${postData?.article?.user.givenName ?? ''} ${postData?.article?.user.familyName ?? ''}`
  return (
    <Modal
      onClose={closeModal}
      classes=" md:w-screen w-full!    dark:bg-dark-lucres-black-500 max-w-full p-0! rounded-none max-h-screen! h-screen!   overflow-hidden!"
    >
      <div className="flex h-screen max-h-full w-full flex-col items-center justify-center">
        <div className="relative h-full w-full lg:h-full xl:w-9/12">
          <div className="relative w-full">
            {images && images.length > 0 && (
              <ImageCarousel
                images={images}
                id={id}
                isFullScreenModal={true}
                activeIndex={activeIndex}
              />
            )}
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default ImageFullScreenModal
