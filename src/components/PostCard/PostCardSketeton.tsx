const PostCardSkeleton = () => {
  return (
    <div className="dark:border-dark-lucres-black-300 flex w-full  animate-pulse flex-col items-start justify-start gap-2 rounded-xl border p-4">
      <div className="flex w-full items-center gap-3">
        <div className="h-11 w-11 rounded-full bg-gray-300 dark:bg-gray-700" />
        <div>
          <div className="flex flex-wrap items-center gap-2">
            <div className="h-4 w-24 rounded-sm bg-gray-300 dark:bg-gray-700" />
            <div className="h-3 w-16 rounded-sm bg-gray-300 dark:bg-gray-700" />
          </div>
          <div className="mt-1 h-3 w-20 rounded-sm bg-gray-300 dark:bg-gray-700" />
        </div>
      </div>

      <div className="flex w-full flex-row">
        <div className="hidden w-[5.5%] lg:flex"></div>
        <div className="w-[94.5%] space-y-2">
          <div className="space-y-2">
            <div className="h-4 w-full rounded-sm bg-gray-300 dark:bg-gray-700" />
            <div className="h-4 w-4/5 rounded-sm bg-gray-300 dark:bg-gray-700" />
          </div>

          <div className="mt-2 h-60 w-full rounded-md bg-gray-300 dark:bg-gray-700" />

          <div className="mt-4 flex flex-wrap-reverse items-start justify-between">
            <div className="flex items-center gap-6">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center gap-2">
                  <div className="h-5 w-5 rounded-full bg-gray-300 dark:bg-gray-700" />
                  <div className="h-3 w-4 rounded-sm bg-gray-300 dark:bg-gray-700" />
                </div>
              ))}
              <div className="h-5 w-5 rounded-full bg-gray-300 dark:bg-gray-700" />
            </div>

            <div className="h-5 w-5 rounded-full bg-gray-300 dark:bg-gray-700" />
          </div>
        </div>
      </div>
    </div>
  )
}

export default PostCardSkeleton
