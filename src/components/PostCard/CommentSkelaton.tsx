const SkeletonComment = () => {
  return (
    <div className="flex animate-pulse flex-col gap-4 p-2">
      {Array.from({ length: 2 }).map((_, idx) => (
        <div key={idx} className="flex gap-2">
          {/* Avatar skeleton */}
          <div className="dark:bg-dark-lucres-black-300 h-9 w-9 rounded-full bg-gray-300" />

          {/* Right side skeleton */}
          <div className="flex flex-1 flex-col gap-2">
            {/* Name and timestamp */}
            <div className="flex w-full justify-between">
              <div className="dark:bg-dark-lucres-black-300 h-4 w-1/3 rounded-sm bg-gray-300" />
              <div className="dark:bg-dark-lucres-black-300 h-4 w-10 rounded-sm bg-gray-300" />
            </div>

            {/* Comment text */}
            <div className="dark:bg-dark-lucres-black-300 h-3 w-full rounded-sm bg-gray-300" />
            <div className="dark:bg-dark-lucres-black-300 h-3 w-3/4 rounded-sm bg-gray-300" />
            <div className="dark:bg-dark-lucres-black-300 h-3 w-1/2 rounded-sm bg-gray-300" />

            {/* Buttons/Icons */}
            <div className="mt-2 flex gap-4">
              <div className="dark:bg-dark-lucres-black-300 h-4 w-4 rounded-sm bg-gray-300" />
              <div className="dark:bg-dark-lucres-black-300 h-4 w-4 rounded-sm bg-gray-300" />
              <div className="dark:bg-dark-lucres-black-300 h-4 w-4 rounded-sm bg-gray-300" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export default SkeletonComment
