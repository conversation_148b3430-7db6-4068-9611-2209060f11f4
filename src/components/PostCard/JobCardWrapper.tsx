import { DotIcon, RepeatIcon } from '@phosphor-icons/react'
import React from 'react'
import Avatar from '../Avatar/Avatar'
import { capitalizeWords, getFullName, truncateText } from '@/utils/commonUtils'
import TimeAgo from '../TimeAgo'
import JobCard from '@/app/jobs/JobCard'
import { useRouter } from 'next/navigation'
import { Job } from '@/models/Job'
import { PostCard } from '@/models/Post'
export interface JobCardWrapperProps {
  postData?: PostCard
  setData?: React.Dispatch<React.SetStateAction<PostCard[]>>
}
const JobCardWrapper: React.FC<JobCardWrapperProps> = ({ postData, setData }) => {
  // console.log(postData)
  const router = useRouter()
  const job = postData?.job
  const handleJobCardClick = (job: Job) => {
    router.push(`/job/${job?.job?.permalink}`)
  }
  return (
    <div className="dark:border-dark-lucres-black-300 h-full  rounded-xl border   p-3">
      <div className="flex items-center gap-x-1 ps-5 text-sm">
        <div className="text-lucres-gray-700 dark:text-dark-lucres-green-300  flex items-center gap-x-1 pb-0.5">
          {postData?.isReposted && (
            <>
              <RepeatIcon />
              <span>You reposted</span>
            </>
          )}
        </div>
      </div>
      <div className="flex items-start gap-x-0.5">
        <div className="min-w-14">
          <Avatar
            src={job.jobPoster.profileImage.url || ''}
            alt={`${job.jobPoster.profileImage.name}'s Avatar`}
            size={12}
            className="cursor-pointer object-cover"
          />
        </div>
        <div className="flex w-11/12 flex-col">
          <div className="flex flex-col  items-start sm:flex-row sm:items-center">
            <span
              className="text-lucres-black dark:text-dark-lucres-green-100 cursor-pointer text-lg font-bold hover:underline"
              onClick={() => router.push(`/${job.user.username}`)}
            >
              {job && getFullName(job.jobPoster.givenName, job.jobPoster.familyName)}
            </span>
            <DotIcon
              weight="bold"
              size={14}
              className="text-lucres-gray-700 dark:text-dark-lucres-green-100 hidden sm:block"
            />
            <div className="flex">
              <div className="text-lucres-gray-700 dark:text-dark-lucres-green-300 flex items-center text-sm">
                @{job.jobPoster.username}
              </div>
              <DotIcon
                weight="bold"
                size={14}
                className="text-lucres-gray-700 dark:text-dark-lucres-green-100"
              />
              {job && <TimeAgo dateString={job.createdAt} />}
            </div>
          </div>
          <div className="mt-2 block">
            <JobCard
              key={postData?.id}
              onClick={handleJobCardClick}
              setData={setData}
              job={{
                job: {
                  ...job,
                  name: job.title,
                  img: job?.user?.companyLogo?.url || '',
                  headline: `${job.title || 'Unknown'}, ${job.company?.name || 'Unknown'}`,
                  location: `${job.location?.address?.city || ''}, ${job.location?.address?.country || ''}`,
                  skills: job.skills?.map((s: any) => (typeof s === 'string' ? s : s.name)),
                },
                isRePosted: postData?.isReposted,
                isLiked: postData?.isLiked,
                isApplied: postData?.isApplied,
              }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default JobCardWrapper
