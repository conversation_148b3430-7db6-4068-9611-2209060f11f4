import { DotIcon, RepeatIcon } from '@phosphor-icons/react'
import type { PostCard as PostCardType } from '../../models/Post'
import JobCard from '../../app/jobs/JobCard'
import { capitalizeWords, getFullName, truncateText } from '../../utils/commonUtils'
import Avatar from '../Avatar/Avatar'
import QuoteCard from '../QuoteCard/QuoteCard'
import PostCard from './PostCard'
import TimeAgo from '../TimeAgo'
import { useAuth } from '../../context/AuthContext'
// import { Job } from '../../models/Job'
import { useRouter } from 'next/navigation'
type RepostCardProps = {
  repostData: PostCardType
  fetchData: any
  isCommentsOpen?: boolean
  setData: React.Dispatch<React.SetStateAction<PostCardType[]>>
  isProfilePage?: boolean
  data?: PostCardType[]
}
const RepostCard = ({ repostData, fetchData, setData, isProfilePage, data }: RepostCardProps) => {
  const { authUserProfile } = useAuth()
  const router = useRouter()
  if (!repostData.repost) {
    return null
  }

  const handleJobCardClick = () => {
    router.push(`/job/${repostData.repost?.repostedItem?.permalink}`)
  }
  const job = repostData && repostData.repost.repostedItem
  return (
    <div>
      {repostData.repost?.type === 'ARTICLE' ? (
        <PostCard
          postData={{
            id: repostData.repost.repostedItem.id,
            type: 'REPOST',
            article: repostData.repost.repostedItem,
            isLiked: repostData.isLiked,
            isReposted: repostData.isReposted,
          }}
          fetchData={fetchData}
          isRepostedBy={repostData.repost.repostedBy}
          setData={setData}
          isProfilePage={isProfilePage}
          data={data}
        />
      ) : repostData.repost?.type === 'QUOTE' ? (
        <QuoteCard
          quoteData={{
            id: repostData.repost.repostedItem.id,
            type: 'REPOST',
            isLiked: repostData.isLiked,
            isReposted: repostData.isReposted,
            quote: {
              id: repostData.repost.repostedItem.id,
              quoteCode: repostData.repost.repostedItem.quoteCode,
              content: repostData.repost.repostedItem.content,
              createdAt: repostData.repost.repostedItem.createdAt,
              updatedAt: repostData.repost.repostedItem.updatedAt,
              likesCount: repostData.repost.repostedItem.likesCount,
              commentsCount: repostData.repost.repostedItem.commentsCount,
              quoteCount: repostData.repost.repostedItem.quoteCount,
              rePostCount: repostData.repost.repostedItem.rePostCount,
              type: 'QUOTE',
              quotedBy: repostData.repost.repostedItem.quotedBy,
              quotedFrom: repostData.repost.repostedItem.quotedFrom,
            },
          }}
          setData={setData}
          data={data}
        />
      ) : (
        <div className="dark:border-dark-lucres-black-300 h-full rounded-xl border p-3">
          <div className="flex items-center gap-x-1 ps-5 text-sm">
            <div className="text-lucres-gray-700 dark:text-dark-lucres-green-300 flex items-center gap-x-1 pb-0.5">
              <RepeatIcon />
              {repostData.isReposted &&
              repostData.repost.repostedBy.username !== authUserProfile?.username
                ? `${
                    truncateText(
                      capitalizeWords(
                        `${repostData.repost.repostedBy.givenName} ${repostData.repost.repostedBy.familyName}`.trim(),
                      ),
                      15,
                    ).text
                  } and You reposted`
                : repostData.isReposted
                  ? 'You reposted'
                  : `${
                      truncateText(
                        capitalizeWords(
                          `${repostData.repost.repostedBy.givenName} ${repostData.repost.repostedBy.familyName}`.trim(),
                        ),
                        15,
                      ).text
                    } reposted`}
            </div>
          </div>
          <div className="flex items-start gap-x-0.5">
            <div className="min-w-14">
              <Avatar
                src={repostData.repost.repostedBy.profileImage.url || ''}
                alt={`${repostData.repost.repostedBy.profileImage.name}'s Avatar`}
                size={12}
                className="cursor-pointer object-cover"
              />
            </div>
            <div className="flex w-11/12 flex-col">
              <div className="flex flex-col  items-start sm:flex-row sm:items-center">
                <span
                  className="text-lucres-black dark:text-dark-lucres-green-100 cursor-pointer text-lg font-bold hover:underline"
                  onClick={() => router.push(`/${repostData.repost?.repostedBy.username}`)}
                >
                  {repostData &&
                    getFullName(
                      repostData.repost.repostedBy.givenName,
                      repostData.repost.repostedBy.familyName,
                    )}
                </span>
                <DotIcon
                  weight="bold"
                  size={14}
                  className="text-lucres-gray-700 dark:text-dark-lucres-green-100 hidden sm:block"
                />
                <div className="flex">
                  <div className="text-lucres-gray-700 dark:text-dark-lucres-green-300 flex items-center text-sm">
                    @{repostData.repost.repostedBy.username}
                  </div>
                  <DotIcon
                    weight="bold"
                    size={14}
                    className="text-lucres-gray-700 dark:text-dark-lucres-green-100"
                  />
                  {repostData.repost.repostedItem.createdAt && (
                    <TimeAgo dateString={repostData.repost.repostedItem.createdAt} />
                  )}
                </div>
              </div>
              <div className="mt-2 block">
                <JobCard
                  onClick={handleJobCardClick}
                  repostedBy={repostData.repost.repostedBy}
                  setData={setData}
                  job={{
                    job: {
                      ...job,
                      id: job.id,
                      name: job.title,
                      permalink: job.permalink,
                      author: job.id,
                      img: job?.user?.companyLogo?.url || '',
                      headline: `${job.designation || 'Unknown'}, ${
                        job.company?.name || 'Unknown'
                      }`,
                      location: `${job.location?.address?.city || ''}, ${
                        job.location?.address?.country || ''
                      }`,
                      employmentType: job.employmentType,
                      workExperience: job.workExperience,
                      degree: job.degree?.name ?? '',
                      skills: job.skills?.map((s: string | { name: string }) =>
                        typeof s === 'string' ? s : s.name,
                      ),
                      description:
                        job.description.length > 100
                          ? job.description.slice(0, 100) + '...'
                          : job.description,
                      salary: {
                        value: String(job.salary?.max ?? ''),
                        min: job.salary?.min,
                        max: job.salary?.max,
                        period: job.salary?.period,
                      },
                      createdAt: job.createdAt,
                    },
                    isLiked: repostData.isLiked,
                    isRePosted: repostData.isReposted,
                    isApplied: repostData.isApplied,
                    ownJob: job.username === authUserProfile?.username,
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
export default RepostCard
