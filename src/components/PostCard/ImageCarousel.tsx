'use client'
import { Swiper, SwiperSlide } from 'swiper/react'
import { FreeMode, Pagination, Autoplay, Navigation } from 'swiper/modules'
//@ts-ignore
import 'swiper/css'
//@ts-ignore
import 'swiper/css/free-mode'
//@ts-ignore
import 'swiper/css/pagination'
import IconButton from '../IconButton'
import { CaretLeftIcon, CaretRightIcon } from '@phosphor-icons/react'
import { useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '../../context/AuthContext'
import ImageFullScreenModal from './ImageFullScreenModal'

export type ImageType = {
  mimetype: string
  secureUrl: string
  width: number
  height: number
}

type ImageCarouselProps = {
  images: ImageType[]
  id: number | string
  isFullScreenModal?: boolean
  activeIndex?: number
  isCreateQuote?: boolean
}

const ImageCarousel = ({
  images,
  id,
  isFullScreenModal,
  activeIndex = 0,
  isCreateQuote,
}: ImageCarouselProps) => {
  const [currentIndex, setCurrentIndex] = useState<number>(activeIndex)
  const router = useRouter()
  const swiperRef = useRef<any>(null)
  const { isAuthenticated } = useAuth()
  const [slidesPerView, setSlidesPerView] = useState<number>(1)
  const [showModal, setShowModal] = useState<boolean>(false)
  useEffect(() => {
    if (isFullScreenModal) {
      setSlidesPerView(1)
    } else {
      setSlidesPerView(images.length === 1 ? 1 : 2)
    }
  }, [images.length, isFullScreenModal])

  const isFirstSlide = currentIndex === 0
  const isLastSlide = currentIndex === images.length - 1

  const handleImageClick = (e: React.MouseEvent<HTMLElement>, index: number) => {
    e.stopPropagation()
    e.preventDefault()
    if (!isAuthenticated) {
      router.push('/sign-in')
      return
    }
    if (isFullScreenModal) {
      return
    }
    setCurrentIndex(index)
    setShowModal(true)
  }

  // Sync slide index to URL on change
  const handleSlideChange = (swiper: any) => {
    const index = swiper.activeIndex
    setCurrentIndex(index)
    // navigate(`/feed/post/${id}?photo=${index}`, { replace: true })
  }

  const handleCloseModal = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation()
    setShowModal(false)
  }

  useEffect(() => {
    if (swiperRef.current) {
      swiperRef.current.update()
    }
  }, [slidesPerView, images.length])

  if (!images || images.length === 0) return null

  return (
    <>
      <div className="cursor-default!">
        <Swiper
          onSwiper={(swiper) => (swiperRef.current = swiper)}
          // slidesPerView={slidesPerView}
          slidesPerGroup={1}
          spaceBetween={10}
          speed={1000}
          onChange={handleSlideChange}
          modules={[FreeMode, Pagination, Autoplay, Navigation]}
          initialSlide={activeIndex}
          onSlideChange={handleSlideChange}
          navigation={{
            nextEl: `.next-btn-${id}`,
            prevEl: `.prev-btn-${id}`,
          }}
          breakpoints={{
            300: {
              slidesPerView: 1,
              // spaceBetween: 20,
            },
            768: {
              slidesPerView: isFullScreenModal ? 1 : images.length === 1 ? 1 : 2,
              // spaceBetween: 30,
            },
            // // Large screens
            // 1024: {
            //   slidesPerView: 3,
            //   spaceBetween: 40,
            // },
          }}
          pagination={{
            clickable: true,
            el: `.custom-pagination-${id}`,
            bulletClass: 'custom-bullet',
            bulletActiveClass: 'custom-bullet-active',
          }}
          className={`h-full w-full`}
        >
          {images.map((item, index) => (
            <SwiperSlide key={index} className="w-full">
              <div
                className={`flex ${
                  isFullScreenModal
                    ? 'dark:bg-dark-lucres-black-500 h-screen items-start justify-start'
                    : 'rounded-md'
                } `}
              >
                {(item.mimetype === 'image/png' || item.mimetype === 'image/jpeg') && (
                  <img
                    onClick={(e) => handleImageClick(e, index)}
                    className={` ${
                      isFullScreenModal
                        ? 'm-auto max-h-screen max-w-full cursor-pointer object-cover'
                        : `  h-[300px] rounded-md sm:h-[350px] ${
                            images.length === 1 && item.height < 200 && 'h-auto! min-h-60'
                          } ${
                            images.length === 1 && item.height > 200 && 'h-full! max-h-[450px]'
                          } cursor-pointer object-cover ${isCreateQuote && 'max-h-40! my-auto'}`
                    }`}
                    src={item.secureUrl}
                    alt={`Slides`}
                  />
                )}
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        {images.length > 2 && !isFullScreenModal && (
          <div className="m-auto mt-2 flex w-fit items-center gap-2 rounded-full p-1 px-4">
            <div
              onClick={(e) => {
                e.stopPropagation()
              }}
              className={`custom-pagination-${id} flex cursor-pointer items-center justify-center`}
            />
          </div>
        )}

        {images.length > 1 && isFullScreenModal && (
          <>
            <span
              className={`bg-dark-lucres-black-300/50 absolute left-0 top-1/2 z-50 rounded-full md:left-10 prev-btn-${id} ${
                isFirstSlide ? 'pointer-events-none opacity-40' : ''
              }`}
            >
              <IconButton>
                <CaretLeftIcon size={24} />
              </IconButton>
            </span>

            <span
              className={`bg-dark-lucres-black-300/50 absolute right-0 top-1/2 z-50 rounded-full md:right-10 next-btn-${id} ${
                isLastSlide ? 'pointer-events-none opacity-40' : ''
              }`}
            >
              <IconButton>
                <CaretRightIcon size={24} />
              </IconButton>
            </span>
          </>
        )}
        {!isFullScreenModal && showModal && (
          <ImageFullScreenModal
            activeIndex={currentIndex}
            closeModal={handleCloseModal}
            images={images}
            id={id}
          />
        )}
      </div>
    </>
  )
}

export default ImageCarousel
