import { useEffect, useMemo, useRef, useState } from 'react'
import {
  ArrowUpIcon,
  DotIcon,
  DotsThreeIcon,
  PencilSimpleLineIcon,
  SmileyIcon,
  TrashIcon,
  WarningIcon,
  XIcon,
} from '@phosphor-icons/react'
import Button from '../../components/Button'
import ActionMenu from '../ActionMenu'
import { PostService } from '../../services/PostService'
import Avatar from '../Avatar/Avatar'
import { capitalizeWords, truncateText } from '../../utils/commonUtils'
import SkeletonComment from './CommentSkelaton'
import { useAuth } from '../../context/AuthContext'
import Textarea from '../Textarea'
import { Comment, PostCard } from '../../models/Post'
import TimeAgo from '../TimeAgo'
import { usePathname, useRouter } from 'next/navigation'
import IconButton from '../IconButton'
import Overlay from '../Overlay'
import Tooltip from '../Tooltip'
import EmojiPicker from '../../app/feed/EmojiPicker'
import ContentWithMentions from '../ContentWithMentions'
import useError from '../../context/ErrorContext'

interface CommentsProps {
  postId: string
  // setCommentsCount: React.Dispatch<React.SetStateAction<number>>
  handleClose?: any
  type?: string // 'QUOTE' or undefined for post
  setData?: React.Dispatch<React.SetStateAction<PostCard[]>>
  setPostData?: React.Dispatch<React.SetStateAction<PostCard | undefined>>
  isViewPost?: boolean
}

/**
 * Comments component for displaying, adding, editing, and deleting comments on a post or quote.
 */
const Comments: React.FC<CommentsProps> = ({
  postId,
  // setCommentsCount,
  handleClose,
  type,
  setData,
  setPostData,
  isViewPost,
}) => {
  //  all comments loaded so far
  const [comments, setComments] = useState<Comment[]>([])
  //  new comment input
  const [commentText, setCommentText] = useState('')
  //  editing a comment
  const [editCommentId, setEditCommentId] = useState<string | null>(null)
  const [editCommentText, setEditCommentText] = useState<string>('')
  //  showing the delete confirmation modal
  const [deleteCommentId, setDeleteCommentId] = useState<string | null>(null)

  // Pagination state
  const [page, setPage] = useState(1)
  // Loading  fetching comments
  const [loading, setLoading] = useState(true)
  // Refs for emoji pickers and containers
  const commentInputRef = useRef<HTMLTextAreaElement | null>(null)
  const emojiButtonRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const editemojiButtonRef = useRef<HTMLDivElement>(null)
  const editcontainerRef = useRef<HTMLDivElement>(null)
  // Emoji picker open state
  const [openEmojiPicker, setOpenEmojiPicker] = useState(false)
  const [openEditEmojiPicker, setOpenEditEmojiPicker] = useState(false)
  const editcommentInputRef = useRef<HTMLTextAreaElement | null>(null)
  // Pagination: whether there are more comments to load
  const [hasNextPage, setHasNextPage] = useState(true)
  // Navigation hook
  const router = useRouter()
  // Authenticated user profile
  const { authUserProfile: userProfile } = useAuth()
  const { handleError } = useError()
  // Max allowed characters in a comment
  const MAX_CHARS = 900

  /**
   * Fetch comments for the current post or quote.
   * Handles pagination and sets loading state.
   */
  const getComments = async () => {
    try {
      const response =
        type === 'QUOTE'
          ? await PostService.getQuoteComments(postId, page)
          : await PostService.getPostComments(postId, page)
      const { items, paginator } = response.data
      if (page === 1) {
        setComments(items)
      } else {
        setComments((prev) => [...prev, ...items])
      }
      // console.log('RESPONSE', response)
      setHasNextPage(paginator.hasNextPage)
    } catch (err: any) {
      handleError(err)
    } finally {
      setLoading(false)
    }
  }

  // Fetch comments when the page changes (initial load and pagination)
  useEffect(() => {
    getComments()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page])

  // Handler for loading more comments (pagination)
  const handleLoadMore = () => {
    setPage((prev) => prev + 1)
  }

  const updateCommentsCount = (data: PostCard[], postId: string, delta: number) => {
    return data.map((item) => {
      // ARTICLE
      if (item.type === 'ARTICLE' && item.article?.id === postId) {
        return {
          ...item,
          article: {
            ...item.article,
            commentsCount: (item.article.commentsCount || 0) + delta,
          },
        }
      }

      // QUOTE
      if (item.type === 'QUOTE' && item.quote?.id === postId) {
        return {
          ...item,
          quote: {
            ...item.quote,
            commentsCount: (item.quote.commentsCount || 0) + delta,
          },
        }
      }

      // REPOST (repostedItem matches postId)
      if (item.type === 'REPOST' && item.repost?.repostedItem?.id === postId) {
        return {
          ...item,
          repost: {
            ...item.repost,
            repostedItem: {
              ...item.repost.repostedItem,
              commentsCount: (item.repost.repostedItem.commentsCount || 0) + delta,
            },
          },
        }
      }

      return item
    })
  }

  /**
   * Submit a new comment for the post or quote.
   * Resets input and updates comment list on success.
   */
  const handleSubmitComment = async () => {
    if (!commentText.trim()) return

    try {
      const response =
        type === 'QUOTE'
          ? await PostService.addQuoteComment({
              quoteId: postId,
              text: commentText.trim(),
            })
          : await PostService.addPostComment({
              articleId: postId,
              text: commentText.trim(),
            })

      // if (response.status === 'success') {
      setCommentText('')
      setComments((prevComments) => [response.data, ...prevComments])

      // Update view post data
      if (isViewPost && setPostData) {
        if (type === 'POST') {
          setPostData?.((prev: any) => {
            if (!prev) return prev
            return {
              ...prev,
              article: {
                ...prev.article,
                commentsCount: (prev.article?.commentsCount || 0) + 1,
              },
            }
          })
        } else {
          setPostData?.((prev: any) => {
            if (!prev || !prev.quote) return prev
            return {
              ...prev,
              quote: {
                ...prev.quote,
                commentsCount: (prev.quote.commentsCount || 0) + 1,
              },
            }
          })
        }
      }

      setData?.((prev) => updateCommentsCount(prev, postId, 1))
    } catch (err: any) {
      handleError(err)
    }
  }

  /**
   * Delete a comment by its ID.
   * Updates comment list and count on success.
   */
  const handleDeleteComment = async (commentId: string) => {
    try {
      type === 'QUOTE'
        ? await PostService.deleteQuoteComment({
            quoteId: postId,
            commentId: commentId,
          })
        : await PostService.deletePostComment({
            articleId: postId,
            commentId: commentId,
          })

      setDeleteCommentId(null)
      setEditCommentText('')
      setComments((prevComments) => prevComments.filter((comment) => comment.id !== commentId))
      if (isViewPost && setPostData) {
        if (type === 'POST') {
          setPostData?.((prev: any) => {
            if (!prev) return prev
            return {
              ...prev,
              article: {
                ...prev.article,
                commentsCount: (prev.article?.commentsCount || 0) - 1,
              },
            }
          })
        } else {
          setPostData?.((prev: any) => {
            if (!prev || !prev.quote) return prev
            return {
              ...prev,
              quote: {
                ...prev.quote,
                commentsCount: (prev.quote.commentsCount || 0) - 1,
              },
            }
          })
        }
      }

      setData?.((prev) => updateCommentsCount(prev, postId, -1))
    } catch (err: any) {
      handleError(err)
    }
  }

  /**
   * Edit an existing comment.
   * Updates the comment in the list on success.
   */
  const handleEditComment = async (commentId: string) => {
    try {
      const response = await PostService.editPostComment({
        articleId: postId,
        text: editCommentText.trim(),
        commentId: commentId,
      })
      const data = response.data
      setEditCommentId(null)
      setEditCommentText('')
      setComments((prevComments) =>
        prevComments.map((comment) => (comment.id === data.id ? data : comment)),
      )
    } catch (err: any) {
      handleError(err)
    }
  }

  /**
   * Handles closing the emoji picker when clicking outside the relevant area (for new comment).
   */
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node

      if (
        containerRef.current &&
        !containerRef.current.contains(target) &&
        emojiButtonRef.current &&
        !emojiButtonRef.current.contains(target)
      ) {
        setOpenEmojiPicker(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  /**
   * Handles closing the emoji picker when clicking outside the relevant area (for editing comment).
   */
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node

      if (
        editcontainerRef.current &&
        !editcontainerRef.current.contains(target) &&
        editemojiButtonRef.current &&
        !editemojiButtonRef.current.contains(target)
      ) {
        setOpenEditEmojiPicker(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  /**
   * Utility to get a user's full name, truncated and capitalized.
   */
  function getFullName(givenName?: string, familyName?: string): string {
    const fullName = `${givenName ?? ''} ${familyName ?? ''}`.trim()
    const truncated = truncateText(fullName, 15).text
    return capitalizeWords(truncated)
  }

  return (
    <div className="dark:border-t-dark-lucres-black-300 flex max-h-full min-h-full w-full flex-col sm:border-t md:flex-col-reverse">
      {/* Header for mobile view */}
      <div className="flex w-full items-center justify-start gap-x-20 p-3 md:hidden">
        <span
          className="text-lucres-900 dark:text-dark-lucres-green-100 z-50! rounded-full text-base font-semibold hover:text-gray-500"
          onClick={handleClose}
        >
          Cancel
        </span>
        <span className="text-lucres-900 dark:text-dark-lucres-green-100 text-lg font-bold">
          Comments
        </span>
        <p></p>
      </div>

      {/* Comments list */}
      <div className="flex h-4/5 flex-1 flex-col overflow-y-auto md:h-full md:overflow-visible">
        {loading ? (
          // Show skeleton while loading
          <SkeletonComment />
        ) : (
          comments?.map((comment, index) => {
            const isLast = index === comments.length - 1
            return (
              <div key={comment.id} className="dark:border-t-dark-lucres-black-300 border-t">
                <div className="relative flex w-full flex-col p-4">
                  <div className="flex w-full justify-between">
                    <div className="flex w-full items-start gap-2">
                      {/* User avatar */}
                      <div className="w-12 md:w-10">
                        <Avatar
                          src={comment?.entity?.profileImage.url}
                          alt={`${comment?.entity?.profileImage.url}'s Avatar`}
                          size={9}
                          className="cursor-pointer object-cover"
                        />
                      </div>
                      {/* Edit mode for comment */}
                      {editCommentId === comment.id ? (
                        <div className="relative w-full">
                          <Textarea
                            height="40px"
                            placeholder="Enter your comment"
                            className="px-2! py-1! pe-32! max-h-[80px]"
                            maxLength={MAX_CHARS}
                            ref={editcommentInputRef}
                            value={editCommentText}
                            onChange={(e) => setEditCommentText(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault()
                                handleEditComment(comment.id)
                              }
                            }}
                          />
                          <div ref={editcontainerRef} className=" w-full">
                            {/* Emoji picker for editing */}
                            {openEditEmojiPicker && (
                              <div className="absolute left-20 top-full">
                                <EmojiPicker
                                  inputRef={
                                    editcommentInputRef as React.RefObject<HTMLTextAreaElement>
                                  }
                                  setData={setEditCommentText}
                                  closePicker={() => setOpenEditEmojiPicker(false)}
                                />
                              </div>
                            )}
                          </div>
                          {/* Edit actions: cancel, emoji, submit */}
                          <div className="absolute right-2 top-0 flex h-full items-center gap-x-2">
                            <Button
                              size="small"
                              theme="transparent"
                              className="border-none! p-1!"
                              onClick={() => setEditCommentId('')}
                            >
                              <XIcon
                                size={20}
                                className="text-lucres-gray-700 dark:text-lucres-gray-400 cursor-pointer"
                              />
                            </Button>
                            <div className="flex items-end" ref={editemojiButtonRef}>
                              <Tooltip text="Add emoji" direction="top" classes="whitespace-nowrap">
                                <IconButton
                                  className="hidden lg:block"
                                  onClick={() => setOpenEditEmojiPicker(!openEditEmojiPicker)}
                                >
                                  <SmileyIcon size={20} className="cursor-pointer" />
                                </IconButton>
                              </Tooltip>
                            </div>
                            <Button
                              size="small"
                              type="submit"
                              className="rounded-full! p-2!"
                              loaderClasses="w-1! h-1! p-1!"
                              theme="dark"
                              disabled={editCommentText.trim() === comment.text}
                              onClick={() => handleEditComment(comment.id)}
                            >
                              <ArrowUpIcon />
                            </Button>
                          </div>
                        </div>
                      ) : (
                        // Normal comment display
                        <div className="flex w-full  items-start justify-between">
                          <div className="flex flex-col ">
                            <div className="flex items-start  sm:items-center">
                              <div className="flex flex-col md:flex-row">
                                {/* User name (clickable) */}
                                <span
                                  className="text-lucres-black dark:text-dark-lucres-green-100 cursor-pointer text-sm font-medium hover:underline"
                                  onClick={() => router.push(`/${comment.user.username}`)}
                                >
                                  {getFullName(
                                    comment?.entity?.givenName,
                                    comment?.entity?.familyName,
                                  )}
                                </span>
                                <span className="flex items-center text-sm ">
                                  <DotIcon weight="bold" size={14} className="hidden md:block" />@
                                  {comment?.entity?.username}
                                </span>
                              </div>
                              {/* Timestamp */}
                              <div className="text-lucres-gray-700 dark:text-dark-lucres-green-300 flex items-center text-sm">
                                <DotIcon weight="bold" size={14} />
                                <TimeAgo dateString={comment.createdAt} ClassName="text-xs!" />
                              </div>
                            </div>
                            {/* Comment text with mentions */}
                            <span className="text-lucres-gray-700 dark:text-lucres-gray-400 break-all text-sm">
                              <ContentWithMentions text={comment.text} />
                            </span>
                          </div>
                          {/* Action menu for edit/delete (only for own comments) */}
                          <div className="relative flex flex-col">
                            <div className="flex w-full items-center gap-6">
                              {userProfile?.username === comment?.entity?.username && (
                                <ActionMenu
                                  classes={`${
                                    isLast ? '-lg:top-10 !lg:right-10' : 'top-3 right-10!'
                                  } w-40`}
                                  trigger={
                                    <IconButton
                                      theme="secondary"
                                      className="text-lucres-gray-700 dark:text-lucres-gray-400 flex items-center gap-1 text-sm"
                                    >
                                      <DotsThreeIcon size={16} weight="bold" />
                                    </IconButton>
                                  }
                                  menuItems={
                                    type === 'QUOTE'
                                      ? [
                                          {
                                            label: 'Delete',
                                            icon: <TrashIcon size={20} weight="bold" />,
                                            className: 'text-red-500',
                                            onClick: (close) => {
                                              setDeleteCommentId(comment.id)

                                              close()
                                            },
                                          },
                                        ]
                                      : [
                                          {
                                            label: 'Edit',
                                            icon: <PencilSimpleLineIcon size={20} weight="bold" />,
                                            onClick: (close) => {
                                              setEditCommentId(comment.id)
                                              setEditCommentText(comment.text)
                                              close()
                                            },
                                          },
                                          {
                                            label: 'Delete',
                                            icon: <TrashIcon size={20} weight="bold" />,
                                            className: 'text-red-500',
                                            onClick: (close) => {
                                              setDeleteCommentId(comment.id) // ✅ track specific comment
                                              close()
                                            },
                                          },
                                        ]
                                  }
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                {/* Delete confirmation modal */}
                {deleteCommentId === comment.id && (
                  <Overlay
                    heading="Delete Comment"
                    handleClose={() => setDeleteCommentId(null)}
                    size="min-h-fit mt-20"
                    classes="p-0!"
                  >
                    <div className="dark:bg-dark-lucres-black-500 w-full rounded-lg bg-white p-4 shadow-lg">
                      <div className="flex flex-col items-center">
                        <WarningIcon size={32} />
                        <h2 className="mt-3 text-center text-lg font-semibold">
                          Delete this comment?
                        </h2>
                        <p className="text-center text-sm text-gray-500 dark:text-gray-300">
                          Are you sure you want to delete this comment?
                        </p>
                      </div>
                      <div className="dark:border-t-dark-lucres-black-200 mt-4 flex justify-between gap-4 border-t pt-4">
                        <Button
                          size="small"
                          theme="transparent"
                          className="px-4! py-1.5! !border"
                          onClick={() => setDeleteCommentId(null)}
                        >
                          Cancel
                        </Button>
                        <Button
                          size="small"
                          theme="dark"
                          className="px-4! py-1.5! text-white!"
                          onClick={() => handleDeleteComment(comment.id)}
                        >
                          Yes, Delete
                        </Button>
                      </div>
                    </div>
                  </Overlay>
                )}
              </div>
            )
          })
        )}

        {/* Load more button for pagination */}
        {!loading && hasNextPage && (
          <div className="dark:border-t-dark-lucres-black-300 flex w-full items-center border-t py-2">
            <Button
              onClick={handleLoadMore}
              size="small"
              theme="transparent"
              className="px-4! py-2! mx-auto w-fit"
            >
              Load More
            </Button>
          </div>
        )}
      </div>

      {/* New comment input area */}
      <div className="dark:border-dark-lucres-black-300 relative my-4 flex w-full shrink-0 flex-col rounded-lg border border-gray-200 p-2 md:mx-4 md:w-auto">
        <div className="flex gap-x-2">
          {/* Current user avatar */}
          <div className="w-10">
            <Avatar
              src={userProfile?.profileImage.url}
              alt={`${userProfile?.profileImage.name}'s Avatar`}
              size={9}
              className="cursor-pointer object-cover"
            />
          </div>
          {/* Comment input */}
          <Textarea
            theme="transparent"
            className="border-none! p-1! !max-h-40"
            ref={commentInputRef}
            maxLength={MAX_CHARS}
            onChange={(e) => setCommentText(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault()
                handleSubmitComment()
              }
            }}
            height="40px"
            placeholder="Add a comment..."
            value={commentText}
          />
          <div ref={containerRef} className="">
            {/* Emoji picker for new comment */}
            {openEmojiPicker && (
              <div className="absolute left-20 top-[85%]">
                <EmojiPicker
                  inputRef={commentInputRef as React.RefObject<HTMLTextAreaElement>}
                  setData={setCommentText}
                  closePicker={() => setOpenEmojiPicker(false)}
                />
              </div>
            )}
          </div>
        </div>
        {/* Emoji button and submit button */}
        <div className="flex items-end justify-between">
          <div className="flex items-center ps-8 md:ps-9" ref={emojiButtonRef}>
            <Tooltip text="Add emoji" direction="top" classes="whitespace-nowrap">
              <IconButton
                className="lg:flex! !hidden"
                onClick={() => setOpenEmojiPicker(!openEmojiPicker)}
              >
                <SmileyIcon size={20} className="cursor-pointer" />
              </IconButton>
            </Tooltip>
          </div>
          <div>
            <Button
              size="small"
              type="submit"
              className="rounded-full! p-2!"
              loaderClasses="w-1! h-1! p-1!"
              theme="dark"
              disabled={commentText.length < 1}
              onClick={handleSubmitComment}
            >
              <ArrowUpIcon />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Comments
