import React from 'react'
import Link from 'next/link'

interface LogoProps {
  width: number
  height: number
  src: string
  className?: string
}

const Logo: React.FC<LogoProps> = ({ width, height, src, className }) => {
  return (
    <Link href="/" className={className}>
      {' '}
      {/* Replaced Next.js Link */}
      <img
        src={src}
        width={width}
        height={height}
        alt="logo"
        className={`z-50 min-w-20 md:w-24 ${className || ''}`} // Apply className dynamically
      />
    </Link>
  )
}

export default Logo
