import { FC, useMemo } from 'react'
import { pdf } from '@react-pdf/renderer'
import { UserService } from '../services/UserService'
import Overlay from './Overlay'
import Button from './Button'
import Tooltip from './Tooltip'
import { JobService } from '../services/JobService'
import IconButton from './IconButton'
import { useAuth } from '../context/AuthContext'
import { useTheme } from '../context/ThemeProvider'
import Credits from '@/app/talent/Credits'
import useError from '@/context/ErrorContext'
import { usePathname, useRouter } from 'next/navigation'
import { templates } from '@/app/resume/ResumeTemplates/templates'

interface ResumeDownloadModalProps {
  userId: string
  setShowPricing: (val: boolean) => void
  jobId?: string
  application?: Record<string, any>
  isApplicantResume?: boolean
  isOwnProfile?: boolean
  username: string
  showResumeDownloadModal: boolean
  setShowResumeDownloadModal: (val: boolean) => void
  handleUnlockFreeResume?: any
  handleUnlockResume?: any
  handleSuccessfullPurchase?: any
  revealResume?: boolean
  freeUnlockCount?: number
  setApplicantDetails?: any
  isApplicationExpired?: boolean
  isContactRestricted?: boolean
}

const ResumeDownload: FC<ResumeDownloadModalProps> = ({
  userId,
  application,
  setShowPricing,
  isApplicantResume,
  isOwnProfile,
  username,
  showResumeDownloadModal,
  setShowResumeDownloadModal,
  handleUnlockResume,
  revealResume,
  isApplicationExpired,
  isContactRestricted,
}) => {
  const { authUserProfile, setAuthUserProfile } = useAuth()
  const { theme } = useTheme()
  const { handleError } = useError()
  const pathname = usePathname()
  const selectedTemplateObj = templates[0]
  const PdfComponent = selectedTemplateObj.pdfComponent
  const totalCredits = authUserProfile && authUserProfile.subscription.resumesTotal
  const remainingCredits = authUserProfile && authUserProfile.subscription.resumesRemaining
  const filename = `${username.replace(/\s+/g, '_')}_Resume.pdf`
  const flow = useMemo(() => {
    if (pathname.startsWith('/talent')) return 'TALENT'
    if (pathname.startsWith('/applicants')) return 'JOB_DASHBOARD'
    return 'PROFILE' // fallback for /username and others
  }, [pathname])

  const handleUnlockProfile = async () => {
    try {
      // let userName = username
      if (!revealResume || (revealResume && isApplicationExpired)) {
        // Step 1: Attempt purchase/unlock
        if (flow === 'TALENT') {
          // await UserService.purchaseResume(userId)
          await handleDownloadResume()
        } else if (flow === 'JOB_DASHBOARD') {
          // const unlockRes = await handleUnlockResume?.()
          await handleUnlockResume()
          // setUserName(unlockRes?.data?.user?.username)
        } else {
          !isOwnProfile && (await UserService.purchaseResume(userId))
          await handleDownloadResume()
        }

        if (!isOwnProfile && !isApplicantResume && authUserProfile?.username) {
          const authUser = await UserService.getUserProfileByPermalink(authUserProfile.username)
          setAuthUserProfile(authUser.data)
        }
      }

      setShowResumeDownloadModal(false)
    } catch (error: any) {
      if (
        error.data?.messageType === 'NO_ACTIVE_SUBSCRIPTION' ||
        error.data?.messageType === 'NO_RESUME_REMAINING'
      ) {
        setShowPricing(true)
      } else {
        console.error('Resume unlock failed:', error)
      }
    }
  }

  const formatToMonthYear = (
    date: string | null | undefined,
    isCurrent = false,
    format: 'MM/YYYY' | 'MMM YYYY' = 'MM/YYYY',
  ): string => {
    if (!date) return isCurrent ? 'Present' : ''

    const parsedDate = new Date(date)
    if (isNaN(parsedDate.getTime())) return ''

    const month = parsedDate.getMonth()
    const year = parsedDate.getFullYear()

    if (format === 'MM/YYYY') {
      return `${(month + 1).toString().padStart(2, '0')}/${year}`
    } else {
      const monthNames = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ]
      return `${monthNames[month]} ${year}`
    }
  }

  const handleDownloadResume = async () => {
    // if(!userName) return
    try {
      let apiResponse = null
      if (flow !== 'TALENT' && flow !== 'PROFILE' && application) {
        const res = await JobService.downloadApplicantResume(application.id)
        apiResponse = res.data
      } else {
        const res = await UserService.purchaseResume(userId)
        apiResponse = res.data
      }

      const mappedData = {
        personalDetails: {
          name: `${apiResponse.user?.givenName ?? ''} ${apiResponse.user?.familyName ?? ''}`.trim(),
          profilePicture: apiResponse.user?.profileImage?.url ?? '',
          contactNumber: apiResponse.contact?.primaryPhone ?? '',
          email: apiResponse.contact?.primaryEmail ?? '',
          location: `${apiResponse.user?.location?.address?.city ?? ''} ${
            apiResponse.user?.location?.address?.country ?? ''
          }`.trim(),
          nationality: apiResponse.contact?.nationality ?? '',
          profile: apiResponse.user?.permalink ?? '',
          dateOfBirth: apiResponse.contact?.dateOfBirth ?? '',
        },
        aboutMe: apiResponse.careerData?.aboutMe ?? '',
        experiences: (apiResponse.careerData?.experiences ?? []).map((exp: any) => ({
          ...exp,
          startDate: formatToMonthYear(exp.startDate),
          endDate: formatToMonthYear(exp.endDate, exp.isCurrent),
        })),
        educations: apiResponse.careerData?.educations ?? [],
        achievements: (apiResponse.careerData?.achievements ?? []).map((achievement: any) => ({
          title: achievement.title ?? '',
          awardedBy: achievement.awardedBy ?? '',
          date: formatToMonthYear(achievement.date),
        })),
        skills: apiResponse.careerData?.skills ?? [],
        languages: apiResponse.careerData?.languages ?? [],
        socialLinks: apiResponse.careerData?.socialLinks ?? [],
        references: apiResponse.careerData?.references ?? [],
        isSaved: apiResponse.careerData?.isSaved ?? false,
      }

      // Step 3: Generate and trigger download
      const blob = await pdf(<PdfComponent data={mappedData} />).toBlob()
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (error) {
      handleError(error)
    }
  }

  const isDisabled = (isApplicantResume && !revealResume) || (revealResume && isApplicationExpired)
  return (
    <div className="flex gap-4 rounded-full">
      {flow !== 'TALENT' && !isOwnProfile && (
        <Tooltip text="Download Resume" classes="whitespace-nowrap text-center" direction="top">
          <div
            className={`dark:bg-dark-lucres-black-500 rounded-full bg-white ${flow === 'PROFILE' && !isContactRestricted && 'hidden'} `}
          >
            <IconButton
              spanClass="group-hover:bg-lucres-200"
              isDisabled={isDisabled}
              onClick={(e) => {
                e.stopPropagation()
                if (isDisabled) return
                if (revealResume || isOwnProfile) {
                  handleDownloadResume()
                } else if (authUserProfile?.subscription.status === 'ACTIVE') {
                  setShowResumeDownloadModal(true)
                } else {
                  setShowPricing(true)
                }
              }}
            >
              {/* <DownloadSimple size={22} />  */}
              {theme === 'dark' ? (
                <img src="/common/download-profile-icon-dark.svg" alt="download" className="w-5" />
              ) : (
                <img src="/common/download-profile-icon-light.svg" alt="download" className="w-5" />
              )}
            </IconButton>
          </div>
        </Tooltip>
      )}

      {showResumeDownloadModal && (
        <Overlay
          heading=""
          handleClose={() => setShowResumeDownloadModal(false)}
          classes="!p-0 !pb-6 "
          crossRequired={false}
          size="min-h-fit  mt-20"
        >
          <div className="flex h-full w-full flex-col items-center justify-center rounded-md">
            <div className="dark:bg-dark-lucres-black-500 relative h-40 w-full rounded-md bg-[#ECFFD5]">
              <img
                src="/common/resume-download.svg"
                alt="Treasure"
                className="absolute -top-4 left-1/2 w-7/12 -translate-x-1/2 transform"
              />
            </div>

            <div className="mt-20 flex flex-col items-center">
              <h3 className="text-lucres-900 dark:text-dark-lucres-green-100 text-2xl font-semibold">
                You're Almost There!
              </h3>
              <p className="text-lucres-gray-700 dark:text-dark-lucres-green-300 mt-1 w-9/12 text-center text-sm">
                You’re about to use 1 credit to download this resume.
              </p>
              <div className="mt-4">
                <div className="flex w-full justify-center text-sm">
                  <Credits
                    credits={remainingCredits}
                    totalcredits={totalCredits}
                    sqSize={14}
                    strokeWidth={3}
                  />
                </div>
              </div>
              <div className="mt-8 flex w-full justify-center gap-8">
                <Button
                  size="small"
                  theme="transparent"
                  isRectangle
                  onClick={() => setShowResumeDownloadModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  size="small"
                  theme="opaque"
                  isRectangle
                  onClick={() => {
                    handleUnlockProfile()
                  }}
                >
                  {flow === 'JOB_DASHBOARD' ? 'Yes, Unlock' : 'Yes, Download'}
                </Button>
              </div>
            </div>
          </div>
        </Overlay>
      )}
    </div>
  )
}

export default ResumeDownload
