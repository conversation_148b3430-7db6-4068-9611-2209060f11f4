'use client'
import React, { useEffect, useRef, useState } from 'react'
import Tooltip from './Tooltip'
import IconButton from './IconButton'
import { RepeatIcon } from '@phosphor-icons/react'

interface BookmarkIconProps {
  onRepostToggle: (isReposted: boolean) => void
  initialRepostCount: number
  isJobReposted: boolean | undefined
}

const RepostIcon: React.FC<BookmarkIconProps> = ({
  onRepostToggle,
  initialRepostCount,
  isJobReposted,
}) => {
  const [reposted, setReposted] = useState(isJobReposted ?? false)
  const [count, setCount] = useState(initialRepostCount || 0)
  const debounceRef = useRef<NodeJS.Timeout | null>(null)
  const committedRepostedRef = useRef(isJobReposted ?? false)

  useEffect(() => {
    setReposted(isJobReposted ?? false)
    setCount(initialRepostCount)
    committedRepostedRef.current = isJobReposted ?? false
  }, [isJobReposted, initialRepostCount])

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()

    const newReposted = !reposted
    const newCount = newReposted ? count + 1 : Math.max(count - 1, 0)

    setReposted(newReposted)
    setCount(newCount)

    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    debounceRef.current = setTimeout(() => {
      if (newReposted !== committedRepostedRef.current) {
        onRepostToggle(newReposted)
        committedRepostedRef.current = newReposted
      }
    }, 500)
  }

  return (
    <div className="text-lucres-gray-700 dark:text-dark-lucres-green-100 flex items-center gap-x-2">
      <Tooltip text="Repost" classes="whitespace-nowrap text-center" direction="bottom">
        <IconButton onClick={handleClick}>
          <RepeatIcon
            size={19}
            weight="bold"
            className={`${reposted ? 'animate-pop text-lucres-green-200' : 'animate-shrink '}`}
          />
        </IconButton>
      </Tooltip>
      <span>{count}</span>
    </div>
  )
}

export default React.memo(RepostIcon)
