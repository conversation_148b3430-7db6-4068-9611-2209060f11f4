import { ArrowRightIcon, MapPinIcon } from '@phosphor-icons/react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, Pagination, FreeMode, Navigation } from 'swiper/modules'
// @ts-ignore
import 'swiper/css'
// @ts-ignore
import 'swiper/css/free-mode'
// @ts-ignore
import 'swiper/css/pagination'
import { useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/context/AuthContext'
import { SuggestedTalent } from '@/models/User'
import { UserService } from '@/services/UserService'
import InterestingTalentSkeleton from '@/app/talent/InterestingTalentSkeleton'
import Avatar from './Avatar/Avatar'
import { truncateText } from '@/utils/commonUtils'
import FollowButton from './FollowButton'
import useError from '@/context/ErrorContext'

export type InterestingUser = {
  name: string
  title: string
  location: string
  image: string
}
interface InterestingTalentProps {
  className?: string
  breakpoints?: {}
}

const InterestingTalent: React.FC<InterestingTalentProps> = ({ className, breakpoints }) => {
  const router = useRouter()
  const { authUserProfile: userProfile, isAuthenticated } = useAuth()
  const swiperRef = useRef<any>(null)
  const [isAtEnd, setIsAtEnd] = useState<boolean>(false)
  const [loading, setLoading] = useState<boolean>(true)
  const [isFollowing, setIsFollowing] = useState(false)
  const [error, setError] = useState<string | null>()
  const [interestingUsers, setInterestingUsers] = useState<SuggestedTalent[]>()
  const { handleError } = useError()
  const fetchIntrestingTalent = async () => {
    setLoading(true)
    setError(null)
    if (!userProfile?.permalink) return
    try {
      const response = await UserService.getIntrestingTalent(userProfile?.permalink)
      if (response.status === 'success') {
        setInterestingUsers(response.data)
        setLoading(false)
      }
    } catch (error: any) {
      handleError(error)
    }
  }

  useEffect(() => {
    if (userProfile?.permalink) {
      fetchIntrestingTalent()
    }
  }, [userProfile?.permalink])

  const handleSlideChange = (swiper: any) => {
    setIsAtEnd(swiper.isEnd)
  }

  const handleUnFollowUser = async (id: string) => {
    try {
      const entityType = localStorage.getItem("x-active-entity-type");
      const res = await UserService.handleUnFollowUser(id, entityType as string);
      if (res.status === 'success') {
        // await refreshUserProfile()
        // if (user?.user?.permalink) {
        //   // fetchProfileData(user.user.permalink)
        // }
      } else {
        throw new Error(res)
      }
    } catch (error) {
      throw error
      // toast.error(`User Unfollow Failed`)
    }
  }

  const handleFollowUser = async (id: string) => {
    try {
      const entityType = localStorage.getItem("x-active-entity-type");
      const res = await UserService.handleFollowUser(id, entityType as string);
      if (res.status === 'success') {
        // await refreshUserProfile()
        // if (user?.user?.permalink) {
        //   fetchProfileData(user.user.permalink)
        // }
      } else {
        throw new Error(res)
      }
    } catch (error) {
      throw error
      // toast.error(`User Follow Failed`)
    }
  }
  return (
    <section
      className={`dark:border-dark-lucres-black-300 flex h-full flex-col gap-y-3 overflow-hidden border-y py-4 ${className}`}
    >
      <div className="flex w-full items-start justify-between pe-4 md:ps-4">
        <span className="text-lucres-black dark:text-dark-lucres-green-100 text-xl font-medium">
          Interesting Talent
        </span>
        <ArrowRightIcon
          size={24}
          className={`${isAtEnd && 'cursor-default text-gray-300'} cursor-pointer`}
          onClick={() => swiperRef.current?.swiper.slideNext()}
        />
      </div>
      {loading ? (
        <InterestingTalentSkeleton />
      ) : (
        <div className="w-full md:px-4">
          <Swiper
            ref={swiperRef}
            slidesPerView={1}
            slidesPerGroup={2}
            spaceBetween={10}
            // freeMode={true}
            speed={1000}
            modules={[Autoplay, FreeMode, Pagination, Navigation]}
            onSlideChange={handleSlideChange}
            breakpoints={
              breakpoints
                ? breakpoints
                : {
                    768: { slidesPerView: 2 },
                    1024: { slidesPerView: 2 },
                    1440: { slidesPerView: 2 },
                  }
            }
            pagination={{
              clickable: true,
              el: '.custom-trending-job-pagination',
              bulletClass: 'swiper-trending-job-pagination-bullet',
              bulletActiveClass: 'swiper-trending-job-pagination-bullet-active',
            }}
          >
            {interestingUsers &&
              interestingUsers.map((user, index) => (
                <SwiperSlide key={index}>
                  <div
                    key={index}
                    className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 flex w-full cursor-pointer flex-col items-start justify-between gap-2 rounded-xl border px-6 py-4"
                  >
                    <div className="flex w-full items-start justify-between">
                      <div className="flex min-w-12 items-center gap-x-2">
                        <Avatar
                          src={user?.profileImage.url}
                          alt={`${userProfile?.givenName}'s Avatar`}
                          size={10}
                          className="cursor-pointer object-cover"
                        />
                        <div>
                          <h3
                            className="text-lucres-black dark:text-dark-lucres-green-100 cursor-pointer whitespace-nowrap text-lg font-bold hover:underline"
                            onClick={() => router.push(`/${user.permalink}`)}
                          >
                            {/* {`${user.givenName} ${user.familyName}`} */}
                            {truncateText(`${user.givenName} ${user.familyName}`, 15).text}
                          </h3>
                          <p className="text-lucres-950 dark:text-dark-lucres-green-100 -mt-1 cursor-default">
                            {truncateText(user.headline, 15).text}
                          </p>
                        </div>
                      </div>
                      <span className="cursor-pointer rounded-md p-1">
                        {/* <UserPlus
                          size={24}
                          weight='bold'
                          className="text-lucres-gray-700 dark:text-dark-lucres-green-100"
                        /> */}
                        <FollowButton
                          isIconTheme={true}
                          id={user.id}
                          size="small"
                          isInitiallyFollowing={isFollowing}
                          className="px-6! py-2!"
                          handleFollow={() => handleFollowUser(user.id)}
                          handleUnFollow={() => handleUnFollowUser(user.id)}
                        >
                          {isFollowing ? 'Following' : 'Follow'}
                        </FollowButton>
                      </span>
                    </div>
                    <span className="text-lucres-gray-700 dark:text-dark-lucres-green-100 flex items-center gap-1">
                      <MapPinIcon size={22} weight="bold" />
                      {user?.location?.address?.state || 'undefined'}
                    </span>
                  </div>
                </SwiperSlide>
              ))}
            <div className="custom-trending-job-pagination mt-4 flex justify-center"></div>
          </Swiper>
        </div>
      )}
    </section>
  )
}

export default InterestingTalent
