import {
  Table,
  User,
  ReadCvLogo,
  Dot,
  DotIcon,
  UserIcon,
  TableIcon,
  ReadCvLogoIcon,
} from '@phosphor-icons/react'
import { useAuth } from '../context/AuthContext'
import IconButton from './IconButton'
import Button from './Button'
import { getFullName } from '../utils/commonUtils'
import { useRouter } from 'next/navigation'
import Avatar from './Avatar/Avatar'
function Sidebar({ data }: any) {
  // const { user } = useAuth()
  const router = useRouter()
  return (
    <div
      className={`border-r-lucres-300 dark:bg-dark-lucres-black-400 relative  flex w-full flex-col items-start bg-white md:fixed md:h-5/6 md:w-60 md:items-start md:justify-start md:border-r md:pb-10 md:pe-6 md:pt-5`}
    >
      <div className={`text-dark-lucres-green-100 h-32 w-full text-center text-3xl font-semibold`}>
        <div
          style={{
            backgroundImage: `url('${data?.coverImage?.url || '/dashboard/banner.svg'}')`,
            backgroundSize: 'cover',
            height: '100%',
            backgroundRepeat: 'no-repeat',
          }}
        ></div>
      </div>
      <div className="w-full px-2">
        <div className="relative bottom-10 flex w-full justify-between md:block">
          <div className="flex flex-col items-start gap-2">
            <Avatar src={data?.profileImage?.url} size={24} alt="profile" />
            <div className="pl-1">
              <h2 className={`mb-1 text-3xl font-semibold md:mt-4`}>
                {getFullName(data?.givenName, data?.familyName)}
              </h2>
              <div
                className={`dark:text-dark-lucres-black-100 flex items-center gap-1 text-sm font-medium text-gray-600 opacity-90`}
              >
                <p>@{data?.username}</p>
                {/* <DotIcon /> */}
                <p>{/* {data?.location?.address?.city}, {data?.location?.address?.country} */}</p>
              </div>
            </div>
          </div>
          <Button
            size="small"
            className="h-fit! !relative -bottom-10 mt-3 py-2 text-sm"
            theme="translucent"
            onClick={() => router.push('/personal-information')}
          >
            Edit Profile
          </Button>
        </div>
        <div
          className={`relative bottom-8 flex flex-col gap-2 px-1 text-sm text-gray-600 dark:text-gray-300`}
        >
          <div className={`text-lucres-900 text-base dark:text-white`}>
            {data?.headline || 'Keep it real'}
          </div>
          <div className="flex gap-3">
            <div>
              <span className={`text-sm font-semibold dark:text-white`}>
                {data?.followingCount}
              </span>{' '}
              Following
            </div>
            <div>
              <span className={`text-sm font-semibold dark:text-white`}>
                {data?.followersCount}
              </span>{' '}
              Followers
            </div>
          </div>
        </div>
      </div>
      {/* Menu */}
      <div className="hidden space-y-1 text-center md:block">
        <div className="cursor-pointer">
          <IconButton linkText="Profile">
            <UserIcon size={26} />
          </IconButton>
        </div>
        <div className="cursor-pointer">
          <IconButton linkText="Job Dashboard">
            <TableIcon size={26} />
          </IconButton>
        </div>
        <div className="cursor-pointer">
          <IconButton linkText="Resume">
            <ReadCvLogoIcon size={26} />
          </IconButton>
        </div>
      </div>
    </div>
  )
}
export default Sidebar
