'use client'
import React, { useState, useRef } from 'react'
import { useTheme } from '../context/ThemeProvider'
import { useAuth } from '../context/AuthContext'
import { useRouter } from 'next/navigation'
import { useToast } from './ToastX'
import useError from '../context/ErrorContext'
import IconButton from './IconButton'
import { UserPlusIcon } from '@phosphor-icons/react'
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean
  className?: string
  theme?: 'transparent' | 'translucent' | 'opaque' | 'white' | 'dark'
  size?: 'small' | 'medium' | 'large'
  isRectangle?: boolean
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void | Promise<void>
  id: string
  isInitiallyFollowing?: boolean
  handleFollow: (id: string) => Promise<void>
  handleUnFollow: (id: string) => Promise<void>
  isIconTheme?: boolean
}

const FollowButton: React.FC<ButtonProps> = ({
  children,
  className,
  id,
  theme = 'opaque',
  size = 'medium',
  isRectangle = false,
  isInitiallyFollowing = false,
  handleFollow,
  handleUnFollow,
  isIconTheme = false,
  ...props
}) => {
  const { isAuthenticated } = useAuth()
  const router = useRouter()
  const toast = useToast()
  const { handleError } = useError()
  const debounceRef = useRef<NodeJS.Timeout | null>(null)
  // const [isFollowing, setIsFollowing] = useState(isInitiallyFollowing)
  // const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
  //   e.stopPropagation()

  //   if (!isAuthenticated) {
  //     navigate('/sign-in')
  //     return
  //   }

  //   // Visual toggle
  //   setIsFollowing((prev) => !prev)
  //   // Clear previous debounce
  //   if (debounceRef.current) {
  //     clearTimeout(debounceRef.current)
  //   }
  //   debounceRef.current = setTimeout(() => {
  //     if (!isFollowing) {
  //       handleFollow(id).catch((e) => {
  //         toast.error('user follow failed')
  //         setIsFollowing(isFollowing)
  //       })
  //     } else {
  //       handleUnFollow(id).catch((e) => {
  //         toast.error('user unfollow failed')
  //         setIsFollowing(isFollowing)
  //       })
  //     }
  //   }, 3000)
  // }
  const [isFollowing, setIsFollowing] = useState(isInitiallyFollowing)
  // const [initialFollowState] = useState(isInitiallyFollowing)

  const committedFollowState = useRef(isInitiallyFollowing)

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()

    if (!isAuthenticated) {
      router.push('/sign-in')
      return
    }

    const newFollowState = !isFollowing
    setIsFollowing(newFollowState)

    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    debounceRef.current = setTimeout(async () => {
      if (newFollowState === committedFollowState.current) return

      try {
        if (newFollowState) {
          await handleFollow(id)
        } else {
          await handleUnFollow(id)
        }

        committedFollowState.current = newFollowState
      } catch (e: any) {
        console.error(e.message)
        // toast.error(`User ${newFollowState ? 'follow' : 'unfollow'} failed`)
        handleError(e)
        setIsFollowing(committedFollowState.current)
      }
    }, 500)
  }

  const { theme: currentTheme } = useTheme()
  const mode = currentTheme === 'dark' ? 'dark' : 'light'
  const isDisabled = props.disabled

  const sizeClasses = {
    small: 'py-3 px-6 text-sm',
    medium: 'py-4 px-10 text-sm',
    large: 'py-5 px-[61px] text-base',
  }

  const opaqueStyleClasses = {
    light: {
      translucent: 'bg-lucres-green-200 hover:text-black hover:bg-opacity-45',
      opaque: 'bg-lucres-green-200/35 text-lucres-green-400 hover:text-black',
      transparent:
        'bg-transparent border border-solid border-lucres-gray-700 text-lucres-gray-700 hover:bg-gray-100',
      white: 'bg-white text-black hover:bg-lucres-gray-100',
      dark: 'bg-lucres-green-400 text-white',
    },
    dark: {
      translucent:
        'bg-dark-lucres-black-300 border border-dark-lucres-black-200 bg-opacity-30 hover:bg-opacity-45 hover:text-lime-300',
      opaque: 'bg-dark-lucres-black-300 hover:bg-dark-lucres-black-200 hover:text-lucres-500',
      transparent:
        'bg-transparent border border-solid border-dark-lucres-black-200 hover:bg-dark-lucres-black-200 hover:text-lucres-500',
      white: 'bg-white text-black hover:bg-lucres-gray-100',
      dark: 'bg-dark-lucres-black-300 hover:bg-dark-lucres-black-200 hover:text-lucres-500',
    },
  }

  const combinedClassName = `inline-flex justify-center group overflow-hidden relative whitespace-nowrap items-center box-border font-semibold disabled:opacity-50 disabled:pointer-events-none ${
    isRectangle ? 'rounded-lg' : 'rounded-full'
  } ${sizeClasses[size]} ${opaqueStyleClasses[mode][theme]} ${
    isDisabled ? 'text-white! bg-lucres-800!' : ''
  } ${className || ''}`

  return (
    <>
      {isIconTheme ? (
        <IconButton onClick={handleClick} disabled={isDisabled}>
          <UserPlusIcon
            size={24}
            weight="bold"
            className={` ${
              isFollowing
                ? 'animate-pop-follow text-lucres-green-300'
                : 'animate-shrink-follow text-lucres-gray-700 dark:text-dark-lucres-green-100'
            } `}
          />
        </IconButton>
      ) : (
        <button
          {...props}
          className={`${
            isFollowing ? 'animate-pop-follow' : 'animate-shrink-follow'
          } ${combinedClassName}`}
          onClick={handleClick}
          disabled={isDisabled}
        >
          {isFollowing ? (
            <div>
              <span className="transition-opacity duration-200 hover:opacity-0">Following</span>
              <div
                className={`rounded-inherit absolute inset-0 flex h-full w-full items-center justify-center ${
                  mode === 'light' ? 'bg-[#E39D90]' : 'bg-[#EAB4AA]'
                } font-normal text-black opacity-0 transition-opacity duration-200 hover:opacity-100`}
              >
                Unfollow
              </div>
            </div>
          ) : (
            <>
              <div className="group-hover:animate-button-animation -skew-x-45 absolute -left-20 h-24 w-4 bg-white bg-opacity-25"></div>
              Follow
            </>
          )}
        </button>
      )}
    </>
  )
}

export default FollowButton
