import React, { useState } from 'react'
import Logo from './Logo'
import {
  Gear,
  ShoppingCart,
  ShoppingCartIcon,
  SignOut,
  SignOutIcon,
  User,
  UserIcon,
  UserList,
  UserListIcon,
  Wallet,
  WalletIcon,
  X,
  XIcon,
} from '@phosphor-icons/react'
import { useRouter } from 'next/navigation'
import ThemeToggleButton from './ThemeToggle'
import { useAuth } from '../context/AuthContext'
import Button from './Button'

interface LinkObj {
  Id: number
  Title: string
  Link: string
  IconPath: React.ReactNode
}

interface ProfileMenuProps {
  className?: string
  isAuthenticated: boolean
  handlePostJobNavigation: () => void
  handleLogout: () => void
  handleMenu: () => void
  links: LinkObj[]
  isOpen: boolean
}

const ProfileMenu: React.FC<ProfileMenuProps> = ({
  isAuthenticated,
  handlePostJobNavigation,
  handleLogout,
  handleMenu,
  links,
}) => {
  const [activeSettings, setActiveSettings] = useState(false)
  const { authUserProfile } = useAuth()
  const router = useRouter()

  const handleActive = () => {
    setActiveSettings(!activeSettings)
  }

  const navigateThenClose = (path: string) => {
    router.push(path)
    handleMenu()
  }

  return (
    <div className="relative overflow-hidden">
      <div className="flex h-20 items-center justify-between px-4">
        {/* <Logo width={103} height={29.1} src="/common/whitelogo.svg" /> */}
        <ThemeToggleButton />
        <XIcon size={28} weight="bold" onClick={handleMenu} className="cursor-pointer" />
      </div>

      <div className="text-lucres-black dark:text-dark-lucres-green-100 mt-6 flex grow flex-col items-start justify-start gap-y-2">
        {isAuthenticated && !activeSettings ? (
          <ul className="flex flex-col gap-y-2 ps-2">
            <li
              className="flex cursor-pointer items-center gap-x-3 rounded-lg py-1 ps-3"
              onClick={() => navigateThenClose(`/${authUserProfile?.username}`)}
            >
              <UserIcon size={20} /> Profile
            </li>
            <li
              className="flex cursor-pointer items-center gap-x-3 rounded-lg py-1 ps-3"
              onClick={() => navigateThenClose('/personal-information')}
            >
              <UserListIcon size={20} /> Account
            </li>
            <li
              className="flex cursor-pointer items-center gap-x-3 rounded-lg py-1 ps-3"
              onClick={() => navigateThenClose('/orders')}
            >
              <ShoppingCartIcon size={20} /> Orders
            </li>
            <li
              className="flex cursor-pointer items-center gap-x-3 rounded-lg py-1 ps-3"
              onClick={() => navigateThenClose('/wallet')}
            >
              <WalletIcon size={20} /> Wallet
            </li>
            {/* <li
              className="flex cursor-pointer items-center gap-x-3 rounded-lg py-1 ps-3"
              onClick={handleActive}
            >
              <Gear size={20} /> Settings
            </li> */}
            <li
              className="flex cursor-pointer items-center gap-x-3 rounded-lg py-1 ps-3"
              onClick={handleLogout}
            >
              <SignOutIcon size={20} /> Logout
            </li>
          </ul>
        ) : (
          // : activeSettings ? (
          //   <ul className="flex flex-col gap-y-2 ps-2">
          //     <li className="flex cursor-pointer items-center gap-x-3 rounded-lg py-1 ps-3">
          //       <ThemeToggleButton />
          //     </li>
          //   </ul>
          // )
          links.map((link) => (
            <li
              key={link.Id}
              onClick={() => navigateThenClose(link.Link)}
              className="text-lucres-black dark:text-dark-lucres-green-100 group relative flex cursor-pointer gap-4 ps-3 text-base font-medium  md:text-sm"
            >
              {link.IconPath} {link.Title}
            </li>
          ))
        )}

        {!activeSettings && (
          <div className={`mt-1 flex md:hidden ${isAuthenticated && 'ps-5'}`}>
            <Button theme={'translucent'} size={'small'} onClick={handlePostJobNavigation}>
              Create a Job
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

export default ProfileMenu
