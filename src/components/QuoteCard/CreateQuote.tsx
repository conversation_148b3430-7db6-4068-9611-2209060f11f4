import { DotIcon, SmileyIcon, WarningIcon } from '@phosphor-icons/react'
import { PostCard } from '../../models/Post'
import { getFullName } from '../../utils/commonUtils'
import Avatar from '../Avatar/Avatar'
import TimeAgo from '../TimeAgo'
import ImageCarousel from '../PostCard/ImageCarousel'
import Textarea from '../Textarea'
import { useAuth } from '../../context/AuthContext'
import Tooltip from '../Tooltip'
import IconButton from '../IconButton'
import { useEffect, useRef, useState } from 'react'
import Button from '../Button'
import EmojiPicker from '../../app/feed/EmojiPicker'
import { PostService } from '../../services/PostService'
import { useToast } from '../ToastX'
import ContentWithMentions from '../ContentWithMentions'
import useError from '../../context/ErrorContext'
import Overlay from '../Overlay'

interface CreateQuoteProps {
  post: PostCard
  setData?: React.Dispatch<React.SetStateAction<PostCard[]>>
  handleClose: any
  type: string
  isViewPost: boolean | undefined
  setPostData?: React.Dispatch<React.SetStateAction<PostCard | undefined>>
}

const CreateQuote: React.FC<CreateQuoteProps> = ({
  post,
  setData,
  handleClose,
  type,
  isViewPost,
  setPostData,
}) => {
  const [openEmojiPicker, setOpenEmojiPicker] = useState(false)
  const { authUserProfile: userProfile } = useAuth()
  const [quoteContent, setQuoteContent] = useState('')
  const [showConfirmModal, setShowConfirmModal] = useState(false)
  const postInputRef = useRef<HTMLTextAreaElement | null>(null)
  const emojiButtonRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const modalRef = useRef<HTMLDivElement | null>(null)

  // const quoteRef = useRef<HTMLDivElement>(null)
  const toast = useToast()
  const { handleError } = useError()
  const { article } = post
  const MAX_CHARS = 3000
  const createdAt = post.article?.createdAt || post.quote?.createdAt
  const content = post.quote?.content || post.article?.content
  const user = post.quote?.quotedBy || post.article?.user

  const handleQuote = async () => {
    let id: string | undefined
    try {
      let response
      if (type === 'ARTICLE') {
        id = post.article?.id
        if (!id) return
        response = await PostService.createQuote(id, quoteContent)
        toast.success('Quote Successful')
      } else {
        id = post.quote?.id
        if (!id) return
        response = await PostService.createNestedQuote(id, quoteContent)
        toast.success('Quote Successful')
      }

      const newQuote = response.data
      const quotedFromId = newQuote?.quotedFrom?.id

      if (!newQuote || !quotedFromId) return
      if (isViewPost) {
        if (type === 'ARTICLE') {
          setPostData?.((prev): any => {
            if (!prev) return prev

            return {
              ...prev,
              article: {
                ...prev.article,
                quoteCount: (prev.article?.quoteCount ?? 0) + 1,
              },
            }
          })
        } else {
          setPostData?.((prev): any => {
            if (!prev) return prev

            return {
              ...prev,
              quote: {
                ...prev.quote,
                quoteCount: (prev.quote?.quoteCount ?? 0) + 1,
              },
            }
          })
        }
      }

      setData?.((prev): PostCard[] => {
        const updated: PostCard[] = prev.map((post): PostCard => {
          const updateQuoteCount = (count: number | undefined) => (count ?? 0) + 1

          if (post.type === 'ARTICLE' && post.article?.id === quotedFromId) {
            return {
              ...post,
              article: {
                ...post.article,
                quoteCount: updateQuoteCount(post.article?.quoteCount),
              },
            } as PostCard
          }

          if (post.type === 'REPOST' && post.repost?.repostedItem?.id === quotedFromId) {
            return {
              ...post,
              repost: {
                ...post.repost,
                repostedItem: {
                  ...post.repost?.repostedItem,
                  quoteCount: updateQuoteCount(post.repost?.repostedItem.quoteCount),
                },
              },
            } as PostCard
          }

          if (post.type === 'QUOTE' && post.quote?.id === quotedFromId) {
            return {
              ...post,
              quote: {
                ...post.quote,
                quoteCount: updateQuoteCount(post.quote?.quoteCount),
              },
            } as PostCard
          }

          return post
        })

        const newQuotePost = {
          id: newQuote.id,
          type: 'QUOTE' as const,
          isLiked: false,
          isReposted: false,
          quote: newQuote,
        }

        return [newQuotePost, ...updated]
      })

      handleClose()
    } catch (error) {
      console.error('Error creating quote:', error)
      handleError(error)
    }
  }

  // Click outside handler for emoji
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node

      if (
        containerRef.current &&
        !containerRef.current.contains(target) &&
        emojiButtonRef.current &&
        !emojiButtonRef.current.contains(target)
      ) {
        setOpenEmojiPicker(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node

      // Handle emoji picker separately
      if (
        containerRef.current &&
        !containerRef.current.contains(target) &&
        emojiButtonRef.current &&
        !emojiButtonRef.current.contains(target)
      ) {
        setOpenEmojiPicker(false)
      }

      // Handle modal discard logic
      if (modalRef.current && !modalRef.current.contains(target)) {
        if (quoteContent.trim()) {
          setShowConfirmModal(true)
        } else {
          handleClose()
        }
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [quoteContent, handleClose])

  const handleDiscardPost = () => {
    setShowConfirmModal(false)
    handleClose()
  }

  return (
    <>
      <div
        ref={modalRef}
        className="relative flex h-full min-h-full flex-col justify-between gap-y-2"
      >
        <div className="flex h-full items-start">
          <div className="w-12">
            <Avatar
              src={userProfile?.profileImage.url}
              alt={`${userProfile?.profileImage.name}'s Avatar`}
              size={10}
              className="cursor-default object-cover"
            />
          </div>
          <div className="relative w-full" ref={containerRef}>
            <Textarea
              placeholder="Quote..."
              className="border-none"
              height="40px"
              autoFocus
              maxLength={MAX_CHARS}
              ref={postInputRef}
              value={quoteContent}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                setQuoteContent(e.target.value)
              }
            />
            {openEmojiPicker && (
              <div className="absolute left-0 top-60 mt-2">
                <EmojiPicker
                  inputRef={postInputRef as React.RefObject<HTMLTextAreaElement>}
                  setData={setQuoteContent}
                  closePicker={() => setOpenEmojiPicker(false)}
                />
              </div>
            )}
          </div>
        </div>
        <div className="ms-14 flex flex-col gap-2">
          <div className="dark:border-dark-lucres-black-300 rounded-xl border p-4">
            {/* <div className="min-w-12 min-h-2"></div> */}
            <div className="flex w-full items-start justify-between">
              <div className="flex items-center gap-x-2">
                <Avatar
                  src={user?.profileImage.url || ''}
                  alt={`${user?.profileImage.name}'s Avatar`}
                  size={11}
                  className="cursor-pointer object-cover"
                />
                <div className="flex flex-col justify-center">
                  <div className="flex flex-wrap items-center">
                    <span className="text-lucres-black dark:text-dark-lucres-green-100 cursor-pointer font-bold hover:underline">
                      {user && getFullName(user?.givenName, user?.familyName)}
                    </span>
                    <DotIcon
                      weight="bold"
                      size={14}
                      className="text-lucres-gray-700 dark:text-dark-lucres-green-100"
                    />
                    {createdAt && <TimeAgo dateString={createdAt} />}
                  </div>
                  <div className="text-lucres-gray-700 dark:text-dark-lucres-green-300 -mt-1 flex items-center text-sm">
                    @{user?.username}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex w-full flex-row">
              <div className="text-lucres-900 w-11/12">
                {content && <ContentWithMentions text={content} />}
                <div className="mt-4">
                  {article?.images &&
                    article.images.length > 0 &&
                    article?.articleCode !== undefined && (
                      <ImageCarousel
                        images={article.images}
                        id={article.articleCode}
                        isCreateQuote={true}
                      />
                    )}
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div ref={emojiButtonRef}>
              <Tooltip text="Add emoji" direction="top" classes="whitespace-nowrap">
                <IconButton onClick={() => setOpenEmojiPicker(!openEmojiPicker)}>
                  <SmileyIcon
                    size={24}
                    className="dark:text-dark-lucres-green-100 cursor-pointer"
                  />
                </IconButton>
              </Tooltip>
            </div>

            <Button size="small" theme="translucent" className="py-2!" onClick={handleQuote}>
              Post
            </Button>
          </div>
        </div>
      </div>
      {showConfirmModal && (
        <Overlay
          heading="Discard Post"
          handleClose={() => setShowConfirmModal(false)}
          size="min-h-fit mt-20"
          classes="p-0! "
        >
          <div className="dark:bg-dark-lucres-black-500 w-full rounded-lg  bg-white p-4 shadow-lg">
            <div className="flex  flex-col items-center">
              <WarningIcon size={32} />
              <h2 className=" mt-3 text-center text-lg font-semibold">Discard this post?</h2>
              <p className=" text-center text-sm text-gray-500 dark:text-gray-300">
                Are you sure you want to discard this post?
              </p>
            </div>
            <div className="dark:border-t-dark-lucres-black-200  mt-4 flex justify-between gap-4 border-t  pt-4">
              <Button
                size="small"
                theme="transparent"
                className="px-4! py-1.5! !border"
                onClick={() => setShowConfirmModal(false)}
              >
                Cancel
              </Button>
              <Button
                size="small"
                theme="dark"
                className="px-4! py-1.5! text-white!"
                onClick={handleDiscardPost}
              >
                Yes, Discard
              </Button>
            </div>
          </div>
        </Overlay>
      )}
    </>
  )
}

export default CreateQuote
