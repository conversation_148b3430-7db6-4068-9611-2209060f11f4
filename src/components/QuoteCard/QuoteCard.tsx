import {
  BookmarkSimpleIcon,
  ChatCircleIcon,
  DotIcon,
  DotsThreeIcon,
  PencilLineIcon,
  RepeatIcon,
  TrashIcon,
  WarningIcon,
} from '@phosphor-icons/react'
import { PostCard } from '../../models/Post'
import Avatar from '../Avatar/Avatar'
import { capitalizeWords, getFullName, truncateText } from '../../utils/commonUtils'
import TimeAgo from '../TimeAgo'
import { useAuth } from '../../context/AuthContext'
import ActionMenu from '../ActionMenu'
import IconButton from '../IconButton'
import { MouseEvent, ReactNode, useState } from 'react'
import ImageCarousel from '../PostCard/ImageCarousel'
// import Like from '../LikeN'
import CopyLinkIcon from '../CopyLinkIcon'
import Tooltip from '../Tooltip'
import { PostService } from '../../services/PostService'
import { useToast } from '../ToastX'
import { useRouter } from 'next/navigation'
import Like from '../Like'
import Overlay from '../Overlay'
import CreateQuote from './CreateQuote'
import Button from '../Button'
import { UserProfile } from '../../models/User'
import Comments from '../PostCard/Comments'
import SlideUpView from '../SlideUpView'
import ContentWithMentions from '../ContentWithMentions'
import useError from '../../context/ErrorContext'

interface QuoteCardProps {
  quoteData: PostCard
  isCommentsOpen?: boolean
  setData?: React.Dispatch<React.SetStateAction<PostCard[]>>
  setQuoteData?: React.Dispatch<React.SetStateAction<PostCard | undefined>>
  data?: PostCard[]
  isViewPost?: boolean
}

const QuoteCard: React.FC<QuoteCardProps> = ({
  quoteData,
  isCommentsOpen,
  setData,
  data,
  setQuoteData,
  isViewPost,
}) => {
  const [toggleBottomSlider, setToggleBottomSlider] = useState<boolean>(false)
  const [showComments, setShowComments] = useState<boolean>(isCommentsOpen || false)
  const [confirmDeleteQuote, setConfirmDeleteQuote] = useState(false)
  // const [commentsCount, setCommentsCount] = useState(quoteData.quote?.commentsCount || 0)
  const [showQuoteModal, setShowQuoteModal] = useState(false)
  const toast = useToast()
  const { authUserProfile: userProfile } = useAuth()
  const router = useRouter()
  const { handleError } = useError()
  const { quote } = quoteData

  const handleOpenComment = (e: any) => {
    e.stopPropagation()
    setShowComments(!showComments)
    setToggleBottomSlider(true)
  }

  const handleNavigateToProfile = (e: any) => {
    e.stopPropagation()
    router.push(`/${quoteData.quote?.quotedBy.username}`)
  }
  const handleNavigateToNestedQuote = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (quote?.type === 'ARTICLE') {
      router.push(`/feed/post/${quoteData.quote?.quotedFrom.articleCode}`)
    } else {
      router.push(`/feed/quote/${quoteData.quote?.quotedFrom.quoteCode}`)
    }
  }
  function isMatchingQuote(post: PostCard, quoteId: string) {
    return (
      (post.type === 'QUOTE' && post.quote?.id === quoteId) ||
      (post.type === 'REPOST' &&
        post.repost?.type === 'QUOTE' &&
        post.repost?.repostedItem?.id === quoteId)
    )
  }

  function updatePostLikeState(post: PostCard, liked: boolean, count: number): PostCard {
    if (post.type === 'QUOTE') {
      return {
        ...post,
        isLiked: liked,
        quote: {
          ...post.quote!, // ✅ Use the quote from the post itself
          likesCount: count,
        },
      }
    }

    if (post.type === 'REPOST') {
      return {
        ...post,
        isLiked: liked,
        repost: {
          ...post.repost!,
          repostedItem: {
            ...post.repost!.repostedItem!,
            likesCount: count,
          },
        },
      }
    }

    return post
  }

  const handleSyncQuoteLike = (id: string, liked: boolean) => {
    setData?.((prev) =>
      prev.map((item) => {
        if (item.type === 'QUOTE' && item.quote?.id === id) {
          const prevCount = item.quote?.likesCount ?? 0
          const newCount = liked ? prevCount + 1 : Math.max(prevCount - 1, 0)

          return {
            ...item,
            isLiked: liked,
            quote: {
              ...item.quote!, // ✅ Use the quote from the post itself
              likesCount: newCount,
            },
          }
        }
        if (item.type === 'REPOST' && item.repost?.repostedItem?.id === id) {
          const prevCount = item.repost.repostedItem?.likesCount ?? 0
          const newCount = liked ? prevCount + 1 : Math.max(prevCount - 1, 0)

          return {
            ...item,
            isLiked: liked,
            repost: {
              ...item.repost!,
              repostedItem: {
                ...item.repost!.repostedItem!,
                likesCount: newCount,
              },
            },
          }
        }

        return item
      }),
    )
  }

  const rollbackQuoteLike = (
    quoteData: PostCard,
    prevLiked: boolean,
    prevCount: number,
    setData?: React.Dispatch<React.SetStateAction<PostCard[]>>,
  ) => {
    const quoteId = quoteData.quote?.id
    if (!quoteId) {
      console.error('Quote ID is undefined')
      return
    }
    if (isViewPost && setQuoteData) {
      setQuoteData((prev) =>
        prev && isMatchingQuote(prev, quoteId)
          ? updatePostLikeState(prev, prevLiked, prevCount)
          : prev,
      )
    }

    setData?.((prev) =>
      prev.map((item) =>
        isMatchingQuote(item, quoteId) ? updatePostLikeState(item, prevLiked, prevCount) : item,
      ),
    )
  }

  const handleToggleQuoteLike = async (postData: PostCard, newLiked: boolean) => {
    const quoteId = quoteData.quote?.id
    if (!quoteId) return
    const newCount = newLiked ? quoteData.quote!.likesCount + 1 : quoteData.quote!.likesCount - 1
    handleSyncQuoteLike(quoteId, newLiked)

    try {
      if (!quoteId) throw new Error('Invalid quote ID')

      if (newLiked) {
        await PostService.likeQuote(quoteId)
      } else {
        await PostService.dislikeQuote(quoteId)
      }
    } catch (error) {
      rollbackQuoteLike(postData, !newLiked, newLiked ? newCount - 1 : newCount + 1, setData)
      handleError(error)
    }
  }

  const handleDeleteQuote = async () => {
    try {
      if (!quote?.id) return

      await PostService.deleteQuote(quote?.id)

      const quotedFromId = quote?.quotedFrom?.id

      setData?.((prevPosts) =>
        prevPosts
          // 1. Remove the QUOTE post from the list
          .filter((post) => {
            // QUOTE ID to remove
            const quoteId = quoteData?.quote?.id

            // Remove the original QUOTE post
            if (post.type === 'QUOTE' && post.quote?.id === quoteId) return false

            // Remove the REPOST of the QUOTE (linked via repostedItem.id === quoteId)
            if (
              post.type === 'REPOST' &&
              post.repost?.type === 'QUOTE' &&
              post.repost?.repostedItem?.id === quoteId
            ) {
              return false
            }

            return true
          })
          // 2. Update quoteCount on the quoted post
          .map((post) => {
            if (!quotedFromId) return post

            if (post.type === 'ARTICLE' && post.article?.id === quotedFromId) {
              return {
                ...post,
                article: {
                  ...post.article,
                  quoteCount: Math.max((post.article.quoteCount ?? 1) - 1, 0),
                },
              }
            }

            if (post.type === 'REPOST' && post.repost?.repostedItem?.id === quotedFromId) {
              return {
                ...post,
                repost: {
                  ...post.repost,
                  repostedItem: {
                    ...post.repost.repostedItem,
                    quoteCount: Math.max((post.repost.repostedItem.quoteCount ?? 1) - 1, 0),
                  },
                },
              }
            }

            // ✅ Handle nested quotes
            if (post.type === 'QUOTE' && post.quote?.id === quotedFromId) {
              return {
                ...post,
                quote: {
                  ...post.quote,
                  quoteCount: Math.max((post.quote.quoteCount ?? 1) - 1, 0),
                },
              }
            }

            return post
          }),
      )
      if (isViewPost) {
        router.back()
      }

      toast.success('Post Deleted.')
      setConfirmDeleteQuote(false)
    } catch (error) {
      // toast.error('Delete post failed')
      handleError(error)
      setConfirmDeleteQuote(false)
    }
  }

  const handleRepost = async () => {
    try {
      if (!quoteData.quote?.id) return

      await PostService.rePostQuote(quoteData.quote.id)

      const updatedQuoteData = {
        ...quoteData,
        isReposted: true,
        quote: {
          ...quoteData.quote,
          rePostCount: (quoteData.quote.rePostCount ?? 0) + 1,
        },
      }

      // Update view post data if in single post view
      if (isViewPost) {
        setQuoteData?.(updatedQuoteData)
      }

      // Update post list state
      setData?.((prevPosts) =>
        prevPosts.map((post) => {
          // Update the original QUOTE
          if (post.quote?.id === quoteData.quote?.id) {
            return {
              ...post,
              ...updatedQuoteData,
            }
          }

          // Update REPOSTs of this QUOTE
          if (post.type === 'REPOST' && post.repost?.repostedItem?.id === quoteData.quote?.id) {
            return {
              ...post,
              isReposted: true,
              repost: {
                ...post.repost,
                repostedItem: {
                  ...post.repost?.repostedItem,
                  rePostCount: (post.repost?.repostedItem?.rePostCount ?? 0) + 1,
                },
              },
            } as PostCard
          }

          return post
        }),
      )

      toast.success('Post Reposted')
    } catch (error) {
      // toast.error('Repost failed')
      handleError(error)
    }
  }

  const handleUndoRepost = async () => {
    try {
      if (!quoteData.quote?.id) return

      await PostService.undoRepost(quoteData.quote.id)

      const quoteId = quoteData.quote.id

      setData?.((prevPosts) =>
        prevPosts
          // 1. Remove repost version of the quote
          .filter(
            (post) =>
              !(
                post.type === 'REPOST' &&
                post.repost?.type === 'QUOTE' &&
                post.repost?.repostedItem?.id === quoteId
              ),
          )
          // 2. Update rePostCount on the original quote
          .map((post) => {
            if (post.type === 'QUOTE' && post.quote?.id === quoteId) {
              return {
                ...post,
                isReposted: false,
                quote: {
                  ...post.quote,
                  rePostCount: Math.max((post.quote.rePostCount ?? 1) - 1, 0),
                },
              }
            }
            return post
          }),
      )

      toast.success('Repost Removed')
    } catch (error) {
      // toast.error('Undo repost failed')
      handleError(error)
    }
  }

  const getRepostTag = (
    postData: PostCard,
    userProfile: UserProfile,
    allPosts: PostCard[],
  ): ReactNode | null => {
    const repostedBy = new Set<string>()
    const currentUserFullName = `${userProfile.givenName} ${userProfile.familyName}`.trim()

    // Skip for ARTICLE reposts
    if (
      postData.type === 'QUOTE' &&
      allPosts.some(
        (post) => post.type === 'REPOST' && post.repost?.repostedItem?.id === postData.quote?.id,
      )
    ) {
      return null
    }

    // Collect other reposters
    allPosts.forEach((post) => {
      if (
        post.type === 'REPOST' &&
        post.repost?.repostedItem?.id === postData.quote?.id &&
        post.repost?.repostedBy?.username
      ) {
        if (post.repost.repostedBy.username !== userProfile.username) {
          repostedBy.add(
            truncateText(
              capitalizeWords(
                `${post.repost.repostedBy.givenName} ${post.repost.repostedBy.familyName}`.trim(),
              ),
              15, // or any max length you prefer
            ).text,
          )
        }
      }
    })

    // Include current user if they reposted
    if (postData.isReposted) {
      repostedBy.add(currentUserFullName)
    }

    // Format output
    const usernames = Array.from(repostedBy)
    const youIncluded = usernames.includes(currentUserFullName)
    const others = usernames.filter((name) => name !== currentUserFullName)

    if (others.length > 0 && youIncluded) {
      return (
        <div className="flex items-center gap-x-1">
          <RepeatIcon /> {others[0]} and You reposted
        </div>
      )
    } else if (others.length > 0) {
      return (
        <div className="flex items-center gap-x-1">
          <RepeatIcon /> {others[0]} reposted
        </div>
      )
    } else if (youIncluded) {
      return (
        <div
          className="flex items-center gap-x-1"
          //  onClick={() => navigate(`/${userProfile.username}`)}
        >
          <RepeatIcon /> You reposted
        </div>
      )
    }

    return null
  }
  const handleNavigateToViewPost = () => {
    if (isViewPost) return
    router.push(`/feed/quote/${quote?.quoteCode}`)
  }

  return (
    <div
      className={`h-full ${!isViewPost && 'cursor-pointer'} dark:border-dark-lucres-black-300 rounded-lg border`}
    >
      <div className="p-3 pb-1" onClick={handleNavigateToViewPost}>
        <div className="cursor-pointer ps-5">
          {data && userProfile && (
            <div className="text-lucres-gray-700 dark:text-dark-lucres-green-300 flex items-center gap-x-1 pb-0.5 text-sm">
              {getRepostTag(quoteData, userProfile as UserProfile, data)}
            </div>
          )}
          {isViewPost && (
            <div className="flex items-center gap-x-1 pb-0.5 text-sm">
              {quoteData.isReposted && (
                <div className="text-lucres-gray-700 dark:text-dark-lucres-green-300 flex items-center gap-x-1">
                  <RepeatIcon /> You reposted
                </div>
              )}
            </div>
          )}
        </div>

        <div className="flex w-full items-start justify-between">
          <div className="flex w-full items-start gap-0.5">
            <div className="w-14 shrink-0">
              <Avatar
                src={quote?.quotedBy?.profileImage?.url || ''}
                alt={`${quote?.quotedBy?.profileImage?.name}'s Avatar`}
                size={12}
                className="cursor-pointer object-cover"
              />
            </div>
            <div className="flex w-10/12 flex-1  flex-col justify-center sm:w-10/12">
              <div className="flex w-full items-start  justify-between">
                <div className="flex w-full flex-col flex-wrap items-start sm:flex-row sm:items-center">
                  <span
                    className="text-lucres-black dark:text-dark-lucres-green-100 cursor-pointer  font-semibold hover:underline"
                    onClick={handleNavigateToProfile}
                  >
                    {quote && getFullName(quote?.quoter?.givenName, quote?.quoter?.familyName)}
                  </span>
                  <div className="text-lucres-gray-700 dark:text-dark-lucres-green-300 flex items-center text-sm">
                    <DotIcon
                      weight="bold"
                      size={14}
                      className="text-lucres-gray-700 dark:text-dark-lucres-green-100 hidden sm:block"
                    />
                    @{quote?.quoter?.username}
                    <DotIcon
                      weight="bold"
                      size={14}
                      className="text-lucres-gray-700 dark:text-dark-lucres-green-100"
                    />
                    {quote?.createdAt && <TimeAgo dateString={quote.createdAt} />}
                  </div>
                </div>
                {quote?.quoter?.username === userProfile?.username && (
                  <ActionMenu
                    classes="top-3 right-10!"
                    trigger={
                      <IconButton theme="secondary">
                        <DotsThreeIcon
                          size={19}
                          weight="bold"
                          className="text-lucres-gray-700 dark:text-dark-lucres-green-100"
                        />
                      </IconButton>
                    }
                    menuItems={[
                      {
                        label: 'Delete',
                        icon: <TrashIcon size={19} weight="bold" />,
                        className: 'text-red-500',
                        onClick: (close: any) => {
                          setConfirmDeleteQuote(true)
                          close()
                        },
                      },
                    ]}
                  />
                )}
              </div>
              <div className="mt-0 w-full">
                <div className="text-lucres-900 w-full">
                  <div className="text-lucres-900 dark:text-dark-lucres-green-100 break-words">
                    {quote?.content && <ContentWithMentions text={quote?.content} />}
                  </div>
                  <div
                    className="dark:border-dark-lucres-black-300 me-1  mt-2 cursor-pointer rounded-lg border p-3 sm:me-3"
                    onClick={handleNavigateToNestedQuote}
                  >
                    {quote?.quotedFrom.id ? (
                      <>
                        <div className="flex w-full items-start justify-between">
                          <div className="flex items-center gap-x-2 sm:items-start">
                            <div className="min-w-8">
                              <Avatar
                                src={quote?.quotedFrom?.user?.profileImage?.url || ''}
                                alt={`${quote?.quotedFrom?.user?.profileImage?.name}'s Avatar`}
                                size={8}
                                className="cursor-pointer object-cover"
                              />
                            </div>
                            <div className="flex flex-col justify-center sm:flex-row">
                              <div className="flex flex-wrap items-center">
                                <span
                                  className="text-lucres-black dark:text-dark-lucres-green-100 cursor-pointer text-base font-bold hover:underline"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    router.push(`/${quote.quotedFrom.user.username}/resume`)
                                  }}
                                >
                                  {getFullName(
                                    quote?.quotedFrom.user?.givenName,
                                    quote?.quotedFrom.user?.familyName,
                                  )}
                                </span>
                              </div>
                              <div className="text-lucres-gray-700 dark:text-dark-lucres-green-300 flex items-center text-sm">
                                <DotIcon
                                  weight="bold"
                                  size={14}
                                  className="text-lucres-gray-700 dark:text-dark-lucres-green-100 hidden sm:block"
                                />
                                @{quote?.quotedFrom?.user?.username}
                                <DotIcon
                                  weight="bold"
                                  size={14}
                                  className="text-lucres-gray-700 dark:text-dark-lucres-green-100"
                                />
                                {quote?.quotedFrom && (
                                  <TimeAgo dateString={quote?.quotedFrom?.createdAt} />
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className=" flex w-full flex-row">
                          <div className="text-lucres-900 w-full ">
                            <div className="text-lucres-900 dark:text-dark-lucres-green-100 break-words">
                              {<ContentWithMentions text={quote?.quotedFrom?.content} />}
                            </div>
                            {quote?.quotedFrom?.images &&
                              quote?.quotedFrom.images.length > 0 &&
                              quote?.quotedFrom?.articleCode !== undefined && (
                                <div className="mt-2">
                                  <ImageCarousel
                                    images={quote?.quotedFrom.images}
                                    // id={quote?.quotedFrom.articleCode}
                                    id={`carousel-${Math.random().toString(36).substring(2, 9)}`}
                                  />
                                </div>
                              )}
                          </div>
                        </div>
                      </>
                    ) : (
                      <div className="dark:text-dark-lucres-green-100">This post is unavaiable</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-1 flex w-full flex-wrap-reverse justify-between">
          <div className="min-w-12 shrink-0"></div>
          <div className="flex w-full flex-1 justify-between">
            <div className="ms-0 flex items-start justify-start gap-8">
              {quote?.id && (
                <Like
                  isLiked={quoteData.isLiked}
                  likeCount={quoteData.quote?.likesCount ?? 0}
                  tooltipText={quoteData.isLiked ? 'Unlike' : 'Like'}
                  onToggleLike={(newLiked) => {
                    handleToggleQuoteLike(quoteData, newLiked)
                  }}
                />
              )}

              <div className="flex items-center gap-x-0.5">
                <IconButton onClick={handleOpenComment}>
                  <ChatCircleIcon
                    weight="bold"
                    size={19}
                    className={`text-lucres-gray-700 dark:text-dark-lucres-green-100 cursor-pointer rounded-md`}
                  />
                </IconButton>
                <span className="text-lucres-gray-700 dark:text-dark-lucres-green-100">
                  {quoteData.quote?.commentsCount}
                </span>
              </div>
              <div className="flex items-center gap-x-0.5">
                <ActionMenu
                  classes="top-10 w-40 left-1/2! -translate-x-1/2"
                  trigger={
                    <IconButton>
                      <RepeatIcon
                        size={19}
                        //  weight={isReposted ? 'fill' : 'regular'}
                        weight="bold"
                        className={`${quoteData.isReposted ? 'text-lucres-700' : 'text-lucres-gray-700 dark:text-dark-lucres-green-100'}`}
                      />
                    </IconButton>
                  }
                  menuItems={[
                    {
                      label: quoteData.isReposted ? 'Undo repost' : 'Repost',
                      icon: <RepeatIcon size={19} weight="bold" />,
                      onClick: (close: any) => {
                        close()
                        if (quoteData.isReposted) {
                          // Handle undo repost action here
                          handleUndoRepost()
                        } else {
                          // Handle repost action here
                          handleRepost()
                        }
                      },
                    },
                    {
                      label: 'Quote',
                      icon: <PencilLineIcon size={19} weight="bold" />,
                      onClick: (close: any) => {
                        close()
                        setShowQuoteModal(true)
                      },
                    },
                  ]}
                />
                <span
                  className={`${quoteData.isReposted ? 'text-lucres-700' : 'text-lucres-gray-700 dark:text-dark-lucres-green-100'}`}
                >
                  {/* {quoteData.quote?.rePostCount} */}
                  {(quoteData.quote?.rePostCount ?? 0) + (quoteData.quote?.quoteCount ?? 0)}
                </span>
              </div>
              <CopyLinkIcon
                toCopy={`${location.origin}/feed/quote/${quoteData.quote?.quoteCode}`}
              />
            </div>
            {/* <div>
              <Tooltip text={'Save'} classes="whitespace-nowrap   text-center" direction="bottom">
                <IconButton>
                  <BookmarkSimpleIcon
                    size={19}
                    weight="bold"
                    className="text-lucres-gray-700 dark:text-dark-lucres-green-100 cursor-pointer rounded-md"
                  />
                </IconButton>
              </Tooltip>
            </div> */}
          </div>
        </div>

        {/* Quote a post */}
        {showQuoteModal && (
          <Overlay
            heading="Quote and Repost"
            size="min-w-xl! max-w-xl! min-h-fit!"
            handleClose={() => {}}
          >
            <CreateQuote
              post={quoteData}
              handleClose={() => setShowQuoteModal(false)}
              setData={setData}
              type="QUOTE"
              isViewPost={isViewPost}
              setPostData={setQuoteData}
            />
          </Overlay>
        )}

        {confirmDeleteQuote && (
          <Overlay
            heading="Delete Post"
            handleClose={() => setConfirmDeleteQuote(false)}
            size="min-h-fit mt-20"
            classes="p-0! "
          >
            <div className="dark:bg-dark-lucres-black-500 w-full rounded-lg bg-white p-4 shadow-lg">
              <div className="flex flex-col items-center">
                <WarningIcon size={32} />
                <h2 className="mt-3 text-center text-lg font-semibold">Delete This Post?</h2>
                <p className="text-center text-sm text-gray-500 dark:text-gray-300">
                  Are you sure you want to delete this post?
                </p>
              </div>
              <div className="dark:border-t-dark-lucres-black-200 mt-4 flex justify-between gap-4 border-t pt-4">
                <Button
                  size="small"
                  theme="transparent"
                  className="px-4! py-1.5! !border"
                  onClick={() => setConfirmDeleteQuote(false)}
                >
                  Cancel
                </Button>
                <Button
                  size="small"
                  theme="dark"
                  className="px-4! py-1.5! text-white!"
                  onClick={handleDeleteQuote}
                >
                  Yes, Delete
                </Button>
              </div>
            </div>
          </Overlay>
        )}
      </div>
      {showComments && (
        <div className={`hidden w-full items-center md:flex`}>
          {quoteData.quote?.id && (
            <Comments
              postId={quoteData.quote?.id}
              // setCommentsCount={setCommentsCount}
              type="QUOTE"
              setData={setData}
              setPostData={setQuoteData}
              isViewPost={isViewPost}
            />
          )}
        </div>
      )}
      {toggleBottomSlider && (
        <div className="relative z-50 flex w-full flex-col items-center justify-center transition-transform duration-700 md:hidden">
          <SlideUpView isOpen={toggleBottomSlider}>
            <div className="h-full w-full flex-1">
              {quoteData.quote?.id && (
                <Comments
                  postId={quoteData.quote?.id}
                  // setCommentsCount={setCommentsCount}
                  handleClose={() => setToggleBottomSlider(false)}
                  type="QUOTE"
                  setData={setData}
                  setPostData={setQuoteData}
                  isViewPost={isViewPost}
                />
              )}
            </div>
          </SlideUpView>
        </div>
      )}
    </div>
  )
}

export default QuoteCard
