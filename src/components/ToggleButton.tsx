import React from 'react'

interface ToggleSwitchProps {
  label?: string
  isRequired: boolean
  onToggle: () => void
}

const ToggleButton: React.FC<ToggleSwitchProps> = ({ label, isRequired, onToggle }) => {
  return (
    <div className="mr-3 flex items-center gap-2">
      {label && <span className="text-sm font-medium">{label}</span>}
      <div
        className={`relative flex h-6 w-11 cursor-pointer items-center rounded-full transition-all duration-300 ${
          isRequired
            ? 'bg-lime-300 dark:bg-white'
            : 'bg-dark-lucres-green-200 dark:bg-dark-lucres-black-200'
        }`}
        onClick={onToggle}
        role="switch"
        aria-checked={isRequired}
        aria-label="Toggle question requirement"
      >
        <div
          className={`absolute inset-y-0 left-1 my-auto h-4 w-4 rounded-full bg-white transition-transform dark:bg-black ${
            isRequired ? 'translate-x-[20px]' : ''
          }`}
        />
      </div>
    </div>
  )
}

export default ToggleButton
