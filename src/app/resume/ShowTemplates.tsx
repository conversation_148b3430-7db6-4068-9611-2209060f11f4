'use client'
import { Template } from './ResumeTemplates/templates'
import { DummyResumeData, Resume } from '../../models/Resume'
import { useState } from 'react'
import { ArrowLeftIcon, CaretDownIcon, CaretUpIcon, StarIcon } from '@phosphor-icons/react'

interface ShowTemplatesProps {
  setSelectedTemplate: (id: number) => void
  selectedTemplate: number
  templates: Template[]
  setShowTemplates: (showTemplate: boolean) => void
  setResumeData: (data: Resume) => void
}

const ShowTemplates: React.FC<ShowTemplatesProps> = ({
  setSelectedTemplate,
  selectedTemplate,
  templates,
  setShowTemplates,
  setResumeData,
}) => {
  const [isExpanded, setIsExpanded] = useState(true)

  const handleSelectTemplate = (id: number) => {
    const template = templates.find((t) => t.id === id)
    if (template) {
      const initialData = getInitialDataForTemplate(template)
      setResumeData(initialData)
      setSelectedTemplate(id)
      setShowTemplates(false)
    }
  }

  const getInitialDataForTemplate = (_template: Template): Resume => {
    return DummyResumeData
  }

  return (
    <div className="h-full w-full">
      <div className="mb-4 flex items-center justify-between">
        <div
          className="flex cursor-pointer items-center gap-x-2"
          onClick={() => setShowTemplates(false)}
        >
          <ArrowLeftIcon size={16} weight="bold" />
          <span className="text-sm font-semibold">Back to Editor</span>
        </div>
      </div>
      <div
        className="hidden w-full cursor-pointer items-center justify-between font-bold xl:flex dark:text-white"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <span>Templates</span>
        <span>{isExpanded ? <CaretUpIcon size={20} /> : <CaretDownIcon size={20} />}</span>
      </div>
      <span className="mt-8 flex w-full items-center justify-start text-center font-bold sm:mt-0 xl:hidden">
        Templates
      </span>
      <div
        className={`${
          isExpanded ? 'h-full opacity-100' : 'h-0 opacity-0'
        } mt-6 flex w-full flex-wrap gap-4 transition-all duration-700 ease-in-out xl:mt-12`}
      >
        {templates.map((template) => (
          <div
            key={template.id}
            className={`${
              selectedTemplate === template.id && 'border-lucres-500 rounded-lg border-8'
            } relative h-auto w-full sm:w-[48%] xl:w-[48%]`}
            onClick={() => handleSelectTemplate(template.id)}
          >
            <img
              src={template.thumbnail}
              alt="Template preview"
              className="h-auto w-full cursor-pointer xl:h-96"
            />
            {template.isPro && (
              <span className="dark:bg-dark-lucres-black-200 absolute bottom-3 right-10 flex items-center gap-x-1 rounded-lg bg-yellow-400 px-3 py-1 text-sm font-bold">
                <StarIcon size={16} weight="fill" />
                PRO
              </span>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

export default ShowTemplates
