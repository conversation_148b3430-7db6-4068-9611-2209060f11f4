'use client'
import { useState, useEffect, useMemo, useRef } from 'react'
import { useAuth } from '../../context/AuthContext'
import {
  ArrowsInLineHorizontalIcon,
  ArrowsOutLineHorizontalIcon,
  CaretDownIcon,
  CaretUpIcon,
  PlusIcon,
  SparkleIcon,
  TrashIcon,
  UploadSimpleIcon,
} from '@phosphor-icons/react'
import Input from '../../components/Input'
import Button from '../../components/Button'
import {
  Achievement,
  ClearedSections,
  Education,
  Employment,
  Language,
  Reference,
  Resume,
  Skill,
  SocialLink,
} from '../../models/Resume'
import Section from './CreateResume/Section'
import EmploymentCard from './CreateResume/EmploymentCard'
import EducationCard from './CreateResume/EducationCard'
import AchievementCard from './CreateResume/AchievementCard'
import { CareerService } from '../../services/CareerService'
import { useToast } from '../../components/ToastX'
import { convertToISODate } from '../../utils/resumeUtils'
import { validateField, validateResume, hasErrors, Errors } from './resumeValidation'
import SkillsCard from './CreateResume/SkillsCard'
import LanguagesCard from './CreateResume/LanguagesCard'
import Tooltip from '../../components/Tooltip'
import { AISuggestionService } from '../../services/AISuggestionService'
import { useAccount } from '../../context/UserDetailsContext'
import { useRouter } from 'next/navigation'
import { useResumeProgress } from '@/utils/useResumeProgress'

interface CreateResumeProps {
  toggleViewResume: () => void
  resume: Resume
  setResume: React.Dispatch<React.SetStateAction<Resume>>
  originalResume: Resume
  onSaveSuccess: () => void
  setClearedSections: React.Dispatch<React.SetStateAction<ClearedSections>>
  isProTemplate: boolean
  onProgressChange: (progress: number) => void
}

// Define UpdatePayload interface for the API payload structure
interface UpdatePayload {
  aboutMe?: { updated: string }
  experience?: {
    updated: Array<{ id: string; delta: Partial<Employment> }>
    removed: string[]
    added: Partial<Employment>[]
  }
  education?: {
    updated: Array<{ id: string; delta: Partial<Education> }>
    removed: string[]
    added: Partial<Education>[]
  }
  achievement?: {
    updated: Array<{ id: string; delta: Partial<Achievement> }>
    removed: string[]
    added: Partial<Achievement>[]
  }
  skills?: { removed: string[]; added: Partial<Skill>[] }
  languages?: { removed: string[]; added: Partial<Language>[] }
  socialLinks?: { removed: string[]; added: Partial<SocialLink>[] }
  references?: { removed: string[]; added: Partial<Reference>[] }
}

// Function to build the update payload (delta) based on current and original resume states
const buildUpdatePayload = (
  resume: Resume,
  originalResume: Resume,
  removeSummary: boolean,
): UpdatePayload => {
  const body: UpdatePayload = {}

  const allowedFields: Record<string, string[]> = {
    experience: [
      'designation',
      'company',
      'employmentType',
      'startDate',
      'endDate',
      'isCurrent',
      'description',
      'location',
    ],
    education: ['degree', 'institution', 'startYear', 'endYear', 'description', 'location'],
    achievement: ['title', 'awardedBy', 'date'],
    skills: ['name', 'level'],
    languages: ['name', 'level', 'other'],
    socialLinks: ['title', 'url'],
    references: ['name', 'email'],
  }

  const sectionsWithUpdate = ['experience', 'education', 'achievement']
  const arraySections: Array<{ apiKey: keyof UpdatePayload; stateKey: keyof Resume }> = [
    { apiKey: 'experience', stateKey: 'experiences' },
    { apiKey: 'education', stateKey: 'educations' },
    { apiKey: 'achievement', stateKey: 'achievements' },
    { apiKey: 'skills', stateKey: 'skills' },
    { apiKey: 'languages', stateKey: 'languages' },
    { apiKey: 'socialLinks', stateKey: 'socialLinks' },
    { apiKey: 'references', stateKey: 'references' },
  ]

  const pickFields = <T extends Record<string, any>>(obj: T, fields: string[]): Partial<T> => {
    return fields.reduce((acc, field) => {
      if (Object.prototype.hasOwnProperty.call(obj, field)) {
        acc[field as keyof T] = obj[field]
      }
      return acc
    }, {} as Partial<T>)
  }

  type EmploymentBackendDelta = Partial<
    Omit<Employment, 'startDate' | 'endDate'> & {
      startDate: string | null
      endDate: string | null
    }
  >

  const convertEmploymentDates = (item: Partial<Employment>): EmploymentBackendDelta => {
    const cleanItem = Object.fromEntries(
      Object.entries(item).filter(([_, v]) => v !== undefined),
    ) as Partial<Employment>
    if (cleanItem.isCurrent) {
      return {
        ...cleanItem,
        startDate: cleanItem.startDate ? convertToISODate(cleanItem.startDate) : undefined,
        endDate: undefined,
      }
    }
    return {
      ...cleanItem,
      startDate: cleanItem.startDate ? convertToISODate(cleanItem.startDate) : undefined,
      endDate: cleanItem.endDate ? convertToISODate(cleanItem.endDate) : undefined,
    }
  }

  type EducationBackendDelta = Partial<Education>

  const convertEducationDates = (item: Partial<Education>): EducationBackendDelta => {
    const cleanItem = Object.fromEntries(
      Object.entries(item).filter(([key, v]) => {
        if (key === 'description') {
          return v !== undefined && v !== ''
        }
        return v !== undefined
      }),
    ) as Partial<Education>
    return cleanItem
  }

  type AchievementBackendDelta = Partial<
    Omit<Achievement, 'date'> & {
      date: string | null
    }
  >

  const convertAchievementDates = (item: Partial<Achievement>): AchievementBackendDelta => {
    const cleanItem = Object.fromEntries(
      Object.entries(item).filter(([key, v]) => {
        if (key === 'awardedBy') {
          return v !== undefined && v !== ''
        }
        return v !== undefined
      }),
    ) as Partial<Achievement>
    return {
      ...cleanItem,
      date: cleanItem.date ? convertToISODate(cleanItem.date) : undefined,
    }
  }

  arraySections.forEach(({ apiKey, stateKey }) => {
    const current = resume[stateKey] as Array<any>
    const original = originalResume[stateKey] as Array<any>
    const hasUpdate = sectionsWithUpdate.includes(apiKey as string)

    const currentWithId = current.filter((item) => item.id)
    const currentWithoutId = current.filter((item) => !item.id)
    const originalIds = original.map((item) => item.id).filter((id): id is string => !!id)
    const currentIds = currentWithId.map((item) => item.id).filter((id): id is string => !!id)
    const removed = originalIds.filter((id) => !currentIds.includes(id))

    if (hasUpdate) {
      const updated = currentWithId
        .map((item) => {
          const originalItem = original.find((o) => o.id === item.id)
          if (originalItem) {
            const isChanged = allowedFields[apiKey].some(
              (field) =>
                JSON.stringify(item[field as keyof typeof item]) !==
                JSON.stringify(originalItem[field as keyof typeof originalItem]),
            )
            if (isChanged) {
              const delta = pickFields(item, allowedFields[apiKey])
              if (apiKey === 'experience') {
                return { id: item.id, delta: convertEmploymentDates(delta) }
              } else if (apiKey === 'education') {
                return { id: item.id, delta: convertEducationDates(delta) }
              } else if (apiKey === 'achievement') {
                return { id: item.id, delta: convertAchievementDates(delta) }
              }
              return { id: item.id, delta }
            }
          }
          return null
        })
        .filter((item): item is { id: string; delta: any } => item !== null)
      let added = currentWithoutId.map((item) => pickFields(item, allowedFields[apiKey]))
      if (apiKey === 'experience') {
        added = added.map((item) => convertEmploymentDates(item as Partial<Employment>))
      } else if (apiKey === 'education') {
        added = added.map((item) => convertEducationDates(item as Partial<Education>))
      } else if (apiKey === 'achievement') {
        added = added.map((item) => convertAchievementDates(item as Partial<Achievement>))
      }
      if (updated.length > 0 || removed.length > 0 || added.length > 0) {
        body[apiKey] = { updated, removed, added } as any
      }
    } else {
      const modified = currentWithId
        .map((item) => {
          const originalItem = original.find((o) => o.id === item.id)
          if (originalItem) {
            const isChanged = allowedFields[apiKey].some(
              (field) =>
                JSON.stringify(item[field as keyof typeof item]) !==
                JSON.stringify(originalItem[field as keyof typeof originalItem]),
            )
            if (isChanged) {
              return { removeId: item.id, addItem: pickFields(item, allowedFields[apiKey]) }
            }
          }
          return null
        })
        .filter((item): item is { removeId: string; addItem: any } => item !== null)

      const removedModifiedIds = modified.map((m) => m.removeId)
      const addedModified = modified.map((m) => m.addItem)
      const addedNew = currentWithoutId.map((item) => pickFields(item, allowedFields[apiKey]))

      const removedTotal = [...removed, ...removedModifiedIds]
      const addedTotal = [...addedModified, ...addedNew]

      if (removedTotal.length > 0 || addedTotal.length > 0) {
        body[apiKey] = { removed: removedTotal, added: addedTotal } as any
      }
    }
  })

  if (resume.aboutMe !== originalResume.aboutMe || removeSummary) {
    const aboutMeValue = removeSummary ? '' : resume.aboutMe
    body.aboutMe = { updated: aboutMeValue }
  }

  return body
}

const CreateResume: React.FC<CreateResumeProps> = ({
  toggleViewResume,
  resume,
  setResume,
  originalResume,
  isProTemplate,
  onProgressChange,
  setClearedSections,
}) => {
  const { authUserProfile } = useAuth()
  const toast = useToast()
  const { setResumeProgress } = useAccount()
  const [removeSummary, setRemoveSummary] = useState<boolean>(false)
  const [removeLanguages, setRemoveLanguages] = useState(false)
  const [skill, setSkill] = useState('')
  const [languages, setLanguages] = useState('')
  const [hasChanges, setHasChanges] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingAction, setProcessingAction] = useState<string | null>(null)
  const router = useRouter()
  //for dynamic about me section height
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [resume.aboutMe])

  const [originalResumeState, setOriginalResumeState] = useState<Resume>(originalResume)
  useEffect(() => {
    setOriginalResumeState(originalResume)
  }, [originalResume])

  // Determine context for AI skill suggestions
  const getSkillsContext = (): string => {
    // Experienced user: Use most recent job title
    if (resume.experiences.length > 0) {
      const sortedExperiences = [...resume.experiences].sort((a, b) => {
        const dateA = a.startDate ? new Date(a.startDate).getTime() : 0
        const dateB = b.startDate ? new Date(b.startDate).getTime() : 0
        return dateB - dateA
      })
      const mostRecentJob = sortedExperiences[0]
      return mostRecentJob?.designation?.name || ''
    }
    // Fresher: Use degree from most recent education
    if (resume.educations.length > 0) {
      const sortedEducations = [...resume.educations].sort(
        (a, b) => (b.startYear || 0) - (a.startYear || 0),
      )
      const mostRecentEducation = sortedEducations[0]
      return mostRecentEducation?.degree?.name || ''
    }
    // Fallback: Use authUserProfile.title
    return authUserProfile?.title || ''
  }

  const skillsContext = getSkillsContext()

  const handleAchievementUpdate = (index: number, field: keyof Achievement, value: string) => {
    setResume((prev) => {
      const updatedAchievements = [...prev.achievements]
      updatedAchievements[index] = { ...updatedAchievements[index], [field]: value }
      return { ...prev, achievements: updatedAchievements }
    })

    // Validate the field
    const error = validateField('achievements', field, value, index)

    // Update errors state
    setErrors((prevErrors) => {
      const updatedErrors = { ...prevErrors }
      if (!updatedErrors.achievements) updatedErrors.achievements = []
      // Ensure the array is long enough
      while (updatedErrors.achievements.length <= index) {
        updatedErrors.achievements.push({})
      }
      updatedErrors.achievements[index] = {
        ...updatedErrors.achievements[index],
        [field]: error,
      }
      return updatedErrors
    })
  }

  const updateNestedField = <T extends Record<string, any>>(
    obj: T,
    fieldPath: string,
    value: any,
  ): T => {
    const keys = fieldPath.split('.')
    if (keys.length === 1) {
      return { ...obj, [keys[0]]: value }
    }
    const [firstKey, ...restKeys] = keys
    return {
      ...obj,
      [firstKey]: updateNestedField(obj[firstKey] || {}, restKeys.join('.'), value),
    }
  }

  const handleEducationUpdate = (index: number, field: string, value: string | number) => {
    setResume((prev) => {
      const updatedEducations = [...prev.educations]
      updatedEducations[index] = updateNestedField(updatedEducations[index], field, value)
      return { ...prev, educations: updatedEducations }
    })

    // Update errors with a callback to ensure the latest state
    setErrors((prevErrors) => {
      const updatedErrors = { ...prevErrors }
      if (!updatedErrors.educations) updatedErrors.educations = []
      while (updatedErrors.educations.length <= index) {
        updatedErrors.educations.push({})
      }

      // Compute the updated education object
      const currentEdu = resume.educations[index]
      const updatedEdu = updateNestedField(currentEdu, field, value)

      // Validate the updated field
      const error = validateField('educations', field, value, index, updatedEdu)
      updatedErrors.educations[index][field] = error

      // If 'startYear' is updated, re-validate 'endYear'
      if (field === 'startYear') {
        const endYearValue = updatedEdu.endYear
        const endYearError = validateField('educations', 'endYear', endYearValue, index, updatedEdu)
        updatedErrors.educations[index].endYear = endYearError
      }

      return updatedErrors
    })
  }

  const handleExperienceUpdate = (index: number, field: string, value: string | boolean) => {
    setResume((prev) => {
      const updatedExperiences = [...prev.experiences]
      updatedExperiences[index] = updateNestedField(updatedExperiences[index], field, value)
      return { ...prev, experiences: updatedExperiences }
    })

    setErrors((prevErrors) => {
      const updatedErrors = { ...prevErrors }
      if (!updatedErrors.experiences) updatedErrors.experiences = []
      while (updatedErrors.experiences.length <= index) {
        updatedErrors.experiences.push({})
      }

      // Get the current employment object and apply the update
      const currentExp = resume.experiences[index]
      const updatedExp = updateNestedField(currentExp, field, value)

      // Validate the updated field
      const error = validateField('experiences', field, value, index, updatedExp)
      updatedErrors.experiences[index][field] = error

      // If 'startDate' is updated, re-validate 'endDate'
      if (field === 'startDate') {
        const endDateValue = updatedExp.endDate
        const endDateError = validateField(
          'experiences',
          'endDate',
          endDateValue,
          index,
          updatedExp,
        )
        updatedErrors.experiences[index].endDate = endDateError
      }

      return updatedErrors
    })
  }

  const handleAboutMeUpdate = (value: string) => {
    setResume((prev) => ({ ...prev, aboutMe: value }))
    const error = validateField('aboutMe', 'aboutMe', value)
    setErrors((prevErrors) => ({ ...prevErrors, aboutMe: error }))
  }

  const validateSkills = (skills: Skill[]) => {
    if (skills.length === 0) {
      return [{ name: 'At least one skill is required !!' }]
    }
    return skills.map((skill, index) => ({
      name: validateField('skills', 'name', skill.name, index),
      level: validateField('skills', 'level', skill.level, index),
    }))
  }

  useEffect(() => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      skills: validateSkills(resume.skills),
    }))
  }, [resume.skills])

  const handleArraySectionUpdate = (
    section: 'socialLinks' | 'references',
    index: number,
    field: string,
    value: string,
  ) => {
    setResume((prev) => {
      const updatedSection = [...prev[section]]
      updatedSection[index] = { ...updatedSection[index], [field]: value }
      return { ...prev, [section]: updatedSection }
    })
    const error = validateField(section, field, value, index)
    setErrors((prevErrors) => {
      const updatedErrors = { ...prevErrors }
      if (!updatedErrors[section]) updatedErrors[section] = []
      while (updatedErrors[section].length <= index) {
        updatedErrors[section].push({})
      }
      updatedErrors[section][index] = {
        ...updatedErrors[section][index],
        [field]: error,
      }
      return updatedErrors
    })
  }

  const [errors, setErrors] = useState<Errors>({
    experiences: [],
    educations: [],
    achievements: [],
    skills: [],
    languages: [],
    socialLinks: [],
    references: [],
  })

  // AI suggestions handler for about me
  const handleAISuggestion = async (action: 'improve' | 'longer' | 'shorter') => {
    if (!authUserProfile?.id) {
      toast.error('User ID not found. Please log in again.')
      return
    }
    if (isProcessing) return

    setProcessingAction(action)
    setIsProcessing(true)

    const userId = authUserProfile.id
    const section = 'aboutMe'
    let context
    let apiAction
    let lastResponse

    if (action === 'improve' && resume.aboutMe.trim() === '') {
      context = resume.personalDetails.profile || authUserProfile.title || 'professional'
    } else if (resume.aboutMe.trim() === '') {
      toast.error(`Please enter some text first to make it ${action}.`)
      setIsProcessing(false)
      setProcessingAction(null)
      return
    } else {
      apiAction = action
      lastResponse = resume.aboutMe
    }

    try {
      const response = await AISuggestionService.getSuggestion(
        'user',
        userId,
        section,
        context,
        apiAction,
        lastResponse,
      )

      if (response.status === 'success') {
        const MAX_LENGTH = 1000
        const newAboutMe = response.data.slice(0, MAX_LENGTH)
        setResume((prev) => ({ ...prev, aboutMe: newAboutMe }))
        setClearedSections((prev) => ({ ...prev, aboutMe: false }))
        const actionVerbs = {
          improve: 'improved',
          longer: 'made longer',
          shorter: 'made shorter',
        }
        toast.success(`About Me ${actionVerbs[action]} successfully!`)
      } else {
        toast.error(`Failed to ${action} About Me section.`)
      }
    } catch (error: any) {
      toast.error(error.message || `An error occurred while trying to ${action} About Me section.`)
    } finally {
      setIsProcessing(false)
      setProcessingAction(null)
    }
  }
  const handleSave = async () => {
    if (!authUserProfile?.permalink) {
      toast.error('User profile not loaded. Cannot save resume.')
      return
    }

    if (!originalResume) {
      toast.error('Original resume data not loaded. Cannot save.')
      return
    }

    // Validate the resume
    const validationErrors = validateResume(resume, { languagesOptional: removeLanguages })
    if (hasErrors(validationErrors)) {
      setErrors(validationErrors)
      toast.error('Please complete all required fields before saving your resume.', {
        duration: 3500,
      })
      return
    }

    const maxItems = {
      experiences: 10,
      educations: 10,
      achievements: 10,
      skills: 20,
      languages: 20,
      socialLinks: 2,
      references: 2,
    }
    if (resume.experiences.length > maxItems.experiences) {
      toast.error('You can only add up to 10 Experiences')
      return
    }
    if (resume.educations.length > maxItems.educations) {
      toast.error('You can only add up to 10 Educations')
      return
    }
    if (resume.achievements.length > maxItems.achievements) {
      toast.error('You can only add up to 10 Achievements')
      return
    }
    if (resume.skills.length > maxItems.skills) {
      toast.error('You can only add up to 20 Skills')
      return
    }
    if (resume.languages.length > maxItems.languages) {
      toast.error('You can only add up to 20 Languages')
      return
    }
    if (resume.socialLinks.length > maxItems.socialLinks) {
      toast.error('You can only add up to 2 Social Links')
      return
    }
    if (resume.references.length > maxItems.references) {
      toast.error('You can only add up to 2 References')
      return
    }

    // Function to build the update payload (delta) based on current and original resume states
    const body = buildUpdatePayload(resume, originalResumeState, removeSummary)
    if (Object.keys(body).length === 0) {
      toast.info('No changes to save.')
      return
    }

    try {
      const response = await CareerService.updateCareerByPermalink(authUserProfile.permalink, body)
      if (response.status === 'success') {
        onSaveSuccess()
        setHasChanges(false)
        setOriginalResumeState(resume)
      } else {
        toast.error('Please complete all required fields before saving your resume.', {
          duration: 3500,
        })
      }
    } catch (error: any) {
      console.error('Error saving resume:', error)
      toast.error(error.message || 'Something went wrong while saving!')
    }
  }

  // Compute the delta and determine if there are changes
  useEffect(() => {
    const delta = buildUpdatePayload(resume, originalResumeState, removeSummary)
    setHasChanges(Object.keys(delta).length > 0)
  }, [resume, originalResumeState, removeSummary])

  const [jobVisibility, setJobVisibility] = useState(resume.experiences.map(() => true))
  const [educationVisibility, setEducationVisibility] = useState(resume.educations.map(() => true))
  const [employmentSuggestions, setEmploymentSuggestions] = useState<string[]>([])
  const [educationSuggestions, setEducationSuggestions] = useState<string[]>([])

  useEffect(() => {
    const initialEmploymentSuggestions = resume.experiences.map(() =>
      generateEmploymentSuggestion(),
    )
    const initialEducationSuggestions = resume.educations.map(() => generateEducationSuggestion())
    setEmploymentSuggestions(initialEmploymentSuggestions)
    setEducationSuggestions(initialEducationSuggestions)
  }, [resume.experiences.length, resume.educations.length])

  type SectionName =
    | 'summary'
    | 'personalDetails'
    | 'employment'
    | 'education'
    | 'skills'
    | 'languages'
    | 'socialLinks'
    | 'references'
    | 'achievements'

  const [visibleSections, setVisibleSections] = useState<Record<SectionName, boolean>>({
    summary: true,
    personalDetails: true,
    employment: true,
    education: true,
    skills: true,
    languages: true,
    socialLinks: true,
    references: true,
    achievements: true,
  })

  const toggleSectionVisibility = (section: SectionName) => {
    setVisibleSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  const toggleJobVisibility = (index: number) => {
    setJobVisibility((prevVisibility) => {
      const updatedVisibility = [...prevVisibility]
      updatedVisibility[index] = !updatedVisibility[index]
      return updatedVisibility
    })
  }

  const toggleEducationVisibility = (index: number) => {
    setEducationVisibility((prevVisibility) => {
      const updatedVisibility = [...prevVisibility]
      updatedVisibility[index] = !updatedVisibility[index]
      return updatedVisibility
    })
  }

  // Functions to add data in each section
  const addEmploymentHistory = () => {
    if (resume.experiences.length >= 10) {
      toast.error('You can only add up to 10 Experiences')
      return
    }
    setResume((prevResume) => ({
      ...prevResume,
      experiences: [
        ...prevResume.experiences,
        {
          location: {
            address: { country: 'USA', state: 'California', city: '', area: 'Downtown' },
            latitude: 37.7749,
            longitude: -122.4194,
          },
          industry: { other: false, name: '' },
          employmentType: '',
          isActive: false,
          company: { other: false, name: '' },
          designation: { other: false, name: '' },
          startDate: '',
          endDate: '',
          description: '',
          isCurrent: false,
        },
      ],
    }))
    setJobVisibility((prevVisibility) => [...prevVisibility, true])
    setEmploymentSuggestions((prevSuggestions) => [
      ...prevSuggestions,
      generateEmploymentSuggestion(),
    ])
  }

  const addEducation = () => {
    if (resume.educations.length >= 10) {
      toast.error('You can only add up to 10 Educations')
      return
    }
    setResume((prevResume) => ({
      ...prevResume,
      educations: [
        ...prevResume.educations,
        {
          location: {
            address: { country: 'USA', state: 'California', city: '', area: 'Downtown' },
            latitude: 37.7749,
            longitude: -122.4194,
          },
          institution: { other: false, name: '' },
          description: '',
          startYear: 0,
          endYear: 0,
          degree: { other: false, name: '' },
          isCurrent: false,
        },
      ],
    }))
    setEducationVisibility((prevVisibility) => [...prevVisibility, true])
    setEducationSuggestions((prevSuggestions) => [
      ...prevSuggestions,
      generateEducationSuggestion(),
    ])
  }

  const addAchievement = () => {
    if (resume.achievements.length >= 10) {
      toast.error('You can only add up to 10 Achievements')
      return
    }
    setResume((prevResume) => ({
      ...prevResume,
      achievements: [
        ...prevResume.achievements,
        { title: '', awardedBy: '', description: '', date: '', isActive: false },
      ],
    }))
  }

  const handleAddSkill = (name: string) => {
    const trimmedName = name.trim()
    const error = validateField('skills', 'name', trimmedName, resume.skills.length)
    if (error) {
      toast.error(error)
      return
    }
    if (resume.skills.length >= 20) {
      toast.error('You can only add up to 20 Skills')
      return
    }
    const newSkill: Skill = { name: trimmedName, level: 1 }
    if (!resume.skills.some((s) => s.name === trimmedName)) {
      setResume((prev) => ({
        ...prev,
        skills: [...prev.skills, newSkill],
      }))
    }
  }

  const handleAddLanguage = (name: string) => {
    const trimmedName = name.trim()

    const error = validateField('languages', 'name', trimmedName, resume.languages.length)
    if (error) {
      toast.error(error)
      return
    }

    if (resume.languages.length >= 5) {
      toast.error('You can only add up to 5 Languages')
      return
    }

    // duplicate language  check
    const isDuplicate = resume.languages.some(
      (l) => l.name.toLowerCase() === trimmedName.toLowerCase(),
    )

    if (isDuplicate) {
      toast.warning(`"${trimmedName}" is already added.`)
      return
    }

    const newLanguage: Language = {
      name: trimmedName,
      level: 'BEGINNER',
      other: false,
    }

    setResume((prev) => ({
      ...prev,
      languages: [...prev.languages, newLanguage],
    }))

    // Clear error after successful addition
    setErrors((prevErrors) => ({
      ...prevErrors,
      languages: [],
    }))

    setLanguages('')
  }

  const addSocialLink = () => {
    if (resume.socialLinks.length >= 2) {
      toast.error('You can only add up to 2 Social Links')
      return
    }
    setResume((prevResume) => ({
      ...prevResume,
      socialLinks: [...prevResume.socialLinks, { title: '', url: '' }],
    }))
  }

  const addReference = () => {
    if (resume.references.length >= 2) {
      toast.error('You can only add up to 2 References')
      return
    }
    setResume((prevResume) => ({
      ...prevResume,
      references: [...prevResume.references, { name: '', email: '' }],
    }))
  }

  //Functions to remove each Session
  const handleRemoveSummary = () => {
    setRemoveSummary(true)
    setResume((prev) => ({ ...prev, aboutMe: '' }))
    setClearedSections((prev) => ({ ...prev, aboutMe: true }))
  }

  const removeJob = (jobIndex: number) => {
    setResume((prevState) => {
      const newExperiences = prevState.experiences.filter((_, index) => index !== jobIndex)
      if (newExperiences.length === 0) {
        setClearedSections((prev) => ({ ...prev, experiences: true }))
      }
      return { ...prevState, experiences: newExperiences }
    })
  }

  const removeEducation = (index: number) => {
    setResume((prevState) => {
      const newEducations = prevState.educations.filter((_, i) => i !== index)
      if (newEducations.length === 0) {
        setClearedSections((prev) => ({ ...prev, educations: true }))
      }
      return { ...prevState, educations: newEducations }
    })
  }

  const removeAchievement = (index: number) => {
    setResume((prevState) => {
      const newAchievements = prevState.achievements.filter((_, i) => i !== index)
      if (newAchievements.length === 0) {
        setClearedSections((prev) => ({ ...prev, achievements: true }))
      }
      return { ...prevState, achievements: newAchievements }
    })
  }

  const handleRemoveSkill = (index: number) => {
    setResume((prev) => {
      const newSkills = prev.skills.filter((_, i) => i !== index)
      if (newSkills.length === 0) {
        setClearedSections((prevCleared) => ({ ...prevCleared, skills: true }))
      }
      return { ...prev, skills: newSkills }
    })
  }

  const removeSocialLinks = (index: number) => {
    setResume((prev) => {
      const newSocialLinks = prev.socialLinks.filter((_, i) => i !== index)
      if (newSocialLinks.length === 0) {
        setClearedSections((prevCleared) => ({ ...prevCleared, socialLinks: true }))
      }
      return { ...prev, socialLinks: newSocialLinks }
    })
  }

  const handleRemoveLanguage = (index: number) => {
    setResume((prev) => {
      const newLanguages = prev.languages.filter((_, i) => i !== index)
      if (newLanguages.length === 0) {
        setClearedSections((prevCleared) => ({ ...prevCleared, languages: true }))
      }
      return { ...prev, languages: newLanguages }
    })
  }
  //to remove entire section
  const handleRemoveLanguagesSection = () => {
    setResume((prev) => ({ ...prev, languages: [] })) // Clear all languages
    setRemoveLanguages(true) // Mark the section as removed
    setClearedSections((prev) => ({ ...prev, languages: true }))
  }

  const removeReference = (index: number) => {
    setResume((prev) => {
      const newReferences = prev.references.filter((_, i) => i !== index)
      if (newReferences.length === 0) {
        setClearedSections((prevCleared) => ({ ...prevCleared, references: true }))
      }
      return { ...prev, references: newReferences }
    })
  }

  const generateEmploymentSuggestion = () => {
    const suggestionsList = [
      'Developed scalable web applications using modern frameworks.',
      'Led a team of developers to deliver a cloud-based solution.',
      'Optimized backend services, improving performance by 30%.',
      'Contributed to open-source projects, enhancing software reliability.',
    ]
    return suggestionsList[Math.floor(Math.random() * suggestionsList.length)]
  }

  function onSaveSuccess() {
    toast.success('Resume saved successfully', { duration: 3000 })
  }

  const generateEducationSuggestion = () => {
    const suggestionsList = [
      'Completed coursework in advanced software engineering techniques.',
      'Participated in research projects focused on machine learning applications.',
      'Led a team project to develop a mobile application for campus navigation.',
      'Achieved a GPA of 9.8 in computer science studies.',
    ]
    return suggestionsList[Math.floor(Math.random() * suggestionsList.length)]
  }

  const progress = useMemo(
    () => useResumeProgress(resume, removeSummary, isProTemplate),
    [resume, removeSummary, isProTemplate],
  )

  // Effect to update progress in localStorage and context
  useEffect(() => {
    if (authUserProfile?.id) {
      const key = `resumeProgress_${authUserProfile.id}`
      localStorage.setItem(key, progress.toString())
      setResumeProgress(progress) // Update context state
      onProgressChange(progress) // Notify parent component
    }
  }, [progress, authUserProfile, setResumeProgress, onProgressChange])

  return (
    <div className="h-full w-full">
      <div className="dark:bg-dark-lucres-black-500 sticky top-14 z-10 bg-white">
        <div className="flex w-full items-center justify-between">
          <div className="mt-4 flex items-center gap-x-3">
            <span className="dark:bg-dark-lucres-black-200 cursor-not-allowed rounded-lg bg-yellow-400/50 px-3 py-1 text-sm font-bold">
              {progress}%
            </span>
            <span className="text-base">Completed</span>
          </div>
          <div
            className={`
    flex items-center gap-3 p-4 md:p-2 xl:p-0
    ${hasChanges ? 'cursor-pointer' : 'cursor-not-allowed'}
  `}
          >
            <Button
              theme="transparent"
              size="small"
              className="
      border-lucres-gray-200/40! dark:border-dark-lucres-black-200! border! px-4!
      py-2! md:px-6! md:py-2! mt-2 md:mt-2
    "
              onClick={handleSave}
              disabled={!hasChanges}
            >
              Save
            </Button>

            {/* Preview button hidden on xl and above */}
            <span className="mt-2 md:mt-2 xl:hidden">
              <Button
                theme="translucent"
                size="small"
                className="px-3! py-2! md:px-5! md:py-2!"
                onClick={toggleViewResume}
              >
                Preview
              </Button>
            </span>
          </div>
        </div>
        <div className="bg-lucres-300 dark:bg-dark-lucres-black-300 relative mt-3 h-1 rounded-lg">
          <span
            className="from-lucres-400 bg-linear-to-r absolute left-0 top-0 mb-2 h-full rounded-lg to-[#E4FFC1]"
            style={{ width: `${progress}%` }}
          ></span>
        </div>
      </div>
      <div>
        <Section
          title="Personal Details"
          isVisible={visibleSections.personalDetails}
          onToggle={() => toggleSectionVisibility('personalDetails')}
        >
          <p className="dark:text-dark-lucres-green-100 mt-1 text-sm text-gray-500">
            Personal details such as Location and Email ID are essential in a resume to give the
            recruiter a quick overview of the candidate.
          </p>
          <div className="dark:border-dark-lucres-black-200 mt-5 flex flex-col gap-2 rounded-lg border border-gray-100 p-4">
            <div className="flex flex-wrap gap-5 md:flex-nowrap">
              <div className="w-full md:w-1/2">
                <label className="text-lucres-900/75 dark:text-dark-lucres-green-300 mb-2 block text-sm font-medium">
                  Profile Picture
                </label>
                <div className="flex items-center gap-2">
                  {resume.personalDetails.profilePicture ? (
                    <img
                      src={resume.personalDetails.profilePicture}
                      alt="Profile"
                      className="h-10 w-10 rounded-full"
                    />
                  ) : (
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-yellow-400 p-4"></div>
                  )}
                  <button
                    onClick={() => router.push('/personal-information')}
                    className="border-lucres-300/40  flex w-full flex-1 items-center justify-center gap-x-2 rounded-lg border px-5 py-3 text-sm md:w-48 lg:w-60"
                  >
                    <UploadSimpleIcon size={20} /> Change Photo
                  </button>
                </div>
              </div>
              <div className="w-full md:w-1/2">
                <Input
                  disabled
                  type="text"
                  id="contactNumber"
                  name="contactNumber"
                  value={resume.personalDetails.contactNumber}
                  label="Contact Number"
                  labelClassName="font-medium!"
                  className="w-full py-[10px] pl-3 pr-10"
                  readOnly
                />
              </div>
            </div>
            <div className="flex flex-wrap gap-5 md:flex-nowrap">
              <Input
                type="email"
                id="email"
                name="email"
                value={resume.personalDetails.email}
                label="Email ID"
                labelClassName="font-medium!"
                className="w-full py-[10px] pl-3 pr-10"
                disabled
                readOnly
              />
              <Input
                type="text"
                id="location"
                name="location"
                value={resume.personalDetails.location}
                label="Location"
                labelClassName="font-medium!"
                className="w-full py-[10px] pl-3 pr-10"
                disabled
                readOnly
              />
            </div>
            <div className="flex flex-wrap gap-5 md:flex-nowrap">
              <Input
                type="text"
                id="nationality"
                name="nationality"
                value={resume.personalDetails.nationality}
                label="Nationality"
                labelClassName="font-medium!"
                className="w-full py-[10px] pl-3 pr-10"
                disabled
                readOnly
              />
              <Input
                id="dateOfBirth"
                name="dateOfBirth"
                value={resume.personalDetails.dateOfBirth}
                label="Date of Birth"
                labelClassName="font-medium!"
                className="w-full py-[10px] pl-3 pr-10"
                disabled
                readOnly
              />
            </div>
          </div>
        </Section>

        {/* Professional Sumamry/About Me Section  */}
        <Section
          title="Professional Summary"
          isVisible={visibleSections.summary}
          onToggle={() => toggleSectionVisibility('summary')}
          canRemove={true}
          isRemoved={removeSummary}
          setIsRemoved={setRemoveSummary}
          onRemoveSection={handleRemoveSummary}
        >
          {!removeSummary && (
            <>
              <span className="text-lucres-gray-800 dark:text-dark-lucres-green-100 mt-2 text-sm">
                Write 2-4 short, energetic sentences about how great you are.
              </span>
              <div className="border-lucres-300/40  mt-2 flex items-start gap-x-3 rounded-lg border p-4">
                <div className="flex-1">
                  <textarea
                    ref={textareaRef}
                    value={resume.aboutMe}
                    onChange={(e) => {
                      const newAboutMe = e.target.value
                      handleAboutMeUpdate(newAboutMe)
                      if (newAboutMe.trim() === '') {
                        setClearedSections((prev) => ({ ...prev, aboutMe: true }))
                      } else {
                        setClearedSections((prev) => ({ ...prev, aboutMe: false }))
                      }
                    }}
                    maxLength={1000}
                    className="scrollbar-none outline-hidden focus:outline-hidden min-h-16 w-full resize-none rounded-lg border-none bg-transparent text-sm placeholder:text-sm"
                    placeholder="Enter your Summary"
                  />
                  {errors.aboutMe && <p className="text-sm text-red-500">{errors.aboutMe}</p>}
                  <div className="mt-1 text-sm text-gray-500">
                    {resume.aboutMe.length}/1000 characters
                  </div>
                </div>
              </div>
              <div className="mt-3 flex flex-wrap gap-4">
                <button
                  className="bg-lucres-200 dark:bg-dark-lucres-black-200 flex items-center gap-x-2 rounded-lg p-1 px-3 text-xs md:text-sm"
                  onClick={() => handleAISuggestion('improve')}
                  disabled={isProcessing}
                >
                  <SparkleIcon size={16} />{' '}
                  {isProcessing && processingAction === 'improve' ? 'Improving...' : 'Improve'}
                </button>
                <button
                  className="bg-lucres-200 dark:bg-dark-lucres-black-200 flex items-center gap-x-2 rounded-lg p-1 px-3  text-xs md:text-sm"
                  onClick={() => handleAISuggestion('longer')}
                  disabled={isProcessing || resume.aboutMe.trim() === ''}
                >
                  <ArrowsOutLineHorizontalIcon size={16} />{' '}
                  {isProcessing && processingAction === 'longer'
                    ? 'Making longer...'
                    : 'Make longer'}
                </button>
                <button
                  className="border-lucres-900 flex items-center gap-x-2 rounded-lg border p-1 px-3  text-xs md:text-sm"
                  onClick={() => handleAISuggestion('shorter')}
                  disabled={isProcessing || resume.aboutMe.trim() === ''}
                >
                  <ArrowsInLineHorizontalIcon size={16} />{' '}
                  {isProcessing && processingAction === 'shorter'
                    ? 'Making shorter...'
                    : 'Make shorter'}
                </button>
              </div>
            </>
          )}
        </Section>

        {/* Employment History Section  */}
        <Section
          title="Employment History"
          isVisible={visibleSections.employment}
          onToggle={() => toggleSectionVisibility('employment')}
        >
          <span className="text-lucres-900/60 dark:text-dark-lucres-green-100 mt-4 text-sm">
            Show relevant experience (Last 10 years).
          </span>
          {resume.experiences.map((job, index) => (
            <EmploymentCard
              userId={authUserProfile?.id || ''}
              key={index}
              job={job}
              index={index}
              isVisible={jobVisibility[index]}
              onToggleVisibility={() => toggleJobVisibility(index)}
              onRemove={() => removeJob(index)}
              onUpdate={handleExperienceUpdate}
              onToggleCurrent={() => {
                setResume((prev) => {
                  const updatedExperiences = prev.experiences.map((job, i) => {
                    if (i === index) {
                      const newIsCurrent = !job.isCurrent
                      return {
                        ...job,
                        isCurrent: newIsCurrent,
                        endDate: newIsCurrent ? 'Present' : '',
                      }
                    }
                    return job
                  })
                  return { ...prev, experiences: updatedExperiences }
                })
              }}
              suggestions={employmentSuggestions[index]}
              errors={errors.experiences[index] || {}}
            />
          ))}
          <div className="mt-4">
            <Button
              theme="translucent"
              size="small"
              className="rounded-lg!"
              onClick={addEmploymentHistory}
            >
              <PlusIcon size={16} className="me-2 font-bold" /> Add More Employment
            </Button>
          </div>
        </Section>
        <Section
          title="Educations & Certifications"
          isVisible={visibleSections.education}
          onToggle={() => toggleSectionVisibility('education')}
        >
          <span className="text-lucres-900/60 dark:text-dark-lucres-green-100 mt-4 text-sm">
            Show relevant education and certifications.
          </span>
          {resume.educations.map((education, index) => (
            <EducationCard
              userId={authUserProfile?.id || ''}
              key={index}
              education={education}
              index={index}
              isVisible={educationVisibility[index]}
              onToggleVisibility={() => toggleEducationVisibility(index)}
              onRemove={() => removeEducation(index)}
              onUpdate={handleEducationUpdate}
              suggestions={educationSuggestions[index]}
              errors={errors.educations[index] || {}}
            />
          ))}
          <div className="mt-4">
            <Button theme="translucent" size="small" className="rounded-lg!" onClick={addEducation}>
              <PlusIcon size={16} className="me-2 font-bold" /> Add More Education
            </Button>
          </div>
        </Section>

        <Section
          title="Achievements"
          isVisible={visibleSections.achievements}
          onToggle={() => toggleSectionVisibility('achievements')}
        >
          <span className="text-lucres-900/60 dark:text-dark-lucres-green-100 mt-4 text-sm">
            List your notable achievements.
          </span>
          {resume.achievements.map((achievement, index) => (
            <AchievementCard
              key={index}
              achievement={achievement}
              index={index}
              onUpdate={(field, value) => handleAchievementUpdate(index, field, value)}
              onRemove={() => removeAchievement(index)}
              errors={errors.achievements[index] || {}}
            />
          ))}
          <div className="mt-4">
            <Button
              theme="translucent"
              size="small"
              className="rounded-lg!"
              onClick={addAchievement}
            >
              <PlusIcon size={16} className="me-2 font-bold" /> Add More Achievements
            </Button>
          </div>
        </Section>
        {/* Skills Section */}
        <Section
          title="Skills"
          isVisible={visibleSections.skills}
          onToggle={() => toggleSectionVisibility('skills')}
        >
          <SkillsCard
            skills={resume.skills}
            skillInput={skill}
            errors={errors.skills}
            onInputChange={setSkill}
            onAddSkill={handleAddSkill}
            onRemoveSkill={handleRemoveSkill}
            userId={authUserProfile?.id || ''}
            context={skillsContext}
          />
        </Section>
        {/* Languages Section */}
        <Section
          title="Languages"
          isVisible={visibleSections.languages}
          onToggle={() => toggleSectionVisibility('languages')}
          canRemove={true}
          isRemoved={removeLanguages}
          setIsRemoved={setRemoveLanguages}
          onRemoveSection={handleRemoveLanguagesSection}
        >
          <LanguagesCard
            languages={resume.languages}
            languageInput={languages}
            userId={authUserProfile?.id || ''}
            errors={errors.languages}
            onInputChange={setLanguages}
            onAddLanguage={handleAddLanguage}
            onRemoveLanguage={handleRemoveLanguage}
          />
        </Section>

        {/* Pro sections */}
        {/* Social Links Section */}
        <div>
          <div
            className="mt-4 flex w-full cursor-pointer items-center justify-between pb-2"
            onClick={() => toggleSectionVisibility('socialLinks')}
          >
            <div className="flex items-center justify-center gap-x-2">
              <h2 className="text-2xl font-bold">Websites and Social Links</h2>
              {!isProTemplate ? (
                <Tooltip
                  text="Available only for pro templates"
                  classes="whitespace-nowrap text-center"
                  direction="bottom"
                >
                  <span className="rounded-lg bg-lime-300/30 px-2 py-1 text-sm font-medium">
                    PRO
                  </span>
                </Tooltip>
              ) : (
                <span className="rounded-lg bg-lime-300/30 px-2 py-1 text-sm font-medium">PRO</span>
              )}
            </div>

            {visibleSections.socialLinks ? <CaretUpIcon size={20} /> : <CaretDownIcon size={20} />}
          </div>
          <div
            className={`${
              visibleSections.socialLinks ? 'max-h-full opacity-100' : 'h-0 max-h-0 opacity-0'
            } overflow-hidden transition-all duration-500 ease-in-out`}
          >
            <div className="dark:border-dark-lucres-black-200 mt-2 flex flex-col gap-2 rounded-lg border border-gray-100 p-4">
              {isProTemplate ? (
                resume.socialLinks.map((socialLink, index) => (
                  <div
                    key={index}
                    className="flex flex-wrap items-start gap-2 gap-x-4 md:flex-nowrap"
                  >
                    <Input
                      type="text"
                      id={`LinkTitle-${index}`}
                      name="LinkTitle"
                      label="Link Title"
                      className="w-1/2 py-[10px] pl-3 pr-10"
                      value={socialLink.title}
                      placeholder="Portfolio"
                      onChange={(e) =>
                        handleArraySectionUpdate('socialLinks', index, 'title', e.target.value)
                      }
                      error={errors.socialLinks[index]?.title}
                    />
                    <Input
                      type="text"
                      id={`LinkURL-${index}`}
                      name="LinkURL"
                      label="URL"
                      className="w-1/2 py-[10px] pl-3 pr-10"
                      placeholder="https://"
                      value={socialLink.url}
                      onChange={(e) =>
                        handleArraySectionUpdate('socialLinks', index, 'url', e.target.value)
                      }
                      error={errors.socialLinks[index]?.url}
                    />
                    <div className="flex gap-x-2">
                      <TrashIcon
                        size={20}
                        className="cursor-pointer"
                        onClick={() => removeSocialLinks(index)}
                      />
                    </div>
                  </div>
                ))
              ) : (
                <div className="flex flex-wrap items-start gap-2 gap-x-4 md:flex-nowrap">
                  <Input
                    type="text"
                    label="Link Title"
                    className="w-1/2 cursor-not-allowed bg-gray-100 py-[10px] pl-3 pr-10 text-gray-500"
                    placeholder="Portfolio"
                    disabled
                  />
                  <Input
                    type="text"
                    label="URL"
                    className="w-1/2 cursor-not-allowed bg-gray-100 py-[10px] pl-3 pr-10 text-gray-500"
                    placeholder="https://"
                    disabled
                  />
                </div>
              )}
              {isProTemplate && (
                <Button
                  theme="translucent"
                  size="small"
                  className="rounded-lg! w-auto self-start"
                  onClick={addSocialLink}
                >
                  <PlusIcon size={16} className="me-2 font-bold" /> Add More Links
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* References Section */}
        <div>
          <div
            className="mt-4 flex w-full cursor-pointer items-center justify-between pb-2"
            onClick={() => toggleSectionVisibility('references')}
          >
            <div className="flex items-center justify-center gap-x-3">
              <h2 className="text-2xl font-bold">References</h2>
              {!isProTemplate ? (
                <Tooltip
                  text="Available only for pro templates."
                  classes="whitespace-nowrap text-center"
                  direction="bottom"
                >
                  <span className="rounded-lg bg-lime-300/30  px-2 py-1 text-sm font-medium">
                    PRO
                  </span>
                </Tooltip>
              ) : (
                <span className="rounded-lg bg-lime-300/30 px-2 py-1 text-sm font-medium">PRO</span>
              )}
            </div>

            {visibleSections.references ? <CaretUpIcon size={20} /> : <CaretDownIcon size={20} />}
          </div>
          <div
            className={`${
              visibleSections.references ? 'max-h-full opacity-100' : 'h-0 max-h-0 opacity-0'
            } overflow-hidden transition-all duration-500 ease-in-out`}
          >
            <div className="dark:border-dark-lucres-black-200 mt-2 flex flex-col gap-2 rounded-lg border border-gray-100 p-4">
              {isProTemplate ? (
                resume.references.map((reference, index) => (
                  <div
                    key={index}
                    className="flex flex-wrap items-start gap-2 gap-x-4 md:flex-nowrap"
                  >
                    <Input
                      type="text"
                      id={`ReferenceName-${index}`}
                      name="ReferenceName"
                      label="Name"
                      className="w-1/2 py-[10px] pl-3 pr-10"
                      value={reference.name}
                      onChange={(e) =>
                        handleArraySectionUpdate('references', index, 'name', e.target.value)
                      }
                      error={errors.references[index]?.name}
                    />
                    <Input
                      type="text"
                      id={`ReferenceEmail-${index}`}
                      name="ReferenceEmail"
                      label="Email ID"
                      className="w-1/2 py-[10px] pl-3 pr-10"
                      value={reference.email}
                      onChange={(e) =>
                        handleArraySectionUpdate('references', index, 'email', e.target.value)
                      }
                      error={errors.references[index]?.email}
                    />
                    <div className="flex gap-x-2">
                      <TrashIcon
                        size={20}
                        className="cursor-pointer"
                        onClick={() => removeReference(index)}
                      />
                    </div>
                  </div>
                ))
              ) : (
                <div className="flex flex-wrap items-start gap-2 gap-x-4 md:flex-nowrap">
                  <Input
                    type="text"
                    label="Name"
                    className="w-1/2 cursor-not-allowed bg-gray-100 py-[10px] pl-3 pr-10 text-gray-500"
                    placeholder="John Doe"
                    disabled
                  />
                  <Input
                    type="text"
                    label="Email ID"
                    className="w-1/2 cursor-not-allowed bg-gray-100 py-[10px] pl-3 pr-10 text-gray-500"
                    placeholder="<EMAIL>"
                    disabled
                  />
                </div>
              )}
              {isProTemplate && (
                <Button
                  theme="translucent"
                  size="small"
                  className="rounded-lg! w-auto self-start"
                  onClick={addReference}
                >
                  <PlusIcon size={16} className="me-2 font-bold" /> Add 1 More Reference
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CreateResume
