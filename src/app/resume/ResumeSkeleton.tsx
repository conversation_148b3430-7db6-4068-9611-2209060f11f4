export const ResumeFormSkeleton = () => {
  return (
    <div className="animate-pulse">
      {/* Progress Section */}
      <div className="dark:bg-dark-lucres-black-500 sticky top-14 z-10 mt-2 bg-white">
        <div className="flex w-full items-center justify-between">
          <div className="mt-4 flex items-center gap-x-3">
            <div className="dark:bg-dark-lucres-black-400 h-5 w-10 rounded-full bg-gray-300"></div>
            <div className="dark:bg-dark-lucres-black-400 h-4 w-20 rounded-sm bg-gray-300"></div>
          </div>
          <div className="flex gap-3 p-4 md:p-2 xl:p-0">
            <div className="dark:bg-dark-lucres-black-400 h-8 w-20 rounded-lg bg-gray-300"></div>
          </div>
        </div>
        <div className="dark:bg-dark-lucres-black-300 relative mt-4 h-1 rounded-lg bg-gray-300">
          <div
            className="dark:bg-dark-lucres-black-400 absolute left-0 top-0 h-full rounded-lg bg-gray-400"
            style={{ width: '50%' }}
          ></div>
        </div>
      </div>

      {/* Sections */}
      <div className="mt-6">
        {/* Personal Details */}
        <div>
          <div className="flex w-full items-center justify-between">
            <div className="dark:bg-dark-lucres-black-400 h-6 w-40 rounded-sm bg-gray-300"></div>
            <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300"></div>
          </div>
          <div className="mt-4">
            <div className="dark:bg-dark-lucres-black-400 h-4 w-full rounded-sm bg-gray-300"></div>
            <div className="dark:border-dark-lucres-black-200 mt-5 flex flex-col gap-6 rounded-lg border border-gray-100 p-4">
              <div className="flex flex-wrap gap-5 md:flex-nowrap">
                <div className="w-full md:w-1/2">
                  <div className="dark:bg-dark-lucres-black-400 h-4 w-28 rounded-sm bg-gray-300"></div>
                  <div className="mt-2 flex items-center gap-2">
                    <div className="dark:bg-dark-lucres-black-400 h-10 w-10 rounded-full bg-gray-300"></div>
                    <div className="dark:bg-dark-lucres-black-400 h-10 w-48 rounded-lg bg-gray-300"></div>
                  </div>
                </div>
                <div className="w-full md:w-1/2">
                  <div className="dark:bg-dark-lucres-black-400 h-10 w-full rounded-lg bg-gray-300"></div>
                </div>
              </div>
              <div className="flex flex-wrap gap-5 md:flex-nowrap">
                <div className="dark:bg-dark-lucres-black-400 h-10 w-full rounded-lg bg-gray-300"></div>
                <div className="dark:bg-dark-lucres-black-400 h-10 w-full rounded-lg bg-gray-300"></div>
              </div>
              <div className="flex flex-wrap gap-5 md:flex-nowrap">
                <div className="dark:bg-dark-lucres-black-400 h-10 w-full rounded-lg bg-gray-300"></div>
                <div className="dark:bg-dark-lucres-black-400 h-10 w-full rounded-lg bg-gray-300"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Professional Summary */}
        <div className="mt-6">
          <div className="flex w-full items-center justify-between">
            <div className="dark:bg-dark-lucres-black-400 h-6 w-40 rounded-sm bg-gray-300"></div>
            <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300"></div>
          </div>
          <div className="mt-4">
            <div className="dark:bg-dark-lucres-black-400 h-4 w-full rounded-sm bg-gray-300"></div>
            <div className="dark:bg-dark-lucres-black-400 mt-4 h-24 w-full rounded-lg bg-gray-300"></div>
            <div className="mt-4 flex flex-wrap gap-4">
              <div className="dark:bg-dark-lucres-black-400 h-8 w-24 rounded-lg bg-gray-300"></div>
              <div className="dark:bg-dark-lucres-black-400 h-8 w-24 rounded-lg bg-gray-300"></div>
              <div className="dark:bg-dark-lucres-black-400 h-8 w-24 rounded-lg bg-gray-300"></div>
            </div>
          </div>
        </div>

        {/* Employment History */}
        <div className="mt-6">
          <div className="flex w-full items-center justify-between">
            <div className="dark:bg-dark-lucres-black-400 h-6 w-40 rounded-sm bg-gray-300"></div>
            <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300"></div>
          </div>
          <div className="mt-4">
            <div className="dark:bg-dark-lucres-black-400 h-4 w-full rounded-sm bg-gray-300"></div>
            <div className="dark:border-dark-lucres-black-200 mt-5 flex flex-col rounded-lg border border-gray-100 p-4">
              <div className="flex w-full items-center justify-between">
                <div className="dark:bg-dark-lucres-black-400 h-6 w-32 rounded-sm bg-gray-300"></div>
                <div className="flex items-center gap-x-2">
                  <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300"></div>
                  <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300"></div>
                </div>
              </div>
              <div className="mt-4">
                <div className="flex flex-wrap gap-5 md:flex-nowrap">
                  <div className="dark:bg-dark-lucres-black-400 h-10 w-full rounded-lg bg-gray-300"></div>
                  <div className="dark:bg-dark-lucres-black-400 h-10 w-full rounded-lg bg-gray-300"></div>
                </div>
                <div className="mt-4 flex flex-wrap gap-4">
                  <div className="dark:bg-dark-lucres-black-400 h-10 w-1/2 rounded-lg bg-gray-300"></div>
                  <div className="dark:bg-dark-lucres-black-400 h-10 w-1/2 rounded-lg bg-gray-300"></div>
                </div>
                <div className="dark:bg-dark-lucres-black-400 mt-4 h-24 w-full rounded-lg bg-gray-300"></div>
              </div>
            </div>
            <div className="dark:bg-dark-lucres-black-400 mt-4 h-8 w-32 rounded-lg bg-gray-300"></div>
          </div>
        </div>

        {/* Educations & Certifications */}
        <div className="mt-6">
          <div className="flex w-full items-center justify-between">
            <div className="dark:bg-dark-lucres-black-400 h-6 w-40 rounded-sm bg-gray-300"></div>
            <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300"></div>
          </div>
          <div className="mt-4">
            <div className="dark:bg-dark-lucres-black-400 h-4 w-full rounded-sm bg-gray-300"></div>
            <div className="dark:border-dark-lucres-black-200 mt-5 flex flex-col rounded-lg border border-gray-100 p-4">
              <div className="flex w-full items-center justify-between">
                <div className="dark:bg-dark-lucres-black-400 h-6 w-32 rounded-sm bg-gray-300"></div>
                <div className="flex items-center gap-x-2">
                  <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300"></div>
                  <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300"></div>
                </div>
              </div>
              <div className="mt-4">
                <div className="flex flex-wrap gap-5 md:flex-nowrap">
                  <div className="dark:bg-dark-lucres-black-400 h-10 w-full rounded-lg bg-gray-300"></div>
                  <div className="dark:bg-dark-lucres-black-400 h-10 w-full rounded-lg bg-gray-300"></div>
                </div>
                <div className="mt-4 flex flex-wrap gap-4">
                  <div className="dark:bg-dark-lucres-black-400 h-10 w-1/2 rounded-lg bg-gray-300"></div>
                  <div className="dark:bg-dark-lucres-black-400 h-10 w-1/2 rounded-lg bg-gray-300"></div>
                </div>
                <div className="dark:bg-dark-lucres-black-400 mt-4 h-24 w-full rounded-lg bg-gray-300"></div>
              </div>
            </div>
            <div className="dark:bg-dark-lucres-black-400 mt-4 h-8 w-32 rounded-lg bg-gray-300"></div>
          </div>
        </div>

        {/* Achievements */}
        <div className="mt-6">
          <div className="flex w-full items-center justify-between">
            <div className="dark:bg-dark-lucres-black-400 h-6 w-40 rounded-sm bg-gray-300"></div>
            <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300"></div>
          </div>
          <div className="mt-4">
            <div className="dark:bg-dark-lucres-black-400 h-4 w-full rounded-sm bg-gray-300"></div>
            <div className="dark:border-dark-lucres-black-200 mt-5 flex flex-col rounded-lg border border-gray-100 p-4">
              <div className="flex w-full items-center justify-between">
                <div className="dark:bg-dark-lucres-black-400 h-6 w-32 rounded-sm bg-gray-300"></div>
                <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300"></div>
              </div>
              <div className="mt-4">
                <div className="flex flex-wrap gap-5 md:flex-nowrap">
                  <div className="dark:bg-dark-lucres-black-400 h-10 w-full rounded-lg bg-gray-300"></div>
                  <div className="dark:bg-dark-lucres-black-400 h-10 w-full rounded-lg bg-gray-300"></div>
                </div>
              </div>
            </div>
            <div className="dark:bg-dark-lucres-black-400 mt-4 h-8 w-32 rounded-lg bg-gray-300"></div>
          </div>
        </div>

        {/* Skills */}
        <div className="mt-6">
          <div className="flex w-full items-center justify-between">
            <div className="dark:bg-dark-lucres-black-400 h-6 w-40 rounded-sm bg-gray-300"></div>
            <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300"></div>
          </div>
          <div className="mt-4">
            <div className="dark:bg-dark-lucres-black-400 h-10 w-1/2 rounded-lg bg-gray-300"></div>
            <div className="mt-4 flex flex-wrap gap-4">
              <div className="dark:bg-dark-lucres-black-400 h-8 w-24 rounded-lg bg-gray-300"></div>
              <div className="dark:bg-dark-lucres-black-400 h-8 w-24 rounded-lg bg-gray-300"></div>
              <div className="dark:bg-dark-lucres-black-400 h-8 w-24 rounded-lg bg-gray-300"></div>
            </div>
            <div className="mt-4 flex flex-wrap gap-2">
              <div className="dark:bg-dark-lucres-black-400 h-6 w-20 rounded-lg bg-gray-300"></div>
              <div className="dark:bg-dark-lucres-black-400 h-6 w-20 rounded-lg bg-gray-300"></div>
              <div className="dark:bg-dark-lucres-black-400 h-6 w-20 rounded-lg bg-gray-300"></div>
            </div>
          </div>
        </div>

        {/* Languages */}
        <div className="mt-6">
          <div className="flex w-full items-center justify-between">
            <div className="dark:bg-dark-lucres-black-400 h-6 w-40 rounded-sm bg-gray-300"></div>
            <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300"></div>
          </div>
          <div className="mt-4">
            <div className="dark:bg-dark-lucres-black-400 h-10 w-1/2 rounded-lg bg-gray-300"></div>
            <div className="mt-4 flex flex-wrap gap-4">
              <div className="dark:bg-dark-lucres-black-400 h-8 w-24 rounded-lg bg-gray-300"></div>
              <div className="dark:bg-dark-lucres-black-400 h-8 w-24 rounded-lg bg-gray-300"></div>
              <div className="dark:bg-dark-lucres-black-400 h-8 w-24 rounded-lg bg-gray-300"></div>
            </div>
            <div className="mt-4 flex flex-wrap gap-2">
              <div className="dark:bg-dark-lucres-black-400 h-6 w-20 rounded-lg bg-gray-300"></div>
              <div className="dark:bg-dark-lucres-black-400 h-6 w-20 rounded-lg bg-gray-300"></div>
              <div className="dark:bg-dark-lucres-black-400 h-6 w-20 rounded-lg bg-gray-300"></div>
            </div>
          </div>
        </div>

        {/* Websites and Social Links */}
        <div className="mt-6">
          <div className="flex w-full items-center justify-between">
            <div className="dark:bg-dark-lucres-black-400 h-6 w-40 rounded-sm bg-gray-300"></div>
            <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300"></div>
          </div>
          <div className="mt-4">
            <div className="dark:border-dark-lucres-black-200 mt-5 flex flex-col gap-5 rounded-lg border border-gray-100 p-4">
              <div className="flex flex-wrap gap-5 md:flex-nowrap">
                <div className="dark:bg-dark-lucres-black-400 h-10 w-full rounded-lg bg-gray-300"></div>
                <div className="dark:bg-dark-lucres-black-400 h-10 w-full rounded-lg bg-gray-300"></div>
              </div>
              <div className="dark:bg-dark-lucres-black-400 mt-3 h-8 w-32 rounded-lg bg-gray-300"></div>
            </div>
          </div>
        </div>

        {/* References */}
        <div className="mt-6">
          <div className="flex w-full items-center justify-between">
            <div className="dark:bg-dark-lucres-black-400 h-6 w-40 rounded-sm bg-gray-300"></div>
            <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300"></div>
          </div>
          <div className="mt-4">
            <div className="dark:border-dark-lucres-black-200 mt-5 flex flex-col gap-5 rounded-lg border border-gray-100 p-4">
              <div className="flex flex-wrap gap-5 md:flex-nowrap">
                <div className="dark:bg-dark-lucres-black-400 h-10 w-full rounded-lg bg-gray-300"></div>
                <div className="dark:bg-dark-lucres-black-400 h-10 w-full rounded-lg bg-gray-300"></div>
              </div>
              <div className="dark:bg-dark-lucres-black-400 mt-3 h-8 w-32 rounded-lg bg-gray-300"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export const TemplateViewerSkeleton = () => {
  return (
    <div className="w-full animate-pulse xl:p-8">
      {/* Top section: Select Template, Button (Download/Buy), and Close Button */}
      <div className="flex w-full items-center justify-between gap-x-2">
        {/* Select Template placeholder */}
        <div className="flex items-center gap-x-2">
          <div className="h-6 w-6 rounded-sm bg-gray-300 dark:bg-gray-600"></div>
          <div className="h-4 w-24 rounded-sm bg-gray-300 dark:bg-gray-600"></div>
        </div>
        {/* Download/Buy Button and Close Button placeholders */}
        <div className="flex items-center gap-x-2">
          <div className="h-8 w-24 rounded-lg bg-gray-300 dark:bg-gray-600"></div>
        </div>
      </div>

      {/* Color Swatch placeholders
      <div className="mt-2 flex gap-x-2">
        <div className="h-6 w-6 rounded-full bg-gray-300 dark:bg-gray-600"></div>
        <div className="h-6 w-6 rounded-full bg-gray-300 dark:bg-gray-600"></div>
        <div className="h-6 w-6 rounded-full bg-gray-300 dark:bg-gray-600"></div>
      </div> */}

      {/* Resume Preview: A4-sized rectangle */}
      <div className="mt-10">
        <div className="relative">
          <div
            className="mx-auto w-full max-w-[210mm] bg-gray-100 dark:bg-gray-600"
            style={{ aspectRatio: '1 / 1.414' }}
          ></div>
        </div>
      </div>
    </div>
  )
}

export const ResumeSkeleton = () => {
  return (
    <div className="bg-lucres-900 m-auto flex h-full min-h-full w-full max-w-[1440px] items-start justify-start xl:mt-10">
      <div className="dark:bg-dark-lucres-black-500 min-h-screen w-full bg-white p-4 xl:w-3/5 xl:p-12">
        <ResumeFormSkeleton />
      </div>
      <div className="sticky top-0 hidden min-h-full w-1/2 items-start justify-center rounded-lg xl:block">
        <TemplateViewerSkeleton />
      </div>
    </div>
  )
}
