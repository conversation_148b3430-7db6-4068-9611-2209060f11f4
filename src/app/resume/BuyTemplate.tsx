'use client'
import {
  Check,
  CheckIcon,
  CurrencyInr,
  CurrencyInrIcon,
  Lock,
  LockIcon,
  X,
} from '@phosphor-icons/react'
import Button from '../../components/Button'
import Input from '../../components/Input'
import IconButton from '../../components/IconButton'
import { useEffect } from 'react'

interface BuyTemplateProps {
  onPayNowClick: () => void // Function to trigger on "Pay Now" click
  onClose: () => void // Function to close the view
}

const BuyTemplate: React.FC<BuyTemplateProps> = ({ onPayNowClick, onClose }) => {
  // Prevent body scrolling when the overlay is open
  useEffect(() => {
    document.body.style.overflow = 'hidden'
    return () => {
      document.body.style.overflow = '' // Reset to default
    }
  }, [])
  return (
    <div className="fixed inset-0 z-10 flex">
      <div className="dark:bg-dark-lucres-black-500 relative h-full w-full overflow-y-hidden bg-white p-6">
        {/* Close Button */}
        <span className="absolute right-8 top-3 cursor-pointer sm:top-16" onClick={onClose}>
          <IconButton>
            <X size={24} className="dark:text-dark-lucres-green-100" />
          </IconButton>
        </span>
        {/* Page Content */}
        <div className="mx-auto flex max-w-6xl flex-col-reverse items-center justify-between gap-2 lg:flex-row lg:items-start">
          {/* Left Side */}
          <div className="lg:w-1/2">
            <div className="mt-10 flex flex-col gap-y-2">
              <h4 className="text-lucres-green-300 dark:text-dark-lucres-green-500 mb-2 mt-8 hidden  font-semibold lg:block">
                FINAL STEP
              </h4>
              <h2 className="text-2xl font-semibold">Secure Your Premium Resume Template</h2>
              <span className="text-lucres-gray-800/80 dark:text-dark-lucres-green-100 w-full max-w-sm">
                Once completed, you’ll have immediate access to download your resume and start
                applying with confidence!
              </span>
            </div>
            <div className="mt-6 flex flex-col gap-y-3">
              <h3 className="text-xl font-bold">Why Choose Our Premium Template?</h3>
              <ul className="mt-2 flex flex-col space-y-3">
                {[
                  'Professionally designed for maximum impact',
                  'Easy-to-edit, tailored for all industries',
                  'One-time payment for a high-quality, editable file',
                  'Unlimited downloads for the next 7 days',
                ].map((text, index) => (
                  <li key={index} className="flex items-start gap-x-2">
                    <CheckIcon size={24} className="text-lucres-400 flex-shrink-0" />
                    <span className="text-lucres-gray-800 dark:text-dark-lucres-green-100">
                      {text}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="mt-6 w-full max-w-sm flex-col">
              <h3 className="lg:bloc text-xl font-bold ">Secure Payment</h3>
              <span className=" text-lucres-gray-800/80 dark:text-dark-lucres-green-100">
                We accept all major payment methods. Your information is safe with us.
              </span>
            </div>
          </div>
          {/* Right Side */}
          <div className="ml-0 lg:w-3/5 ">
            <div className="border-lucres-gray-200 bg-lucres-gray-100 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 mx-auto mt-20 w-full max-w-[26rem] rounded-xl  border p-8 dark:border">
              <div className="flex w-full items-start justify-between">
                <div className="flex flex-col">
                  <span className="text-lg font-semibold">Premium Resume</span>
                  <span className="text-lucres-gray-500 text-sm">One Time Payment</span>
                </div>
                <div className="flex items-center text-lg font-semibold">
                  <CurrencyInrIcon size={16} className="mt-1" weight="bold" />
                  <span> 80.00</span>
                </div>
              </div>
              <div className="mt-4 flex items-center justify-between text-sm font-semibold">
                <span>CGST 9%</span>
                <div className="flex items-center">
                  <CurrencyInrIcon size={14} weight="bold" />
                  <span>7.20</span>
                </div>
              </div>
              <div className="mt-4 flex items-center justify-between text-sm font-semibold">
                <span>SGST 9%</span>
                <div className="flex items-center">
                  <CurrencyInrIcon size={14} weight="bold" />
                  <span>7.20</span>
                </div>
              </div>
              <div className="mt-4 flex w-full justify-center">
                <Button
                  size="small"
                  theme="opaque"
                  className="w-full !rounded-lg"
                  onClick={onPayNowClick}
                >
                  <LockIcon size={16} weight="bold" className="mr-2" /> Pay ₹ 94.4
                </Button>
              </div>
            </div>
            <div className="border-lucres-gray-200 bg-lucres-gray-100 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 mx-auto mt-5 flex w-full max-w-[26rem] items-center justify-end gap-x-3  rounded-xl border p-3 dark:border">
              <Input
                label="PromoCode"
                placeholder="Enter Discount Code"
                className="!placeholder-lucres-gray-700 dark:!border-dark-lucres-black-200  dark:!bg-dark-lucres-black-300 dark:!placeholder-dark-lucres-green-100 !border !border-gray-300 !bg-neutral-50  "
              />
              <Button
                size="medium"
                theme="transparent"
                className="!border-lucres-gray-500 text-lucres-gray-700 dark:!text-dark-lucres-green-100  mt-1 cursor-pointer 
              !rounded-lg !py-3 !pb-4"
              >
                Apply
              </Button>
            </div>
            {/* <div className="mt-8 flex w-full justify-center gap-x-8 text-xs text-gray-400">
              <Link to="">Contact</Link>
              <Link to="">FAQ</Link>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  )
}

export default BuyTemplate
