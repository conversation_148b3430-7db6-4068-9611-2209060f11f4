import { Education, Employment, Resume } from '../../models/Resume'
import { isDateValid, isValidDateFormat } from '../../utils/resumeUtils'
import { isValid, parse } from 'date-fns'

export type Errors = {
  aboutMe?: string
  experiences: Array<Record<string, string>>
  educations: Array<Record<string, string>>
  achievements: Array<Record<string, string>>
  skills: Array<Record<string, string>>
  languages: Array<Record<string, string>>
  socialLinks: Array<Record<string, string>>
  references: Array<Record<string, string>>
}

export const validateField = (
  section: keyof Resume,
  field: string,
  value: any,
  index?: number,
  context?: any,
): string => {
  const trimmedValue = typeof value === 'string' ? value.trim() : value
  const isEmpty = (val: any) =>
    val === undefined ||
    val === null ||
    val === '' ||
    (typeof val === 'string' && val.trim() === '')

  switch (section) {
    case 'aboutMe':
      if (field === 'aboutMe') {
        if (trimmedValue && trimmedValue.length >= 1000) {
          return 'Your bio has reached above 1000 characters.'
        }
      }
      break
    case 'experiences':
      if (index === undefined) return ''
      const exp = context as Employment
      switch (field) {
        case 'designation.name':
          if (isEmpty(trimmedValue)) return 'Designation is required.'
          if (trimmedValue.length < 2 || trimmedValue.length > 30) {
            return 'Designation must be 2-30 characters.'
          }
          break
        case 'company.name':
          if (isEmpty(trimmedValue)) return 'Company name is required.'
          if (trimmedValue.length < 2 || trimmedValue.length > 30) {
            return 'Company name must be 2-30 characters.'
          }
          break
        case 'employmentType':
          if (isEmpty(trimmedValue)) return 'Employment type is required.'
          if (
            !['FULL_TIME', 'PART_TIME', 'CONTRACT', 'REMOTE_WORK', 'TEMPORARY'].includes(
              trimmedValue,
            )
          ) {
            return 'Invalid employment type.'
          }
          break
        case 'startDate':
          if (isEmpty(trimmedValue)) return 'Start date is required.'
          break
        case 'endDate':
          if (exp.isCurrent) {
            if (trimmedValue !== 'Present') {
              return 'End date should be "Present" if currently working here'
            }
          } else {
            if (!trimmedValue) {
              return 'End date is required if not currently working here'
            }
            if (exp.startDate && isValidDateFormat(exp.startDate) && isDateValid(exp.startDate)) {
              const startDateObj = parse(exp.startDate, 'MM/yyyy', new Date())
              const endDateObj = parse(trimmedValue, 'MM/yyyy', new Date())
              if (isValid(startDateObj) && isValid(endDateObj) && endDateObj <= startDateObj) {
                return 'End date must be after start date'
              }
            }
          }
          break
        case 'description':
          if (trimmedValue) {
            const textContent = trimmedValue.replace(/<[^>]*>/g, '')
            if (textContent.length > 750) {
              return 'Description exceeds the 100 words limit.'
            }
          }
          break
        case 'location.address.city':
          if (isEmpty(trimmedValue)) return 'City is required.'
          if (trimmedValue.length < 2 || trimmedValue.length > 30) {
            return 'City must be 2-30 characters.'
          }
          break
      }
      break
    case 'educations':
      if (index === undefined) return ''
      const edu = context as Education
      const currentYear = new Date().getFullYear()
      switch (field) {
        case 'degree.name':
          if (isEmpty(trimmedValue)) return 'Degree is required.'
          if (trimmedValue.length < 2 || trimmedValue.length > 30) {
            return 'Degree name must be 2-30 characters.'
          }
          break
        case 'institution.name':
          if (isEmpty(trimmedValue)) return 'Institution is required.'
          if (trimmedValue.length < 2 || trimmedValue.length > 30) {
            return 'Institution name must be 2-30 characters.'
          }
          break
        case 'startYear':
          if (isEmpty(trimmedValue)) return 'Start year is required.'
          if (trimmedValue < 1900 || trimmedValue > currentYear) {
            return `Start year must be between 1900 and ${currentYear}.`
          }
          break
        case 'endYear':
          if (isEmpty(trimmedValue)) return 'End year is required.'
          if (trimmedValue < 1900 || trimmedValue > currentYear + 6) {
            return `End year must be between 1900 and ${currentYear + 6}.`
          }
          if (edu.startYear && value && value <= edu.startYear) {
            return 'End Year must be after start Year'
          }
          break
        case 'location.address.city':
          if (isEmpty(trimmedValue)) return 'City is required.'
          if (trimmedValue.length < 2 || trimmedValue.length > 30) {
            return 'City must be 2-30 characters.'
          }
          break
        case 'description':
          if (trimmedValue) {
            const textContent = trimmedValue.replace(/<[^>]*>/g, '')
            if (textContent.length > 750) {
              return 'Description exceeds the 100 words limit.'
            }
          }
          break
      }
      break
    case 'achievements':
      if (index === undefined) return ''
      switch (field) {
        case 'title':
          if (isEmpty(trimmedValue)) return 'Title is required.'
          if (trimmedValue.length < 3 || trimmedValue.length > 30) {
            return 'Title must be 3-30 characters.'
          }
          break
        case 'awardedBy':
          if (trimmedValue && (trimmedValue.length < 3 || trimmedValue.length > 20)) {
            return 'Awarded by must be 3-20 characters.'
          }
          break
        case 'date':
          if (isEmpty(trimmedValue)) return 'Date is required.'
          break
      }
      break
    case 'skills':
      if (index === undefined) return ''
      switch (field) {
        case 'name':
          if (isEmpty(trimmedValue)) return 'Skill name is required.'
          if (trimmedValue.length < 1 || trimmedValue.length > 30) {
            return 'Skill name must be 1-30 characters.'
          }
          break
        case 'level':
          if (isEmpty(trimmedValue)) return 'Level is required.'
          if (trimmedValue < 0 || trimmedValue > 100) {
            return 'Level must be between 0 and 100.'
          }
          break
      }
      break
    case 'languages':
      if (index === undefined) return ''
      switch (field) {
        case 'name':
          if (isEmpty(trimmedValue)) return 'Language name is required.'
          if (trimmedValue.length > 30 || !/^[a-zA-Z\-()]+( [a-zA-Z\-()]+)?$/.test(trimmedValue)) {
            return 'Language names exceed 2 words or contain unauthorized special characters.'
          }
          break
        case 'level':
          if (isEmpty(trimmedValue)) return 'Level is required.'
          if (!['BEGINNER', 'INTERMEDIATE', 'FLUENT'].includes(trimmedValue)) {
            return 'Invalid level.'
          }
          break
      }
      break
    case 'socialLinks':
      if (index === undefined) return ''
      switch (field) {
        case 'title':
          if (isEmpty(trimmedValue)) return 'Link title is required.'
          if (trimmedValue.length < 1 || trimmedValue.length > 20) {
            return 'Link title must be 1-20 characters.'
          }
          break
        case 'url':
          if (isEmpty(trimmedValue)) return 'URL is required.'
          if (trimmedValue.length < 1 || trimmedValue.length > 50) {
            return 'URL must be 1-50 characters.'
          }
          try {
            new URL(trimmedValue)
          } catch {
            return 'Invalid URL format.'
          }
          break
      }
      break
    case 'references':
      if (index === undefined) return ''
      switch (field) {
        case 'name':
          if (isEmpty(trimmedValue)) return 'Name is required.'
          if (
            trimmedValue.length < 1 ||
            trimmedValue.length > 20 ||
            !/^[a-zA-Z ]+$/.test(trimmedValue)
          ) {
            return 'Name must be 1-20 characters and contain only letters.'
          }
          break
        case 'email':
          if (isEmpty(trimmedValue)) return 'Email is required.'
          if (!/\S+@\S+\.\S+/.test(trimmedValue)) {
            return 'Invalid email format.'
          }
          break
      }
      break
    default:
      return ''
  }
  return ''
}

export const validateResume = (resume: Resume, options?: { languagesOptional?: boolean }) => {
  const { languagesOptional = false } = options || {}
  const errors = {
    aboutMe: validateField('aboutMe', 'aboutMe', resume.aboutMe),
    experiences: resume.experiences.map((exp, index) => ({
      'designation.name': validateField(
        'experiences',
        'designation.name',
        exp.designation.name,
        index,
      ),
      'company.name': validateField('experiences', 'company.name', exp.company.name, index),
      employmentType: validateField('experiences', 'employmentType', exp.employmentType, index),
      startDate: validateField('experiences', 'startDate', exp.startDate, index),
      endDate: validateField('experiences', 'endDate', exp.endDate, index, exp),
      isCurrent: validateField('experiences', 'isCurrent', exp.isCurrent, index),
      'location.address.city': validateField(
        'experiences',
        'location.address.city',
        exp.location.address.city,
        index,
      ),
      description: validateField('experiences', 'description', exp.description, index),
    })),
    educations: resume.educations.map((edu, index) => ({
      'degree.name': validateField('educations', 'degree.name', edu.degree.name, index),
      'institution.name': validateField(
        'educations',
        'institution.name',
        edu.institution.name,
        index,
      ),
      startYear: validateField('educations', 'startYear', edu.startYear, index),
      endYear: validateField('educations', 'endYear', edu.endYear, index, edu),
      'location.address.city': validateField(
        'educations',
        'location.address.city',
        edu.location.address.city,
        index,
      ),
      description: validateField('educations', 'description', edu.description, index),
    })),
    achievements: resume.achievements.map((ach, index) => ({
      title: validateField('achievements', 'title', ach.title, index),
      date: validateField('achievements', 'date', ach.date, index),
    })),
    skills:
      resume.skills.length > 0
        ? resume.skills.map((skill, index) => ({
            name: validateField('skills', 'name', skill.name, index),
            level: validateField('skills', 'level', skill.level, index),
          }))
        : [{ name: 'At least one skill is required !!' }],
    languages: languagesOptional
      ? resume.languages.map((lang, index) => ({
          name: validateField('languages', 'name', lang.name, index),
          level: validateField('languages', 'level', lang.level, index),
        }))
      : resume.languages.length > 0
        ? resume.languages.map((lang, index) => ({
            name: validateField('languages', 'name', lang.name, index),
            level: validateField('languages', 'level', lang.level, index),
          }))
        : [{ name: 'At least one language is required !!' }],
    socialLinks: resume.socialLinks.map((link, index) => ({
      title: validateField('socialLinks', 'title', link.title, index),
      url: validateField('socialLinks', 'url', link.url, index),
    })),
    references: resume.references.map((ref, index) => ({
      name: validateField('references', 'name', ref.name, index),
      email: validateField('references', 'email', ref.email, index),
    })),
  }
  return errors
}

export const hasErrors = (errors: Errors): boolean => {
  return Object.values(errors).some((section) => {
    if (Array.isArray(section)) {
      return section.some((item) =>
        Object.values(item).some((error) => error !== '' && error !== null),
      )
    }
    return section !== '' && section !== null
  })
}
