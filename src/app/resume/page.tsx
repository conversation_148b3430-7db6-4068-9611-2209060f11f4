'use client'
import { useEffect, useState, useMemo } from 'react'
import { useAuth } from '../../context/AuthContext'
import { UserService } from '../../services/UserService'
import { CareerService } from '../../services/CareerService'
import {
  Resume as ResumeType,
  Employment,
  ClearedSections,
  Achievement,
  Education,
  SocialLink,
  Reference,
  Skill,
  Language,
  DummyResumeData,
} from '../../models/Resume'
import CreateResume from './CreateResume'
import ViewResume from './ViewResume'
import ShowTemplates from './ShowTemplates'
import DownLoadResume from './DownLoadResume'
import BuyTemplate from './BuyTemplate'
import { templates } from './ResumeTemplates/templates'
import { formatMonthYear, formatToDDMMYYYY } from '../../utils/resumeUtils'
import { ResumeSkeleton } from './ResumeSkeleton'
import { X, XIcon } from '@phosphor-icons/react'
import IconButton from '../../components/IconButton'
import SlideUpView from '../../components/SlideUpView'
import { useColorContext } from '../../context/ColorContext'
import { useToast } from '../../components/ToastX'
import { initiateRazorpayPayment } from '../../utils/razorpay'
import useError from '@/context/ErrorContext'
import { useAccount } from '../../context/UserDetailsContext'
const initialFormResume: ResumeType = {
  personalDetails: {
    name: '',
    profilePicture: '',
    contactNumber: '',
    email: '',
    location: '',
    nationality: '',
    profile: '',
    dateOfBirth: '',
  },
  aboutMe: '',
  experiences: [],
  educations: [],
  achievements: [],
  skills: [],
  languages: [],
  socialLinks: [],
  references: [],
}

const processExperienceDates = (experience: Employment): Employment => {
  return {
    ...experience,
    startDate: experience.startDate ? formatMonthYear(experience.startDate) : '',
    endDate: experience.isCurrent
      ? 'Present'
      : experience.endDate
        ? formatMonthYear(experience.endDate)
        : '',
  }
}

const processAchievementDates = (achievement: Achievement): Achievement => {
  return {
    ...achievement,
    date: achievement.date ? formatMonthYear(achievement.date) : '',
  }
}

const isEmploymentEmpty = (employment: Employment) => {
  return (
    !employment.company.name &&
    !employment.designation.name &&
    !employment.startDate &&
    !employment.endDate &&
    !employment.description
  )
}

const isEducationEmpty = (education: Education) => {
  return (
    !education.institution.name &&
    !education.degree.name &&
    education.startYear === 0 &&
    education.endYear === 0 &&
    !education.description
  )
}

const isAchievementEmpty = (achievement: Achievement) => {
  return !achievement.title && !achievement.awardedBy && !achievement.date
}

const isSkillEmpty = (skill: Skill) => {
  return !skill.name && (skill.level === undefined || skill.level === 0)
}

const isLanguageEmpty = (language: Language) => {
  return !language.name && !language.level
}

const isSocialLinkEmpty = (socialLink: SocialLink) => {
  return !socialLink.title && !socialLink.url
}

const isReferenceEmpty = (reference: Reference) => {
  return !reference.name && !reference.email
}

function mergeWithDummy(
  formData: ResumeType,
  dummyData: ResumeType,
  clearedSections: Record<string, boolean>,
): ResumeType {
  const result: ResumeType = { ...formData }

  // Fill in dummy data if fields are empty and not cleared in the current session
  // Handle scalar fields like aboutMe
  if (!formData.aboutMe.trim() && !clearedSections.aboutMe) {
    result.aboutMe = dummyData.aboutMe
  }

  // Handle array sections
  if (formData.experiences.every(isEmploymentEmpty) && !clearedSections.experiences) {
    result.experiences = dummyData.experiences
  }

  if (formData.educations.every(isEducationEmpty) && !clearedSections.educations) {
    result.educations = dummyData.educations
  }

  if (formData.achievements.every(isAchievementEmpty) && !clearedSections.achievements) {
    result.achievements = dummyData.achievements
  }
  if (formData.skills.every(isSkillEmpty) && !clearedSections.skills) {
    result.skills = dummyData.skills
  }

  if (formData.languages.every(isLanguageEmpty) && !clearedSections.languages) {
    result.languages = dummyData.languages
  }

  if (formData.socialLinks.every(isSocialLinkEmpty) && !clearedSections.socialLinks) {
    result.socialLinks = dummyData.socialLinks
  }

  if (formData.references.every(isReferenceEmpty) && !clearedSections.references) {
    result.references = dummyData.references
  }

  return result
}

const Resume: React.FC = () => {
  const { authUserProfile, isAuthenticated } = useAuth()
  const { setSelectedColor } = useColorContext()
  const [formResume, setFormResume] = useState<ResumeType>(initialFormResume)
  const [originalFormResume, setOriginalFormResume] = useState<ResumeType>(initialFormResume)
  const [isLoading, setIsLoading] = useState(true)
  const [showTemplates, setShowTemplates] = useState<boolean>(false)
  const [selectedTemplate, setSelectedTemplate] = useState<number>(1)
  const [downloadResume, setDownloadResume] = useState<string | null>(null)
  const [buyResume, setBuyResume] = useState<boolean>(false)
  const [isViewResumeOpen, setIsViewResumeOpen] = useState<boolean>(false)
  const [isDownloadOpen, setIsDownloadOpen] = useState<boolean>(false)
  const [isProResumePurchased, setIsProResumePurchased] = useState<boolean>(false)
  const toast = useToast()
  const { setResumeProgress } = useAccount()
  const { handleError } = useError()
  const [clearedSections, setClearedSections] = useState<ClearedSections>({
    aboutMe: false,
    experiences: false,
    educations: false,
    achievements: false,
    skills: false,
    languages: false,
    socialLinks: false,
    references: false,
  })

  const displayResume = useMemo(
    () => mergeWithDummy(formResume, DummyResumeData, clearedSections),
    [formResume, clearedSections],
  )

  const handleSaveSuccess = () => {
    setOriginalFormResume({ ...formResume })
  }

  const isProTemplate = useMemo(() => {
    const template = templates.find((t) => t.id === selectedTemplate)
    return template ? template.isPro : false
  }, [selectedTemplate])

  const handleProgressChange = (newProgress: number) => {
    if (authUserProfile?.id) {
      const key = `resumeProgress_${authUserProfile.id}`
      localStorage.setItem(key, newProgress.toString())
      setResumeProgress(newProgress)
    }
  }

  const defaultEmployment: Employment = {
    location: {
      address: {
        country: 'USA',
        state: 'California',
        city: '',
        area: 'Downtown',
      },
      latitude: 0,
      longitude: 0,
    },
    employmentType: '',
    isActive: false,
    company: { other: false, name: '' },
    designation: { other: false, name: '' },
    startDate: '',
    endDate: '',
    description: '',
    isCurrent: false,
  }

  const defaultEducation: Education = {
    location: {
      address: {
        country: 'USA',
        state: 'California',
        city: '',
        area: 'Downtown',
      },
      latitude: 0,
      longitude: 0,
    },
    institution: { other: false, name: '' },
    description: '',
    startYear: 0,
    endYear: 0,
    degree: { other: false, name: '' },
    isCurrent: false,
  }

  const defaultAchievement: Achievement = {
    title: '',
    awardedBy: '',
    date: '',
  }

  const defaultSocialLink: SocialLink = {
    title: '',
    url: '',
  }

  const defaultReference: Reference = {
    name: '',
    email: '',
  }

  // Fetching of all Resume data
  useEffect(() => {
    const fetchAllData = async () => {
      if (!isAuthenticated) return
      setIsLoading(true)
      try {
        let updatedFormResume = { ...initialFormResume }

        if (authUserProfile) {
          updatedFormResume.personalDetails.name = `${
            authUserProfile.givenName || ''
          } ${authUserProfile.familyName || ''}`.trim()

          updatedFormResume.personalDetails.profilePicture = authUserProfile.profileImage?.url || ''
          updatedFormResume.personalDetails.location = authUserProfile.address?.line1 || ''
          updatedFormResume.personalDetails.dateOfBirth =
            formatToDDMMYYYY(authUserProfile.birthDate) || ''
          updatedFormResume.personalDetails.nationality = authUserProfile.nationality || ''
          updatedFormResume.personalDetails.profile = authUserProfile.title || ''

          const contactResponse = await UserService.getUserContact()
          const userContact = contactResponse.data.contact
          updatedFormResume.personalDetails.contactNumber = userContact?.primaryPhone || ''
          updatedFormResume.personalDetails.email = userContact?.primaryEmail || ''

          if (authUserProfile.permalink) {
            const resumeResponse = await CareerService.getCareerByPermalink(
              authUserProfile.permalink,
            )
            if (resumeResponse.status === 'success' && resumeResponse.data) {
              const fetchedResumeData = resumeResponse.data
              updatedFormResume = {
                ...updatedFormResume,
                ...fetchedResumeData,
                personalDetails: {
                  ...updatedFormResume.personalDetails,
                  ...(fetchedResumeData.personalDetails || {}),
                },
                experiences: fetchedResumeData.experiences?.map(processExperienceDates) || [],
                achievements: fetchedResumeData.achievements?.map(processAchievementDates) || [],
                socialLinks: fetchedResumeData.socialLinks || [],
                references: fetchedResumeData.references || [],
              }
            }
          }
        }

        // Add default empty items if arrays are empty
        if (updatedFormResume.experiences.length === 0) {
          updatedFormResume.experiences = [defaultEmployment]
        }
        if (updatedFormResume.educations.length === 0) {
          updatedFormResume.educations = [defaultEducation]
        }
        if (updatedFormResume.achievements.length === 0) {
          updatedFormResume.achievements = [defaultAchievement]
        }
        // For pro templates, add defaults if socialLinks or references are empty
        if (isProTemplate) {
          if (updatedFormResume.socialLinks.length === 0) {
            updatedFormResume.socialLinks = [defaultSocialLink]
          }
          if (updatedFormResume.references.length === 0) {
            updatedFormResume.references = [defaultReference]
          }
        }
        setFormResume(updatedFormResume)
        setOriginalFormResume(updatedFormResume)
      } catch (error) {
        console.error('Error fetching data:', error)
        const fallbackResume = { ...initialFormResume }
        if (authUserProfile) {
          fallbackResume.personalDetails.name = `${
            authUserProfile.givenName || ''
          } ${authUserProfile.familyName || ''}`.trim()
          fallbackResume.personalDetails.profilePicture = authUserProfile.profileImage?.url || ''
          fallbackResume.personalDetails.location = authUserProfile.address?.line1 || ''
          fallbackResume.personalDetails.dateOfBirth = authUserProfile.birthDate || ''
          fallbackResume.personalDetails.nationality = 'Indian'
          fallbackResume.personalDetails.profile = 'Software Engineer'
          try {
            const contactResponse = await UserService.getUserContact()
            const userContact = contactResponse.data.contact
            fallbackResume.personalDetails.contactNumber = userContact?.primaryPhone || ''
            fallbackResume.personalDetails.email = userContact?.primaryEmail || ''
          } catch (contactError) {
            console.error('Error fetching contact details:', contactError)
          }
        }
        // Add default empty items in fallback case
        if (fallbackResume.experiences.length === 0) {
          fallbackResume.experiences = [defaultEmployment]
        }
        if (fallbackResume.educations.length === 0) {
          fallbackResume.educations = [defaultEducation]
        }
        if (fallbackResume.achievements.length === 0) {
          fallbackResume.achievements = [defaultAchievement]
        }
        // For pro templates, add defaults if socialLinks or references are empty
        if (isProTemplate) {
          if (fallbackResume.socialLinks.length === 0) {
            fallbackResume.socialLinks = [defaultSocialLink]
          }
          if (fallbackResume.references.length === 0) {
            fallbackResume.references = [defaultReference]
          }
        }
        setFormResume(fallbackResume)
        setOriginalFormResume(fallbackResume)
      } finally {
        setIsLoading(false)
      }
    }

    fetchAllData()
  }, [authUserProfile])

  // Adjust formResume when template type changes
  useEffect(() => {
    setFormResume((prev) => {
      const updated = { ...prev }
      if (isProTemplate) {
        if (updated.socialLinks.length === 0) {
          updated.socialLinks = [defaultSocialLink]
        }
        if (updated.references.length === 0) {
          updated.references = [defaultReference]
        }
      }
      // Do not clear socialLinks and references for free templates
      return updated
    })
  }, [isProTemplate])

  const handleSuccess = () => {
    setIsDownloadOpen(true)
    setIsProResumePurchased(true)
  }

  const handleClose = () => setBuyResume(false)
  const handlePayNowClick = async () => {
    try {
      const formData = {
        amount: {
          subtotal: 80,
          taxes: {
            gst: 14.4,
          },
          total: 94.4,
        },
        flow: 'PRO_RESUME',
        resumeId: 'pro-resume-orange',
      }
      const response = await UserService.createOrder(formData)
      const result = await initiateRazorpayPayment(
        response.data.payment.processor.orderId,
        response.data._id,
        toast,
        handleClose,
        handleSuccess,
      )
    } catch (error) {
      handleError(error)
    }
  }

  const toggleViewResume = () => {
    setIsViewResumeOpen(!isViewResumeOpen)
  }

  const handleTemplateSelect = (templateId: number) => {
    setSelectedTemplate(templateId)
    const template = templates.find((t) => t.id === templateId)
    if (template && formResume) {
      setFormResume({
        ...formResume,
      })
      if (template.isPro && template.colorOptions && template.colorOptions.length > 0) {
        setSelectedColor(template.colorOptions[0])
      } else {
        setSelectedColor('')
      }
    }
  }

  if (isLoading) {
    return <ResumeSkeleton />
  }

  return (
    <div className="relative h-full w-full pb-16 lg:py-2 xl:pb-0">
      <div
        className={`m-auto w-full max-w-[1440px] ${
          downloadResume || buyResume || isViewResumeOpen
            ? 'scrollbar-none dark:bg-dark-lucres-black-500 max-h-screen overflow-hidden bg-white'
            : 'bg-lucres-900'
        } flex h-full min-h-full items-start justify-start xl:mt-10`}
      >
        <div className="dark:bg-dark-lucres-black-500 min-h-screen w-full bg-white xl:w-3/5">
          {showTemplates ? (
            <div className="dark:bg-dark-lucres-black-500 h-full min-h-full w-full bg-white p-12">
              <ShowTemplates
                setShowTemplates={setShowTemplates}
                selectedTemplate={selectedTemplate}
                setSelectedTemplate={handleTemplateSelect}
                templates={templates}
                setResumeData={setFormResume}
              />
            </div>
          ) : (
            <div className="dark:bg-dark-lucres-black-500 h-full w-full bg-white p-4 xl:px-10 xl:pt-0">
              <CreateResume
                toggleViewResume={toggleViewResume}
                resume={formResume}
                setResume={setFormResume}
                originalResume={originalFormResume}
                onSaveSuccess={handleSaveSuccess}
                setClearedSections={setClearedSections}
                isProTemplate={isProTemplate}
                onProgressChange={handleProgressChange}
              />
            </div>
          )}
        </div>
        <div className="sticky top-0 hidden min-h-full w-1/2 items-start justify-center rounded-lg xl:block">
          <ViewResume
            setShowTemplates={setShowTemplates}
            showTemplates={showTemplates}
            setIsDownloadOpen={setIsDownloadOpen}
            setBuyResume={setBuyResume}
            setDownloadResume={setDownloadResume}
            selectedTemplate={selectedTemplate}
            templates={templates}
            resumeData={displayResume}
            setSelectedTemplate={handleTemplateSelect}
            setResumeData={setFormResume}
            isProResumePurchased={isProResumePurchased}
            setIsProResumePurchased={setIsProResumePurchased}
          />
        </div>
      </div>

      {isDownloadOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto bg-black/80 ">
          <div className="dark:bg-dark-lucres-black-500 relative mx-8 w-full max-w-2xl transform overflow-y-auto rounded-lg bg-white p-6 shadow-xl transition duration-300 sm:p-8 md:w-[600px]">
            <DownLoadResume
              resumeData={displayResume}
              setDownloadResume={setIsDownloadOpen}
              templateId={selectedTemplate}
              selectedColor={downloadResume}
              // isProResumePurchased={isProResumePurchased}
              // setIsProResumePurchased={setIsProResumePurchased}
            />
          </div>
        </div>
      )}
      {buyResume && (
        <BuyTemplate onPayNowClick={handlePayNowClick} onClose={() => setBuyResume(false)} />
      )}

      <div
        className={`bg-lucres-900 dark:bg-dark-lucres-black-500 fixed right-0 top-0 z-50 h-full min-h-full w-full transform overflow-y-auto shadow-lg transition-transform duration-300 xl:hidden ${
          isViewResumeOpen ? 'translate-x-0' : 'translate-x-full'
        } w-full overflow-x-hidden`}
      >
        <div className="flex h-full w-full flex-col p-4">
          <div className="flex w-full flex-col">
            <span className={`${showTemplates && 'hidden'}`}>
              <ViewResume
                showTemplates={showTemplates}
                setSelectedTemplate={handleTemplateSelect}
                setIsDownloadOpen={setIsDownloadOpen}
                setBuyResume={setBuyResume}
                setDownloadResume={setDownloadResume}
                setShowTemplates={setShowTemplates}
                selectedTemplate={selectedTemplate}
                templates={templates}
                resumeData={displayResume}
                setResumeData={setFormResume}
                showCloseButton={true}
                onClose={toggleViewResume}
                isProResumePurchased={isProResumePurchased}
                setIsProResumePurchased={setIsProResumePurchased}
              />
            </span>
            <SlideUpView isOpen={showTemplates}>
              <div className="h-screen p-8">
                <ShowTemplates
                  setShowTemplates={setShowTemplates}
                  selectedTemplate={selectedTemplate}
                  setSelectedTemplate={handleTemplateSelect}
                  templates={templates}
                  setResumeData={setFormResume}
                />
              </div>
            </SlideUpView>
            <SlideUpView isOpen={isDownloadOpen}>
              <span className="absolute right-5 top-5 text-white">
                <IconButton onClick={() => setIsDownloadOpen(false)}>
                  <XIcon size={20} />
                </IconButton>
              </span>
              <div className="mt-16 p-8">
                {isDownloadOpen && (
                  <DownLoadResume
                    resumeData={displayResume}
                    setDownloadResume={setIsDownloadOpen}
                    templateId={selectedTemplate}
                    selectedColor={downloadResume}
                  />
                )}
              </div>
            </SlideUpView>
            {buyResume && (
              <BuyTemplate onPayNowClick={handlePayNowClick} onClose={() => setBuyResume(false)} />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Resume
