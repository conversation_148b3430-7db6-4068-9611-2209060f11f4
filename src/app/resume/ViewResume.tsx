'use client'
import { useRef, useState, useEffect } from 'react'
import { Swatches, SwatchesIcon, X, XIcon } from '@phosphor-icons/react'
import Button from '../../components/Button'
import { Template } from './ResumeTemplates/templates'
import { Resume } from '../../models/Resume'
import ProBadge from '../../components/ProBadge'
import TemplateViewer from './ResumeTemplates/TemplateViewer'
import { useColorContext } from '../../context/ColorContext'
import ColorSwatch from '../../components/ColorSwatch'
import IconButton from '../../components/IconButton'
import { CareerService } from '../../services/CareerService'

interface ViewResumeProps {
  setShowTemplates: React.Dispatch<React.SetStateAction<boolean>>
  showTemplates: boolean
  setDownloadResume: (color: string | null) => void
  setIsDownloadOpen: (open: boolean) => void
  setBuyResume: (buyResume: boolean) => void
  selectedTemplate: number
  setSelectedTemplate: (id: number) => void
  templates: Template[]
  resumeData: Resume
  setResumeData: (data: Resume) => void
  isProResumePurchased: boolean
  setIsProResumePurchased: (purchased: boolean) => void
  showCloseButton?: boolean
  onClose?: () => void
}

const ViewResume: React.FC<ViewResumeProps> = (props) => {
  const {
    setShowTemplates,
    setDownloadResume,
    setIsDownloadOpen,
    setBuyResume,
    selectedTemplate,
    templates,
    resumeData,
    showCloseButton = false,
    onClose,
    isProResumePurchased,
    setIsProResumePurchased,
    showTemplates,
  } = props

  const { selectedColor, setSelectedColor, setSecondaryColor } = useColorContext()
  const selectedTemplateData = templates.find((t) => t.id === selectedTemplate)
  const stickyRef = useRef<HTMLDivElement>(null)
  const [isStuck, setIsStuck] = useState(false)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsStuck(!entry.isIntersecting)
      },
      { threshold: [1.0] },
    )

    if (stickyRef.current) {
      observer.observe(stickyRef.current)
    }

    return () => {
      if (stickyRef.current) {
        observer.unobserve(stickyRef.current)
      }
    }
  }, [])

  const handleDownload = () => {
    if (selectedTemplate === 1) {
      setDownloadResume(null) // Use null for free template (no color needed)
      setIsDownloadOpen(true)
    } else {
      setDownloadResume(selectedColor || 'default') // Set color for premium
      setIsDownloadOpen(true) // Open the download modal
    }
  }
  // Reset secondaryColor based on template
  useEffect(() => {
    if (selectedTemplate === 4) {
      // Get the index of selectedColor from colorOptions
      const colorIndex = selectedTemplateData?.colorOptions?.indexOf(selectedColor) ?? 0
      const fallbackIndex = colorIndex >= 0 ? colorIndex : 0
      const secondary = selectedTemplateData?.secondaryColorOptions?.[fallbackIndex] ?? 'white'
      setSecondaryColor(secondary)
    } else {
      setSecondaryColor('white')
    }
  }, [selectedColor, selectedTemplate, selectedTemplateData])

  const getProResumeDownloadDetails = async () => {
    try {
      const response = await CareerService.downLoadProResume()
      setIsProResumePurchased(true)
    } catch (error: any) {
      if (error.data.messageType === 'PRO_RESUME_NOT_PURCHASED') {
        setIsProResumePurchased(false)
      }
    }
  }
  useEffect(() => {
    getProResumeDownloadDetails()
  }, [])

  const handleDownloadProResume = () => {
    setIsDownloadOpen(true)
    // getProResumeDownloadDetails()
  }
  return (
    <div className="w-full xl:p-8 xl:pt-5">
      {/* Sticky Header */}
      <div
        ref={stickyRef}
        className="bg-lucres-900  sticky top-0 z-10 flex w-full items-center justify-between gap-x-2 xl:top-14"
      >
        {/* Left Section: Select Template + Color Swatch */}
        <div className="my-3 flex flex-col gap-y-3">
          <div
            className="flex cursor-pointer items-center gap-x-2 whitespace-nowrap text-gray-100"
            onClick={() => setShowTemplates(true)}
          >
            <SwatchesIcon className="h-5 w-5 md:h-6 md:w-6" />
            <span className="text-sm md:text-base">Select Pro Template</span>
          </div>
          {selectedTemplateData?.isPro && selectedTemplateData?.colorOptions && (
            <ColorSwatch
              colors={selectedTemplateData.colorOptions}
              secondaryColors={selectedTemplateData.secondaryColorOptions}
              selectedColor={selectedColor}
              onSelect={setSelectedColor}
              selectedTemplateId={selectedTemplate}
            />
          )}
        </div>

        {/* Right Section: Action Buttons */}
        <div className="mb-2 mt-3 flex items-end gap-x-2">
          {selectedTemplateData?.isPro ? (
            !isProResumePurchased ? (
              <Button
                size="small"
                className="px-3! py-2! xl:px-6! xl:py-3! text-xs! md:text-sm! mt-2 md:mt-0"
                onClick={() => setBuyResume(true)}
              >
                Buy Template
              </Button>
            ) : (
              <Button
                onClick={handleDownloadProResume}
                size="small"
                className="rounded-lg! px-3! py-2! xl:px-6! xl:py-3! text-xs! md:text-sm!  mt-3 md:mt-0"
              >
                Download
              </Button>
            )
          ) : (
            <Button
              onClick={handleDownload}
              size="small"
              className="rounded-lg! px-3! py-2! xl:px-6! xl:py-3! mt-3 md:mt-0"
            >
              Download
            </Button>
          )}
        </div>

        {/* Close Button */}
        {showCloseButton && !showTemplates && onClose && (
          <div className="absolute right-0 top-[-18px] md:static">
            <IconButton onClick={onClose}>
              <XIcon size={18} className="text-white " />
            </IconButton>
          </div>
        )}
      </div>

      {/* Resume Viewer with Dynamic Margin */}
      <div className={`transition-all duration-300 ${isStuck ? 'mt-16' : 'mt-5'}`}>
        <div className="relative">
          {selectedTemplateData && (
            <>
              <TemplateViewer resumeData={resumeData} selectedTemplate={selectedTemplate} />
              {selectedTemplateData?.isPro && <ProBadge />}
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default ViewResume
