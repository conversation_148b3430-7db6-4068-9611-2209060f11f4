'use client'
import {
  FC,
  useLayoutEffect,
  useEffect,
  useRef,
  useState,
  useMemo,
  Children,
  isValidElement,
  JSX,
} from 'react'
import { Resume } from '../../../models/Resume'
import { useColorContext } from '../../../context/ColorContext'
import React from 'react'

interface ResumePreviewProps {
  resumeData: Resume
  templateComponent: FC<{ data: Resume; selectedColor: string }> // Update interface
  templateId: number
}

// Constants for A4 dimensions and layout
const A4_WIDTH = 794 // pixels
const A4_HEIGHT = 1123 // pixels, consistent for all templates
const PAGE_MARGIN = 16 // pixels between sections
const SIDEBAR_WIDTH = '2.7in'
const SIDEBAR_PADDING = 21 // pixelsAdd commentMore actions
const MAIN_PADDING = 25 // pixels
const SINGLE_COLUMN_PADDING = 32 // pixels for Template ID 1

const ResumePreview: FC<ResumePreviewProps> = ({
  resumeData,
  templateComponent: TemplateComponent,
  templateId,
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const sectionRefs = useRef<(HTMLDivElement | null)[]>([])
  const sidebarSectionRefs = useRef<(HTMLDivElement | null)[]>([])
  const { selectedColor, secondaryColor } = useColorContext()
  const headerRef = useRef<HTMLDivElement>(null)
  const [scale, setScale] = useState(1)
  const [pages, setPages] = useState<React.ReactElement[][]>([])

  // Type guard for elements with children
  const hasChildren = (
    element: React.ReactElement,
  ): element is React.ReactElement<{ children: React.ReactNode }> => {
    return !!(element.props && typeof element.props === 'object' && 'children' in element.props)
  }

  // Memoized template sections for single column layout
  const singleColumnSections = useMemo(() => {
    if (templateId === 1) {
      return Children.toArray(
        TemplateComponent({ data: resumeData, selectedColor }) as JSX.Element[],
      )
        .filter(isValidElement)
        .map((section, index) => (
          <div
            key={index}
            ref={(el) => {
              sectionRefs.current[index] = el
            }}
          >
            {section}
          </div>
        ))
    }
    return []
  }, [resumeData, templateId, selectedColor])

  // Memoized template data for two-column layout
  const twoColumnData = useMemo(() => {
    if (templateId !== 1) {
      const template = TemplateComponent({ data: resumeData, selectedColor })
      if (!isValidElement(template)) return { header: null, sidebarSections: [], mainSections: [] }

      if (templateId === 3 || templateId === 4) {
        const [header, flexContainer] = Children.toArray(
          (template.props as { children: React.ReactNode }).children,
        )
        if (!isValidElement(header) || !isValidElement(flexContainer)) {
          return { header: null, sidebarSections: [], mainSections: [] }
        }
        const [sidebar, mainContent] = Children.toArray(
          (flexContainer.props as { children: React.ReactNode }).children,
        )
        if (
          !isValidElement(sidebar) ||
          !hasChildren(sidebar) ||
          !isValidElement(mainContent) ||
          !hasChildren(mainContent)
        ) {
          return { header: null, sidebarSections: [], mainSections: [] }
        }
        return {
          header,
          sidebarSections: Children.toArray(sidebar.props.children).filter(isValidElement),
          mainSections: Children.toArray(mainContent.props.children).filter(isValidElement),
        }
      } else {
        const [sidebar, mainContent] = Children.toArray(
          (template.props as { children: React.ReactNode }).children,
        )
        if (
          !isValidElement(sidebar) ||
          !hasChildren(sidebar) ||
          !isValidElement(mainContent) ||
          !hasChildren(mainContent)
        ) {
          return { header: null, sidebarSections: [], mainSections: [] }
        }
        return {
          header: null,
          sidebarSections: Children.toArray(sidebar.props.children).filter(isValidElement),
          mainSections: Children.toArray(mainContent.props.children).filter(isValidElement),
        }
      }
    }
    return { header: null, sidebarSections: [], mainSections: [] }
  }, [resumeData, templateId, selectedColor])

  const paginateSections = (
    sections: JSX.Element[],
    heights: number[],
    availableHeight: number,
  ): JSX.Element[][] => {
    const pages: React.ReactElement[][] = []
    let currentPage: React.ReactElement[] = []
    let currentHeight = 0

    sections.forEach((section, index) => {
      const height = heights[index]
      const heightWithMargin = currentHeight > 0 ? height + PAGE_MARGIN : height

      if (currentHeight + heightWithMargin > availableHeight) {
        pages.push(currentPage)
        currentPage = [section]
        currentHeight = height
      } else {
        currentPage.push(section)
        currentHeight += heightWithMargin
      }
    })

    if (currentPage.length > 0) pages.push(currentPage)
    return pages
  }

  // Pagination function for templates with header
  const paginateSectionsWithHeader = (
    sections: React.ReactElement[],
    heights: number[],
    headerHeight: number,
    padding: number,
  ): React.ReactElement[][] => {
    const pages: React.ReactElement[][] = []
    let currentPage: React.ReactElement[] = []
    let currentHeight = 0
    let isFirstPage = true

    sections.forEach((section, index) => {
      const height = heights[index]
      const heightWithMargin = currentHeight > 0 ? height + PAGE_MARGIN : height
      const availableHeight = isFirstPage
        ? A4_HEIGHT - headerHeight - 2 * padding
        : A4_HEIGHT - 2 * padding

      if (currentHeight + heightWithMargin > availableHeight) {
        pages.push(currentPage)
        currentPage = [section]
        currentHeight = height
        isFirstPage = false
      } else {
        currentPage.push(section)
        currentHeight += heightWithMargin
      }
    })

    if (currentPage.length > 0) pages.push(currentPage)
    return pages
  }

  // Initial scale calculation based on container width
  useLayoutEffect(() => {
    if (containerRef.current) {
      const containerWidth = containerRef.current.offsetWidth
      const newScale = containerWidth / A4_WIDTH
      setScale(Math.min(newScale, 1))
    }
  }, [])

  // Resize observer for dynamic scaling
  useEffect(() => {
    const adjustScale = () => {
      if (containerRef.current) {
        const containerWidth = containerRef.current.offsetWidth
        setScale(Math.min(containerWidth / A4_WIDTH, 1))
      }
    }

    const resizeObserver = new ResizeObserver(adjustScale)
    if (containerRef.current) resizeObserver.observe(containerRef.current)

    return () => resizeObserver.disconnect()
  }, [])

  // Handle font loading
  useEffect(() => {
    const handleFontLoad = async () => {
      await document.fonts.ready // Wait for all fonts to load
      if (templateId === 1) {
        const sectionHeights = sectionRefs.current.map((ref) => ref?.offsetHeight || 0)
        setPages(
          paginateSections(
            singleColumnSections,
            sectionHeights,
            A4_HEIGHT - 2 * SINGLE_COLUMN_PADDING,
          ),
        )
      }
    }
    handleFontLoad()
  }, [templateId, singleColumnSections])

  // Pagination logic based on template type
  useLayoutEffect(() => {
    if (templateId === 1) {
      // Single column pagination
      const sectionHeights = sectionRefs.current.map((ref) => ref?.offsetHeight || 0)
      const availableHeight = A4_HEIGHT - 2 * SINGLE_COLUMN_PADDING // 1123 - 64 = 1059px
      setPages(paginateSections(singleColumnSections, sectionHeights, availableHeight))
    } else if (templateId === 3 || templateId === 4) {
      // Two-column pagination with header on first page
      const headerHeight = headerRef.current?.offsetHeight || 0
      const sidebarHeights = sidebarSectionRefs.current.map((ref) => ref?.offsetHeight || 0)
      const mainHeights = sectionRefs.current.map((ref) => ref?.offsetHeight || 0)

      const sidebarPages = paginateSectionsWithHeader(
        twoColumnData.sidebarSections,
        sidebarHeights,
        headerHeight,
        SIDEBAR_PADDING,
      )
      const mainPages = paginateSectionsWithHeader(
        twoColumnData.mainSections,
        mainHeights,
        headerHeight,
        MAIN_PADDING,
      )
      const totalPages = Math.max(sidebarPages.length, mainPages.length)

      const combinedPages: React.ReactElement[][] = Array.from(
        { length: totalPages },
        (_, index) => {
          const sidebarContent = sidebarPages[index] || []
          const mainContent = mainPages[index] || []
          const isFirstPage = index === 0
          // Set sidebar background color based on templateId
          const sidebarBackgroundColor =
            templateId === 4 ? secondaryColor || '#34C2FF29' : '#FFFFFF'
          const twoColumnContent = (
            <div className="font-dm-sans flex gap-0" key={`two-column-${index}`}>
              <div
                className="text-[#1E1E1E]"
                style={{
                  width: SIDEBAR_WIDTH,
                  height: A4_HEIGHT,
                  padding: `${SIDEBAR_PADDING}px`,
                  backgroundColor: sidebarBackgroundColor,
                }}
              >
                {sidebarContent}
              </div>
              <div className="flex-1" style={{ padding: `${MAIN_PADDING}px` }}>
                {mainContent}
              </div>
            </div>
          )

          if (isFirstPage) {
            // Apply selectedColor to header only for templateId === 4
            const styledHeader =
              templateId === 4 ? (
                <div key="header" style={{ backgroundColor: selectedColor || '#145349' }}>
                  {twoColumnData.header}
                </div>
              ) : (
                twoColumnData.header
              )
            return [styledHeader, twoColumnContent].filter(Boolean) as React.ReactElement[]
          } else {
            return [twoColumnContent]
          }
        },
      )
      setPages(combinedPages)
    } else {
      // Two-column pagination for templateId === 2
      const sidebarHeights = sidebarSectionRefs.current.map((ref) => ref?.offsetHeight || 0)
      const mainHeights = sectionRefs.current.map((ref) => ref?.offsetHeight || 0)

      const sidebarAvailableHeight = A4_HEIGHT - 2 * SIDEBAR_PADDING // 1123 - 42 = 1081px
      const mainAvailableHeight = A4_HEIGHT - 2 * MAIN_PADDING // 1123 - 50 = 1073px

      const sidebarPages = paginateSections(
        twoColumnData.sidebarSections,
        sidebarHeights,
        sidebarAvailableHeight,
      )
      const mainPages = paginateSections(
        twoColumnData.mainSections,
        mainHeights,
        mainAvailableHeight,
      )
      const totalPages = Math.max(sidebarPages.length, mainPages.length)

      const combinedPages: React.ReactElement[][] = Array.from(
        { length: totalPages },
        (_, index) => {
          const sidebarContent = sidebarPages[index] || []
          const mainContent = mainPages[index] || []

          return [
            <div
              className="font-dm-sans flex gap-0"
              key={`page-${index}`}
              style={{ height: '100%' }}
            >
              <div
                className="text-white"
                style={{
                  width: SIDEBAR_WIDTH,
                  padding: `${SIDEBAR_PADDING}px`,
                  backgroundColor: selectedColor || '#145349',
                }}
              >
                {sidebarContent}
              </div>
              <div className="flex-1" style={{ padding: `${MAIN_PADDING}px`, height: '100%' }}>
                {mainContent}
              </div>
            </div>,
          ]
        },
      )
      setPages(combinedPages)
    }
  }, [resumeData, templateId, singleColumnSections, twoColumnData, selectedColor, secondaryColor])

  // Render hidden measurement container
  const renderMeasurementContainer = () => {
    if (templateId === 1) {
      return <div>{singleColumnSections}</div>
    } else if (templateId === 3 || templateId === 4) {
      const styledHeader =
        templateId === 4 ? (
          <div style={{ backgroundColor: selectedColor || '#145349' }}>{twoColumnData.header}</div>
        ) : (
          twoColumnData.header
        )
      return (
        <div>
          <div ref={headerRef}>{styledHeader}</div>
          <div style={{ display: 'flex', gap: '0' }}>
            <div style={{ width: SIDEBAR_WIDTH, padding: `${SIDEBAR_PADDING}px` }}>
              {twoColumnData.sidebarSections.map((section, index) => (
                <div
                  key={index}
                  ref={(el) => {
                    sidebarSectionRefs.current[index] = el
                  }}
                >
                  {section}
                </div>
              ))}
            </div>
            <div style={{ flex: 1, padding: `${MAIN_PADDING}px` }}>
              {twoColumnData.mainSections.map((section, index) => (
                <div
                  key={index}
                  ref={(el) => {
                    sectionRefs.current[index] = el
                  }}
                >
                  {section}
                </div>
              ))}
            </div>
          </div>
        </div>
      )
    } else {
      return (
        <div style={{ display: 'flex', gap: '0' }}>
          <div style={{ width: SIDEBAR_WIDTH, padding: `${SIDEBAR_PADDING}px` }}>
            {twoColumnData.sidebarSections.map((section, index) => (
              <div
                key={index}
                ref={(el) => {
                  sidebarSectionRefs.current[index] = el
                }}
              >
                {section}
              </div>
            ))}
          </div>
          <div style={{ flex: 1, padding: `${MAIN_PADDING}px` }}>
            {twoColumnData.mainSections.map((section, index) => (
              <div
                key={index}
                ref={(el) => {
                  sectionRefs.current[index] = el
                }}
              >
                {section}
              </div>
            ))}
          </div>
        </div>
      )
    }
  }

  return (
    <div ref={containerRef} style={{ width: '100%', height: 'auto', backgroundColor: '#2D4232' }}>
      {/* Hidden measurement container for accurate sizing */}
      <div
        style={{
          position: 'absolute',
          visibility: 'hidden',
          width: `${A4_WIDTH}px`,
        }}
      >
        {renderMeasurementContainer()}
      </div>

      {/* Render paginated resume pages */}
      {pages.map((pageSections, pageIndex) => (
        <div
          key={pageIndex}
          style={{
            width: `${A4_WIDTH * scale}px`,
            height: `${A4_HEIGHT * scale}px`,
            marginBottom: '18px',
            overflow: 'hidden',
          }}
        >
          <div
            className="overflow-hidden bg-white"
            style={{
              width: `${A4_WIDTH}px`,
              height: `${A4_HEIGHT}px`,
              padding:
                templateId === 1 ? `${SINGLE_COLUMN_PADDING}px` : templateId === 3 ? '16px' : '0',
              boxSizing: 'border-box',
              transform: `scale(${scale})`,
              transformOrigin: 'top left',
              boxShadow: '0 0 10px rgba(0,0,0,0.1)',
            }}
          >
            {pageSections}
          </div>
        </div>
      ))}
    </div>
  )
}

export default ResumePreview
