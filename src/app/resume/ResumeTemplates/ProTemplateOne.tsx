import { FC } from 'react'
import { Resume } from '../../../models/Resume'
import { ArrowUpRightIcon } from '@phosphor-icons/react'
import { formatDate } from '../../../utils/resumeUtils'
import SafeHtml from '../../../components/SafeHtml'

export interface PremiumTemplateProps {
  data: Resume
  selectedColor: string
}

const ProTemplateOne: FC<PremiumTemplateProps> = ({ data, selectedColor }) => {
  // Sidebar content
  const sidebarSections = [
    // Profile Section
    <div key="profile-picture">
      {data.personalDetails?.profilePicture && (
        <img
          src={data.personalDetails.profilePicture}
          alt="Profile"
          className="mb-4 ml-[11px] h-24 w-24 rounded-full"
        />
      )}
    </div>,
    <div key="profile-name">
      <h1 className="mb-2 text-xl font-bold leading-none">{data.personalDetails?.name}</h1>
    </div>,
    <div key="profile-title">
      <p className="mb-4 text-lg font-medium leading-none tracking-[0.02em] opacity-70">
        {data.personalDetails.profile || ''}
      </p>
    </div>,

    // Details Section
    <div key="details-heading">
      <h3 className="mt-8 pb-3 text-lg font-bold capitalize leading-none tracking-[2%]">Details</h3>
      <div className="mb-3 border-b border-white opacity-20" />
    </div>,
    <div key="details-address">
      <p className="mb-0.5 font-medium leading-tight tracking-[0.02em]">Address</p>
      <p className="mb-4 leading-tight tracking-[0.011em] opacity-70">
        {data.personalDetails?.location}
      </p>
    </div>,
    <div key="details-phone">
      <p className="mb-0.5 font-medium leading-tight tracking-[0.02em]">Phone</p>
      <p className="mb-4 leading-tight tracking-[0.011em] opacity-70">
        {data.personalDetails?.contactNumber}
      </p>
    </div>,
    <div key="details-email">
      <p className="mb-0.5 font-medium leading-tight tracking-[0.02em]">Email</p>
      <p className="mb-4 hyphens-none break-words break-all leading-tight tracking-[0.011em] opacity-70">
        <a
          href={`mailto:${data.personalDetails?.email}`}
          className="text-inherit no-underline"
          style={{ hyphens: 'none' }}
        >
          {data.personalDetails?.email}
        </a>
      </p>
    </div>,

    // Skills Section
    data.skills?.length > 0 && (
      <>
        <div key="skills-heading">
          <h3 className="mt-8 pb-3 text-lg font-bold capitalize leading-none tracking-[0.02em]">
            Skills
          </h3>
          <div className="mb-3 border-b border-white opacity-20" />
        </div>
        {data.skills.map((skill, index) => (
          <div key={`skill-${index}`}>
            <p className="mb-2 text-[15px] leading-none tracking-[0.032em]">{skill.name}</p>
          </div>
        ))}
      </>
    ),

    // Languages Section
    data.languages?.length > 0 && (
      <>
        <div key="languages-heading">
          <h3 className="mt-8 pb-3 text-lg font-bold capitalize leading-none tracking-[0.02em]">
            Languages
          </h3>
          <div className="mb-3 border-b border-white opacity-20" />
        </div>
        {data.languages.map((lang, index) => (
          <div key={`language-${index}`}>
            <p className="pb-[3px] text-[15px] font-medium capitalize leading-none tracking-[0.024em]">
              {lang.name}
            </p>
          </div>
        ))}
      </>
    ),

    // Links Section
    data.socialLinks?.length > 0 && (
      <>
        <div key="links-heading">
          <h3 className="mt-8 pb-3 text-lg font-bold capitalize leading-none tracking-[0.02em]">
            Links
          </h3>
          <div className="mb-3 border-b border-white opacity-20" />
        </div>
        {data.socialLinks.map((link, index) => (
          <div key={`link-${index}`} className="mb-[5px] flex items-center gap-1">
            <a
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-base capitalize text-white no-underline"
            >
              {link.title}
            </a>
            <ArrowUpRightIcon size={14} />
          </div>
        ))}
      </>
    ),

    // References Section
    data.references?.length > 0 && (
      <>
        <div key="references-heading">
          <h3 className="mt-8 pb-3 text-lg font-bold capitalize leading-none tracking-[0.02em]">
            Reference
          </h3>
          <div className="mb-3 border-b border-white opacity-20" />
        </div>
        {data.references.map((ref, index) => (
          <div key={`reference-${index}`} className="mb-[5px]">
            <p className="pb-1 text-base font-bold capitalize leading-none tracking-[0.022em]">
              {ref.name}
            </p>
            <p className="mb-2 hyphens-none break-words break-all text-base leading-none tracking-[0.022em]">
              {ref.email}
            </p>
          </div>
        ))}
      </>
    ),
  ].filter(Boolean)

  // Main content sections
  const mainSections = [
    // Professional Summary
    data.aboutMe && data.aboutMe.trim() && (
      <div key="summary" className="mb-2">
        <h2 className="mb-2 text-lg font-bold" style={{ color: selectedColor }}>
          Professional Summary
        </h2>
        <p className="mb-6 text-[15px] leading-[1.2] text-black opacity-70">{data.aboutMe}</p>
      </div>
    ),

    // Work History
    data.experiences?.length > 0 && (
      <>
        <div key="work-heading" className="mb-2">
          <h2 className="mb-2 text-lg font-bold" style={{ color: selectedColor }}>
            Work History
          </h2>
        </div>
        {data.experiences.map((job, index) => (
          <div key={`job-${index}`} className="mb-4">
            <h3 className="text-base font-semibold leading-tight tracking-[1%] text-black">
              {job.company.name}{' '}
              <span className="ml-1 text-sm font-normal leading-tight text-black">
                {job.designation.name}
              </span>
            </h3>
            <p className="mt-1 text-sm text-black opacity-70">
              {`${job.startDate ? formatDate(job.startDate) : 'Start Date'} – ${
                job.endDate ? formatDate(job.endDate) : 'End Date'
              }`}
            </p>
            {job.description && (
              <SafeHtml
                html={job.description}
                className="text-sm text-black opacity-70 [&_ol]:ml-6 [&_ol]:list-decimal [&_ul]:ml-4 [&_ul]:list-disc"
              />
            )}
          </div>
        ))}
      </>
    ),

    // Education (only if educations exist)
    data.educations?.length > 0 && (
      <>
        <div key="education-heading" className="mb-2">
          <h2 className="mb-2 text-lg font-bold" style={{ color: selectedColor }}>
            Education
          </h2>
        </div>
        {data.educations.map((edu, index) => (
          <div key={`edu-${index}`} className="mb-4">
            <h3 className="text-base font-semibold leading-tight tracking-tight text-black">
              {edu.institution.name}{' '}
              <span className="ml-1 text-sm font-normal leading-tight text-black">
                {edu.degree.name}
              </span>
            </h3>
            <p className="mt-1 text-sm text-black opacity-70">
              {(edu.startYear && edu.startYear !== 0 ? edu.startYear : 'Start Year') +
                ' – ' +
                (edu.endYear && edu.endYear !== 0 ? edu.endYear : 'End Year')}
            </p>
            {edu.description && (
              <SafeHtml
                html={edu.description}
                className="mt-1 text-sm text-black opacity-70 [&_ol]:ml-6 [&_ol]:list-decimal [&_ul]:ml-6 [&_ul]:list-disc"
              />
            )}
          </div>
        ))}
      </>
    ),

    // Achievements
    data.achievements?.length > 0 && (
      <>
        <div key="achievements-heading" className="mb-2">
          <h2 className="mb-2 text-lg font-bold" style={{ color: selectedColor }}>
            Achievements
          </h2>
        </div>
        {data.achievements.map((achievement, index) => (
          <div key={`achievement-${index}`} className="mb-4">
            <h3 className="mb-[2px] text-sm font-bold tracking-tight text-black">
              {achievement.title}
            </h3>
            <p className="text-sm text-black">{achievement.awardedBy}</p>
            <p className="text-sm text-black opacity-70">{formatDate(achievement.date)}</p>
          </div>
        ))}
      </>
    ),
  ].filter(Boolean) // Remove falsy values (e.g., undefined from conditional sections)
  return (
    <div className="font-dm-sans flex gap-0">
      <div
        className="text-white"
        style={{
          backgroundColor: selectedColor || '#145349',
        }}
      >
        {sidebarSections}
      </div>
      <div className="flex-1 p-5 text-black">{mainSections}</div>
    </div>
  )
}

export default ProTemplateOne
