import { Resume } from '../../../models/Resume'
import { FC } from 'react'
import FreeTemplate from './FreeTemplate'
import ProTemplateOne from './ProTemplateOne'
import ProTemplateTwo from './ProTemplateTwo'
import ProTemplateThree from './ProTemplateThree'

import FreeTemplatePDF from './ResumePdf/FreeTempPdf'
import ProTemplatePDF from './ResumePdf/ProTempPdfOne'
import ProTempPdfTwo from './ResumePdf/ProTempPdfTwo'
import ProTempPdfThree from './ResumePdf/ProTempPdfThree'

export interface Template {
  id: number
  thumbnail: string
  component: React.FC<{ data: Resume; selectedColor: string; secondaryColor?: string }>
  pdfComponent: FC<{ data: Resume; selectedColor?: string; secondaryColor?: string }>
  isPro: boolean
  colorOptions?: string[]
  secondaryColorOptions?: string[]
}

export const templates: Template[] = [
  {
    id: 1,
    thumbnail: '/dashboard/resumeTemplate1.svg',
    component: FreeTemplate,
    pdfComponent: FreeTemplatePDF,
    isPro: false,
  },
  {
    id: 2,
    thumbnail: '/dashboard/resumeTemplate2.svg',
    component: ProTemplateOne,
    pdfComponent: ProTemplatePDF,
    isPro: true,
    colorOptions: ['#145349', '#003B7D', '#7D2100', '#73007D', '#1E1E1E'],
  },
  {
    id: 3,
    thumbnail: 'dashboard/resumeTemplate3.svg',
    component: ProTemplateTwo,
    pdfComponent: ProTempPdfTwo,
    isPro: true,
    colorOptions: ['#00C0BA', '#FF7BC3', '#34C2FF', '#FFB834'],
  },

  {
    id: 4,
    thumbnail: 'dashboard/resumeTemplate4.svg',
    component: ProTemplateThree,
    pdfComponent: ProTempPdfThree,
    isPro: true,
    colorOptions: ['#003B7D', '#145349', '#000000', '#FFB834'],
    secondaryColorOptions: ['#34C2FF29', '#B9FFF3', '#DBDBDBB2', '#FFEDCD'],
  },
]
