import { FC } from 'react'
import { Resume } from '../../../models/Resume'
import { templates } from './templates'
import ResumePreview from './ResumePreview'

interface TemplateViewerProps {
  resumeData: Resume
  selectedTemplate: number
}

const TemplateViewer: FC<TemplateViewerProps> = ({ resumeData, selectedTemplate }) => {
  const selectedTemplateData = templates.find((t) => t.id === selectedTemplate)

  return (
    <div className="z-10 w-full overflow-auto">
      {selectedTemplateData && (
        <ResumePreview
          resumeData={resumeData}
          templateComponent={selectedTemplateData.component}
          templateId={selectedTemplate}
        />
      )}
    </div>
  )
}

export default TemplateViewer
