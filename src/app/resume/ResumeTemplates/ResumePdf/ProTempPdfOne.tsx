import React from 'react'
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Image,
  Link,
  Font,
  Svg,
  Path,
} from '@react-pdf/renderer'
import { Resume } from '../../../../models/Resume'
import { formatDate } from '../../../../utils/resumeUtils'
import { createParserStyles, parseDescription } from '../../../../utils/pdfUtils'

Font.register({
  family: 'DM Sans',
  fonts: [
    { src: '/fonts/DM_Sans/static/DMSans-Regular.ttf', fontWeight: 400 },
    { src: '/fonts/DM_Sans/static/DMSans-Bold.ttf', fontWeight: 700 },
    {
      src: '/fonts/DM_Sans/static/DMSans-Italic.ttf',
      fontWeight: 400,
      fontStyle: 'italic',
    },
  ],
})

// Styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'row',
    fontFamily: 'D<PERSON> Sans',
  },
  sidebar: {
    width: '2.7in',
    padding: 16,
    color: '#FFFFFF',
  },
  mainContent: {
    flex: 1,
    padding: 16,
    paddingTop: 10,
    marginRight: 14,
    backgroundColor: '#FFFFFF',
  },
  profileImage: {
    width: 56,
    height: 56,
    borderRadius: 28,
    marginBottom: 12,
    marginLeft: 8,
  },
  name: {
    fontSize: 15,
    fontWeight: 700,
    lineHeight: 1,
    marginBottom: 8,
  },
  jobTitle: {
    fontSize: 12,
    fontWeight: 500,
    lineHeight: 1,
    opacity: 0.7,
    marginBottom: 14,
    letterSpacing: 0.24,
  },
  sectionHeadingSidebar: {
    fontSize: 13,
    fontWeight: 'bold',
    lineHeight: 1,
    paddingBottom: 12,
    letterSpacing: 0.26,
    marginTop: 24,
    textTransform: 'capitalize',
  },
  divider: {
    borderBottomWidth: 1,
    borderBottomColor: '#FFFFFF',
    opacity: 0.2,
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 11,
    fontWeight: 500,
    lineHeight: 1.2,
    marginBottom: 2,
    letterSpacing: 0.22,
  },
  detailValue: {
    fontSize: 11,
    opacity: 0.7,
    lineHeight: 1.2,
    marginBottom: 12,
    letterSpacing: 0.12,
  },
  skill: {
    fontSize: 11,
    lineHeight: 1,
    marginBottom: 6,
    letterSpacing: 0.24,
  },
  language: {
    fontSize: 11,
    fontWeight: 500,
    lineHeight: 1,
    letterSpacing: 0.26,
    paddingBottom: 2.5,
  },
  link: {
    fontSize: 12,
    marginBottom: 4,
    textDecoration: 'none',
    color: '#FFFFFF',
  },
  linkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  reference: {
    fontSize: 12,
    marginBottom: 4,
    fontWeight: 'bold',
    lineHeight: 1,
    letterSpacing: 0.26,
  },
  referenceEmail: {
    fontSize: 12,
    marginBottom: 8,
    lineHeight: 1,
    letterSpacing: 0.26,
  },
  sectionHeadingMain: {
    fontSize: 13,
    fontWeight: 700,
    marginBottom: 6,
  },
  summaryText: {
    fontSize: 11,
    color: '#000000',
    opacity: 0.7,
    lineHeight: 1.2,
    marginBottom: 24,
  },
  jobTitleMain: {
    fontSize: 12,
    fontWeight: 600,
    lineHeight: 1.2,
    letterSpacing: 0.2,
  },
  jobTitleNormal: {
    fontSize: 10,
    fontWeight: 400,
    lineHeight: 1.2,
    letterSpacing: 0.1,
    marginLeft: 1,
  },
  dateLocation: {
    fontSize: 10,
    marginTop: 3,
    color: '#000000',
    opacity: 0.7,
  },
  jobDescriptionContainer: {
    marginTop: '0.5px',
    paddingLeft: 6,
  },
  bullet: {
    fontSize: 12,
    color: 'black',
    marginRight: 6,
  },
  description: {
    fontSize: 10,
    fontWeight: 'normal',
    letterSpacing: 0,
    opacity: 0.7,
  },
  achievementTitle: {
    fontSize: 12,
    fontWeight: 600,
    marginBottom: 2,
  },
  organization: {
    fontSize: 10,
    color: '#000000',
  },
  flexRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  jobContainer: {
    marginBottom: 16,
  },
})

// Create custom parser styles to match the design
const parserStyles = createParserStyles(undefined, {
  paragraph: {
    marginBottom: 3,
    fontSize: 10,
    lineHeight: 1.2,
    opacity: 0.7,
  },
  bold: {
    fontWeight: 700,
    fontSize: 10,
    opacity: 0.7,
  },
  italic: {
    fontStyle: 'italic',
    fontSize: 10,
    opacity: 0.7,
  },
  underline: {
    textDecoration: 'underline',
    fontSize: 10,
    opacity: 0.7,
  },
  normal: {
    fontSize: 10,
    opacity: 0.7,
  },
  listItem: {
    flexDirection: 'row',
    marginBottom: 2,
    alignItems: 'flex-start',
    width: '100%',
  },
  bullet: {
    width: 8,
    fontSize: 10,
    paddingRight: 3,
    opacity: 0.7,
  },
  listText: {
    flex: 1,
    fontSize: 10,
    lineHeight: 1.2,
    opacity: 0.7,
  },
})

const ArrowUpRightIcon = () => (
  <Svg width="10" height="10" viewBox="0 0 256 256">
    <Path
      fill="white"
      d="M216 48v96a8 8 0 0 1-16 0V67.31L69.66 197.66a8 8 0 0 1-11.32-11.32L188.69 56H112a8 8 0 0 1 0-16h96a8 8 0 1 1 8 8Z"
    />
  </Svg>
)

export interface PremiumTemplatePDFProps {
  data: Resume
  selectedColor?: string
}

const PremiumTemplatePDF: React.FC<PremiumTemplatePDFProps> = ({ data, selectedColor }) => {
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Left Sidebar */}
        <View style={[styles.sidebar, { backgroundColor: selectedColor || '#145349' }]}>
          {/* Profile Section */}
          <View wrap={false}>
            {data.personalDetails?.profilePicture && (
              <Image style={styles.profileImage} src={data.personalDetails?.profilePicture} />
            )}
            <Text style={styles.name}>{data.personalDetails?.name}</Text>
            <Text style={styles.jobTitle}>{data.personalDetails?.profile || ''}</Text>
          </View>

          {/* Details Section */}
          <View wrap={false}>
            <Text style={styles.sectionHeadingSidebar}>details</Text>
            <View style={styles.divider} />
            <Text style={styles.detailLabel}>Address</Text>
            <Text style={styles.detailValue}>{data.personalDetails?.location}</Text>
            <Text style={styles.detailLabel}>Phone</Text>
            <Text style={styles.detailValue}>{data.personalDetails?.contactNumber}</Text>
            <Text style={styles.detailLabel}>Email</Text>
            <Text style={styles.detailValue}>{data.personalDetails?.email}</Text>
          </View>

          {/* Skills Section */}
          {data.skills?.length > 0 && (
            <View wrap={false}>
              <Text style={styles.sectionHeadingSidebar}>Skills</Text>
              <View style={styles.divider} />
              {data.skills?.map((skill, index) => (
                <Text key={index} style={styles.skill}>
                  {skill.name}
                </Text>
              ))}
            </View>
          )}
          {/* Languages Section */}
          {data.languages?.length > 0 && (
            <View wrap={false}>
              <Text style={styles.sectionHeadingSidebar}>Languages</Text>
              <View style={styles.divider} />
              {data.languages?.map((lang, index) => (
                <Text key={index} style={styles.language}>
                  {lang.name}
                </Text>
              ))}
            </View>
          )}
          {/* Links Section */}
          {data.socialLinks?.length > 0 && (
            <View wrap={false}>
              <Text style={styles.sectionHeadingSidebar}>Links</Text>
              <View style={styles.divider} />
              {data.socialLinks?.map((link, index) => (
                <View key={index} style={styles.linkContainer}>
                  <Link src={link.url} style={styles.link}>
                    {link.title}
                  </Link>
                  <ArrowUpRightIcon />
                </View>
              ))}
            </View>
          )}
          {/* References Section */}
          {data.references?.length > 0 && (
            <View wrap={false}>
              <Text style={styles.sectionHeadingSidebar}>Reference</Text>
              <View style={styles.divider} />
              {data.references?.map((ref, index) => (
                <View key={index}>
                  <Text style={styles.reference}>{ref.name}</Text>
                  <Text style={styles.referenceEmail}>{ref.email}</Text>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Right Main Content */}
        <View style={styles.mainContent}>
          <View fixed style={{ height: 16 }} />
          {/* Professional Summary */}
          {data.aboutMe && data.aboutMe.trim() && (
            <View wrap={false}>
              <Text style={[styles.sectionHeadingMain, { color: selectedColor }]}>
                Professional Summary
              </Text>
              <Text style={styles.summaryText}>{data.aboutMe}</Text>
            </View>
          )}

          {/* Work History */}
          {data.experiences?.length > 0 && (
            <View wrap={false}>
              <Text style={[styles.sectionHeadingMain, { color: selectedColor }]}>
                Work History
              </Text>
              {data.experiences.map((job, index) => (
                <View wrap={false} key={index} style={styles.jobContainer}>
                  <Text style={styles.jobTitleMain}>
                    {job.company.name}{' '}
                    <Text style={styles.jobTitleNormal}>{job.designation.name}</Text>
                  </Text>
                  <Text style={styles.dateLocation}>
                    {`${formatDate(job.startDate)} – ${
                      job.endDate ? formatDate(job.endDate) : 'Present'
                    } • ${job.location?.address?.city || ''}`}
                  </Text>
                  {job.description && (
                    <View style={styles.jobDescriptionContainer}>
                      {parseDescription(job.description, {
                        customStyles: parserStyles,
                      })}
                    </View>
                  )}
                </View>
              ))}
            </View>
          )}
          {/* Education */}
          {data.educations?.length > 0 && (
            <View wrap={false}>
              <Text style={[styles.sectionHeadingMain, { color: selectedColor }]}>Education</Text>
              {data.educations.map((edu, index) => (
                <View wrap={false} key={index} style={styles.jobContainer}>
                  <Text style={styles.jobTitleMain}>
                    {edu.institution.name}
                    <Text style={styles.jobTitleNormal}> {edu.degree.name}</Text>
                  </Text>
                  <Text style={styles.dateLocation}>
                    {`${edu.startYear} – ${
                      edu.isCurrent ? 'Present' : edu.endYear
                    } • ${edu.location?.address?.city || ''}`}
                  </Text>
                  {edu.description && (
                    <View style={styles.jobDescriptionContainer}>
                      {parseDescription(edu.description, {
                        customStyles: parserStyles,
                      })}
                    </View>
                  )}
                </View>
              ))}
            </View>
          )}

          {/* Achievements */}
          {data.achievements?.length > 0 && (
            <View wrap={false}>
              <Text style={[styles.sectionHeadingMain, { color: selectedColor }]}>
                Achievements
              </Text>
              {data.achievements.map((achievement, index) => (
                <View wrap={false} key={index} style={styles.jobContainer}>
                  <Text style={styles.achievementTitle}>{achievement.title}</Text>
                  <Text style={styles.organization}>{achievement.awardedBy}</Text>
                  <Text style={styles.dateLocation}>{formatDate(achievement.date)}</Text>
                </View>
              ))}
            </View>
          )}
        </View>
      </Page>
    </Document>
  )
}

export default PremiumTemplatePDF
