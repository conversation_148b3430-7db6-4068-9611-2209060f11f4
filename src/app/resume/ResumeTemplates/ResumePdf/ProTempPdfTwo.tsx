import React from 'react'
import { Document, Page, Text, View, StyleSheet, Link, Font, Svg, Path } from '@react-pdf/renderer'
import { Resume } from '../../../../models/Resume'
import { formatDate } from '../../../../utils/resumeUtils'
import { createParserStyles, parseDescription } from '../../../../utils/pdfUtils'

Font.register({
  family: 'Khand',
  fonts: [
    { src: '/fonts/khand/khand-regular.ttf', fontWeight: 400 },
    { src: '/fonts/khand/khand-bold.ttf', fontWeight: 700 },
  ],
})

Font.register({
  family: 'DM Sans',
  fonts: [{ src: '/fonts/DM_Sans/static/DMSans-Regular.ttf', fontWeight: 400 }],
})

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    fontFamily: 'DM Sans',
  },
  header: { paddingHorizontal: 20, paddingTop: 12 },
  headerBorder: {
    width: '98%',
    height: 1,
    backgroundColor: '#E5E7EB',
    marginTop: 20,
  },
  name: {
    fontFamily: 'Khand',
    fontSize: 32,
    fontWeight: 'bold',
    lineHeight: 1,
    letterSpacing: 0.64,
    marginBottom: 8,
  },
  subName: {
    fontFamily: 'DM Sans',
    fontSize: 12,
    fontWeight: 500,
    lineHeight: 1,
    letterSpacing: 0.24,
    textTransform: 'capitalize',
    color: '#1E1E1EB2',
    marginTop: 4,
  },
  sectionHeadingSidebar: {
    fontSize: 13,
    fontWeight: 'bold',
    lineHeight: 1,
    paddingBottom: 10,
    letterSpacing: 0.26,
    marginTop: 20,
    textTransform: 'uppercase',
    fontFamily: 'Khand',
  },
  sectionHeadingDetails: {
    fontSize: 13,
    fontWeight: 'bold',
    lineHeight: 1,
    paddingBottom: 10,
    letterSpacing: 0.26,
    marginTop: 12,
    textTransform: 'uppercase',
    fontFamily: 'Khand',
  },
  detailLabel: {
    fontSize: 11,
    fontWeight: '500',
    lineHeight: 1.2,
    marginBottom: 2,
    letterSpacing: 0.22,
  },
  detailValue: {
    fontSize: 11,
    opacity: 0.7,
    lineHeight: 1.2,
    marginBottom: 12,
    letterSpacing: 0.12,
  },
  skill: {
    fontSize: 11,
    textTransform: 'capitalize',
    lineHeight: 1,
    marginBottom: 6,
    letterSpacing: 0.24,
  },
  language: {
    fontSize: 11,
    fontWeight: '500',
    textTransform: 'capitalize',
    lineHeight: 1,
    letterSpacing: 0.26,
    paddingBottom: 2.5,
  },

  link: {
    fontSize: 12,
    marginBottom: 4,
    textDecoration: 'none',
    color: '#1E1E1E',
  },
  linkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    color: '#1E1E1E',
    gap: 4,
  },
  reference: {
    fontSize: 12,
    marginBottom: 4,
    lineHeight: 1,
    fontWeight: 'bold',
    letterSpacing: 0.26,
  },
  referenceEmail: {
    fontSize: 12,
    marginBottom: 8,
    lineHeight: 1,
    letterSpacing: 0.26,
  },
  columns: { flexDirection: 'row' },
  sidebar: { width: '2.4in', padding: 16, color: '#1E1E1E' },

  sectionHeadingMain: {
    fontFamily: 'Khand',
    fontSize: 13,
    fontWeight: 700,
    lineHeight: '100%',
    letterSpacing: 0.02 * 13,
    textTransform: 'uppercase',
    marginBottom: 12,
  },
  sectionHeadingSummary: {
    fontFamily: 'Khand',
    fontSize: 13,
    fontWeight: 700,
    lineHeight: '100%',
    letterSpacing: 0.02 * 13,
    textTransform: 'uppercase',
    marginVertical: 12,
    marginTop: 0,
  },
  mainContent: {
    flex: 1,
    padding: 12,
    marginRight: 14,
    backgroundColor: '#FFFFFF',
  },
  summaryText: {
    fontSize: 10,
    color: '#000000',
    opacity: 0.7,
    lineHeight: 1.2,
    marginBottom: 16,
  },
  jobTitleMain: {
    fontSize: 12,
    fontWeight: 600,
    lineHeight: 1.2,
    letterSpacing: 0.2,
  },
  jobTitleNormal: {
    fontSize: 10,
    fontWeight: 400,
    lineHeight: 1.2,
    letterSpacing: 0.1,
    marginLeft: 1,
  },
  dateLocation: { fontSize: 10, marginTop: 3, color: '#000000', opacity: 0.7 },
  jobDescriptionContainer: { marginTop: '0.5px', paddingLeft: 6 },
  bullet: { fontSize: 12, color: 'black', marginRight: 6 },
  description: {
    fontSize: 10,
    fontWeight: 'normal',
    letterSpacing: 0,
    opacity: 0.7,
  },
  achievementTitle: { fontSize: 12, fontWeight: 600, marginBottom: 2 },
  organization: {
    fontSize: 10,
    color: '#000000',
    letterSpacing: 1,
    lineHeight: 1,
  },
  jobContainer: { marginBottom: 14 },
  achievementContainer: { flexDirection: 'row' },
})

// custom parser styles for descriptions
const descriptionParserStyles = createParserStyles(
  {},
  {
    paragraph: {
      marginBottom: 3,
      fontSize: 10,
      lineHeight: 1.2,
      textAlign: 'left',
      fontFamily: 'DM Sans',
    },
    bold: {
      fontWeight: 600,
      fontSize: 10,
      fontFamily: 'DM Sans',
    },
    italic: {
      fontStyle: 'italic',
      fontSize: 10,
      fontFamily: 'DM Sans',
    },
    underline: {
      textDecoration: 'underline',
      fontSize: 10,
      fontFamily: 'DM Sans',
    },
    normal: {
      fontSize: 10,
      fontFamily: 'DM Sans',
      opacity: 0.7,
    },
    listItem: {
      flexDirection: 'row',
      marginBottom: 2,
      alignItems: 'flex-start',
      width: '100%',
      paddingLeft: 4,
    },
    bullet: {
      width: 8,
      fontSize: 10,
      paddingRight: 4,
      fontFamily: 'DM Sans',
    },
    listText: {
      flex: 1,
      fontSize: 10,
      lineHeight: 1.2,
      fontFamily: 'DM Sans',
      opacity: 0.7,
    },
  },
)

const ArrowUpRightIcon = () => (
  <Svg width="10" height="10" viewBox="0 0 256 256">
    <Path
      fill="black"
      d="M196 28v96a8 8 0 0 1-16 0V47.31L49.66 177.66a8 8 0 0 1-11.32-11.32L168.69 36H92a8 8 0 0 1 0-16h96a8 8 0 1 1 8 8Z"
    />
  </Svg>
)

interface ProTemplateTwoPDFProps {
  data: Resume
  selectedColor?: string
}

const ProTempPdfTwo: React.FC<ProTemplateTwoPDFProps> = ({ data, selectedColor }) => {
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.name, { color: selectedColor }]}>{data.personalDetails?.name}</Text>
          <Text style={styles.subName}>{data.personalDetails.profile || ''}</Text>
          <View style={styles.headerBorder} />
        </View>

        {/* Columns */}
        <View style={styles.columns}>
          <View style={styles.sidebar}>
            {/* Details Section */}
            <View wrap={false}>
              <Text style={[styles.sectionHeadingDetails, { color: selectedColor }]}>Details</Text>
              <Text style={styles.detailLabel}>Address</Text>
              <Text style={styles.detailValue}>{data.personalDetails?.location}</Text>
              <Text style={styles.detailLabel}>Phone</Text>
              <Text style={styles.detailValue}>{data.personalDetails?.contactNumber}</Text>
              <Text style={styles.detailLabel}>Email</Text>
              <Text style={styles.detailValue}>{data.personalDetails?.email}</Text>
            </View>

            {/* Skills Section */}
            {data.skills?.length > 0 && (
              <View wrap={false}>
                <Text style={[styles.sectionHeadingSidebar, { color: selectedColor }]}>Skills</Text>
                {data.skills?.map((skill, index) => (
                  <Text key={index} style={styles.skill}>
                    {skill.name}
                  </Text>
                ))}
              </View>
            )}
            {/* Languages Section */}
            {data.languages?.length > 0 && (
              <View wrap={false}>
                <Text style={[styles.sectionHeadingSidebar, { color: selectedColor }]}>
                  Languages
                </Text>
                {data.languages?.map((lang, index) => (
                  <Text key={index} style={styles.language}>
                    {lang.name}
                  </Text>
                ))}
              </View>
            )}
            {/* Links Section */}
            {data.socialLinks?.length > 0 && (
              <View wrap={false}>
                <Text style={[styles.sectionHeadingSidebar, { color: selectedColor }]}>Links</Text>
                {data.socialLinks?.map((link, index) => (
                  <View key={index} style={styles.linkContainer}>
                    <Link src={link.url} style={styles.link}>
                      {link.title}
                    </Link>
                    <ArrowUpRightIcon />
                  </View>
                ))}
              </View>
            )}
            {/* References Section */}
            {data.references?.length > 0 && (
              <View wrap={false}>
                <Text style={[styles.sectionHeadingSidebar, { color: selectedColor }]}>
                  Reference
                </Text>
                {data.references?.map((ref, index) => (
                  <View key={index}>
                    <Text style={styles.reference}>{ref.name}</Text>
                    <Text style={styles.referenceEmail}>{ref.email}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>

          {/* Right Main Content */}
          <View style={styles.mainContent}>
            <View fixed style={{ height: 16 }} />

            {/* Professional Summary */}
            {data.aboutMe && data.aboutMe.trim() && (
              <View wrap={false}>
                <Text style={[styles.sectionHeadingSummary, { color: selectedColor }]}>
                  Professional Summary
                </Text>
                <Text style={styles.summaryText}>{data.aboutMe}</Text>
              </View>
            )}

            {/* Work History */}
            {data.experiences?.length > 0 && (
              <View wrap={false}>
                <Text style={[styles.sectionHeadingMain, { color: selectedColor }]}>
                  Work History
                </Text>
                {data.experiences.map((job, index) => (
                  <View wrap={false} key={index} style={styles.jobContainer}>
                    <Text style={styles.jobTitleMain}>
                      {job.company.name}{' '}
                      <Text style={styles.jobTitleNormal}>{job.designation.name}</Text>
                    </Text>
                    <Text style={styles.dateLocation}>
                      {`${formatDate(job.startDate)} – ${
                        job.endDate ? formatDate(job.endDate) : 'Present'
                      } • ${job.location?.address?.city || ''}`}
                    </Text>
                    {job.description && (
                      <View style={styles.jobDescriptionContainer}>
                        {parseDescription(job.description, {
                          customStyles: descriptionParserStyles,
                        })}
                      </View>
                    )}
                  </View>
                ))}
              </View>
            )}

            {/* Education */}
            {data.educations?.length > 0 && (
              <View wrap={false}>
                <Text style={[styles.sectionHeadingMain, { color: selectedColor }]}>Education</Text>
                {data.educations.map((edu, index) => (
                  <View wrap={false} key={index} style={styles.jobContainer}>
                    <Text style={styles.jobTitleMain}>
                      {edu.institution.name}
                      <Text style={styles.jobTitleNormal}> {edu.degree.name}</Text>
                    </Text>
                    <Text style={styles.dateLocation}>
                      {`${edu.startYear} – ${
                        edu.endYear ? edu.endYear : 'Present'
                      } • ${edu.location?.address?.city || ''}`}
                    </Text>
                    {edu.description && (
                      <View style={styles.jobDescriptionContainer}>
                        {parseDescription(edu.description, {
                          customStyles: descriptionParserStyles,
                        })}
                      </View>
                    )}
                  </View>
                ))}
              </View>
            )}

            {/* Achievements */}
            {data.achievements?.length > 0 && (
              <View wrap={false}>
                <Text style={[styles.sectionHeadingMain, { color: selectedColor }]}>
                  Achievements
                </Text>
                {data.achievements.map((achievement, index) => (
                  <View wrap={false} key={index} style={styles.jobContainer}>
                    <Text style={styles.achievementTitle}>{achievement.title}</Text>
                    <Text style={styles.organization}>{achievement.awardedBy}</Text>
                    <Text style={styles.dateLocation}>{formatDate(achievement.date)}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        </View>
      </Page>
    </Document>
  )
}

export default ProTempPdfTwo
