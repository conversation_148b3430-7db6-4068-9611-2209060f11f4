import { FC } from 'react'
import { Document, Page, View, Text, StyleSheet, Font } from '@react-pdf/renderer'
import { Resume } from '../../../../models/Resume'
import { formatDate } from '../../../../utils/resumeUtils'
import { parseDescription } from '../../../../utils/pdfUtils'

Font.register({
  family: 'PT Serif',
  fonts: [
    { src: '/fonts/PT_Serif/PTSerif-Regular.ttf', fontWeight: 400 },
    { src: '/fonts/PT_Serif/PTSerif-Italic.ttf', fontStyle: 'italic' },
    { src: '/fonts/PT_Serif/PTSerif-Bold.ttf', fontWeight: 700 },
  ],
})

interface FreeTemplateProps {
  data: Resume
}

const styles = StyleSheet.create({
  page: {
    padding: 24,
    fontFamily: 'PT Serif',
    backgroundColor: '#FFFFFF',
  },
  name: {
    fontSize: 24,
    textAlign: 'center',
    marginBottom: 8,
    textTransform: 'capitalize',
  },
  contact: {
    fontSize: 12,
    marginBottom: 16,
    textAlign: 'center',
    lineHeight: 1,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'uppercase',
    borderBottomWidth: 0.5,
    borderBottomColor: 'black',
    paddingBottom: 2,
    marginBottom: 2,
    lineHeight: 1.2,
  },
  summaryText: {
    fontSize: 12,
    lineHeight: 1.2,
    textAlign: 'justify',
  },
  company: {
    fontSize: 12,
    fontWeight: 'bold',
    paddingBottom: 4,
  },
  date: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  jobTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    paddingBottom: 4,
    lineHeight: 1.22,
    letterSpacing: 0.36,
  },
  jobCity: {
    fontSize: 12,
    paddingBottom: 4,
  },
  description: {
    fontSize: 12,
    marginBottom: 4,
    lineHeight: 1.2,
    textAlign: 'justify',
  },
  achievementTitle: {
    fontSize: 12,
    fontWeight: 'semibold',
    textTransform: 'uppercase',
  },
  achievementDate: {
    fontSize: 12,
  },
  achievementOrganization: {
    fontSize: 12,
    fontWeight: 'semibold',
    paddingBottom: 4,
    lineHeight: 1.22,
    letterSpacing: 0.36,
  },
  skillsLanguages: {
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'capitalize',
    paddingTop: 1,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 18,
    right: 15,
    paddingVertical: 6,
    paddingHorizontal: 8,
    borderRadius: 4,
    borderStyle: 'solid',
    borderWidth: 1,
    marginTop: 4,
    borderColor: '#ccc',
    display: 'flex',
    alignItems: 'center',
    fontSize: 10,
  },
  lucresText: {
    fontWeight: 'bold',
    color: '#145349',
  },
})

const FreeTemplatePDF: FC<FreeTemplateProps> = ({ data }) => {
  type PersonalDetailsKeys =
    | 'profile'
    | 'email'
    | 'contactNumber'
    | 'location'
    | 'nationality'
    | 'dateOfBirth'
  const fieldNames: PersonalDetailsKeys[] = [
    'profile',
    'email',
    'contactNumber',
    'location',
    'nationality',
    'dateOfBirth',
  ]
  const contactInfo = fieldNames
    .map((field) => data.personalDetails[field]?.trim())
    .filter(Boolean)
    .join(' | ')
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <Text wrap={false} style={styles.name}>
          {data.personalDetails.name}
        </Text>

        {/* Contact Information */}
        {contactInfo && (
          <View wrap={false} style={styles.contact}>
            <Text>{contactInfo}</Text>
          </View>
        )}

        {/* Professional Summary */}
        {data.aboutMe && data.aboutMe.trim() && (
          <View wrap={false} style={{ marginBottom: 14 }}>
            <Text wrap={false} style={styles.sectionTitle}>
              PROFESSIONAL SUMMARY
            </Text>
            <Text wrap={false} style={styles.summaryText}>
              {data.aboutMe}
            </Text>
          </View>
        )}
        {/* Experience */}
        {data.experiences.map((job, index) => (
          <View wrap={false} key={index} style={{ marginBottom: 12 }}>
            {index === 0 && <Text style={styles.sectionTitle}>EXPERIENCE</Text>}
            <View
              wrap={false}
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}
            >
              <Text wrap={false} style={styles.company}>
                {job.company.name}
              </Text>
              <Text wrap={false} style={styles.jobCity}>
                {job.location.address.city}
              </Text>
            </View>
            <View
              wrap={false}
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}
            >
              <Text wrap={false} style={styles.jobTitle}>
                {job.designation.name}
              </Text>
              <Text wrap={false} style={styles.date}>
                {`${formatDate(job.startDate)} – ${
                  job.endDate ? formatDate(job.endDate) : 'Present'
                }`}
              </Text>
            </View>
            {job.description && parseDescription(job.description)}
          </View>
        ))}
        {/* Education */}
        {data.educations.length > 0 && (
          <View>
            <Text style={styles.sectionTitle}>EDUCATION</Text>
            {data.educations.map((edu, index) => (
              <View wrap={false} key={index} style={{ marginBottom: 12 }}>
                <View
                  wrap={false}
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}
                >
                  <Text wrap={false} style={styles.company}>
                    {edu.institution.name}
                  </Text>
                  <Text wrap={false} style={styles.jobCity}>
                    {edu.location.address.city}
                  </Text>
                </View>
                <View
                  wrap={false}
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}
                >
                  <Text wrap={false} style={styles.jobTitle}>
                    {edu.degree.name}
                  </Text>
                  <Text wrap={false} style={styles.date}>
                    {`${edu.startYear} – ${edu.endYear ? edu.endYear : 'Present'}`}
                  </Text>
                </View>
                {edu.description && parseDescription(edu.description)}
              </View>
            ))}
          </View>
        )}
        {/* Achievements */}
        {data.achievements.length > 0 && (
          <View wrap={false} style={{ marginBottom: 8 }}>
            <Text wrap={false} style={styles.sectionTitle}>
              ACHIEVEMENTS
            </Text>
            {data.achievements.map((achievement, index) => (
              <View wrap={false} key={index} style={{ marginBottom: 4 }}>
                <View
                  wrap={false}
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}
                >
                  <Text wrap={false} style={styles.achievementTitle}>
                    {achievement.title}
                  </Text>
                  <Text wrap={false} style={styles.achievementDate}>
                    {formatDate(achievement.date)}
                  </Text>
                </View>
                <Text wrap={false} style={styles.achievementOrganization}>
                  {achievement.awardedBy}
                </Text>
              </View>
            ))}
          </View>
        )}
        {/* Skills */}
        {data.skills.length > 0 && (
          <View wrap={false} style={{ marginBottom: 14 }}>
            <Text wrap={false} style={styles.sectionTitle}>
              SKILLS
            </Text>
            <Text wrap={false} style={styles.skillsLanguages}>
              {data.skills.map((skill) => skill.name).join(' | ')}
            </Text>
          </View>
        )}
        {/* Languages */}
        {data.languages.length > 0 && (
          <View wrap={false}>
            <Text wrap={false} style={styles.sectionTitle}>
              LANGUAGES
            </Text>
            <Text wrap={false} style={styles.skillsLanguages}>
              {data.languages.map((lang) => lang.name).join(' | ')}
            </Text>
          </View>
        )}
        {/* Footer Button */}
        <View fixed wrap={false} style={styles.buttonContainer}>
          <Text wrap={false}>
            Made with{' '}
            <Text wrap={false} style={styles.lucresText}>
              Lucres
            </Text>
          </Text>
        </View>
      </Page>
    </Document>
  )
}

export default FreeTemplatePDF
