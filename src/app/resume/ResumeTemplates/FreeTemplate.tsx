import { FC, JSX } from 'react'
import { Resume } from '../../../models/Resume'
import { formatDate } from '../../../utils/resumeUtils'
import SafeHtml from '../../../components/SafeHtml'
import React from 'react'

interface FreeTemplateProps {
  data: Resume
}

const FreeTemplate: FC<FreeTemplateProps> = ({ data }) => {
  const sections: JSX.Element[] = []

  // Define the detail fields with keys and values
  const detailFields = [
    { key: 'profile', value: data.personalDetails.profile?.trim() },
    { key: 'email', value: data.personalDetails.email?.trim() },
    { key: 'contactNumber', value: data.personalDetails.contactNumber?.trim() },
    { key: 'location', value: data.personalDetails.location?.trim() },
    { key: 'nationality', value: data.personalDetails.nationality?.trim() },
    { key: 'dateOfBirth', value: data.personalDetails.dateOfBirth?.trim() },
  ]

  // Filter out undefined, null, or empty string values
  const filteredDetails = detailFields.filter(
    (item) => item.value !== undefined && item.value !== '',
  )

  // Add the header to sections
  sections.push(
    <div
      key="header"
      className="font-pt-serif leading-[1.2] tracking-[0em] text-black"
      style={{ breakInside: 'avoid' }}
    >
      <h1 className="mb-2 text-center text-4xl capitalize">{data.personalDetails.name}</h1>
      <div className="mb-4 text-center leading-none">
        {filteredDetails.map((item, index) => (
          <React.Fragment key={item.key}>
            {index > 0 && '|'}
            <span className="p-1">{item.value}</span>
          </React.Fragment>
        ))}
      </div>
    </div>,
  )

  // Professional Summary
  if (data.aboutMe && data.aboutMe.trim()) {
    sections.push(
      <div
        key="summary"
        className="font-pt-serif mb-4 leading-[1.2] tracking-[0em] text-black"
        style={{ breakInside: 'avoid' }}
      >
        <h2 className="border-b-[0.5px] border-black font-bold uppercase">PROFESSIONAL SUMMARY</h2>
        <p className="pt-[2px] text-justify leading-[1.2]">{data.aboutMe}</p>
      </div>,
    )
  }
  // Experience Section Title
  if (data.experiences.length > 0) {
    sections.push(
      <div
        key="experience-title"
        className="font-pt-serif leading-[1.2] tracking-[0em] text-black"
        style={{ breakInside: 'avoid' }}
      >
        <h2 className="border-b-[0.5px] border-black font-bold uppercase">EXPERIENCE</h2>
      </div>,
    )

    // Individual Experience Entries (updated to use 'experiences' and nested fields)
    data.experiences.forEach((job, index) => {
      sections.push(
        <div
          key={`job-${index}`}
          className="font-pt-serif mb-4 leading-[1.2] tracking-[0em] text-black"
          style={{ breakInside: 'avoid' }}
        >
          <div className="flex items-start justify-between">
            <h3 className="pb-1 font-bold">{job.company.name}</h3>
            <h3 className="pb-1">{job.location.address.city}</h3>
          </div>
          <div className="flex items-start justify-between">
            <h3 className="pb-1 font-bold leading-[1.22]">{job.designation.name}</h3>
            <span className="pb-1 italic">
              {`${job.startDate ? formatDate(job.startDate) : 'Start Date'} – ${job.endDate ? formatDate(job.endDate) : 'End Date'}`}
            </span>
          </div>

          {job.description && (
            <SafeHtml
              html={job.description}
              className="text-justify text-base font-normal leading-[1.2] [&_ol]:ml-6 [&_ol]:list-decimal [&_ul]:ml-6 [&_ul]:list-disc"
            />
          )}
        </div>,
      )
    })
  }
  // Education Section Title
  if (data.educations.length > 0) {
    sections.push(
      <div
        key="education-title"
        className="font-pt-serif leading-[1.2] tracking-[0em] text-black"
        style={{ breakInside: 'avoid' }}
      >
        <h2 className="border-b-[0.5px] border-black text-lg font-bold uppercase sm:text-base">
          EDUCATION
        </h2>
      </div>,
    )

    // Individual Education Entries (updated to use nested fields)
    data.educations.forEach((edu, index) => {
      sections.push(
        <div
          key={`edu-${index}`}
          className="font-pt-serif mb-4 leading-[1.2] tracking-[0em] text-black"
          style={{ breakInside: 'avoid' }}
        >
          <div className="flex items-start justify-between">
            <h3 className="pb-1 text-lg font-bold sm:text-base">{edu.institution.name}</h3>
            <h3 className="pb-1 text-lg sm:text-base">{edu.location.address.city}</h3>
          </div>
          <div className="flex items-start justify-between">
            <h3 className="pb-1 text-lg font-bold leading-[1.22] sm:text-base">
              {edu.degree.name}
            </h3>
            <span className="pb-1 text-lg italic sm:text-base">
              {(edu.startYear && edu.startYear !== 0 ? edu.startYear : 'Start Year') +
                ' – ' +
                (edu.endYear && edu.endYear !== 0 ? edu.endYear : 'End Year')}
            </span>
          </div>
          {edu.description && (
            <SafeHtml
              html={edu.description}
              className="text-justify text-base font-normal leading-tight sm:leading-[1.2] [&_ol]:ml-6 [&_ol]:list-decimal [&_ul]:ml-6 [&_ul]:list-disc"
            />
          )}
        </div>,
      )
    })
  }
  // Achievements Section Title
  if (data.achievements.length > 0) {
    sections.push(
      <div
        key="achievements-title"
        className="font-pt-serif leading-[1.2] tracking-[0em] text-black"
        style={{ breakInside: 'avoid' }}
      >
        <h2 className="border-b-[0.5px] border-black text-lg font-bold uppercase sm:text-base">
          ACHIEVEMENTS
        </h2>
      </div>,
    )

    data.achievements.forEach((achievement, index) => {
      sections.push(
        <div
          key={`achievement-${index}`}
          className="font-pt-serif leading-[1.2] tracking-[0em] text-black"
          style={{ breakInside: 'avoid' }}
        >
          <div className="flex flex-col">
            <div className="flex justify-between">
              <h3 className="text-lg font-semibold uppercase sm:text-base">{achievement.title}</h3>
              <div className="text-lg sm:text-base">{formatDate(achievement.date)}</div>
            </div>
            <div className="mb-1 flex items-center justify-between">
              <div className="pb-1 text-lg font-bold leading-[1.22] sm:text-base">
                {achievement.awardedBy}
              </div>
            </div>
          </div>
        </div>,
      )
    })
  }
  // Skills Section (updated to use 'skills' array of objects)
  if (data.skills.length > 0) {
    sections.push(
      <div
        key="skills"
        className="font-pt-serif mb-3 leading-[1.2] tracking-[0em] text-black"
        style={{ breakInside: 'avoid' }}
      >
        <h2 className="border-b-[0.5px] border-black text-lg font-bold uppercase sm:text-base">
          SKILLS
        </h2>
        <p className="pt-[2px] text-lg font-bold capitalize sm:text-base">
          {data.skills.map((skill) => skill.name).join(' | ')}
        </p>
      </div>,
    )
  }
  // Languages Section (updated to use 'languages' array of objects)
  if (data.languages.length > 0) {
    sections.push(
      <div
        key="languages"
        className="font-pt-serif leading-[1.2] tracking-[0em] text-black"
        style={{ breakInside: 'avoid' }}
      >
        <h2 className="border-b-[0.5px] border-black text-lg font-bold uppercase sm:text-base">
          LANGUAGES
        </h2>
        <p className="pt-[2px] font-bold capitalize sm:text-base">
          {data.languages.map((lang) => lang.name).join(' | ')}
        </p>
      </div>,
    )
  }
  return sections
}

export default FreeTemplate
