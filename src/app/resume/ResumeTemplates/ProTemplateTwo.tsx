import { FC } from 'react'
import { ArrowUpRightIcon } from '@phosphor-icons/react'
import { formatDate } from '../../../utils/resumeUtils'
import SafeHtml from '../../../components/SafeHtml'
import { PremiumTemplateProps } from './ProTemplateOne'

const ProTemplateTwo: FC<PremiumTemplateProps> = ({ data, selectedColor }) => {
  // Header section
  const header = (
    <div className="px-5 pt-4">
      <h1
        style={{ color: selectedColor }}
        className="font-khand mb-2 text-5xl font-bold leading-none tracking-tight"
      >
        {data.personalDetails?.name}
      </h1>
      <p className="mt-1 text-lg font-medium capitalize leading-none tracking-tight text-[#1E1E1EB2]">
        {data.personalDetails.profile || ''}
      </p>
      <div className="mt-5 h-[2px] w-[98%] bg-gray-200" />
    </div>
  )

  // Sidebar sections
  const sidebarSections = [
    // Details Section
    <div key="details">
      <h3
        style={{ color: selectedColor }}
        className="font-khand mt-10 pb-3 text-lg font-bold uppercase leading-[1.2] tracking-[0.03em]"
      >
        Details
      </h3>
      <p className="mb-0.5 font-bold leading-tight tracking-[0.02em] text-[#1E1E1E]">Address</p>
      <p className="mb-4 leading-tight tracking-[0.011em] text-[#1E1E1E] opacity-70">
        {data.personalDetails?.location}
      </p>
      <p className="mb-0.5 font-bold leading-tight tracking-[0.02em] text-[#1E1E1E]">Phone</p>
      <p className="mb-4 leading-tight tracking-[0.011em] text-[#1E1E1E] opacity-70">
        {data.personalDetails?.contactNumber}
      </p>
      <p className="mb-0.5 font-bold leading-tight tracking-[0.02em] text-[#1E1E1E]">Email</p>
      <p className="mb-4 hyphens-none break-words break-all leading-tight tracking-[0.011em] opacity-70">
        <a
          href={`mailto:${data.personalDetails?.email}`}
          className="text-inherit no-underline"
          style={{ hyphens: 'none' }}
        >
          {data.personalDetails?.email}
        </a>
      </p>
    </div>,
    // Skills Section
    data.skills?.length > 0 && (
      <div key="skills">
        <h3
          style={{ color: selectedColor }}
          className="font-khand mt-12 pb-3 text-lg font-bold uppercase leading-none tracking-[0.03em]"
        >
          Skills
        </h3>
        {data.skills.map((skill, index) => (
          <p
            key={index}
            className="mb-1 text-[15px] font-medium capitalize leading-none tracking-[0.02em] text-[#1E1E1E]"
          >
            {skill.name}
          </p>
        ))}
      </div>
    ),
    // Languages Section
    data.languages?.length > 0 && (
      <div key="languages">
        <h3
          style={{ color: selectedColor }}
          className="font-khand mt-12 pb-3 text-lg font-bold uppercase leading-none tracking-[0.03em]"
        >
          Languages
        </h3>
        {data.languages.map((lang, index) => (
          <p
            key={index}
            className="pb-[3px] text-[15px] font-medium capitalize leading-none tracking-[0.024em] text-[#1E1E1E]"
          >
            {lang.name}
          </p>
        ))}
      </div>
    ),
    // Links Section
    data.socialLinks?.length > 0 && (
      <div key="links">
        <h3
          style={{ color: selectedColor }}
          className="font-khand mt-10 pb-3 text-lg font-bold uppercase leading-none tracking-[0.03em]"
        >
          Links
        </h3>
        {data.socialLinks.map((link, index) => (
          <div key={index} className="mb-[5px] flex items-center gap-1">
            <a
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-base capitalize  tracking-[0.022em] text-[#1E1E1E] no-underline"
            >
              {link.title}
            </a>
            <ArrowUpRightIcon size={14} />
          </div>
        ))}
      </div>
    ),

    // Reference Section
    data.references?.length > 0 && (
      <div key="references">
        <h3
          style={{ color: selectedColor }}
          className="font-khand mt-10 pb-3 text-lg font-bold uppercase leading-none tracking-[0.03em]"
        >
          Reference
        </h3>
        {data.references.map((ref, index) => (
          <div key={index} className="mb-[5px]">
            <p className="pb-1 text-base font-bold leading-none tracking-[0.022em] text-[#1E1E1E]">
              {ref.name}
            </p>
            <p className="mb-2 hyphens-none break-words break-all text-base font-medium leading-none tracking-[0.022em] text-[#1E1E1E]">
              {ref.email}
            </p>
          </div>
        ))}
      </div>
    ),
  ].filter(Boolean)
  // Main content sections
  const mainSections = [
    // Professional Summary
    data.aboutMe && data.aboutMe.trim() && (
      <div key="summary" className="mb-8 mt-8">
        <h2
          style={{ color: selectedColor }}
          className="font-khand mb-2 text-lg font-bold uppercase leading-none tracking-[0.03em]"
        >
          Professional Summary
        </h2>
        <p className="mb-6 text-[15px] leading-[1.2] text-black opacity-70">{data.aboutMe}</p>
      </div>
    ),

    // Work History
    data.experiences?.length > 0 && (
      <>
        <div key="work-heading" className="mb-2">
          <h2
            style={{ color: selectedColor }}
            className="font-khand mb-2 text-lg font-bold uppercase leading-none tracking-[0.03em]"
          >
            Work History
          </h2>
        </div>
        {data.experiences.map((job, index) => (
          <div key={`job-${index}`} className="mb-4">
            <h3 className="text-base font-semibold leading-tight tracking-[0.01em] text-black">
              {job.company.name}{' '}
              <span className="ml-1 text-sm font-normal leading-tight text-black">
                {job.designation.name}
              </span>
            </h3>
            <p className="mt-1 text-sm text-black opacity-70">
              {`${job.startDate ? formatDate(job.startDate) : 'Start Date'} – ${
                job.endDate ? formatDate(job.endDate) : 'End Date'
              }`}
            </p>
            {job.description && (
              <SafeHtml
                html={job.description}
                className="text-sm text-black opacity-70 [&_ol]:ml-6 [&_ol]:list-decimal [&_ul]:ml-4 [&_ul]:list-disc"
              />
            )}
          </div>
        ))}
      </>
    ),

    // Education
    data.educations?.length > 0 && (
      <>
        <div key="education-heading" className="mb-2 pt-2">
          <h2
            style={{ color: selectedColor }}
            className="font-khand mb-2 text-lg font-bold uppercase leading-none tracking-[0.03em]"
          >
            Education
          </h2>
        </div>
        {data.educations.map((edu, index) => (
          <div key={`edu-${index}`} className="mb-3">
            <h3 className="text-base font-semibold leading-tight tracking-[0.01em] text-black">
              {edu.institution.name}{' '}
              <span className="ml-1 text-sm font-normal leading-tight text-black">
                {edu.degree.name}
              </span>
            </h3>
            <p className="text bid-sm mt-1 text-black opacity-70">
              {(edu.startYear && edu.startYear !== 0 ? edu.startYear : 'Start Year') +
                ' – ' +
                (edu.endYear && edu.endYear !== 0 ? edu.endYear : 'End Year')}
            </p>
            {edu.description && (
              <SafeHtml
                html={edu.description}
                className="text-sm text-black opacity-70 [&_ol]:ml-6 [&_ol]:list-decimal [&_ul]:ml-4 [&_ul]:list-disc"
              />
            )}
          </div>
        ))}
      </>
    ),

    // Achievements
    data.achievements?.length > 0 && (
      <>
        <div key="achievements-heading" className="mb-2 pt-2">
          <h2
            style={{ color: selectedColor }}
            className="font-khand mb-2 text-lg font-bold uppercase leading-none tracking-[0.03em]"
          >
            Achievements
          </h2>
        </div>
        {data.achievements.map((achievement, index) => (
          <div key={`achievement-${index}`} className="mb-4">
            <h3 className="mb-[2px] text-base font-bold tracking-[0.01em] text-black">
              {achievement.title}
            </h3>
            <p className="text-sm tracking-[0%] text-black">{achievement.awardedBy}</p>
            <p className="text-sm text-black opacity-70">{formatDate(achievement.date)}</p>
          </div>
        ))}
      </>
    ),
  ].filter(Boolean)
  return (
    <div>
      {header}
      <div className="font-dm-sans flex gap-0">
        <div
          className="sidebar-container text-[#1E1E1E]"
          style={{
            backgroundColor: selectedColor || '#145349',
          }}
        >
          {sidebarSections}
        </div>
        <div className="flex-1 p-5 text-black">{mainSections}</div>
      </div>
    </div>
  )
}

export default ProTemplateTwo
