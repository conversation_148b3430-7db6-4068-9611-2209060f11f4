import React from 'react'
import { pdf, PDFDownloadLink } from '@react-pdf/renderer'
import Button from '../../components/Button'
import { templates } from './ResumeTemplates/templates'
import { Resume } from '../../models/Resume'
import { useColorContext } from '../../context/ColorContext'
import { UserService } from '../../services/UserService'
import { initiateRazorpayPayment } from '../../utils/razorpay'
import { useToast } from '../../components/ToastX'
import { CareerService } from '../../services/CareerService'
import { useAuth } from '../../context/AuthContext'

interface DownloadResumeProps {
  resumeData: Resume
  selectedColor?: string | null
  templateId?: number
  setDownloadResume: React.Dispatch<React.SetStateAction<boolean>>
}

const DownLoadResume: React.FC<DownloadResumeProps> = ({
  resumeData,
  selectedColor,
  setDownloadResume,
  templateId,
}) => {
  const { selectedColor: contextColor, secondaryColor: contextSecondaryColor } = useColorContext()
  const { authUserProfile } = useAuth()
  const colorToUse = selectedColor || contextColor || '#145349'
  const secondaryColorToUse = contextSecondaryColor || 'white'
  const toast = useToast()
  // Find the selected template object based on templateId
  const selectedTemplateObj = templates.find((t) => t.id === templateId)

  // Handle case where templateId is invalid
  if (!selectedTemplateObj) {
    console.error(`Template with id ${templateId} not found.`)
    return <div>Error: Invalid template selected.</div> // Or use a default template
  }

  // Get the pdfComponent for the selected template
  const PdfComponent = selectedTemplateObj.pdfComponent
  const isPremium = selectedTemplateObj.isPro // Check if the template is premium
  const filename = `${resumeData.personalDetails.name.replace(/\s+/g, '')}Resume.pdf`

  const handleDownloadResume = async () => {
    try {
      if (!authUserProfile) return

      let mappedData = null

      if (isPremium) {
        const res = await CareerService.downLoadProResume()
        const apiResponse = res.data

        mappedData = {
          personalDetails: {
            name: `${apiResponse.user?.givenName ?? ''} ${
              apiResponse.user?.familyName ?? ''
            }`.trim(),
            profilePicture: apiResponse.user?.profileImage?.url ?? '',
            contactNumber: apiResponse.contact?.contactNumber ?? '',
            email: apiResponse.contact?.primaryEmail ?? '',
            location: `${apiResponse.user?.location?.address?.city ?? ''} ${
              apiResponse.user?.location?.address?.country ?? ''
            }`.trim(),
            nationality: apiResponse.contact?.nationality ?? '',
            profile: apiResponse.user?.permalink ?? '',
            dateOfBirth: apiResponse.contact?.dateOfBirth ?? '',
          },
          aboutMe: apiResponse.careerData?.aboutMe ?? '',
          experiences: apiResponse.careerData?.experiences ?? [],
          educations: apiResponse.careerData?.educations ?? [],
          achievements: apiResponse.careerData?.achievements ?? [],
          skills: apiResponse.careerData?.skills ?? [],
          languages: apiResponse.careerData?.languages ?? [],
          socialLinks: apiResponse.careerData?.socialLinks ?? [],
          references: apiResponse.careerData?.references ?? [],
          isSaved: apiResponse.careerData?.isSaved ?? false,
        }
      }

      setDownloadResume(false)

      const data = resumeData

      const blob = await pdf(
        isPremium && selectedTemplateObj.secondaryColorOptions ? (
          <PdfComponent
            data={data}
            selectedColor={colorToUse}
            secondaryColor={secondaryColorToUse}
          />
        ) : isPremium ? (
          <PdfComponent data={data} selectedColor={colorToUse} />
        ) : (
          <PdfComponent data={data} />
        ),
      ).toBlob()

      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (error: any) {
      // Handle error appropriately
      console.error(error)
      // toast.error(error?.data?.message ?? "Something went wrong!");
    }
  }

  return (
    <div className="mb-4 flex h-full w-full flex-col items-center justify-center xl:mb-0">
      <div className="flex w-full flex-col items-center justify-center xl:px-16">
        <h3 className="mt-4 text-center text-2xl font-bold">
          Final Check before Downloading your Job{' '}
          <span className="text-lucres-600 dark:text-dark-lucres-green-500">Winning Resume</span>
        </h3>
        <span className="text-lucres-900 dark:text-dark-lucres-green-100 mt-4 text-center text-sm text-opacity-60">
          Double-check for any missing information and fill in the necessary fields to achieve a
          polished and professional finish before downloading.
        </span>
      </div>
      <div className="mt-10 flex items-center gap-x-4">
        {/* <PDFDownloadLink
          document={
            isPremium && selectedTemplateObj.secondaryColorOptions ? (
              <PdfComponent 
                data={resumeData}
                selectedColor={colorToUse}
                secondaryColor={secondaryColorToUse}
              />
            ) : isPremium ? (
              <PdfComponent data={resumeData} selectedColor={colorToUse} />
            ) : (
              <PdfComponent data={resumeData}/>
            )
          }
          fileName={filename}
        >
          {({ loading }) => (
          )}
        </PDFDownloadLink> */}

        <Button size="small" theme="opaque" className="!rounded-lg" onClick={handleDownloadResume}>
          Download Resume
        </Button>
        <Button
          size="small"
          theme="transparent"
          className="rounded-lg! px-10!"
          onClick={() => setDownloadResume(false)}
        >
          Edit Resume
        </Button>
      </div>
    </div>
  )
}

export default DownLoadResume
