import { Trash, CaretDown, CaretUp, NotePencil, Plus, NotePencilIcon } from '@phosphor-icons/react'
import { useRouter } from 'next/navigation'

interface SectionProps {
  title: string
  isVisible: boolean
  onToggle: () => void
  children: React.ReactNode
  tag?: string
  canRemove?: boolean // Indicates if the section can be removed
  isRemoved?: boolean // Indicates if the section is currently removed
  setIsRemoved?: (value: boolean) => void // Callback to update removal state
  onRemoveSection?: () => void // Callback to handle removal logic
}

const Section: React.FC<SectionProps> = ({
  title,
  isVisible,
  onToggle,
  children,
  tag,
  canRemove = false,
  isRemoved = false,
  setIsRemoved,
  onRemoveSection,
}) => {
  const router = useRouter()

  const handleHeaderClick = () => {
    if (!isRemoved) {
      onToggle()
    }
  }

  return (
    <div className="section-container">
      {/* Entire header is clickable */}
      <div
        className={`section-header outline-hidden flex w-full cursor-pointer items-center justify-between ${
          title === 'Personal Details' ? 'mt-6 pt-8 xl:pt-0' : 'mt-6'
        }`}
        onClick={handleHeaderClick}
        role="button"
        tabIndex={0}
        aria-expanded={!isRemoved && isVisible}
        aria-controls={`section-${title}`}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault()
            handleHeaderClick()
          }
        }}
      >
        {/* Title and tag */}
        <div className="outline-hidden flex items-center gap-x-3">
          <h2 className="text-2xl font-bold">{title}</h2>
          {tag && (
            <span className="rounded-lg bg-lime-300 bg-opacity-30 px-2 py-1 text-sm font-medium">
              {tag}
            </span>
          )}
        </div>

        {/* Icons section */}
        <div className="outline-hidden flex items-center gap-x-3">
          {title === 'Personal Details' && (
            <button
              onClick={(e) => {
                e.stopPropagation()
                router.push('/personal-information?fromResume=true&editMode=true')
              }}
              aria-label="Edit personal details"
            >
              <NotePencilIcon size={26} className="hover:text-lucres-300 cursor-pointer" />
            </button>
          )}
          {canRemove && setIsRemoved ? (
            isRemoved ? (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  setIsRemoved(false)
                  if (!isVisible) {
                    onToggle()
                  }
                }}
                aria-label={`Add ${title.toLowerCase()}`}
              >
                <Plus size={20} className="cursor-pointer" />
              </button>
            ) : (
              <>
                {isVisible && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      if (onRemoveSection) onRemoveSection()
                      setIsRemoved(true)
                    }}
                    aria-label={`Remove ${title.toLowerCase()}`}
                  >
                    <Trash size={20} className="cursor-pointer" />
                  </button>
                )}
                <span>{isVisible ? <CaretUp size={20} /> : <CaretDown size={20} />}</span>
              </>
            )
          ) : (
            <span>{isVisible ? <CaretUp size={20} /> : <CaretDown size={20} />}</span>
          )}
        </div>
      </div>

      {/* Section content */}
      {!isRemoved && (
        <div
          id={`section-${title}`}
          className={`section-content ${isVisible ? 'visible' : 'hidden'}`}
        >
          {children}
        </div>
      )}
    </div>
  )
}

export default Section
