'use client'
import { useState, useEffect, useCallback } from 'react'
import DescriptionList from './DescriptionList'
import { Education } from '../../../models/Resume'
import Input from '../../../components/Input'
import { AISuggestionService } from '../../../services/AISuggestionService'
import { useToast } from '../../../components/ToastX'
import { debounce } from '../../../utils/resumeUtils'
import { CaretDownIcon, CaretUpIcon, TrashIcon } from '@phosphor-icons/react'

interface EducationCardProps {
  education: Education
  index: number
  isVisible: boolean
  onToggleVisibility: () => void
  onRemove: () => void
  onUpdate: (index: number, field: string, value: string | number) => void
  suggestions: string
  errors: Record<string, string>
  userId: string
}

const EducationCard: React.FC<EducationCardProps> = ({
  education,
  index,
  isVisible,
  onToggleVisibility,
  onRemove,
  onUpdate,
  errors,
  userId,
}) => {
  const [isGenerating, setIsGenerating] = useState(false)
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([])
  const [selectedSuggestions, setSelectedSuggestions] = useState<string[]>([])
  const toast = useToast()

  const escapeHtmlAttr = (str: string) => {
    return str.replace(/"/g, '"')
  }

  const generateAISuggestions = useCallback(
    debounce(async (degree: string, institution: string) => {
      if (!degree || !institution) return

      setIsGenerating(true)
      const context = `${degree} from ${institution}`

      try {
        const response = await AISuggestionService.getSuggestion(
          'user',
          userId,
          'education',
          context,
        )

        if (response.status === 'success') {
          setAiSuggestions(response.data)
        } else {
          console.error('Failed to generate AI suggestions.')
        }
      } catch (error: any) {
        console.error(error.message || 'An error occurred while generating suggestions.')
      } finally {
        setIsGenerating(false)
      }
    }, 500),
    [userId, toast],
  )

  useEffect(() => {
    if (education.degree.name && education.institution.name) {
      generateAISuggestions(education.degree.name, education.institution.name)
    }
  }, [education.degree.name, education.institution.name, generateAISuggestions])

  const handleToggleAISuggestion = (suggestion: string) => {
    const isSelected = selectedSuggestions.includes(suggestion)

    if (isSelected) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(`<body>${education.description || ''}</body>`, 'text/html')
      const escapedSuggestion = escapeHtmlAttr(suggestion)
      const elements = doc.querySelectorAll(`div[data-suggestion="${escapedSuggestion}"]`)
      elements.forEach((el) => el.remove())
      const updatedDescription = doc.body.innerHTML
      onUpdate(index, 'description', updatedDescription)
      setSelectedSuggestions((prev) => prev.filter((s) => s !== suggestion))
    } else {
      // Check for case-insensitive duplicates among already selected suggestions
      const isDuplicate = selectedSuggestions.some(
        (selected) => selected.toLowerCase().trim() === suggestion.toLowerCase().trim(),
      )
      if (isDuplicate) {
        toast.warning(`"${suggestion}" is already added.`)
        return
      }

      const escapedSuggestion = escapeHtmlAttr(suggestion)
      const newSuggestionHtml = `<div data-suggestion="${escapedSuggestion}"><p>${suggestion}</p></div>`
      const newDescription = education.description
        ? `${education.description}${newSuggestionHtml}`
        : newSuggestionHtml
      onUpdate(index, 'description', newDescription)
      setSelectedSuggestions((prev) => [...prev, suggestion])
    }
  }

  return (
    <div className="dark:border-dark-lucres-black-200 mt-2 flex flex-col rounded-lg border border-gray-100 p-4">
      <div className="card-header flex w-full items-center justify-between">
        <h3 className="text-lg font-bold">{education.institution.name || 'School/College'}</h3>
        <div className="card-controls flex items-center gap-x-2">
          <TrashIcon size={20} onClick={onRemove} className="cursor-pointer" />
          {isVisible ? (
            <CaretUpIcon size={20} onClick={onToggleVisibility} className="cursor-pointer" />
          ) : (
            <CaretDownIcon size={20} onClick={onToggleVisibility} className="cursor-pointer" />
          )}
        </div>
      </div>

      {isVisible && (
        <div className="card-content">
          <div className="mt-2 grid grid-cols-1 items-start gap-y-2 md:grid-cols-2 md:gap-x-4">
            <Input
              type="text"
              id={`school-college-${index}`}
              name="school_college"
              value={education.institution.name}
              placeholder="Enter your School/College"
              onChange={(e) => onUpdate(index, 'institution.name', e.target.value)}
              label="School/College"
              labelClassName="font-medium!"
              className="w-full py-[10px] pl-3 pr-10"
              error={errors['institution.name']}
            />
            <Input
              type="text"
              id={`degree-${index}`}
              name="degree"
              value={education.degree.name}
              placeholder="Enter your Degree"
              onChange={(e) => onUpdate(index, 'degree.name', e.target.value)}
              label="Degree"
              labelClassName="font-medium!"
              className="w-full py-[10px] pl-3 pr-10"
              error={errors['degree.name']}
            />
          </div>

          {/* Dates & City */}
          <div className="grid grid-cols-1 items-start gap-y-2 md:grid-cols-3 md:gap-x-4">
            <Input
              type="text"
              id={`startYear-${index}`}
              name="startYear"
              placeholder="Enter Start Year"
              value={
                education.startYear && education.startYear !== 0
                  ? education.startYear.toString()
                  : ''
              }
              onChange={(e) => {
                const value = e.target.value
                const numericValue = /^\d{0,4}$/.test(value) ? parseInt(value, 10) || 0 : 0
                onUpdate(index, 'startYear', numericValue)
              }}
              label="Start Year"
              labelClassName="font-medium!"
              className="w-full py-[10px] pl-3 pr-10"
              error={errors['startYear']}
            />
            <Input
              type="text"
              id={`endYear-${index}`}
              name="endYear"
              placeholder="Enter End Year"
              value={
                education.endYear && education.endYear !== 0 ? education.endYear.toString() : ''
              }
              onChange={(e) => {
                const value = e.target.value
                const numericValue = /^\d{0,4}$/.test(value) ? parseInt(value, 10) || 0 : 0
                onUpdate(index, 'endYear', numericValue)
              }}
              label="End Year"
              labelClassName="font-medium!"
              className="w-full py-[10px] pl-3 pr-10"
              error={errors['endYear']}
            />
            <Input
              type="text"
              id={`city-${index}`}
              name="city"
              placeholder="Enter your City"
              value={education.location.address.city}
              onChange={(e) => onUpdate(index, 'location.address.city', e.target.value)}
              label="City"
              labelClassName="font-medium!"
              className="w-full py-[10px] pl-3 pr-10"
              error={errors['location.address.city']}
            />
          </div>

          {/* Description */}
          <div>
            <DescriptionList
              description={education.description || ''}
              onUpdateDescription={(value) => onUpdate(index, 'description', value)}
              aiSuggestions={aiSuggestions}
              selectedSuggestions={selectedSuggestions}
              onSelectAISuggestion={handleToggleAISuggestion}
              placeholder="Write your description here..."
              onRegenerateSuggestions={() =>
                generateAISuggestions(education.degree.name, education.institution.name)
              }
              isGenerating={isGenerating}
              error={errors['description']}
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default EducationCard
