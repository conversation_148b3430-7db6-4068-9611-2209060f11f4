'use client'
import React, { useState } from 'react'
import Input from '../../../components/Input'
import { Trash } from '@phosphor-icons/react'
import { Achievement } from '../../../models/Resume'
import MonthPicker from '../../../components/MonthPicker'
import { format, parse, isValid } from 'date-fns'

interface AchievementCardProps {
  achievement: Achievement
  index: number
  onUpdate: (field: keyof Achievement, value: string) => void
  onRemove: () => void
  errors: Record<string, string>
}

const AchievementCard: React.FC<AchievementCardProps> = ({
  achievement,
  index,
  onUpdate,
  onRemove,
  errors,
}) => {
  const [showMonthPicker, setShowMonthPicker] = useState(false)

  const formatDisplayDate = (dateString: string): string => {
    if (!dateString) return 'Select Date'
    const date = parse(dateString, 'MM/yyyy', new Date())
    if (!isValid(date)) return 'Invalid Date'
    return format(date, 'MMM yyyy')
  }

  return (
    <div className="achievement-card dark:border-dark-lucres-black-200 mt-4 flex flex-col rounded-lg border border-gray-100 p-3">
      <div className="card-header mb-2 flex w-full items-center justify-between">
        <h3 className="text-base font-semibold">{achievement.title || 'Achievement'}</h3>
        <div className="card-controls flex items-center gap-x-2">
          <Trash size={18} className="cursor-pointer" onClick={onRemove} />
        </div>
      </div>

      <div className="card-content">
        <div className="flex flex-wrap gap-2 md:flex-nowrap">
          <Input
            type="text"
            id={`AchievementName-${index}`}
            name="AwardsTitle"
            label="Awards Title"
            labelClassName="font-medium!"
            className="w-full py-2 pl-3 pr-10"
            value={achievement.title}
            onChange={(e) => onUpdate('title', e.target.value)}
            placeholder="IBM Award"
            error={errors['title']}
          />

          <div className="relative w-full">
            <label className="text-lucres-900 dark:text-dark-lucres-green-300 mb-2 block pl-1 text-sm font-medium text-opacity-75">
              Date
            </label>
            <button
              type="button"
              onClick={() => setShowMonthPicker(!showMonthPicker)}
              className="dark:border-dark-lucres-black-200 w-full rounded-md border border-gray-200 py-[12px] pl-3 pr-10 text-left"
            >
              {formatDisplayDate(achievement.date)}
            </button>
            {showMonthPicker && (
              <div className="absolute z-10 mt-1">
                <MonthPicker
                  currentMonth={
                    achievement.date && isValid(parse(achievement.date, 'MM/yyyy', new Date()))
                      ? parse(achievement.date, 'MM/yyyy', new Date())
                      : new Date()
                  }
                  onMonthChange={(newMonth) => {
                    const formattedDate = format(newMonth, 'MM/yyyy')
                    onUpdate('date', formattedDate)
                    setShowMonthPicker(false)
                  }}
                  isOpen={showMonthPicker}
                  onClose={() => setShowMonthPicker(false)}
                />
              </div>
            )}
            {errors['date'] && (
              <p className="mt-1 text-sm text-red-500 dark:text-red-400">{errors['date']}</p>
            )}
          </div>
        </div>

        <Input
          type="text"
          id={`AwardedBy-${index}`}
          name="AwardedBy"
          label="Awarded By"
          labelClassName="font-medium! mt-3 xl:mt-0"
          className="md:w-1/2! w-full py-2 pl-3 pr-10"
          value={achievement.awardedBy || ''}
          onChange={(e) => onUpdate('awardedBy', e.target.value)}
          placeholder="IBM, CEO"
          error={errors['awardedBy']}
        />
      </div>
    </div>
  )
}

export default AchievementCard
