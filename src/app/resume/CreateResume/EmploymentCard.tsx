'use client'
import { useState, useEffect, useCallback } from 'react'
import { format, isValid, parse } from 'date-fns'
import {
  Trash,
  CaretDown,
  Sparkle,
  TrashIcon,
  CaretUpIcon,
  CaretDownIcon,
} from '@phosphor-icons/react'
import Input from '../../../components/Input'
import Select from '../../../components/Select'
import DescriptionList from './DescriptionList'
import { Employment } from '../../../models/Resume'
import ToggleButton from '../../../components/ToggleButton'
import MonthPicker from '../../../components/MonthPicker'
import { AISuggestionService } from '../../../services/AISuggestionService'
import { useToast } from '../../../components/ToastX'
import { debounce } from '../../../utils/resumeUtils'

interface EmploymentCardProps {
  job: Employment
  index: number
  isVisible: boolean
  onToggleVisibility: () => void
  onRemove: () => void
  onUpdate: (index: number, field: string, value: string | boolean) => void
  onToggleCurrent: () => void
  suggestions: string
  errors: Record<string, string>
  userId: string
}

const EmploymentCard: React.FC<EmploymentCardProps> = ({
  job,
  index,
  isVisible,
  onToggleVisibility,
  onRemove,
  onUpdate,
  onToggleCurrent,
  errors,
  userId,
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [startDatePickerOpen, setStartDatePickerOpen] = useState(false)
  const [endDatePickerOpen, setEndDatePickerOpen] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([])
  const [selectedSuggestions, setSelectedSuggestions] = useState<string[]>([])
  const toast = useToast()

  const jobTypes = ['FULL_TIME', 'PART_TIME', 'CONTRACT', 'REMOTE_WORK', 'TEMPORARY']

  const jobTypeDisplayMap: Record<string, string> = {
    FULL_TIME: 'Full-Time',
    PART_TIME: 'Part-Time',
    CONTRACT: 'Contract',
    REMOTE_WORK: 'Remote Work',
    TEMPORARY: 'Temporary',
  }

  const escapeHtmlAttr = (str: string) => {
    return str.replace(/"/g, '"')
  }

  const handleSelect = (jobType: string) => {
    onUpdate(index, 'employmentType', jobType)
    setIsOpen(false)
  }

  const handleToggleCurrent = () => {
    const newIsCurrent = !job.isCurrent
    onToggleCurrent()
    onUpdate(index, 'isCurrent', newIsCurrent)
    onUpdate(index, 'endDate', newIsCurrent ? 'Present' : '')
  }

  const handleMonthSelect = (field: 'startDate' | 'endDate', selectedMonth: Date) => {
    const formattedDate = format(selectedMonth, 'MM/yyyy')
    onUpdate(index, field, formattedDate)
    if (field === 'startDate') {
      setStartDatePickerOpen(false)
    } else {
      setEndDatePickerOpen(false)
    }
  }

  const generateAISuggestions = useCallback(
    debounce(async (designation: string, company: string) => {
      if (!designation || !company) return

      setIsGenerating(true)
      const context = `${designation} at ${company}`

      try {
        const response = await AISuggestionService.getSuggestion(
          'user',
          userId,
          'experience',
          context,
        )

        if (response.status === 'success') {
          setAiSuggestions(response.data)
        } else {
          console.error('Failed to generate AI suggestions.')
        }
      } catch (error: any) {
        toast.error(error.message || 'An error occurred while generating suggestions.')
      } finally {
        setIsGenerating(false)
      }
    }, 500),
    [userId, toast],
  )

  useEffect(() => {
    if (job.designation.name && job.company.name) {
      generateAISuggestions(job.designation.name, job.company.name)
    }
  }, [job.designation.name, job.company.name, generateAISuggestions])

  const handleToggleAISuggestion = (suggestion: string) => {
    const isSelected = selectedSuggestions.includes(suggestion)

    if (isSelected) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(`<body>${job.description || ''}</body>`, 'text/html')
      const escapedSuggestion = escapeHtmlAttr(suggestion)
      const elements = doc.querySelectorAll(`div[data-suggestion="${escapedSuggestion}"]`)
      elements.forEach((el) => el.remove())
      const updatedDescription = doc.body.innerHTML
      onUpdate(index, 'description', updatedDescription)
      setSelectedSuggestions((prev) => prev.filter((s) => s !== suggestion))
    } else {
      const escapedSuggestion = escapeHtmlAttr(suggestion)
      const newSuggestionHtml = `<div data-suggestion="${escapedSuggestion}"><p>${suggestion}</p></div>`
      const newDescription = job.description
        ? `${job.description}${newSuggestionHtml}`
        : newSuggestionHtml
      onUpdate(index, 'description', newDescription)
      setSelectedSuggestions((prev) => [...prev, suggestion])
    }
  }

  return (
    <div className="dark:border-dark-lucres-black-200 mt-2 flex flex-col rounded-lg border border-gray-100 p-4">
      <div className="card-header flex w-full items-center justify-between">
        <h3 className="text-lg font-bold">{job.designation.name || 'Job Title'}</h3>
        <div className="card-controls flex items-center gap-x-2">
          <TrashIcon size={20} onClick={onRemove} className="cursor-pointer" />
          {isVisible ? (
            <CaretUpIcon size={20} onClick={onToggleVisibility} className="cursor-pointer" />
          ) : (
            <CaretDownIcon size={20} onClick={onToggleVisibility} className="cursor-pointer" />
          )}
        </div>
      </div>

      {isVisible && (
        <div className="card-content">
          <div className="mt-2 grid grid-cols-1 items-start gap-y-2 md:grid-cols-2 md:gap-x-4">
            <Input
              type="text"
              id={`designation-${index}`}
              name="designation"
              value={job.designation.name}
              placeholder="Enter your Job Title"
              onChange={(e) => onUpdate(index, 'designation.name', e.target.value)}
              label="Job Title"
              labelClassName="font-medium!"
              className="w-full py-[10px] pl-3 pr-10"
              error={errors['designation.name']}
            />
            <Input
              type="text"
              id={`company-${index}`}
              name="company"
              value={job.company.name}
              placeholder="Enter your Company"
              onChange={(e) => onUpdate(index, 'company.name', e.target.value)}
              label="Company"
              labelClassName="font-medium!"
              className="w-full py-[10px] pl-3 pr-10"
              error={errors['company.name']}
            />
          </div>

          <div className="grid grid-cols-1 items-start gap-y-2 md:grid-cols-2 md:gap-x-4">
            <div>
              <label className="text-lucres-900 dark:text-dark-lucres-green-300 block pb-1 text-sm font-medium text-opacity-75">
                Start Date
              </label>
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setStartDatePickerOpen(!startDatePickerOpen)}
                  className={`dark:border-dark-lucres-black-200 w-full rounded-md border border-gray-200 py-[10px] pl-3 pr-10 text-left text-sm ${
                    job.startDate ? 'text-lucres-900 dark:text-lucres-100' : 'text-lucres-300'
                  }`}
                >
                  {job.startDate || 'Select Start Date'}
                </button>
                {startDatePickerOpen && (
                  <div className="absolute z-10 mt-[2px]">
                    <MonthPicker
                      currentMonth={
                        job.startDate && isValid(parse(job.startDate, 'MM/yyyy', new Date()))
                          ? parse(job.startDate, 'MM/yyyy', new Date())
                          : new Date()
                      }
                      onMonthChange={(newMonth) => handleMonthSelect('startDate', newMonth)}
                      isOpen={startDatePickerOpen}
                      onClose={() => setStartDatePickerOpen(false)}
                    />
                  </div>
                )}
              </div>
              {errors['startDate'] && (
                <span className="text-xs text-red-500 dark:text-red-400">
                  {errors['startDate']}
                </span>
              )}
            </div>

            <div>
              <label className="text-lucres-900 dark:text-dark-lucres-green-300 block pb-1 text-sm font-medium text-opacity-75">
                End Date
              </label>
              <div className="relative">
                <button
                  type="button"
                  onClick={() => {
                    if (!job.isCurrent) {
                      setEndDatePickerOpen(!endDatePickerOpen)
                    }
                  }}
                  className={`dark:border-dark-lucres-black-200 w-full rounded-md border border-gray-200 py-[10px] pl-3 pr-10 text-left text-sm ${
                    job.isCurrent
                      ? 'text-lucres-900! dark:text-lucres-100! cursor-not-allowed'
                      : job.endDate
                        ? 'text-lucres-900 dark:text-lucres-100'
                        : 'text-lucres-300 dark:text-lucres-100'
                  }`}
                  disabled={job.isCurrent}
                >
                  {job.isCurrent ? 'Present' : job.endDate || 'Select End Date'}
                </button>
                {endDatePickerOpen && !job.isCurrent && (
                  <div className="absolute z-10 mt-[2px]">
                    <MonthPicker
                      currentMonth={
                        job.endDate && isValid(parse(job.endDate, 'MM/yyyy', new Date()))
                          ? parse(job.endDate, 'MM/yyyy', new Date())
                          : new Date()
                      }
                      onMonthChange={(newMonth) => handleMonthSelect('endDate', newMonth)}
                      isOpen={endDatePickerOpen}
                      onClose={() => setEndDatePickerOpen(false)}
                    />
                  </div>
                )}
              </div>
              <span className="my-1 flex items-center gap-2">
                <label className="text-dark-lucres-black-200 dark:text-dark-lucres-green-300 text-xs font-medium text-opacity-75">
                  Currently work here
                </label>
                <ToggleButton isRequired={job.isCurrent} onToggle={handleToggleCurrent} />
              </span>
              {!job.isCurrent && errors['endDate'] && (
                <span className="text-xs text-red-500 dark:text-red-400">{errors['endDate']}</span>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 items-start gap-y-2 md:grid-cols-2 md:gap-x-4">
            <Select
              label="Job Type"
              labelClassName="pb-1"
              parentClassName="justify-start! gap-0!"
              value={job.employmentType}
              onChange={handleSelect}
              options={jobTypes.map((jobType) => ({
                value: jobType,
                label: jobTypeDisplayMap[jobType],
              }))}
              placeholder="Select Type"
              error={errors['employmentType']}
            />

            <div>
              <label className="text-lucres-900 dark:text-dark-lucres-green-300 block pb-1 text-sm font-medium text-opacity-75">
                City
              </label>
              <Input
                type="text"
                id={`city-${index}`}
                name="city"
                placeholder="Enter your City"
                value={job.location.address.city}
                onChange={(e) => onUpdate(index, 'location.address.city', e.target.value)}
                label="City"
                labelClassName="sr-only"
                className="w-full py-[10px] pl-3 pr-10"
                error={errors['location.address.city']}
              />
            </div>
          </div>

          <div>
            <DescriptionList
              description={job.description || ''}
              onUpdateDescription={(value) => onUpdate(index, 'description', value)}
              aiSuggestions={aiSuggestions}
              selectedSuggestions={selectedSuggestions}
              onSelectAISuggestion={handleToggleAISuggestion}
              placeholder="Describe your role, achievements, and responsibilities..."
              onRegenerateSuggestions={() =>
                generateAISuggestions(job.designation.name, job.company.name)
              }
              isGenerating={isGenerating}
              error={errors['description']}
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default EmploymentCard
