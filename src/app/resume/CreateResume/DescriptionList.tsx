import { useEffect, useState } from 'react'
import Editor, {
  Toolbar,
  BtnBold,
  BtnItalic,
  BtnBulletList,
  BtnUnderline,
} from 'react-simple-wysiwyg'
import Tooltip from '../../../components/Tooltip'
import Modal from '../../../components/Modal'
import SafeHtml from '../../../components/SafeHtml'
import { sanitizeBasicHtml } from '../../../utils/resumeUtils'
import { ArrowsClockwiseIcon, CheckCircleIcon, CircleDashedIcon } from '@phosphor-icons/react'

interface DescriptionListProps {
  description: string
  onUpdateDescription: (value: string) => void
  aiSuggestions: string[]
  selectedSuggestions: string[]
  onSelectAISuggestion: (suggestion: string) => void
  placeholder: string
  onRegenerateSuggestions: () => void
  isGenerating: boolean
  error: string
}

const DescriptionList: React.FC<DescriptionListProps> = ({
  description,
  onUpdateDescription,
  aiSuggestions,
  selectedSuggestions,
  onSelectAISuggestion,
  placeholder,
  onRegenerateSuggestions,
  isGenerating,
  error,
}) => {
  const [isDescriptionModalOpen, setIsDescriptionModalOpen] = useState(false)
  const [isScreenSmall, setIsScreenSmall] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(max-width: 768px)')
    setIsScreenSmall(mediaQuery.matches)

    const handleResize = () => setIsScreenSmall(mediaQuery.matches)
    mediaQuery.addEventListener('change', handleResize)

    return () => {
      mediaQuery.removeEventListener('change', handleResize)
    }
  }, [])

  const handleDescriptionChange = (value: any) => {
    const newValue = typeof value === 'string' ? value : value.target.value
    const sanitizedValue = sanitizeBasicHtml(newValue)
    onUpdateDescription(sanitizedValue)
  }

  const characterCount = description.replace(/<[^>]*>/g, '').length

  return (
    <div>
      <span className="text-lucres-black dark:text-dark-lucres-green-300 whitespace-nowrap text-sm font-semibold text-opacity-75">
        Description
      </span>

      {/* Desktop View */}
      {!isScreenSmall && (
        <div className="border-lucres-300 placeholder-lucres-300 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 my-2 flex h-full items-start justify-start gap-x-3 rounded-lg border border-opacity-40 bg-opacity-60 p-2 dark:border">
          <div className="flex-1">
            <Editor
              value={sanitizeBasicHtml(description)}
              onChange={handleDescriptionChange}
              placeholder={placeholder}
              containerProps={{ style: { border: 'none' } }}
              className="scrollbar-none  dark:bg-dark-lucres-black-500 dark:text-dark-lucres-green-100 dark:placeholder-lucres-800 focus:outline-hidden w-full resize-none text-sm placeholder:text-sm [&_ol]:list-decimal [&_ol]:pl-5 [&_ul]:list-disc [&_ul]:pl-5"
            >
              <Toolbar className=" dark:border-b-dark-lucres-black-200 flex gap-2 !border-b bg-transparent pb-1">
                <BtnBold className="text-lucres-black hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-7 w-7 items-center justify-center rounded-lg" />
                <BtnItalic className="text-lucres-black hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-7 w-7 items-center justify-center rounded-lg" />
                <BtnBulletList className="text-lucres-black hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-7 w-7 items-center justify-center rounded-lg" />
                <BtnUnderline className="text-lucres-black hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-7 w-7 items-center justify-center rounded-lg" />
              </Toolbar>
            </Editor>
            <div className="text-lucres-600 dark:text-dark-lucres-green-400 text-sm">
              {characterCount}/750 characters
            </div>
            {error && <span className="text-xs text-red-500 dark:text-red-400">{error}</span>}
          </div>
        </div>
      )}

      {/* Mobile View */}
      {isScreenSmall && (
        <>
          <div
            className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 relative my-2 overflow-scroll rounded-lg border border-gray-200 p-4"
            onClick={(e) => {
              e.preventDefault()
              setIsDescriptionModalOpen(true)
            }}
          >
            {description ? (
              <SafeHtml
                html={description}
                className="scrollbar-none !overflow:scroll dark:bg-dark-lucres-black-500 dark:text-dark-lucres-green-100 focus:outline-hidden h-20 w-full resize-none whitespace-pre-wrap text-sm"
              />
            ) : (
              <div className="scrollbar-none text-lucres-300 dark:bg-dark-lucres-black-500 dark:text-lucres-800 focus:outline-hidden h-20 w-full resize-none whitespace-pre-wrap text-sm">
                {placeholder}
              </div>
            )}
          </div>
          <div className="my-1 flex flex-col gap-1 px-1">
            <div className="text-xs text-gray-500">{characterCount}/750 characters</div>
            {error && <span className="text-xs text-red-500 dark:text-red-400">{error}</span>}
          </div>
        </>
      )}

      {/* Modal for Mobile */}
      {isDescriptionModalOpen && (
        <Modal
          onClose={() => setIsDescriptionModalOpen(false)}
          crossRequired
          classes="relative w-full min-h-screen rounded-none"
          crossClass="mr-4!"
        >
          <div className="flex h-full w-full flex-col gap-4 p-2 pt-8">
            <label className="dark:text-dark-lucres-green-300 block font-medium">
              Description:
            </label>
            <Editor
              value={sanitizeBasicHtml(description)}
              onChange={handleDescriptionChange}
              placeholder={placeholder}
              containerProps={{
                style: {
                  border: 'none',
                  background: 'transparent',
                },
              }}
              className="scrollbar-none border-lucres-200 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 dark:text-dark-lucres-green-100 dark:placeholder-lucres-800 focus:outline-hidden h-[300px] w-full max-w-full resize-none overflow-scroll rounded-lg border bg-white p-2 text-sm placeholder:text-sm [&_ol]:list-decimal [&_ol]:pl-5 [&_ul]:list-disc [&_ul]:pl-5"
            >
              <Toolbar className="dark:border-b-dark-lucres-black-200 mb-2 flex gap-2  !border-b bg-transparent pb-1">
                <BtnBold className="text-lucres-black hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-7 w-7 items-center justify-center rounded-lg" />
                <BtnItalic className="text-lucres-black hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-7 w-7 items-center justify-center rounded-lg" />
                <BtnUnderline className="text-lucres-black hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-7 w-7 items-center justify-center rounded-lg" />
                <BtnBulletList className="text-lucres-black hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-7 w-7 items-center justify-center rounded-lg" />
              </Toolbar>
            </Editor>
            <div className="mt-2 flex items-center justify-between">
              {error && <p className="text-xs text-red-600 dark:text-red-400">{error}</p>}
              <div className="text-sm text-gray-500">{characterCount}/750 characters</div>
            </div>
          </div>
        </Modal>
      )}

      {/* AI Suggestions - Responsive */}
      <div className={`${isScreenSmall ? 'space-y-2' : ''}`}>
        {aiSuggestions.map((suggestion, idx) => {
          const isSelected = selectedSuggestions.includes(suggestion)

          if (idx === 2) {
            return (
              <div
                key={idx}
                className={`dark:border-dark-lucres-black-200 mb-2 flex flex-col rounded-lg border border-gray-200 p-4 ${
                  isScreenSmall ? 'p-3' : 'p-4'
                }`}
              >
                <div className="mb-2 flex items-center justify-between">
                  <span
                    className={`dark:text-lucres-200 font-semibold text-gray-700 ${
                      isScreenSmall ? 'text-xs' : 'text-sm'
                    }`}
                  >
                    More suggestions
                  </span>
                  <Tooltip
                    text={isGenerating ? 'Generating...' : 'Generate more AI suggestions'}
                    direction="top"
                    classes={`whitespace-nowrap ${isScreenSmall ? 'pr-2' : 'pr-3'}`}
                  >
                    {isGenerating ? (
                      <CircleDashedIcon
                        size={isScreenSmall ? 22 : 24}
                        className="dark:text-dark-lucres-green-200 animate-spin text-gray-500"
                      />
                    ) : (
                      <ArrowsClockwiseIcon
                        size={isScreenSmall ? 22 : 24}
                        className="dark:text-dark-lucres-green-200 cursor-pointer text-gray-500"
                        onClick={onRegenerateSuggestions}
                      />
                    )}
                  </Tooltip>
                </div>
                <div
                  className={`flex cursor-pointer select-none items-start gap-x-3 rounded-lg p-2 ${
                    isSelected
                      ? 'dark:bg-dark-lucres-black-300 bg-gray-100'
                      : 'dark:bg-dark-lucres-black-500 dark:hover:bg-dark-lucres-black-300 bg-white hover:bg-gray-50'
                  } ${isScreenSmall ? 'gap-x-2' : 'gap-x-3'}`}
                  onClick={() => onSelectAISuggestion(suggestion)}
                >
                  {isSelected ? (
                    <CheckCircleIcon
                      size={isScreenSmall ? 20 : 24}
                      className="text-lucres-500"
                      weight="fill"
                    />
                  ) : (
                    <CircleDashedIcon
                      size={20}
                      className="dark:text-dark-lucres-green-200 text-gray-500"
                    />
                  )}
                  <span
                    className={`dark:text-dark-lucres-green-100 text-gray-700 ${
                      isScreenSmall ? 'text-xs' : 'text-sm'
                    }`}
                  >
                    {suggestion}
                  </span>
                </div>
              </div>
            )
          }

          return (
            <div
              key={idx}
              className={`dark:border-dark-lucres-black-200 mb-2 flex cursor-pointer select-none items-start rounded-lg border border-gray-200 ${
                isSelected
                  ? 'dark:bg-dark-lucres-black-300 bg-gray-100'
                  : 'dark:bg-dark-lucres-black-500 dark:hover:bg-dark-lucres-black-300 bg-white hover:bg-gray-50'
              } ${isScreenSmall ? 'gap-x-2 p-3' : 'gap-x-3 p-4'}`}
              onClick={() => onSelectAISuggestion(suggestion)}
            >
              {isSelected ? (
                <CheckCircleIcon
                  size={isScreenSmall ? 20 : 24}
                  className="text-lucres-500"
                  weight="fill"
                />
              ) : (
                <CircleDashedIcon
                  size={isScreenSmall ? 20 : 24}
                  className="dark:text-dark-lucres-green-200 text-gray-500"
                />
              )}
              <span
                className={`dark:text-dark-lucres-green-100 text-gray-700 ${
                  isScreenSmall ? 'text-xs' : 'text-sm'
                }`}
              >
                {suggestion}
              </span>
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default DescriptionList
