'use client'
import React, { useState, useEffect } from 'react'
import { CheckCircleIcon, PlusIcon, SparkleIcon, XIcon } from '@phosphor-icons/react'
import Input from '../../../components/Input'
import { Language } from '../../../models/Resume'
import { validateField } from '../resumeValidation'
import { AISuggestionService } from '../../../services/AISuggestionService'
import { useToast } from '../../../components/ToastX'

interface LanguagesCardProps {
  languages: Language[]
  languageInput: string
  errors: Array<{ name?: string }>
  onInputChange: (value: string) => void
  onAddLanguage: (name: string) => void
  onRemoveLanguage: (index: number) => void
  userId: string
}

const LanguagesCard: React.FC<LanguagesCardProps> = ({
  languages,
  languageInput,
  errors,
  onInputChange,
  onAddLanguage,
  onRemoveLanguage,
  userId,
}) => {
  const [isFetchingSuggestions, setIsFetchingSuggestions] = useState(false)
  const [aiSuggestions, setAiSuggestions] = useState<Language[]>([])
  const toast = useToast()

  // Fetch AI-generated language suggestions on mount or when languages change
  useEffect(() => {
    const fetchSuggestions = async () => {
      if (!userId) {
        toast.error('User ID not found. Cannot fetch language suggestions.')
        return
      }

      setIsFetchingSuggestions(true)
      try {
        const response = await AISuggestionService.getSuggestion('user', userId, 'languages', '')

        if (response.status === 'success') {
          const selectedLanguageNames = languages.map((lang) => lang.name)

          // Filter out suggestions that are already selected (case-insensitive)
          const filteredSuggestions = response.data
            .filter((lang: string) => {
              const isDuplicate = selectedLanguageNames.some(
                (selectedLang) => selectedLang.toLowerCase().trim() === lang.toLowerCase().trim(),
              )
              return !isDuplicate
            })
            .map((lang: string) => ({
              name: lang,
              other: false,
            }))

          setAiSuggestions(filteredSuggestions)
        } else {
          toast.error('Failed to fetch language suggestions.')
        }
      } catch (error: any) {
        toast.error(error.message || 'An error occurred while fetching language suggestions.')
      } finally {
        setIsFetchingSuggestions(false)
      }
    }

    fetchSuggestions()
  }, [userId, toast]) // Added 'languages' to dependencies to refetch on language changes

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && languageInput.trim()) {
      const trimmedInput = languageInput.trim()
      // Check for duplicates in a case-insensitive manner
      const isDuplicate = languages.some(
        (lang) => lang.name.toLowerCase().trim() === trimmedInput.toLowerCase().trim(),
      )
      if (isDuplicate) {
        toast.warning(`"${trimmedInput}" is already added.`)
        return
      }
      onAddLanguage(trimmedInput)
    }
  }

  const handleAddSuggestion = (suggestionName: string) => {
    // Check for duplicates in a case-insensitive manner
    const isDuplicate = languages.some(
      (lang) => lang.name.toLowerCase().trim() === suggestionName.toLowerCase().trim(),
    )
    if (isDuplicate) {
      toast.warning(`"${suggestionName}" is already added.`)
      return
    }
    onAddLanguage(suggestionName)
  }

  return (
    <div>
      <div className="dark:border-dark-lucres-black-200 mt-3 flex flex-col rounded-lg border border-gray-100 p-4">
        <div className="w-full md:w-2/3 lg:w-2/5">
          <Input
            type="text"
            id="Languages"
            name="Languages"
            value={languageInput}
            onChange={(e) => onInputChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Enter Your Languages"
            label="Enter Your Languages"
            labelClassName="font-medium!"
            className="w-full py-[10px] pl-3 pr-10 capitalize"
            error={languageInput ? validateField('languages', 'name', languageInput, 0) : undefined}
          />
        </div>
        <div className="flex flex-col gap-4">
          <div className="text-sm font-medium">Suggestions Based on your Location</div>
          {isFetchingSuggestions ? (
            <div className="flex items-center gap-x-2 text-sm text-gray-500">
              <SparkleIcon size={16} />
              Fetching language suggestions...
            </div>
          ) : (
            <div className="flex flex-wrap gap-4">
              {aiSuggestions.map((suggestion, index) => {
                // Check if the suggestion is already in the selected languages (case-insensitive)
                const isSelected = languages.some(
                  (lang) => lang.name.toLowerCase().trim() === suggestion.name.toLowerCase().trim(),
                )
                return (
                  <button
                    className={`outline-hidden flex items-center gap-x-2 rounded-md p-1 px-3 text-sm ${
                      isSelected
                        ? 'dark:bg-dark-lucres-green-500 dark:text-dark-lucres-black-300 cursor-not-allowed bg-lime-300 bg-opacity-30'
                        : 'bg-lucres-200 text-gray-500 dark:bg-gray-700 dark:text-gray-100'
                    }`}
                    key={index}
                    onClick={() => handleAddSuggestion(suggestion.name)}
                    disabled={isSelected}
                  >
                    {isSelected ? (
                      <CheckCircleIcon size={16} weight="fill" />
                    ) : (
                      <PlusIcon size={16} />
                    )}{' '}
                    {suggestion.name}
                  </button>
                )
              })}
            </div>
          )}
        </div>
        <div className="my-4 flex flex-col">
          <span className="mb-3 text-sm font-medium">Selected</span>
          {languages.length === 0 && errors.length > 0 && (
            <p className="text-sm text-red-500">{errors[0].name}</p>
          )}
          <div className="flex flex-wrap gap-2">
            {languages.map((language, index) => (
              <button
                key={index}
                onClick={() => onRemoveLanguage(index)}
                className="bg-lucres-200 dark:bg-dark-lucres-black-200 outline-hidden group relative flex items-center gap-x-2 rounded-md p-1 px-3 text-sm capitalize"
              >
                {language.name}
                {errors[index]?.name && (
                  <span className="text-xs text-red-500">{errors[index].name}</span>
                )}
                <span>
                  <XIcon size={10} />
                </span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default LanguagesCard
