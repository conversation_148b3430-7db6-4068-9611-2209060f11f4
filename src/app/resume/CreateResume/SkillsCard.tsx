import React, { useState, useEffect, useCallback, useRef } from 'react'
import Input from '../../../components/Input'
import { Skill } from '../../../models/Resume'
import { validateField } from '../resumeValidation'
import { AISuggestionService } from '../../../services/AISuggestionService'
import { useToast } from '../../../components/ToastX'
import { debounce } from '../../../utils/resumeUtils'
import { PlusIcon, SparkleIcon, XIcon } from '@phosphor-icons/react'

interface SkillsCardProps {
  skills: Skill[]
  skillInput: string
  errors: Array<{ name?: string }>
  onInputChange: (value: string) => void
  onAddSkill: (name: string) => void
  onRemoveSkill: (index: number) => void
  userId: string
  context: string
}

const SkillsCard: React.FC<SkillsCardProps> = ({
  skills,
  skillInput,
  errors,
  onInputChange,
  onAddSkill,
  onRemoveSkill,
  userId,
  context,
}) => {
  const [isFetchingSuggestions, setIsFetchingSuggestions] = useState(false)
  const [aiSuggestions, setAiSuggestions] = useState<Skill[]>([])
  const [isUserTyping, setIsUserTyping] = useState(false)
  const skillsRef = useRef<Skill[]>(skills)
  const toast = useToast()

  // To keep it updated with the latest skills
  useEffect(() => {
    skillsRef.current = skills
  }, [skills])
  // Debounced function for fetching suggestions based on user input
  const debouncedFetchUserInputSuggestions = useCallback(
    debounce(async (input: string) => {
      if (!input.trim() || !userId) return

      setIsFetchingSuggestions(true)
      try {
        const existingSkills = skillsRef.current.map((skill) => skill.name)

        const response = await AISuggestionService.getSuggestion(
          'user',
          userId,
          'skills',
          input,
          undefined,
          undefined,
          existingSkills,
        )

        if (response.status === 'success') {
          const filteredSuggestions = response.data.filter((suggestion: string) => {
            const isDuplicate = existingSkills.some(
              (existingSkill) =>
                existingSkill.toLowerCase().trim() === suggestion.toLowerCase().trim(),
            )
            const meetsLengthRequirement = suggestion.length >= 1 && suggestion.length <= 30
            return !isDuplicate && meetsLengthRequirement
          })

          const newSuggestions = filteredSuggestions.map((skill: string) => ({
            name: skill,
            level: 1,
          }))
          setAiSuggestions(newSuggestions)
        }
      } catch (error: any) {
        console.error('Error fetching user input suggestions:', error)
      } finally {
        setIsFetchingSuggestions(false)
      }
    }, 500),
    [userId],
  )

  // Fetch AI-generated skill suggestions based on profile (when input is empty)
  const fetchProfileBasedSuggestions = useCallback(async () => {
    if (!userId) {
      toast.error('User ID not found. Cannot fetch skill suggestions.')
      return
    }

    setIsFetchingSuggestions(true)
    try {
      const existingSkills = skillsRef.current.map((skill) => skill.name)

      const response = await AISuggestionService.getSuggestion(
        'user',
        userId,
        'skills',
        context,
        undefined,
        undefined,
        existingSkills,
      )

      if (response.status === 'success') {
        const filteredSuggestions = response.data.filter((suggestion: string) => {
          const isDuplicate = existingSkills.some(
            (existingSkill) =>
              existingSkill.toLowerCase().trim() === suggestion.toLowerCase().trim(),
          )
          const meetsLengthRequirement = suggestion.length >= 1 && suggestion.length <= 30
          return !isDuplicate && meetsLengthRequirement
        })

        const newSuggestions = filteredSuggestions.map((skill: string) => ({
          name: skill,
          level: 1,
        }))
        setAiSuggestions(newSuggestions)
      } else {
        toast.error('Failed to fetch skill suggestions.')
      }
    } catch (error: any) {
      toast.error(error.message || 'An error occurred while fetching skill suggestions.')
    } finally {
      setIsFetchingSuggestions(false)
    }
  }, [userId, context, toast])

  // Effect for handling input changes and determining suggestion type
  useEffect(() => {
    if (skillInput.trim()) {
      setIsUserTyping(true)
      debouncedFetchUserInputSuggestions(skillInput)
    } else {
      setIsUserTyping(false)
      fetchProfileBasedSuggestions()
    }
  }, [skillInput, debouncedFetchUserInputSuggestions, fetchProfileBasedSuggestions])

  // Initial load of profile-based suggestions
  useEffect(() => {
    if (!skillInput.trim()) {
      fetchProfileBasedSuggestions()
    }
  }, [userId, context, toast])

  const handleInputChange = (value: string) => {
    onInputChange(value)
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && skillInput.trim()) {
      const trimmedInput = skillInput.trim()
      const isDuplicate = skills.some(
        (skill) => skill.name.toLowerCase() === trimmedInput.toLowerCase(),
      )
      if (isDuplicate) {
        toast.warning(`"${trimmedInput}" is already added.`)
        return
      }
      onAddSkill(trimmedInput)
      onInputChange('') // Clear the input field after adding via Enter
    }
  }

  const handleAddSuggestion = (suggestionName: string) => {
    const isDuplicate = skills.some(
      (skill) => skill.name.toLowerCase() === suggestionName.toLowerCase(),
    )
    if (isDuplicate) {
      toast.warning(`"${suggestionName}" is already added.`)
      return
    }
    onAddSkill(suggestionName) // Add the skill without clearing the input
  }

  const getSuggestionTitle = () => {
    if (isUserTyping && skillInput.trim()) {
      return `Suggestions for "${skillInput}"`
    }
    return 'Suggestions Based on Your Profile'
  }

  return (
    <div>
      <span className="text-lucres-900/60 dark:text-dark-lucres-green-100 mt-4 text-sm ">
        Choose up to 20 relevant Skills.
      </span>
      <div className="dark:border-dark-lucres-black-200 mt-3 flex flex-col rounded-lg border border-gray-100 p-4 ">
        <div className="w-full md:w-2/3 lg:w-2/5">
          <Input
            type="text"
            id="Skills"
            name="Skills"
            value={skillInput}
            placeholder="Type a skill to get suggestions..."
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyDown={handleKeyDown}
            label="Enter Your Skills"
            labelClassName="font-medium!"
            className="w-full p-0.5 capitalize"
            error={skillInput ? validateField('skills', 'name', skillInput, 0) : undefined}
          />
        </div>
        <div className="flex flex-col gap-4">
          <div className="text-sm font-medium">{getSuggestionTitle()}</div>
          {isFetchingSuggestions ? (
            <div className="flex items-center gap-x-2 text-sm text-gray-500">
              <SparkleIcon size={16} />
              {isUserTyping ? 'Finding relevant skills...' : 'Showing skill suggestions...'}
            </div>
          ) : (
            <div className="flex flex-wrap gap-4">
              {aiSuggestions.length > 0 ? (
                aiSuggestions.map((suggestion, index) => {
                  const isSelected = skills.some(
                    (skill) =>
                      skill.name.toLowerCase().trim() === suggestion.name.toLowerCase().trim(),
                  )
                  return (
                    <button
                      className={`outline-hidden flex items-center gap-x-2 rounded-md p-1 px-3 text-sm ${
                        isSelected
                          ? 'dark:bg-dark-lucres-green-500 dark:text-dark-lucres-black-300 cursor-not-allowed bg-lime-300 '
                          : 'bg-lucres-200 hover:bg-lucres-300 text-gray-500 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600'
                      }`}
                      key={index}
                      onClick={() => handleAddSuggestion(suggestion.name)}
                      disabled={isSelected}
                    >
                      {!isSelected && <PlusIcon size={16} />}
                      {suggestion.name}
                    </button>
                  )
                })
              ) : (
                <div className="text-sm text-gray-500">
                  {isUserTyping
                    ? 'No suggestions found for your input.'
                    : 'No suggestions available.'}
                </div>
              )}
            </div>
          )}
        </div>
        <div className="my-3 flex flex-col">
          <span className="mb-1 mt-3 text-sm font-medium">Selected</span>
          {skills.length === 0 && errors.length > 0 && (
            <p className="text-sm text-red-500">{errors[0].name}</p>
          )}
          <div className="flex flex-wrap gap-2">
            {skills.map((skill, index) => (
              <button
                key={index}
                onClick={() => onRemoveSkill(index)}
                className="bg-lucres-200 dark:bg-dark-lucres-black-200 outline-hidden group relative flex items-center gap-x-2 rounded-md p-1 px-3 text-sm capitalize"
              >
                {skill.name}
                {errors[index]?.name && (
                  <span className="text-xs text-red-500">{errors[index].name}</span>
                )}
                <span>
                  <XIcon size={10} />
                </span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default SkillsCard
