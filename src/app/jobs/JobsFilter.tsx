import Button from '../../components/Button'
import MultiSelect from '../../components/MultiSelect'
import Select from '../../components/Select'
import { useAuth } from '../../context/AuthContext'
interface JobFilterProps {
  selectedSkills: string[]
  setSelectedSkills: React.Dispatch<React.SetStateAction<string[]>>
  selectedLocations: string[]
  setSelectedLocations: React.Dispatch<React.SetStateAction<string[]>>
  selectedJobType: string[]
  setSelectedJobType: React.Dispatch<React.SetStateAction<string[]>>
  selectedSeniority: string | undefined
  setSelectedSeniority: React.Dispatch<React.SetStateAction<string | undefined>>
  handleResetFilters: () => void
}

const JobFilter: React.FC<JobFilterProps> = ({
  selectedSkills,
  setSelectedSkills,
  selectedLocations,
  setSelectedLocations,
  selectedJobType,
  setSelectedJobType,
  selectedSeniority,
  setSelectedSeniority,
  handleResetFilters,
}) => {
  const { isAuthenticated } = useAuth()

  const experienceOptions = [
    { value: 'Fresher', label: 'Fresher' },
    { value: '1-3 years', label: '1-3 years' },
    { value: '3-6 years', label: '3-6 years' },
    { value: '6-9 years', label: '6-9 years' },
    { value: '9-12 years', label: '9-12 years' },
    { value: '>12 years', label: '>12 years' },
  ]

  const jobTypeOptions = [
    { value: 'Full Time', label: 'Full Time' },
    { value: 'Part Time', label: 'Part Time' },
    { value: 'Internship', label: 'Internship' },
    { value: 'Contract', label: 'Contract' },
  ]

  return (
    <div className="flex w-full flex-col items-start gap-4">
      <div
        className={`flex w-full items-center justify-between ${!isAuthenticated ? 'md:pt-5' : 'md:mt-0 md:pt-3'}`}
      >
        <div>Filters</div>
        <Button
          theme="transparent"
          className="border-lucres-300/40 dark:border-dark-lucres-black-200 px-4! py-1! rounded-lg border text-sm hover:bg-opacity-50"
          onClick={handleResetFilters}
        >
          Reset
        </Button>
      </div>
      <MultiSelect
        type="text"
        value={selectedSkills}
        classes="p-2!"
        parentClassName="p-0 w-full!"
        onChange={setSelectedSkills}
        placeholder="Skills"
      />
      <MultiSelect
        type="dropdown"
        classes="p-2!"
        parentClassName="p-0"
        options={jobTypeOptions}
        value={selectedJobType}
        onChange={setSelectedJobType}
        placeholder="Job Type"
      />
      <MultiSelect
        type="text"
        classes="p-2!"
        parentClassName="p-0"
        value={selectedLocations}
        onChange={setSelectedLocations}
        placeholder="Location"
        disabled={selectedJobType.includes('Remote')}
      />
      <Select
        classes="md:py-2!"
        options={experienceOptions}
        parentClassName="p-0"
        value={selectedSeniority}
        onChange={setSelectedSeniority}
        placeholder="Experience"
      />
    </div>
  )
}

export default JobFilter
