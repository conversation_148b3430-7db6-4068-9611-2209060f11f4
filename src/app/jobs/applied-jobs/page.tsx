'use client'
import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { SrpService } from '../../../services/SrpService'
import { Job } from '../../../models/Job'
import { useAuth } from '../../../context/AuthContext'
import JobCard from '../JobCard'
import RecruiterQuestionnaire from '../RecruiterQuestionnaire '
import JobCardSkeleton from '../JobCardSkeleton'
import { Funnel, X } from '@phosphor-icons/react'
import JobFilter from '../JobsFilter'
import NoDataFound from '../../../components/NoDataFound/NoDataFound'
import useError from '../../../context/ErrorContext'
import { useToast } from '../../../components/ToastX'

const AppliedJobs: React.FC = () => {
  const { isAuthenticated } = useAuth()
  const router = useRouter()
  const { handleError } = useError()
  const toast = useToast()

  const [activeTab, setActiveTab] = useState('Applied')
  const [jobPreviewData, setJobPreviewData] = useState<Job | undefined>(undefined)
  const [showJobDetails, setShowJobDetails] = useState(false)
  const [showQuestionnaire, setShowQuestionnaire] = useState(false)

  const [selectedSkills, setSelectedSkills] = useState<string[]>([])
  const [selectedJobType, setSelectedJobType] = useState<string[]>([])
  const [selectedLocations, setSelectedLocations] = useState<string[]>([])
  const [selectedWorkExperience, setSelectedWorkExperience] = useState<string | undefined>(
    undefined,
  )

  const [savedJobs, setSavedJobs] = useState<Job[]>([])
  const [appliedJobs, setAppliedJobs] = useState<Job[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [showFilters, setShowFilters] = useState<boolean>(false)

  const fetchAppliedJobs = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await SrpService.getAppliedJobs()
      setAppliedJobs(response.data.items || [])
    } catch (err: any) {
      handleError(err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAppliedJobs()
  }, [])

  const handleApplyJob = (job: Job) => {
    if (!isAuthenticated) router.push('/signIn')
    else {
      if ((job as any).questionnaire) {
        setShowQuestionnaire(true)
        setJobPreviewData(job)
      }
    }
  }

  const handleSavedJob = (job: Job) => {
    if (!isAuthenticated) {
      router.push('/signIn')
    } else {
      const isAlreadySaved = savedJobs.some((savedJob) => savedJob.job.id === job.job.id)
      if (isAlreadySaved) {
        setSavedJobs(savedJobs.filter((savedJob) => savedJob.job.id !== job.job.id))
      } else {
        setSavedJobs([...savedJobs, job])
      }
    }
  }

  const handleSkillTagClick = (skill: string) => {
    setSelectedSkills((prev) => (prev.includes(skill) ? prev : [...prev, skill]))
  }

  const handleJobCardClick = (job: Job) => {
    setShowJobDetails(true)
    setJobPreviewData(job)
    router.push(`/job/${job?.job?.permalink}`)
  }

  const handleRepostJob = (job: Job) => {
    if (!isAuthenticated) router.push('/signIn')
  }

  const handleWithdrawJob = async (job: Job) => {
    if (!isAuthenticated) {
      router.push('/signIn')
    } else {
      try {
        // After successful withdrawal, update the jobs list
        setAppliedJobs(appliedJobs.filter((j) => j.job.id !== job.job.id))
        toast.success('Application withdrawn successfully')
      } catch (err: any) {
        handleError(err)
      }
    }
  }

  const handleResetFilters = () => {
    setSelectedSkills([])
    setSelectedLocations([])
    setSelectedJobType([])
    setSelectedWorkExperience(undefined)
    fetchAppliedJobs()
  }

  return (
    <section className="font-inter flex h-full w-full py-12 lg:mt-12  lg:py-0">
      <div className="flex w-full items-start justify-center   px-2 lg:max-w-[79%] lg:justify-end  lg:px-4  xl:max-w-[70%] ">
        <div className="dark:border-dark-lucres-black-300 min-h-screen w-full max-w-[700px] pb-8 md:border-x lg:pb-0 ">
          {isAuthenticated && (
            <div className="mx-auto flex w-full items-center justify-between px-6 pt-6 sm:w-9/12">
              <span
                className={`cursor-pointer pb-2 ${
                  activeTab === 'Jobs' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => router.push('/jobs')}
              >
                Jobs for You
              </span>
              <span
                className={`cursor-pointer pb-2 ${
                  activeTab === 'Applied' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => setActiveTab('Applied')}
              >
                Jobs Applied
              </span>
              <span
                className={`cursor-pointer pb-2 ${
                  activeTab === 'Saved' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => router.push('/jobs/saved-jobs')}
              >
                Jobs Saved
              </span>
            </div>
          )}
          <div
            className={`mb-6 flex w-full flex-col items-center border-t sm:items-start md:px-6 ${
              isAuthenticated ? '' : 'md:pt-3'
            } dark:border-dark-lucres-black-300`}
          ></div>
          <div className="flex flex-col gap-y-4">
            {loading ? (
              <>
                {Array.from({ length: 10 }).map((_, index) => (
                  <JobCardSkeleton key={index} />
                ))}
              </>
            ) : error ? (
              <div>{error}</div>
            ) : appliedJobs.length ? (
              appliedJobs.map((jobItem, index) => (
                <div key={index} className="md:mx-4">
                  <JobCard
                    job={jobItem}
                    onApply={handleWithdrawJob}
                    onSave={handleSavedJob}
                    saved={savedJobs.some((savedJob) => savedJob.job.id === jobItem.job.id)}
                    onSkillTagClick={handleSkillTagClick}
                    onRepost={handleRepostJob}
                    onClick={handleJobCardClick}
                    isAppliedTab={true}
                  />
                </div>
              ))
            ) : (
              <NoDataFound />
            )}
          </div>
        </div>
      </div>
    </section>
  )
}

export default AppliedJobs
