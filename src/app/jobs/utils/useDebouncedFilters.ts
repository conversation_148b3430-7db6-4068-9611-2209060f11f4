import { useEffect, useRef, useState } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { generateFilterUrl, parseFilterUrl, FilterState } from './urlUtils'

// Debounce utility
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState(value)

  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay)
    return () => clearTimeout(handler)
  }, [value, delay])

  return debouncedValue
}

export function useDebouncedFilters(defaultState: FilterState, debounceMs = 300) {
  const router = useRouter()
  const pathname = usePathname()
  const isSyncingFromUrl = useRef(false)

  // Local filter state
  const [filters, setFilters] = useState<FilterState>(() => parseFilterUrl(pathname))

  // Sync from URL on mount or navigation
  useEffect(() => {
    const urlState = parseFilterUrl(pathname)
    if (JSON.stringify(urlState) !== JSON.stringify(filters)) {
      isSyncingFromUrl.current = true
      setFilters(urlState)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname])

  // Debounced filter state for fetching
  const debouncedFilters = useDebounce(filters, debounceMs)

  // Update URL when filters change (debounced)
  useEffect(() => {
    if (isSyncingFromUrl.current) {
      isSyncingFromUrl.current = false
      return
    }
    const newPath = generateFilterUrl(filters)
    if (pathname !== newPath) {
      router.replace(newPath)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters])

  // Helpers to update filter state
  const setSelectedSkills = (skills: string[]) =>
    setFilters((f) => ({ ...f, selectedSkills: skills, currentPage: 1 }))
  const setSelectedJobType = (types: string[]) =>
    setFilters((f) => ({ ...f, selectedJobType: types, currentPage: 1 }))
  const setSelectedLocations = (locs: string[]) =>
    setFilters((f) => ({ ...f, selectedLocations: locs, currentPage: 1 }))
  const setSelectedWorkExperience = (exp?: string) =>
    setFilters((f) => ({ ...f, selectedWorkExperience: exp, currentPage: 1 }))
  const setCurrentPage = (page: number) => setFilters((f) => ({ ...f, currentPage: page }))
  const resetFilters = () => setFilters({ ...defaultState })

  return {
    filters,
    setSelectedSkills,
    setSelectedJobType,
    setSelectedLocations,
    setSelectedWorkExperience,
    setCurrentPage,
    resetFilters,
    debouncedFilters,
  }
}
