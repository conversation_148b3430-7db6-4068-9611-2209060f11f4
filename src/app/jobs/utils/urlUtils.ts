export interface FilterState {
  selectedSkills: string[]
  selectedJobType: string[]
  selectedLocations: string[]
  selectedWorkExperience?: string
  currentPage: number
}

export const generateFilterUrl = (filters: FilterState): string => {
  const segments: string[] = []
  const order: number[] = []

  const format = (arr: string[]) =>
    arr.map((item) => encodeURIComponent(item.toLowerCase().replace(/\s+/g, '-'))).join(',')

  if (filters.selectedSkills.length) {
    order.push(1)
    segments.push(format(filters.selectedSkills))
  }

  if (filters.selectedJobType.length) {
    order.push(2)
    segments.push(format(filters.selectedJobType))
  }

  if (filters.selectedLocations.length) {
    order.push(3)
    segments.push(format(filters.selectedLocations))
  }

  if (filters.selectedWorkExperience) {
    order.push(4)
    segments.push(
      encodeURIComponent(filters.selectedWorkExperience.toLowerCase().replace(/\s+/g, '-')),
    )
  }

  // Only add page number if there are other filters
  if (segments.length > 0) {
    order.push(5)
    segments.push(filters.currentPage.toString())
  }

  // Create mapping code by joining the order numbers
  const mappingCode = order.join('')

  if (segments.length === 0) {
    return '/jobs'
  }

  return `/jobs/${mappingCode}/${segments.join('/')}`
}

export const parseFilterUrl = (pathname: string): FilterState => {
  const defaultState: FilterState = {
    selectedSkills: [],
    selectedJobType: [],
    selectedLocations: [],
    selectedWorkExperience: undefined,
    currentPage: 1,
  }

  const pathSegments = pathname.split('/').filter(Boolean)

  if (
    pathSegments.length < 3 ||
    pathSegments[1] === 'savedjobs' ||
    pathSegments[1] === 'appliedjobs'
  ) {
    return defaultState
  }

  const mappingCode = pathSegments[1]
  const values = pathSegments.slice(2)

  const result = { ...defaultState }

  mappingCode.split('').forEach((code, index) => {
    const value = values[index]
    if (!value) return

    const decoded = decodeURIComponent(value)
      .split(',')
      .map((v) => v.replace(/-/g, ' '))

    switch (code) {
      case '1':
        result.selectedSkills = decoded
        break
      case '2':
        result.selectedJobType = decoded
        break
      case '3':
        result.selectedLocations = decoded
        break
      case '4':
        result.selectedWorkExperience = decoded[0]
        break
      case '5':
        const page = parseInt(decoded[0], 10)
        if (!isNaN(page)) result.currentPage = page
        break
    }
  })

  return result
}

// Example usage:
// URL: domain/12345/react,html/part-time/delhi,bengaluru/1-3-years/2
// This would parse to:
// - selectedSkills: ["react", "html"]
// - selectedJobType: ["part time"]
// - selectedLocations: ["delhi", "bengaluru"]
// - selectedWorkExperience: "1-3 years"
// - currentPage: 2
// - mappingCode: "12345" (indicating the order of filters)
