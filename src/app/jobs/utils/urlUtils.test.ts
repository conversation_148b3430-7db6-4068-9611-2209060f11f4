import { generateFilterUrl, parseFilterUrl, FilterState } from './urlUtils'

// Test cases for URL generation and parsing
const testCases = [
  {
    name: 'Skills only',
    filters: {
      selectedSkills: ['React', 'HTML'],
      selectedJobType: [],
      selectedLocations: [],
      selectedWorkExperience: undefined,
      currentPage: 1,
    },
    expectedUrl: '/jobs/15/react,html/1',
    expectedMappingCode: '15',
  },
  {
    name: 'Skills and job type',
    filters: {
      selectedSkills: ['React', 'HTML'],
      selectedJobType: ['Part Time'],
      selectedLocations: [],
      selectedWorkExperience: undefined,
      currentPage: 1,
    },
    expectedUrl: '/jobs/125/react,html/part-time/1',
    expectedMappingCode: '125',
  },
  {
    name: 'All filters',
    filters: {
      selectedSkills: ['React', 'HTML'],
      selectedJobType: ['Part Time'],
      selectedLocations: ['Delhi', 'Bengaluru'],
      selectedWorkExperience: '1-3 years',
      currentPage: 2,
    },
    expectedUrl: '/jobs/12345/react,html/part-time/delhi,bengaluru/1-3-years/2',
    expectedMappingCode: '12345',
  },
  {
    name: 'No filters',
    filters: {
      selectedSkills: [],
      selectedJobType: [],
      selectedLocations: [],
      selectedWorkExperience: undefined,
      currentPage: 1,
    },
    expectedUrl: '/jobs',
    expectedMappingCode: '',
  },
]

// Test URL generation
testCases.forEach((testCase) => {
  const generatedUrl = generateFilterUrl(testCase.filters)
  const isCorrect = generatedUrl === testCase.expectedUrl
  if (!isCorrect) {
  }
})

// Test URL parsing
testCases.forEach((testCase) => {
  if (testCase.expectedUrl === '/jobs') return // Skip no filters case for parsing

  const parsedFilters = parseFilterUrl(testCase.expectedUrl)
  const isCorrect = JSON.stringify(parsedFilters) === JSON.stringify(testCase.filters)
  if (!isCorrect) {
  }
})

// Test specific example from requirements
const exampleFilters: FilterState = {
  selectedSkills: ['React', 'HTML'],
  selectedJobType: ['Part Time'],
  selectedLocations: ['Delhi', 'Bengaluru'],
  selectedWorkExperience: '1-3 years',
  currentPage: 2,
}

const exampleUrl = generateFilterUrl(exampleFilters)

const parsedExample = parseFilterUrl(exampleUrl)
