import React from 'react'

const JobCardSkeleton: React.FC = () => {
  return (
    <div className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 relative flex animate-pulse flex-col justify-center rounded-xl border px-4 py-3 md:mx-4 md:px-6 md:py-4">
      {/* Header: Avatar, Name & Headline, Follow Button */}
      <div className="flex items-start justify-between md:items-center">
        <div className="flex flex-col items-start gap-3 gap-x-4 md:flex-row md:items-center">
          {/* Avatar placeholder */}
          <div className="h-16 w-16 rounded-full bg-gray-300 dark:bg-gray-700"></div>
          <div className="flex flex-col space-y-2">
            {/* Name placeholder */}
            <div className="h-6 w-32 rounded-sm bg-gray-300 dark:bg-gray-700"></div>
            {/* Headline placeholder */}
            <div className="h-4 w-24 rounded-sm bg-gray-300 dark:bg-gray-700"></div>
          </div>
        </div>
        {/* Follow button placeholder */}
        <div className="h-8 w-16 rounded-sm bg-gray-300 dark:bg-gray-700"></div>
      </div>

      {/* Info Row: Work Experience and Location */}
      <div className="mb-2 mt-4 flex flex-wrap gap-x-5 gap-y-2">
        <div className="flex items-center gap-x-2">
          {/* Briefcase icon placeholder */}
          <div className="h-6 w-6 rounded-sm bg-gray-300 dark:bg-gray-700"></div>
          {/* Work Experience placeholder */}
          <div className="h-4 w-20 rounded-sm bg-gray-300 dark:bg-gray-700"></div>
        </div>
        <div className="flex items-center gap-x-2">
          {/* MapPin icon placeholder */}
          <div className="h-6 w-6 rounded-sm bg-gray-300 dark:bg-gray-700"></div>
          {/* Location placeholder */}
          <div className="h-4 w-20 rounded-sm bg-gray-300 dark:bg-gray-700"></div>
        </div>
      </div>

      {/* About Me Section */}
      <div className="text-lucres-900 dark:text-dark-lucres-green-100 mt-4 text-sm">
        <div className="mt-2 space-y-2">
          <div className="h-3 w-full rounded-sm bg-gray-300 dark:bg-gray-700"></div>
          <div className="h-3 w-full rounded-sm bg-gray-300 dark:bg-gray-700"></div>
          <div className="h-3 w-5/6 rounded-sm bg-gray-300 dark:bg-gray-700"></div>
        </div>
      </div>

      {/* Skills List */}
      <div className="mt-4 flex w-full flex-wrap items-start justify-start gap-2 sm:w-9/12">
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="h-6 w-16 rounded-lg bg-gray-300 dark:bg-gray-700"></div>
        ))}
      </div>

      {/* Footer: Download, Share, and Save */}
      <div className="mt-3 flex w-full items-center justify-between">
        <div className="flex gap-x-8">
          <div className="flex items-center gap-x-2">
            {/* Download icon placeholder */}
            <div className="h-6 w-6 rounded-sm bg-gray-300 dark:bg-gray-700"></div>
            {/* Download count placeholder */}
            <div className="h-4 w-10 rounded-sm bg-gray-300 dark:bg-gray-700"></div>
          </div>
          <div className="flex items-center gap-x-2">
            {/* Share icon placeholder */}
            <div className="h-6 w-6 rounded-sm bg-gray-300 dark:bg-gray-700"></div>
          </div>
        </div>
        {/* Save icon placeholder */}
        <div className="h-6 w-6 rounded-sm bg-gray-300 dark:bg-gray-700"></div>
      </div>
    </div>
  )
}

export default JobCardSkeleton
