'use client'
import React, { useEffect, useState } from 'react'
import {
  BriefcaseIcon,
  DotIcon,
  IdentificationCardIcon,
  MoneyIcon,
  UsersIcon,
  WarningIcon,
} from '@phosphor-icons/react'
import Button from '../../components/Button'
import { Job } from '../../models/Job'
import {
  calculateSalaryRange,
  convertToTitleCase,
  capitalizeWords,
  truncateText,
} from '../../utils/commonUtils'
import Avatar from '../../components/Avatar/Avatar'
// import Like from '../../components/Like'
import { useToast } from '../../components/ToastX'
import CopyLinkIcon from '../../components/CopyLinkIcon'
import BookmarkIcon from '../../components/BookmarkIcon'
import { useAuth } from '../../context/AuthContext'
import { usePathname, useRouter } from 'next/navigation'
import { BookmarkService } from '../../services/BookmarkService'
import RepostIcon from '../../components/RepostIcon'
import { SrpService } from '../../services/SrpService'
import { PostCard, PostUser } from '../../models/Post'
import { PostService } from '../../services/PostService'
import Like from '../../components/Like'
import { JobPreviewDetails } from '../../models/Job'
import { JobService } from '../../services/JobService'
import useError from '../../context/ErrorContext'
import { CareerService } from '../../services/CareerService'
import { useAccount } from '../../context/UserDetailsContext'
import Overlay from '../../components/Overlay'

export interface JobCardProps {
  job: Job
  onApply?: (job: Job) => void
  onSave?: (job: Job) => void
  saved?: boolean
  onSkillTagClick?: (skill: string) => void
  onLike?: (job: Job) => Promise<any>
  onRepost?: (job: Job) => void
  onClick: (job: Job) => void
  repostedBy?: PostUser
  setData?: React.Dispatch<React.SetStateAction<PostCard[]>>
  activeSaved?: boolean
  isAppliedTab?: boolean
}

const JobCard: React.FC<JobCardProps> = ({
  job,
  onApply,
  saved,
  onSave,
  onSkillTagClick,
  onLike,
  onRepost,
  repostedBy,
  onClick,
  setData,
  activeSaved = false,
  isAppliedTab = false,
}) => {
  const toast = useToast()
  const router = useRouter()
  const { isAuthenticated, authUserProfile } = useAuth()
  const { contactDetails } = useAccount()
  const [liked, setLiked] = useState(false)
  const [likeCount, setLikeCount] = useState<number>(job?.job?.likesCount)
  const { handleError } = useError()
  const [showModal, setShowModal] = useState(false)
  const [missingDetails, setMissingDetails] = useState<string[]>([])
  const [withdrawn, setWithDrawn] = useState(job.status === 'WITHDRAW')
  const [isApplied, setIsApplied] = useState(job.isApplied)
  const pathname = usePathname()

  const hideBookmark = pathname.startsWith('/feed') || pathname.startsWith('/board')
  const validateResume = async () => {
    if (!authUserProfile?.permalink) {
      toast.error('Please complete your profile before applying')
      return false
    }

    try {
      const resumeResponse = await CareerService.getCareerByPermalink(authUserProfile.permalink)
      if (resumeResponse.status !== 'success' || !resumeResponse.data) {
        toast.error('Please complete your resume before applying')
        return false
      }

      const resumeData = resumeResponse.data
      const missing = []

      // Check education
      if (!resumeData.educations || resumeData.educations.length === 0) {
        missing.push('education details')
      }

      // Check skills
      if (!resumeData.skills || resumeData.skills.length === 0) {
        missing.push('skills')
      }

      // Check phone number
      if (!contactDetails.primaryPhone) {
        missing.push('phone number')
      }

      if (missing.length > 0) {
        setMissingDetails(missing)
        setShowModal(true)
        return false
      }

      return true
    } catch (error) {
      toast.error('Failed to validate resume')
      return false
    }
  }

  const handleBookmarkToggle = async (newBookmarked: boolean) => {
    if (!isAuthenticated) {
      toast.error('Please log in to bookmark.')
      router.push('/sign-in')
      return
    }

    try {
      if (newBookmarked) {
        const res = await BookmarkService.addBookmark(job?.job?.id, 'JOBS_SRP')
        if (res.status === 'success') {
          toast.success(res.message || 'Bookmark added.')
          onSave && onSave({ ...job, isBookmark: true })
        } else {
          throw new Error(res.message)
        }
      } else {
        const res = await BookmarkService.deleteBookmark(job?.job?.id, 'JOBS_SRP')
        if (res.status === 'success') {
          toast.success(res.message || 'Bookmark removed.')
          onSave && onSave({ ...job, isBookmark: false })
        } else {
          throw new Error(res.message)
        }
      }
    } catch (error: any) {
      handleError(error)
      throw error // Re-throw to trigger UI rollback
    }
  }

  const removeRepostById = (idToRemove: string) => {
    setData?.((prevData: PostCard[]) =>
      prevData?.filter((item) => {
        // If not a repost or repostedItem is missing, keep the item
        if (item.type !== 'REPOST' || !item.repost?.repostedItem) return true
        // Remove only if repostedItem.id matches the one to remove
        return item.repost.repostedItem.id !== idToRemove
      }),
    )
  }

  const updateRepostTag = (postId: string, newIsReposted: boolean) => {
    setData?.((prev) =>
      prev.map((post) => {
        // If the post is a repost of the job with given postId
        if (post.type === 'REPOST' && post.repost?.repostedItem?.id === postId) {
          return {
            ...post,
            isReposted: newIsReposted,
          }
        }
        if (post.type === 'JOB' && post.job?.id === postId) {
          return {
            ...post,
            isReposted: newIsReposted,
          }
        }

        return post
      }),
    )
  }

  const handleLike = async (id: string) => {
    try {
      const res = await PostService.likeJob(id)
      if (res.status === 'success') {
      } else {
        throw new Error(res)
      }
    } catch (error) {
      throw error
      // toast.error(`User Unfollow Failed`)
    }
  }

  const handleDislike = async (id: string) => {
    try {
      const res = await PostService.dislikeJob(id)
      if (res.status === 'success') {
      } else {
        throw new Error(res)
      }
    } catch (error) {
      throw error
      // toast.error(`User Follow Failed`)
    }
  }

  const handleRepostClick = async (isReposted: boolean) => {
    if (!isAuthenticated) {
      toast.error('Please log in to Repost.')
      router.push('/sign-in')
      return
    }
    try {
      if (!isReposted) {
        const res = await SrpService.undoRePostJob(job?.job?.id)
        if (res.status === 'success') toast.success(res.message || 'Repost Removed.')
        updateRepostTag(job?.job?.id, false)
        if (repostedBy && repostedBy.username === authUserProfile?.username) {
          removeRepostById(job?.job.id)
        }
      } else {
        const res = await SrpService.repostJob(job?.job?.id)
        if (res.status === 'success') {
          toast.success(res.message || 'Job Reposted.')
          updateRepostTag(job?.job?.id, true)
        }
      }
    } catch (error: any) {
      toast.error(error?.message || error?.response?.data?.message || 'Failed to repost job.')
      throw error
    }
  }

  const handleToggleLike = (id: string, newLiked: boolean) => {
    if (!isAuthenticated) {
      toast.error('Please log in to like jobs.')
      router.push('/sign-in')
      return
    }

    if (!newLiked) {
      handleDislike(id)
    } else {
      handleLike(id)
    }
  }

  const handleApply = async () => {
    if (!job?.job?.id) return
    if (!authUserProfile?.id) return
    const userId = authUserProfile.id

    try {
      const isValid = await validateResume()
      if (!isValid) return

      // If all required details are present, proceed with job application
      const response = await JobService.applyJob(job.job.id, userId)

      if (response.data.status === 'success') {
        toast.success('Job Applied Successfully')
        setIsApplied(true)
        onApply && onApply(job)
      } else {
        throw new Error(response.data.message || 'Cannot apply to the job at this moment')
      }
    } catch (error: any) {
      handleError(error)
    }
  }

  const handleWithdraw = async () => {
    if (!job?.id) return
    if (!authUserProfile?.id) return
    const userId = authUserProfile.id

    try {
      const response = await SrpService.WithdrawApplication(job.id, userId)

      if (response.status === 'success') {
        setWithDrawn(true)
        toast.success('Application Withdrawn')
      } else {
        throw new Error(response.data.message || 'Cannot apply to the job at this moment')
      }
    } catch (error: any) {
      handleError(error)
    }
  }
  useEffect(() => {
    if (liked) {
      setLikeCount(job?.job?.likesCount + 1)
    } else {
      setLikeCount(job?.job?.likesCount)
    }
  }, [liked])
  return (
    <>
      <div
        className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 relative flex cursor-pointer flex-col items-start justify-center gap-2 rounded-xl border px-4 py-3 md:p-3"
        onClick={() => onClick(job)}
      >
        <div className="flex w-full items-start justify-between md:items-center">
          <div className="flex flex-col items-start gap-3 gap-x-2 md:flex-row md:items-center">
            <div onClick={(e) => e.stopPropagation()}>
              <Avatar
                src={job.job.img}
                alt={`${job.job.name}'s Avatar`}
                size={12}
                type="company"
                // onClick={() => navigate('/profile')}
                className="cursor-pointer object-cover"
              />
            </div>
            <div className="flex flex-col">
              <div className="flex flex-col items-start lg:flex-row lg:items-center">
                <div className="flex items-center text-lg font-bold">
                  {isAppliedTab
                    ? truncateText(job.job.title, 20).text
                    : truncateText(job.job.name, 20).text}
                </div>
                {job.job.company && (
                  <>
                    <DotIcon size={14} weight="bold" className="hidden lg:block" />

                    <div className="flex items-center text-base">
                      {typeof job.job.company === 'string'
                        ? capitalizeWords(truncateText(job.job.company, 20).text)
                        : capitalizeWords(truncateText(job?.job?.company?.name, 20).text)}
                    </div>
                  </>
                )}
              </div>
              <span className="">
                {' '}
                {job.job.workMode === 'REMOTE'
                  ? 'Remote'
                  : typeof job.job.location === 'string'
                    ? truncateText(job.job.location, 20).text
                    : truncateText(
                        job?.job?.location?.address.state +
                          ', ' +
                          job?.job?.location?.address.country,
                        20,
                      ).text}{' '}
                {job.job.workMode === 'HYBRID' && '(Hybrid)'}
              </span>
            </div>
          </div>
          <Button
            theme={isAppliedTab ? 'transparent' : 'translucent'}
            size="small"
            disabled={isApplied ? !isAppliedTab : withdrawn}
            className={`px-6! py-2! whitespace-nowrap ${
              isAppliedTab
                ? 'text-[#980000]! hover:bg-transparent! hover:text-[#980000]! dark:bg-transparent! dark:text-[#E39D90]! dark:hover:text-[#E39D90]'
                : ''
            }`}
            onClick={(e) => {
              e.stopPropagation()
              if (isAppliedTab) {
                setShowModal(true)
              } else {
                handleApply()
              }
            }}
          >
            {isAppliedTab ? (
              withdrawn ? (
                'Withdrawn'
              ) : (
                'Withdraw'
              )
            ) : isApplied ? (
              <>Applied</>
            ) : (
              'Apply'
            )}
          </Button>
        </div>
        <div className="mb-2 flex w-full flex-wrap items-start gap-4 text-sm font-medium md:flex-row md:items-center md:gap-4 lg:gap-6">
          <div className="flex items-center gap-x-2">
            <UsersIcon size={24} className=" " />
            <span className="whitespace-nowrap text-sm font-medium">
              {job.job.openings} Openings
            </span>
          </div>
          <div className="flex items-center gap-x-2">
            <BriefcaseIcon size={24} className=" " />
            <span>
              {job.job.workExperience.min === 0
                ? 'Fresher'
                : `${job.job.workExperience.min}-${job.job.workExperience.max} Years`}
            </span>
          </div>
          <div className="flex items-center gap-x-2">
            <MoneyIcon size={24} className=" " />
            {activeSaved ? (
              <span>{calculateSalaryRange(job.job.salary)}</span>
            ) : (
              <span>{calculateSalaryRange(job.job.salary)}</span>
            )}
            <span className=" dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-300 inline-block rounded-[4px] border px-3 py-1 text-xs font-normal dark:border">
              {activeSaved
                ? capitalizeWords(job.job.salary.period)
                : capitalizeWords(job.job.salary.period)}
            </span>
          </div>
          <div className="flex items-center gap-x-2">
            <IdentificationCardIcon size={24} className=" " />
            <span>
              {capitalizeWords(
                job.job.employmentType
                  .replace(/[^a-zA-Z0-9]/g, ' ')
                  .replace(/\s+/g, ' ')
                  .trim(),
              )}
            </span>
          </div>
        </div>
        {/* <div className="mt-3 w-full break-words text-sm  ">
          {`${job.job.description}...`}
        </div> */}
        <div className="mb-1 flex w-full flex-wrap items-start justify-start gap-2">
          {job.job.skills.slice(0, 5).map((skill: any, i: number) => (
            <span
              key={i}
              onClick={(e) => {
                e.stopPropagation()
                onSkillTagClick && onSkillTagClick(skill)
              }}
              className="bg-lucres-gray-300 text-lucres-gray-700  dark:border-dark-lucres-black-300   dark:bg-dark-lucres-black-500 dark:hover:bg-dark-lucres-black-300  inline-block cursor-pointer rounded-[4px] px-4 py-1 text-xs font-normal hover:bg-gray-100 dark:border dark:text-white"
            >
              {/* {convertToTitleCase(skill)} */}
              {isAppliedTab ? convertToTitleCase(skill.name) : convertToTitleCase(skill)}
            </span>
          ))}
        </div>
        <div className="flex w-full items-center justify-between">
          <div className="flex gap-x-8">
            <div className="flex items-center gap-x-2">
              <Like
                isLiked={job.isLiked || false}
                likeCount={likeCount ?? 0}
                onToggleLike={(newLiked) => handleToggleLike(job.job.id, newLiked)}
              />
            </div>
            <RepostIcon
              initialRepostCount={job.job.rePostCount}
              onRepostToggle={handleRepostClick}
              isJobReposted={job.isRePosted}
            />
            <CopyLinkIcon toCopy={`${location.origin}/job/${job?.job?.permalink}`} />
          </div>
          {!hideBookmark && (
            <BookmarkIcon
              onBookmarkToggle={handleBookmarkToggle}
              isBookmarked={activeSaved || job.isBookmark}
              tooltipText="Save Job"
            />
          )}
        </div>
      </div>

      {showModal && (
        <Overlay
          heading={isAppliedTab ? 'Withdraw Application' : 'Complete Your Resume'}
          handleClose={() => setShowModal(false)}
          size="min-h-fit mt-20"
          classes="p-0!"
        >
          <div className="dark:bg-dark-lucres-black-500 w-full rounded-lg bg-white p-4 shadow-lg">
            <div className="flex flex-col items-center">
              <WarningIcon size={32} className="text-yellow-500" />
              <p
                className={`mt-3 text-center text-sm ${
                  !isAppliedTab && 'text-lucres-800 dark:text-dark-lucres-black-100'
                }`}
              >
                {isAppliedTab ? (
                  <>
                    Are you sure you want to withdraw your application?
                    <br />
                    You won't be able to apply it again.
                  </>
                ) : (
                  'Please add the following details:'
                )}
              </p>

              {!isAppliedTab && (
                <div className="mt-1 flex flex-col items-center text-sm">
                  {missingDetails.map((detail, index) => (
                    <div key={index} className="capitalize">
                      {detail}
                    </div>
                  ))}
                </div>
              )}
            </div>
            <div className="dark:border-t-dark-lucres-black-200 mt-6 flex justify-between gap-4 border-t pt-4">
              <Button
                size="small"
                theme="transparent"
                className="px-4! py-1.5! !border"
                onClick={() => setShowModal(false)}
              >
                Cancel
              </Button>
              <Button
                size="small"
                theme="dark"
                className="px-4! py-1.5! text-white!"
                onClick={() => {
                  setShowModal(false)
                  isAppliedTab ? handleWithdraw() : router.push('/resume')
                }}
              >
                {isAppliedTab ? 'Withdraw' : 'Go to Resume'}
              </Button>
            </div>
          </div>
        </Overlay>
      )}
    </>
  )
}

export default JobCard
