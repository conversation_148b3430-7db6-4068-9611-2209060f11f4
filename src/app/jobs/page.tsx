'use client'
import React, { useEffect, useState } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import Pagination from '../../components/Pagination'
import { SrpService } from '../../services/SrpService'
import { Job } from '../../models/Job'
import { useAuth } from '../../context/AuthContext'
import { Paginator } from '../../models/Paginator'
import JobCard from './JobCard'
import RecruiterQuestionnaire from './RecruiterQuestionnaire '
import JobCardSkeleton from './JobCardSkeleton'
import JobFilter from './JobsFilter'
import NoDataFound from '../../components/NoDataFound/NoDataFound'
import useError from '../../context/ErrorContext'
import { useToast } from '../../components/ToastX'
import { generateFilterUrl } from './utils/urlUtils'
import { FunnelIcon, XIcon } from '@phosphor-icons/react'
import { useDebouncedFilters } from './utils/useDebouncedFilters'
import { FilterState } from './utils/urlUtils'

const defaultFilterState: FilterState = {
  selectedSkills: [],
  selectedJobType: [],
  selectedLocations: [],
  selectedWorkExperience: undefined,
  currentPage: 1,
}

// Helper to wrap set functions to match React.Dispatch<SetStateAction<T>>
function toDispatch<T>(setter: (value: T) => void): React.Dispatch<React.SetStateAction<T>> {
  return (valueOrUpdater) => {
    // Only allow direct value assignment, not updater functions
    setter(valueOrUpdater as T)
  }
}

const Jobs: React.FC = () => {
  const { isAuthenticated } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const { handleError } = useError()
  const toast = useToast()

  const [activeTab, setActiveTab] = useState('Jobs')
  const [jobPreviewData, setJobPreviewData] = useState<Job | undefined>(undefined)
  const [showJobDetails, setShowJobDetails] = useState(false)
  const [showQuestionnaire, setShowQuestionnaire] = useState(false)

  const [jobs, setJobs] = useState<Job[]>([])
  const [paginator, setPaginator] = useState<Paginator | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [showFilters, setShowFilters] = useState<boolean>(false)

  const {
    filters,
    setSelectedSkills,
    setSelectedJobType,
    setSelectedLocations,
    setSelectedWorkExperience,
    setCurrentPage,
    resetFilters,
    debouncedFilters,
  } = useDebouncedFilters(defaultFilterState, 300)

  useEffect(() => {
    // Only update URL if we have filters
    if (
      debouncedFilters.selectedSkills.length ||
      debouncedFilters.selectedJobType.length ||
      debouncedFilters.selectedLocations.length ||
      debouncedFilters.selectedWorkExperience
    ) {
      const newPath = generateFilterUrl({
        selectedSkills: debouncedFilters.selectedSkills,
        selectedJobType: debouncedFilters.selectedJobType,
        selectedLocations: debouncedFilters.selectedLocations,
        selectedWorkExperience: debouncedFilters.selectedWorkExperience,
        currentPage: debouncedFilters.currentPage,
      })

      if (pathname !== newPath) {
        router.replace(newPath)
      }
    } else {
      // If no filters, stay on the main jobs page
      if (pathname !== '/jobs') {
        router.replace('/jobs')
      }
    }
  }, [
    debouncedFilters.selectedSkills,
    debouncedFilters.selectedLocations,
    debouncedFilters.selectedJobType,
    debouncedFilters.selectedWorkExperience,
    debouncedFilters.currentPage,
  ])

  useEffect(() => {
    const fetchJobs = async () => {
      setLoading(true)
      setError(null)
      const experienceRanges: Record<string, { min: number; max: number }> = {
        Fresher: { min: 0, max: 1 },
        '1-3 years': { min: 1, max: 3 },
        '3-6 years': { min: 3, max: 6 },
        '6-9 years': { min: 6, max: 9 },
        '9-12 years': { min: 9, max: 12 },
        '>12 years': { min: 12, max: 50 },
      }
      const filtersForApi: any = {
        skills: debouncedFilters.selectedSkills.length
          ? debouncedFilters.selectedSkills
          : undefined,
        locations: debouncedFilters.selectedLocations.length
          ? debouncedFilters.selectedLocations
          : undefined,
        employmentTypes: debouncedFilters.selectedJobType.length
          ? debouncedFilters.selectedJobType.map((type) => type.toUpperCase().replace(/ /g, '_'))
          : undefined,
        workExperience: debouncedFilters.selectedWorkExperience
          ? experienceRanges[debouncedFilters.selectedWorkExperience]
          : undefined,
      }
      const actualFilters = Object.fromEntries(
        Object.entries(filtersForApi).filter(([_, value]) => value !== undefined),
      )
      try {
        const response = await SrpService.getJobs(debouncedFilters.currentPage, actualFilters)
        const { items, paginator: pg } = response.data
        setJobs(items || [])
        setPaginator(pg || null)
      } catch (err: any) {
        handleError(err)
      } finally {
        setLoading(false)
      }
    }
    fetchJobs()
  }, [debouncedFilters])

  const handlePageChange = (newPage: number) => {
    if (newPage !== filters.currentPage) {
      setCurrentPage(newPage)
    }
  }

  const handleApplyJob = (job: Job) => {
    if (!isAuthenticated) router.push('/sign-in')
    else {
      if ((job as any).questionnaire) {
        setShowQuestionnaire(true)
        setJobPreviewData(job)
      }
    }
  }

  const handleJobCardClick = (job: Job) => {
    setShowJobDetails(true)
    setJobPreviewData(job)
    router.push(`/job/${job?.job?.permalink}`)
  }

  const handleRepostJob = (job: Job) => {
    if (!isAuthenticated) router.push('/sign-in')
  }

  const handleResetFilters = () => {
    resetFilters()
  }

  const handleWithdrawJob = async (job: Job) => {
    if (!isAuthenticated) {
      router.push('/sign-in')
    } else {
      try {
        // After successful withdrawal, update the jobs list
        setJobs(jobs.filter((j) => j.job.id !== job.job.id))
        toast.success('Application withdrawn successfully')
      } catch (err: any) {
        handleError(err)
      }
    }
  }

  return (
    <section className="font-inter flex h-full w-full py-12 lg:mt-12  lg:py-0">
      <div className="flex w-full items-start justify-center   px-2 lg:max-w-[79%] lg:justify-end  lg:px-4  xl:max-w-[70%] ">
        <div className="dark:border-dark-lucres-black-300 min-h-screen w-full max-w-[700px] pb-8 md:border-x lg:pb-0 ">
          {isAuthenticated && (
            <div className="mx-auto flex w-full items-center justify-between px-6 pt-6 sm:w-9/12">
              <span
                className={`cursor-pointer pb-2 ${
                  activeTab === 'Jobs' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => {
                  setActiveTab('Jobs')
                  router.push('/jobs')
                }}
              >
                Jobs for You
              </span>
              <span
                className={`cursor-pointer pb-2 ${
                  activeTab === 'Applied' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => {
                  setActiveTab('Applied')
                  router.push('/jobs/applied-jobs')
                }}
              >
                Jobs Applied
              </span>
              <span
                className={`cursor-pointer pb-2 ${
                  activeTab === 'Saved' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => {
                  setActiveTab('Saved')
                  router.push('/jobs/saved-jobs')
                }}
              >
                Jobs Saved
              </span>
            </div>
          )}
          {activeTab === 'Jobs' && (
            <>
              <div
                className={`mb-6 flex w-full flex-col items-center border-t sm:items-start md:px-6 ${
                  isAuthenticated ? '' : 'md:pt-3'
                } dark:border-dark-lucres-black-300`}
              ></div>
              <div className="flex flex-col gap-y-4">
                {loading ? (
                  <>
                    {Array.from({ length: 10 }).map((_, index) => (
                      <JobCardSkeleton key={index} />
                    ))}
                  </>
                ) : error ? (
                  <div>{error}</div>
                ) : jobs.length ? (
                  jobs.map((jobItem, index) => {
                    return (
                      <div key={index} className="md:mx-4">
                        <JobCard
                          job={jobItem}
                          onApply={handleApplyJob}
                          onRepost={handleRepostJob}
                          onClick={handleJobCardClick}
                        />
                      </div>
                    )
                  })
                ) : (
                  <NoDataFound />
                )}
              </div>
              {paginator && paginator.pageCount > 1 && (
                <div className="py-6">
                  <Pagination paginator={paginator} onPageChange={handlePageChange} />
                </div>
              )}
              <span
                onClick={() => setShowFilters(!showFilters)}
                className="bg-lucres-green-200 dark:bg-dark-lucres-black-300  fixed bottom-24 right-11 z-50 flex h-14 w-14 cursor-pointer items-center justify-center rounded-full transition-transform duration-200 ease-in-out lg:hidden"
              >
                <span
                  className={`transform transition-all duration-300 ease-in-out ${
                    showFilters ? 'rotate-90 scale-100 opacity-100' : 'absolute scale-50 opacity-0'
                  }`}
                >
                  <XIcon size={24} />
                </span>
                <span
                  className={`transform transition-all duration-300 ease-in-out ${
                    showFilters ? 'absolute scale-50 opacity-0' : 'scale-100 opacity-100'
                  }`}
                >
                  <FunnelIcon size={24} />
                </span>
              </span>
            </>
          )}
        </div>
      </div>
      {activeTab === 'Jobs' && (
        <div className="dark:bg-dark-lucres-black-500 hidden flex-col items-start justify-start gap-4 rounded-lg bg-white p-4 pb-8 pt-4 lg:flex">
          <JobFilter
            selectedSkills={debouncedFilters.selectedSkills}
            setSelectedSkills={toDispatch(setSelectedSkills)}
            selectedLocations={debouncedFilters.selectedLocations}
            setSelectedLocations={toDispatch(setSelectedLocations)}
            selectedJobType={debouncedFilters.selectedJobType}
            setSelectedJobType={toDispatch(setSelectedJobType)}
            selectedSeniority={debouncedFilters.selectedWorkExperience}
            setSelectedSeniority={toDispatch(setSelectedWorkExperience)}
            handleResetFilters={handleResetFilters}
          />
        </div>
      )}

      {showQuestionnaire && jobPreviewData && (
        <div className="dark:bg-dark-lucres-black-500 fixed left-0 top-0 z-50 flex h-screen w-full items-center justify-center bg-black bg-opacity-75">
          <RecruiterQuestionnaire
            showQuestionnaire={showQuestionnaire}
            setShowQuestionnaire={setShowQuestionnaire}
            jobPreviewData={jobPreviewData}
            handleApplyJob={handleApplyJob}
          />
        </div>
      )}
      {showFilters && (
        <div
          className="fixed left-0 top-0 z-40 flex h-screen w-full items-center justify-center rounded-lg bg-black bg-opacity-40 lg:hidden"
          onClick={() => setShowFilters(false)}
        >
          <div
            className="dark:bg-dark-lucres-black-500 flex w-9/12 flex-col items-end justify-start gap-4 rounded-lg bg-white px-8 py-6"
            onClick={(e) => e.stopPropagation()}
          >
            <JobFilter
              selectedSkills={debouncedFilters.selectedSkills}
              setSelectedSkills={toDispatch(setSelectedSkills)}
              selectedLocations={debouncedFilters.selectedLocations}
              setSelectedLocations={toDispatch(setSelectedLocations)}
              selectedJobType={debouncedFilters.selectedJobType}
              setSelectedJobType={toDispatch(setSelectedJobType)}
              selectedSeniority={debouncedFilters.selectedWorkExperience}
              setSelectedSeniority={toDispatch(setSelectedWorkExperience)}
              handleResetFilters={handleResetFilters}
            />
          </div>
        </div>
      )}
    </section>
  )
}

export default Jobs
