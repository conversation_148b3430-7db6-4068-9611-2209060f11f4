'use client'
import { ChangeEvent, FC, useCallback, useEffect, useState, useRef } from 'react'
import Select from '../../components/Select'

interface SalaryRangeSliderProps {
  min: number
  max: number
  onChange: Function
}

const SalaryRangeSlider: FC<SalaryRangeSliderProps> = ({ min, max, onChange }) => {
  const [minVal, setMinVal] = useState<number>(min)
  const [maxVal, setMaxVal] = useState<number>(max)
  const minValRef = useRef<HTMLInputElement>(null)
  const maxValRef = useRef<HTMLInputElement>(null)
  const range = useRef<HTMLDivElement>(null)
  const [selectedSalary, setSelectedSalary] = useState<string>('monthly') // Track selected salary
  const salaryOptions = [
    { value: 'monthly', label: 'Monthly' },
    { value: 'anually', label: 'Anually' },
  ]

  // Convert to percentage
  const getPercent = useCallback(
    (value: number) => Math.round(((value - min) / (max - min)) * 100),
    [min, max],
  )
  const handleSalaryChange = (value: string) => {
    setSelectedSalary(value)
  }
  // Set width of the range to decrease from the left side
  useEffect(() => {
    if (maxValRef.current) {
      const minPercent = getPercent(minVal)
      const maxPercent = getPercent(+maxValRef.current.value) // Precede with '+' to convert the value from type string to type number

      if (range.current) {
        range.current.style.left = `${minPercent}%`
        range.current.style.width = `${maxPercent - minPercent}%`
      }
    }
  }, [minVal, getPercent])

  // Set width of the range to decrease from the right side
  useEffect(() => {
    if (minValRef.current) {
      const minPercent = getPercent(+minValRef.current.value)
      const maxPercent = getPercent(maxVal)

      if (range.current) {
        range.current.style.width = `${maxPercent - minPercent}%`
      }
    }
  }, [maxVal, getPercent])

  // Get min and max values when their state changes
  useEffect(() => {
    onChange({ min: minVal, max: maxVal })
  }, [minVal, maxVal, onChange])

  return (
    <div className="relative flex flex-col">
      <div className="flex items-center gap-x-2 text-xs">
        <span className="text-lucres-950 dark:text-dark-lucres-green-100 whitespace-nowrap text-lg font-bold">
          Annual Salary
        </span>
        <Select
          options={salaryOptions}
          value={selectedSalary}
          onChange={handleSalaryChange}
          placeholder="Monthly"
          classes="px-2! py-1! gap-x-2 mt-0!"
        />
      </div>
      <div className="mb-3 mt-4 flex w-full justify-between">
        <span className="text-lucres-950 text-xs dark:text-gray-300">{minVal}</span>
        <span className="text-lucres-950 text-xs dark:text-gray-300">{maxVal}</span>
      </div>
      <input
        type="range"
        min={min}
        max={max}
        value={minVal}
        ref={minValRef}
        onChange={(event: ChangeEvent<HTMLInputElement>) => {
          const value = Math.min(+event.target.value, maxVal - 1)
          setMinVal(value)
          event.target.value = value.toString()
        }}
        className={`thumb z-30! outline-hidden pointer-events-none absolute bottom-px h-0 w-full ${
          minVal > max - 100 ? 'z-50' : ''
        }`}
      />
      <input
        type="range"
        min={min}
        max={max}
        value={maxVal}
        ref={maxValRef}
        onChange={(event: ChangeEvent<HTMLInputElement>) => {
          const value = Math.max(+event.target.value, minVal + 1)
          setMaxVal(value)
          event.target.value = value.toString()
        }}
        className="thumb bottom-px z-40 w-full"
      />

      <div className="w-full">
        <div className="bg-lucres-500 z-10! absolute h-0.5 w-full rounded-md"></div>
        <div ref={range} className="absolute z-10 h-2 rounded-md"></div>
      </div>
    </div>
  )
}

export default SalaryRangeSlider
