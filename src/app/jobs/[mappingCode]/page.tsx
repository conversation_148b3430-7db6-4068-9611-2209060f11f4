'use client'
import React, { useEffect, useState, useRef } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import Pagination from '../../../components/Pagination'
import { SrpService } from '../../../services/SrpService'
import { Job } from '../../../models/Job'
import { useAuth } from '../../../context/AuthContext'
import { Paginator } from '../../../models/Paginator'
import JobCard from '../JobCard'
import RecruiterQuestionnaire from '../RecruiterQuestionnaire '
import JobCardSkeleton from '../JobCardSkeleton'
import { Funnel, X, XIcon } from '@phosphor-icons/react'
import JobFilter from '../JobsFilter'
import NoDataFound from '../../../components/NoDataFound/NoDataFound'
import useError from '../../../context/ErrorContext'
import { useToast } from '../../../components/ToastX'
import { generateFilterUrl, parseFilterUrl, FilterState } from '../utils/urlUtils'
import { useDebouncedFilters } from '../utils/useDebouncedFilters'

function isEqual(a: any, b: any) {
  return JSON.stringify(a) === JSON.stringify(b)
}

const defaultFilterState: FilterState = {
  selectedSkills: [],
  selectedJobType: [],
  selectedLocations: [],
  selectedWorkExperience: undefined,
  currentPage: 1,
}

// Helper to wrap set functions to match React.Dispatch<SetStateAction<T>>
function toDispatch<T>(setter: (value: T) => void): React.Dispatch<React.SetStateAction<T>> {
  return (valueOrUpdater) => {
    setter(valueOrUpdater as T)
  }
}

const JobsWithFilters: React.FC = () => {
  const { isAuthenticated } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const { handleError } = useError()
  const toast = useToast()

  const [activeTab, setActiveTab] = useState('Jobs')
  const [jobPreviewData, setJobPreviewData] = useState<Job | undefined>(undefined)
  const [showJobDetails, setShowJobDetails] = useState(false)
  const [showQuestionnaire, setShowQuestionnaire] = useState(false)

  const [savedJobs, setSavedJobs] = useState<Job[]>([])
  const [jobs, setJobs] = useState<Job[]>([])
  const [paginator, setPaginator] = useState<Paginator | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [showFilters, setShowFilters] = useState<boolean>(false)

  const {
    filters,
    setSelectedSkills,
    setSelectedJobType,
    setSelectedLocations,
    setSelectedWorkExperience,
    setCurrentPage,
    resetFilters,
    debouncedFilters,
  } = useDebouncedFilters(defaultFilterState, 300)

  const isSyncingFromUrl = useRef(false)

  // Parse URL parameters on component mount or navigation
  useEffect(() => {
    const filterState = parseFilterUrl(pathname)
    if (
      !isEqual(filterState, {
        selectedSkills: filters.selectedSkills,
        selectedJobType: filters.selectedJobType,
        selectedLocations: filters.selectedLocations,
        selectedWorkExperience: filters.selectedWorkExperience,
        currentPage: filters.currentPage,
      })
    ) {
      isSyncingFromUrl.current = true
      setSelectedSkills(filterState.selectedSkills)
      setSelectedJobType(filterState.selectedJobType)
      setSelectedLocations(filterState.selectedLocations)
      setSelectedWorkExperience(filterState.selectedWorkExperience)
      setCurrentPage(filterState.currentPage)
    }
  }, [pathname])

  // Update URL when filters change, but not if just synced from URL
  useEffect(() => {
    if (isSyncingFromUrl.current) {
      isSyncingFromUrl.current = false
      return
    }
    const newPath = generateFilterUrl({
      selectedSkills: filters.selectedSkills,
      selectedLocations: filters.selectedLocations,
      selectedJobType: filters.selectedJobType,
      selectedWorkExperience: filters.selectedWorkExperience,
      currentPage: filters.currentPage,
    })
    if (pathname !== newPath) {
      router.replace(newPath)
    }
  }, [
    filters.selectedSkills,
    filters.selectedLocations,
    filters.selectedJobType,
    filters.selectedWorkExperience,
    filters.currentPage,
  ])

  // Fetch jobs when debounced filters change
  useEffect(() => {
    setLoading(true)
    setError(null)
    const experienceRanges: Record<string, { min: number; max: number }> = {
      Fresher: { min: 0, max: 1 },
      '1-3 years': { min: 1, max: 3 },
      '3-6 years': { min: 3, max: 6 },
      '6-9 years': { min: 6, max: 9 },
      '9-12 years': { min: 9, max: 12 },
      '>12 years': { min: 12, max: 50 },
    }
    const filtersForApi: any = {
      skills: debouncedFilters.selectedSkills.length ? debouncedFilters.selectedSkills : undefined,
      locations: debouncedFilters.selectedLocations.length
        ? debouncedFilters.selectedLocations
        : undefined,
      employmentTypes: debouncedFilters.selectedJobType.length
        ? debouncedFilters.selectedJobType.map((type) => type.toUpperCase().replace(/ /g, '_'))
        : undefined,
      workExperience: debouncedFilters.selectedWorkExperience
        ? experienceRanges[debouncedFilters.selectedWorkExperience]
        : undefined,
    }
    const actualFilters = Object.fromEntries(
      Object.entries(filtersForApi).filter(([_, value]) => value !== undefined),
    )
    SrpService.getJobs(debouncedFilters.currentPage, actualFilters)
      .then((response) => {
        const { items, paginator: pg } = response.data
        setJobs(items || [])
        setPaginator(pg || null)
      })
      .catch(handleError)
      .finally(() => setLoading(false))
  }, [debouncedFilters, handleError])

  const handlePageChange = (newPage: number) => {
    if (newPage !== filters.currentPage) {
      setCurrentPage(newPage)
    }
  }

  const handleApplyJob = (job: Job) => {
    if (!isAuthenticated) router.push('/sign-in')
    else {
      if ((job as any).questionnaire) {
        setShowQuestionnaire(true)
        setJobPreviewData(job)
      }
    }
  }

  const handleSavedJob = (job: Job) => {
    if (!isAuthenticated) {
      router.push('/sign-in')
    } else {
      const isAlreadySaved = savedJobs.some((savedJob) => savedJob.job.id === job.job.id)
      if (isAlreadySaved) {
        setSavedJobs(savedJobs.filter((savedJob) => savedJob.job.id !== job.job.id))
      } else {
        setSavedJobs([...savedJobs, job])
      }
    }
  }

  const handleSkillTagClick = (skill: string) => {
    if (!debouncedFilters.selectedSkills.includes(skill)) {
      setSelectedSkills([...debouncedFilters.selectedSkills, skill])
    }
  }

  const handleJobCardClick = (job: Job) => {
    setShowJobDetails(true)
    setJobPreviewData(job)
    router.push(`/job/${job?.job?.permalink}`)
  }

  const handleRepostJob = (job: Job) => {
    if (!isAuthenticated) router.push('/sign-in')
  }

  const handleResetFilters = () => {
    resetFilters()
  }

  return (
    <section className="font-inter flex h-full w-full py-12 lg:mt-12  lg:py-0">
      <div className="flex w-full items-start justify-center   px-2 lg:max-w-[79%] lg:justify-end  lg:px-4  xl:max-w-[70%] ">
        <div className="dark:border-dark-lucres-black-300 min-h-screen w-full max-w-[700px] pb-8 md:border-x lg:pb-0 ">
          {isAuthenticated && (
            <div className="mx-auto flex w-full items-center justify-between px-6 pt-6 sm:w-9/12">
              <span
                className={`cursor-pointer pb-2 ${
                  activeTab === 'Jobs' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => setActiveTab('Jobs')}
              >
                Jobs for You
              </span>
              <span
                className={`cursor-pointer pb-2 ${
                  activeTab === 'Applied' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => router.push('/jobs/applied-jobs')}
              >
                Jobs Applied
              </span>
              <span
                className={`cursor-pointer pb-2 ${
                  activeTab === 'Saved' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => router.push('/jobs/saved-jobs')}
              >
                Jobs Saved
              </span>
            </div>
          )}
          <div
            className={`mb-6 flex w-full flex-col items-center border-t sm:items-start md:px-6 ${
              isAuthenticated ? '' : 'md:pt-3'
            } dark:border-dark-lucres-black-300`}
          ></div>
          <div className="flex flex-col gap-y-4">
            {loading ? (
              <>
                {Array.from({ length: 10 }).map((_, index) => (
                  <JobCardSkeleton key={index} />
                ))}
              </>
            ) : error ? (
              <div>{error}</div>
            ) : jobs.length ? (
              jobs.map((jobItem, index) => {
                return (
                  <div key={index} className="md:mx-4">
                    <JobCard
                      job={jobItem}
                      onApply={handleApplyJob}
                      onSave={handleSavedJob}
                      onSkillTagClick={handleSkillTagClick}
                      onRepost={handleRepostJob}
                      onClick={handleJobCardClick}
                    />
                  </div>
                )
              })
            ) : (
              <NoDataFound />
            )}
          </div>
          {paginator && paginator.pageCount > 1 && (
            <div className="py-6">
              <Pagination paginator={paginator} onPageChange={handlePageChange} />
            </div>
          )}
          <span
            onClick={() => setShowFilters(!showFilters)}
            className="bg-lucres-green-200 dark:bg-dark-lucres-black-300 fixed bottom-24 right-11 z-50 flex h-14 w-14 cursor-pointer items-center justify-center rounded-full transition-transform duration-200 ease-in-out lg:hidden"
          >
            <span
              className={`transform transition-all duration-300 ease-in-out ${
                showFilters ? 'rotate-90 scale-100 opacity-100' : 'absolute scale-50 opacity-0'
              }`}
            >
              <XIcon size={24} />
            </span>
            <span
              className={`transform transition-all duration-300 ease-in-out ${
                showFilters ? 'absolute scale-50 opacity-0' : 'scale-100 opacity-100'
              }`}
            >
              <Funnel size={24} />
            </span>
          </span>
        </div>
      </div>
      <div className="dark:bg-dark-lucres-black-500   hidden flex-col items-start justify-start gap-4 rounded-lg bg-white p-4 pb-8 pt-4 lg:flex">
        <JobFilter
          selectedSkills={filters.selectedSkills}
          setSelectedSkills={toDispatch(setSelectedSkills)}
          selectedLocations={filters.selectedLocations}
          setSelectedLocations={toDispatch(setSelectedLocations)}
          selectedJobType={filters.selectedJobType}
          setSelectedJobType={toDispatch(setSelectedJobType)}
          selectedSeniority={filters.selectedWorkExperience}
          setSelectedSeniority={toDispatch(setSelectedWorkExperience)}
          handleResetFilters={handleResetFilters}
        />
      </div>

      {showQuestionnaire && jobPreviewData && (
        <div className="dark:bg-dark-lucres-black-500 fixed left-0 top-0 z-50 flex h-screen w-full items-center justify-center bg-black bg-opacity-75">
          <RecruiterQuestionnaire
            showQuestionnaire={showQuestionnaire}
            setShowQuestionnaire={setShowQuestionnaire}
            jobPreviewData={jobPreviewData}
            handleApplyJob={handleApplyJob}
          />
        </div>
      )}
      {showFilters && (
        <div
          className="fixed left-0 top-0 z-40 flex h-screen w-full items-center justify-center rounded-lg bg-black bg-opacity-40 lg:hidden"
          onClick={() => setShowFilters(false)}
        >
          <div
            className="dark:bg-dark-lucres-black-500 flex w-9/12 flex-col items-end justify-start gap-4 rounded-lg bg-white px-8 py-6"
            onClick={(e) => e.stopPropagation()}
          >
            <JobFilter
              selectedSkills={filters.selectedSkills}
              setSelectedSkills={toDispatch(setSelectedSkills)}
              selectedLocations={filters.selectedLocations}
              setSelectedLocations={toDispatch(setSelectedLocations)}
              selectedJobType={filters.selectedJobType}
              setSelectedJobType={toDispatch(setSelectedJobType)}
              selectedSeniority={filters.selectedWorkExperience}
              setSelectedSeniority={toDispatch(setSelectedWorkExperience)}
              handleResetFilters={handleResetFilters}
            />
          </div>
        </div>
      )}
    </section>
  )
}

export default JobsWithFilters
