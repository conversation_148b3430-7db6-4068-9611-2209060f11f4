//! This Questions will show up when user apply to job
import { useEffect, useRef, useState } from 'react'
import Tag from '../../components/Tag'
import CustomCheckbox from '../../components/CheckBox'
import Button from '../../components/Button'
import { Job } from '../../models/Job'

// Extend Job to include an optional questionnaire
export interface Questionnaire {
  description: string
  questions: {
    id: number
    question: string
    required: boolean
    type: 'mcq' | 'checkbox' | 'textarea'
    options?: string[]
  }[]
}

interface RecruiterQuestionnaireProps {
  showQuestionnaire: boolean
  setShowQuestionnaire: (show: boolean) => void
  // Extend jobPreviewData with an optional questionnaire property
  jobPreviewData: Job & { questionnaire?: Questionnaire }
  handleApplyJob?: (job: Job, event: React.MouseEvent) => void
}

export default function RecruiterQuestionnaire({
  showQuestionnaire,
  setShowQuestionnaire,
  jobPreviewData,
  handleApplyJob,
}: RecruiterQuestionnaireProps) {
  const questionRef = useRef<HTMLDivElement>(null)

  // Lock body scroll when questionnaire is open
  useEffect(() => {
    if (showQuestionnaire) {
      document.body.classList.add('overflow-hidden')
    } else {
      document.body.classList.remove('overflow-hidden')
    }
    return () => {
      document.body.classList.remove('overflow-hidden')
    }
  }, [showQuestionnaire])

  // Close questionnaire when clicking outside of it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (questionRef.current && !questionRef.current.contains(event.target as Node)) {
        setShowQuestionnaire(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [setShowQuestionnaire])

  const [responses, setResponses] = useState<Record<number, string | string[]>>({})

  const questionnaireData = jobPreviewData.questionnaire

  const handleChange = (id: number, value: string, type: string) => {
    setResponses((prev) => {
      if (type === 'checkbox') {
        const updatedCheckboxes = prev[id] ? [...(prev[id] as string[])] : []
        if (updatedCheckboxes.includes(value)) {
          return {
            ...prev,
            [id]: updatedCheckboxes.filter((item) => item !== value),
          }
        } else {
          return { ...prev, [id]: [...updatedCheckboxes, value] }
        }
      } else {
        return { ...prev, [id]: value }
      }
    })
  }

  const totalQuestions = questionnaireData?.questions.length || 0
  const answeredQuestions = Object.keys(responses).length
  const progress = totalQuestions > 0 ? (answeredQuestions / totalQuestions) * 100 : 0
  const hasRequiredQuestions = questionnaireData?.questions.some((q) => q.required)

  return (
    <div className="mx-auto flex h-full w-11/12 items-center justify-center sm:h-[95vh] sm:w-full">
      <div
        ref={questionRef}
        className="scrollbar-none dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 h-full w-full max-w-3xl overflow-y-scroll rounded-xl bg-white shadow-lg dark:border"
      >
        <div className="bg-lucres-400 dark:bg-dark-lucres-black-200 flex h-20 items-end justify-start rounded-t-xl bg-opacity-40 pb-4 ps-4">
          <Tag theme="primary" TagText="Recruiter Questionnaire" />
        </div>
        <div className="mx-auto w-11/12 p-4">
          {/* Header */}
          <div className="relative flex w-full items-center">
            <div className="w-9/12">
              <h2 className="text-xl font-semibold">Beyond the Resume</h2>
              <p className="text-lucres-gray-500 mt-2 text-sm">{questionnaireData?.description}</p>
            </div>
            <div>
              <img
                src="/dashboard/questionnaire.svg"
                alt="Questionnaire"
                className="absolute -right-6 -top-14 h-auto w-48"
              />
            </div>
          </div>
          <div className="dark:bg-dark-lucres-black-500 sticky top-0 z-50 flex h-20 w-full flex-col items-center justify-center bg-white px-1">
            <div className="flex w-full items-center justify-between text-sm">
              <span>{Math.round(progress)}%</span>
              <span>
                {answeredQuestions} / {totalQuestions} Completed
              </span>
            </div>
            <div className="bg-lucres-gray-200 dark:bg-dark-lucres-black-300 relative mt-2 h-1 w-full rounded-lg">
              <span
                className="from-lucres-400 bg-linear-to-r absolute left-0 top-0 h-full rounded-lg to-[#E4FFC1]"
                style={{ width: `${progress}%` }}
              ></span>
            </div>
          </div>

          {/* Render Questions */}
          {questionnaireData?.questions.map((q) => (
            <div
              key={q.id}
              className="dark:border-dark-lucres-black-200 mt-4 rounded-lg border p-4"
            >
              <div className="relative w-fit text-gray-600">
                <h4 className="dark:text-lucres-gray-600 mb-2 w-fit text-sm">Question {q.id}</h4>
                {q.required && <span className="absolute -top-1 left-[71px] text-sm">*</span>}
              </div>
              <p className="bg-lucres-gray-300 text-lucres-black dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 dark:text-dark-lucres-green-100 rounded-lg p-2 dark:border">
                {q.question}
              </p>

              {/* Multiple Choice */}
              {q.type === 'mcq' && (
                <div className="mt-3 gap-y-2">
                  {q.options?.map((option) => (
                    <label
                      key={option}
                      className={`flex cursor-pointer items-center rounded-md p-2 text-gray-600 transition-all ${
                        responses[q.id] === option
                          ? 'border-lucres-400 bg-lucres-400 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-400 border bg-opacity-25 dark:bg-opacity-100'
                          : ''
                      }`}
                      onClick={() => handleChange(q.id, option, 'mcq')}
                    >
                      <input
                        type="radio"
                        name={`q${q.id}`}
                        value={option}
                        checked={responses[q.id] === option}
                        readOnly
                        className="hidden"
                      />
                      <span
                        className={`flex h-3 w-3 items-center justify-center rounded-full border-2 transition-all ${
                          responses[q.id] === option
                            ? 'border-lucres-700 bg-lucres-700'
                            : 'dark:border-lucres-gray-600 border-gray-400'
                        }`}
                      ></span>
                      <span className="dark:text-lucres-gray-600 ml-2 text-gray-600">{option}</span>
                    </label>
                  ))}
                </div>
              )}

              {/* Checkbox */}
              {q.type === 'checkbox' && (
                <div className="mt-4 flex flex-col gap-y-2">
                  {q.options?.map((option, idx) => (
                    <label
                      key={idx}
                      className={`flex cursor-pointer items-center rounded-md p-2 text-gray-600 transition-all ${
                        responses[q.id]?.includes(option)
                          ? 'border-lucres-400 bg-lucres-400 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-400 border bg-opacity-25 dark:bg-opacity-100'
                          : ''
                      }`}
                    >
                      <CustomCheckbox
                        label={option}
                        checked={responses[q.id]?.includes(option) || false}
                        onClick={() => handleChange(q.id, option, 'checkbox')}
                        classname="text-base"
                      />
                    </label>
                  ))}
                </div>
              )}

              {/* Textarea */}
              {q.type === 'textarea' && (
                <textarea
                  className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 dark:placeholder:text-lucres-gray-600 focus:outline-hidden mt-4 h-24 w-full resize-none rounded-lg border p-3"
                  placeholder="Type your response here..."
                  onChange={(e) => handleChange(q.id, e.target.value, 'textarea')}
                ></textarea>
              )}
            </div>
          ))}

          {/* Buttons */}
          <div className="mt-6 flex w-full justify-end">
            {hasRequiredQuestions ? (
              <Button size="small" theme="white" onClick={() => setShowQuestionnaire(false)}>
                Cancel
              </Button>
            ) : (
              <Button
                size="small"
                theme="white"
                onClick={(e) => handleApplyJob?.(jobPreviewData, e)}
              >
                Skip & Apply
              </Button>
            )}
            <Button size="small" theme="opaque" isRectangle>
              Submit
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
