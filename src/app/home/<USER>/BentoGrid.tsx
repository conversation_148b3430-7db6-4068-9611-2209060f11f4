import EarnWhileTweet from './earnwhiletweet/EarnWhileTweet'
import DiscoverJobs from './discoverjobs/DiscoverJobs'
import ExpandYourNetwork from './expandyournetwork/ExpandYourNetwork'
import MonetiseInfluence from './monetiseinfluence/MonetiseInfluence'
import Tag from '../../../components/Tag'

const BentoGrid = () => {
  return (
    <div className="w-full">
      <div className="p-10 pb-0 text-center">
        <Tag theme="primary" TagText="Feature Rich Lucres" />
        <h1 className="mt-4 text-4xl font-medium">
          How it works?
          <span className="font-normal"> It's Crazy simple</span>
        </h1>
      </div>
      <div className="min-1140:my-8 min-1140:scale-100 m-auto flex w-full max-w-[1440px] grid-cols-11 grid-rows-2 flex-col gap-8 px-4 md:w-8/12 lg:grid lg:h-2/3 lg:scale-90">
        <div className="bg-lucres-100 min-676:grid min-676:min-h-72 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 relative col-span-7 row-span-1 block grid-cols-5 gap-2 overflow-hidden rounded-xl p-8 dark:border">
          <DiscoverJobs />
        </div>
        <div className="bg-lucres-100 min-676:grid min-676:min-h-48 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 relative col-span-4 row-span-1 block grid-cols-4 overflow-hidden rounded-xl p-8 lg:grid-cols-none lg:grid-rows-5 dark:border">
          <ExpandYourNetwork />
        </div>
        <div className="bg-lucres-100 min-676:grid min-676:min-h-80 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 relative col-span-6 row-span-1 block grid-cols-6 gap-2 overflow-hidden rounded-xl p-8 dark:border">
          <MonetiseInfluence />
        </div>
        <div className="bg-lucres-100 min-676:grid min-676:min-h-48 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 relative col-span-5 row-span-1 block grid-cols-4 overflow-hidden rounded-xl p-8 lg:block dark:border">
          <EarnWhileTweet />
        </div>
      </div>
    </div>
  )
}

export default BentoGrid
