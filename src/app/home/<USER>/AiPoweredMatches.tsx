// "use client";

import Tag from '../../../components/Tag'
// import { useRouter } from "next/navigation";

const AiPoweredMatches = () => {
  // const router = useRouter();
  return (
    <div className="relative mx-auto mt-24 w-full max-w-6xl py-12 md:py-24 xl:scale-100">
      <div className="relative z-20 text-center">
        <Tag TagText="Didn't match - no worries!" />
        <div className="min-1140:w-1/2 m-auto w-full px-8 text-center">
          <div className="pt-5">
            <h1 className="text-2xl font-medium md:text-4xl">AI Powered Matches</h1>
          </div>
          <div className="min-1140:px-0 px-8 py-4 pb-6 text-sm font-normal md:px-20 md:py-6 md:pb-8 md:text-base">
            Find the perfect job or candidate with our advanced AI matching. Our AI model evaluates
            your work experience, skills, and personality.
          </div>
        </div>
      </div>
      <div className="min-1140:block absolute left-40 top-28 z-10 hidden">
        <img
          src={'/aipoweredmatches/LeftLines.svg'}
          width={224}
          height={120}
          alt="left"
          className="relative z-0"
        />
        <div className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 absolute -left-[136px] -top-[6px] flex items-center rounded-sm border-[0.2px] border-solid border-[rgba(155,201,95,0.3)] bg-white px-4 py-[2px]">
          Account Management
        </div>
        <div className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 absolute -left-20 top-[50px] flex items-center rounded-sm border-[0.2px] border-solid border-[rgba(155,201,95,0.3)] bg-white px-4 py-[2px]">
          Project Planning
        </div>
        <div className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 absolute -left-40 top-[104px] flex items-center rounded-sm border-[0.2px] border-solid border-[rgba(155,201,95,0.3)] bg-white px-4 py-[2px]">
          Inventory Management
        </div>
      </div>
      <div className="min-1140:block absolute right-48 top-28 z-10 mt-2 hidden">
        <img
          src={'/aipoweredmatches/RightLines.svg'}
          width={189}
          height={122}
          alt="left"
          className="relative z-0"
        />
        <div className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 absolute -right-[116px] -top-4 flex items-center rounded-sm border-[0.2px] border-solid border-[rgba(155,201,95,0.3)] bg-white px-4 py-[2px]">
          Medical Engineering
        </div>
        <div className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 absolute -right-44 top-9 flex items-center rounded-sm border-[0.2px] border-solid border-[rgba(155,201,95,0.3)] bg-white px-4 py-[2px]">
          Process Development
        </div>
        <div className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 absolute -right-[116px] top-[102px] flex items-center rounded-sm border-[0.2px] border-solid border-[rgba(155,201,95,0.3)] bg-white px-4 py-[2px]">
          Product Design
        </div>
      </div>
      {/* <div className="m-auto w-fit">
        <Button onClick={() => router.push("/sign-in")}>
          <div className="flex gap-2">
            <PhosphorSparkleIcon size={16} />
            <span>Start AI Job Search</span>
          </div>
        </Button>
      </div> */}
    </div>
  )
}

export default AiPoweredMatches
