import React from 'react'

const DiscoverJobs: React.FC = () => {
  return (
    <>
      <div className="col-span-2 px-6">
        <div className="my-2 text-base font-semibold">Discover Jobs in Real&nbsp;Time</div>
        <div className="text-sm font-normal">
          Explore a dynamic feed of job listings udpdated in real-time. Follow industries and roles
          that interest you to stay informed about new opportunities as they become available
        </div>
      </div>
      <div className="min-676:block relative col-span-3 hidden">
        <div className="relative left-1/2 top-1/2 w-fit -translate-x-1/2 -translate-y-1/2">
          <img
            src={'/bentogrid/discoverjobs/MainImage.svg'}
            width={270}
            height={213}
            alt="main image"
            className="animate-discoverjobs-main relative z-10"
          />
          <img
            src={'/bentogrid/discoverjobs/BackImage.svg'}
            width={270}
            height={213}
            alt="main image"
            className="animate-discoverjobs-back absolute top-0 z-0"
          />
        </div>
        <div className="absolute left-1/2 top-1/2 z-20 -translate-x-1/2 -translate-y-1/2">
          <img
            src={'/PlusSign.svg'}
            width={12}
            height={12}
            alt="Plus sign"
            className="animate-discoverjobs-small-plus-right relative -translate-y-[30px] opacity-0"
          />
          <img
            src={'/PlusSign.svg'}
            width={16}
            height={16}
            alt="Plus sign"
            className="animate-discoverjobs-small-plus-left relative h-3 w-3 -translate-y-[30px] opacity-0"
          />
          <img
            src={'/PlusSign.svg'}
            width={16}
            height={16}
            alt="Plus sign"
            className="animate-discoverjobs-big-plus-left relative h-4 w-4 -translate-x-4 opacity-0"
          />
          <img
            src={'/PlusSign.svg'}
            width={16}
            height={16}
            alt="Plus sign"
            className="animate-discoverjobs-big-plus-right relative h-4 w-4 translate-x-4 opacity-0"
          />
        </div>
      </div>
    </>
  )
}

export default DiscoverJobs
