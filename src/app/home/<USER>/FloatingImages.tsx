interface FloatingImage {
  src: string
  width: number
  height: number
  className: string
}

const FloatingImages: React.FC = () => {
  const images: FloatingImage[] = [
    {
      src: '/hero/boy1.svg',
      width: 62,
      height: 62,
      className: 'moveLeftRight -top-6 left-[24.31vw]',
    }, // 250px, 350px
    {
      src: '/hero/boy2.svg',
      width: 26,
      height: 26,
      className: 'moveLeftRightWithoutScale top-32 left-[22.22vw]',
    }, // 400px, 320px
    {
      src: '/hero/girl1.svg',
      width: 48,
      height: 48,
      className: 'moveLeftRightGirl1 top-52 left-[29.17vw]',
    }, // 480px, 420px
    {
      src: '/hero/girl2.svg',
      width: 25,
      height: 25,
      className: 'moveLeftRightGirl2 -top-10 right-[27.78vw]',
    }, // 220px, 400px
    {
      src: '/hero/boy3.svg',
      width: 78,
      height: 78,
      className: 'moveLeftRightboy3 top-24 right-[25.69vw]',
    }, // 350px, 370px
    {
      src: '/hero/girl3.svg',
      width: 44,
      height: 44,
      className: 'moveLeftRightgirl3 top-64 right-[23.61vw]',
    }, // 480px, 340px
    {
      src: '/hero/rs1.svg',
      width: 9,
      height: 12,
      className: 'moveLeftRightrs1 -top-16 left-[32%]',
    }, // 210px, 450px
    {
      src: '/hero/rs2.svg',
      width: 16,
      height: 24,
      className: 'moveLeftRightrs1 top-20 left-[29.17vw]',
    }, // 380px, 420px
    {
      src: '/hero/rs3.svg',
      width: 16,
      height: 24,
      className: 'moveLeftRightrs1 top-56 mt-6 left-[18vw]',
    }, // 520px, 250px
    {
      src: '/hero/rs2.svg',
      width: 9,
      height: 12,
      className: 'moveLeftRightrs1 top-80 mt-3 left-[38.19vw]',
    }, // 580px, 550px
    {
      src: '/hero/rs2.svg',
      width: 16,
      height: 24,
      className: 'moveLeftRightrs1 top-80 right-[34vw]',
    }, // 550px, 580px
    {
      src: '/hero/rs2.svg',
      width: 9,
      height: 12,
      className: 'moveLeftRightrs1 top-24 right-[37.50vw]',
    }, // 380px, 540px
    {
      src: '/hero/rs2.svg',
      width: 16,
      height: 24,
      className: 'moveLeftRightrs2 top-36 right-[23.61vw]',
    }, // 400px, 340px
    {
      src: '/hero/rs2.svg',
      width: 9,
      height: 12,
      className: 'moveLeftRightrs3 top-52 right-[20.11vw]',
    }, // 200px, 520px
    // { src: '/hero/rs2.svg', width: 9, height: 12, className: 'moveLeftRightrs3 top-[21.94vw] right-[19.44vw]' }, // 460px, 280px
  ]

  return (
    <div className="hidden xl:block">
      {images.map((image, index) => (
        <img
          key={index}
          src={image.src}
          width={image.width}
          height={image.height}
          className={`absolute ${image.className}`}
          alt="floating icon"
        />
      ))}
    </div>
  )
}

export default FloatingImages
