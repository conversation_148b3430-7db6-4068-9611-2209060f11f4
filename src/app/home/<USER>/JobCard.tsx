'use client'

// JobCard.tsx
import React from 'react'
import Button from '../../../components/Button'
import { Profile } from './JobProfile'
import { Briefcase, CurrencyInr, Desktop, Lightning, MapPin } from '@phosphor-icons/react'
import { useRouter } from 'next/navigation'

interface JobCardProps {
  profile: Profile
}

const JobCard: React.FC<JobCardProps> = ({ profile }) => {
  const router = useRouter()
  return (
    <div className="slider dark:border-dark-lucres-black-200 relative flex h-44 w-full min-w-80 flex-col items-center justify-start rounded-[12px] border-2 border-gray-100 p-4 dark:border">
      <div className="flex w-full">
        <div className="flex flex-col">
          <h3 className="font-inter text-[13px] font-semibold">{profile.Title}</h3>
          <span className="font-inter text-[11px]">{profile.Company_Name}</span>
        </div>
        <img
          alt="company icon"
          className="absolute right-1 top-2"
          src={profile.Icon}
          height={53}
          width={53}
        />
      </div>

      <div className="font-inter text-lucres-800 dark:text-dark-lucres-green-300 flex w-full items-center gap-x-5 text-xs">
        <div className="flex items-center gap-x-2">
          {/* <img alt='experience icon' src={'/jobprofile/bag-icon.svg'} height={13} width={13}/> */}
          <Briefcase size={14} />
          <span>{profile.Experience_Level}</span>
        </div>
        <div className="flex items-center gap-x-2">
          {/* <span className='text-[13px]'>₹</span> */}
          <CurrencyInr size={14} />
          <span>{profile.Salary}</span>
        </div>
      </div>

      <div className="font-inter text-lucres-800 dark:text-dark-lucres-green-300 flex w-full flex-wrap items-center gap-x-2 py-[2px] text-[11px]">
        <Lightning size={14} />
        <span>{profile.Role} |</span>
        {profile.Languages.map((lan, i) => (
          <span key={lan}>
            {lan}
            {i < profile.Languages.length - 1 ? ',' : ' |'}
          </span>
        ))}
        {profile.Skills.map((skill, i) => (
          <span key={skill}>
            {skill}
            {i < profile.Skills.length - 1 ? ',' : null}
          </span>
        ))}
      </div>

      <div className="font-inter text-lucres-800 dark:text-dark-lucres-green-300 flex w-full items-center gap-x-5 text-[11px]">
        <div className="flex items-center gap-x-2">
          {/* <img alt='location icon' src={'/jobprofile/loaction-icon.svg'} height={13} width={13} /> */}
          <MapPin size={14} />
          <span>{profile.Location}</span>
        </div>
        <div className="flex items-center gap-x-2">
          {/* <img alt='duration icon' src={'/jobprofile/desktop-icon.svg'} height={13} width={13} /> */}
          <Desktop size={14} />
          <span>{profile.Duration}</span>
        </div>
      </div>

      {/* <span className='w-full min-h-0.5 mt-2 mb-1 bg-lucres-200 rounded-xl'></span> */}
      <hr className="dark:border-dark-lucres-black-200 mb-2 mt-2 w-full border-t" />

      <div className="mt-1 flex w-full items-center justify-between">
        <Button
          theme="translucent"
          size="small"
          className="px-6! py-1.5!"
          onClick={() => router.push('/sign-in')}
        >
          Repost
        </Button>
        <div className="flex items-center gap-x-1">
          <img alt="time icon" src={'/jobprofile/time-icon.svg'} height={13} width={13} />
          <span className="font-inter text-lucres-800 dark:text-dark-lucres-green-300 text-[11px]">
            {profile.Date_Posted} Day Ago
          </span>
        </div>
      </div>
    </div>
  )
}

export default JobCard
