// import { useNavigate } from 'next/navigation'
// import Button from "../../../components/Button";
import Tag from '../../../components/Tag'

const ForRecruiter = () => {
  // const navigate = useNavigate()
  return (
    <div className="bg-lucres-100 dark:bg-dark-lucres-black-400 p-8 pb-0 pt-24 md:pt-32">
      <div className="bg-lucres-900 dark:bg-dark-lucres-black-500 relative mx-auto grid h-auto max-w-full grid-cols-1 overflow-hidden rounded-[20px] md:h-2/5 md:w-6/12 md:grid-cols-2">
        <div className="relative w-full">
          <img
            src="/forrecruiter/RecruiterImage.png"
            width={904}
            height={100}
            alt="recruiterImage"
            className="h-full w-full object-cover"
          />
          <img
            src="/forrecruiter/Recruiter.svg"
            width={374}
            height={157}
            alt="Recruiter"
            className="absolute bottom-4 left-1/2 w-1/2 -translate-x-1/2 md:w-auto"
          />
        </div>
        <div className="p-8 md:p-12 md:pt-20">
          <Tag TagText="For Recruiter" theme="secondary" ClassName="ml-0" />
          <h1 className="mt-4 text-2xl text-white md:mt-8 md:text-4xl">
            Post unlimited jobs. For Free.
          </h1>
          <p className="dark:text-dark-lucres-green-100 mb-8 mt-4 text-sm text-white opacity-60 md:mb-16 md:text-base">
            Say goodbye to expensive job ads. We're making job posting accessible to everyone,
            allowing you to hire the best candidates without budget worries.
          </p>
          {/* <Button onClick={() => navigate('/sign-in')}>Post your first job</Button> */}
        </div>
      </div>
    </div>
  )
}

export default ForRecruiter
