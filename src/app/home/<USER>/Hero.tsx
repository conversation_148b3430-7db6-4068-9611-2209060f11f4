// "use client";
import React from 'react'
import FeaturedSlider from './FeaturedSlider'
import FloatingImages from './FloatingImages'
// import Button from "../../../components/Button";
// import { ArrowRight } from "@phosphor-icons/react";
const Hero: React.FC = () => {
  // const [query, setQuery] = useState("");
  // const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);
  // const [highlightedIndex, setHighlightedIndex] = useState(-1);

  // Dummy suggestions list
  const suggestions = [
    'Software Engineer',
    'Frontend Developer',
    'Backend Developer',
    'UI/UX Designer',
    'Data Analyst',
    'Product Manager',
  ]

  // Handle input change
  // const handleChange = (e: any) => {
  //   const value = e.target.value;
  //   setQuery(value);

  //   // Filter suggestions based on input
  //   if (value.trim()) {
  //     setFilteredSuggestions(
  //       suggestions.filter((s) => s.toLowerCase().includes(value.toLowerCase()))
  //     );
  //   } else {
  //     setFilteredSuggestions([]);
  //   }

  //   setHighlightedIndex(-1); // Reset highlight
  // };

  // Handle key navigation (Arrow Up/Down, Enter)
  // const handleKeyDown = (e: any) => {
  //   if (e.key === "ArrowDown") {
  //     setHighlightedIndex((prev) =>
  //       prev < filteredSuggestions.length - 1 ? prev + 1 : prev
  //     );
  //   } else if (e.key === "ArrowUp") {
  //     setHighlightedIndex((prev) => (prev > 0 ? prev - 1 : 0));
  //   } else if (e.key === "Enter" && highlightedIndex >= 0) {
  //     setQuery(filteredSuggestions[highlightedIndex]);
  //     setFilteredSuggestions([]); // Hide suggestions
  //   }
  // };

  // Handle clicking a suggestion
  // const selectSuggestion = (suggestion: any) => {
  //   setQuery(suggestion);
  //   setFilteredSuggestions([]); // Hide suggestions
  // };

  return (
    <section className="bg-lucres-100 dark:bg-dark-lucres-black-400 h-5/6 w-full px-2 pb-[100px] pt-40 xl:pb-[200px]">
      {/* Top Content */}
      <div className="relative flex w-full items-center justify-center">
        <div className="flex w-full flex-col items-center justify-center md:w-10/12 xl:w-5/12">
          <h1 className="text-center text-[42px] leading-[55px] md:text-[56px] md:leading-[63px]">
            <b>Earn</b> while you look for your <b>next job</b>
          </h1>
          <p className="px-2 pb-7 pt-7 text-center text-base font-normal lg:px-28">
            Finding your next career move has never been easier. Job searching is as simple as a
            tweet and you can <b>earn along the way!</b>
          </p>
          {/* <div
            className={`dark:bg-dark-lucres-black-400 relative flex w-10/12 items-center justify-between rounded-full bg-white px-2 py-3 pe-8 md:w-8/12 lg:w-5/12 xl:w-6/12`}
          >
            <input
              type="text"
              value={query}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
              placeholder="Start your job search here"
              className={`dark:bg-dark-lucres-black-400 grow bg-white outline-hidden lg:px-4`}
            />
            <div className="absolute right-1">
              <Button
                theme={"opaque"}
                size={"small"}
                className="rounded-full! p-2!"
              >
                <ArrowRight size={20} />
              </Button>
            </div>
            {filteredSuggestions.length > 0 && (
              <ul className="dark:bg-dark-lucres-black-400 absolute top-12 right-0 left-0 z-10 mt-2 max-h-48 overflow-y-auto rounded-lg bg-white shadow-md">
                {filteredSuggestions.map((suggestion, index) => (
                  <li
                    key={suggestion}
                    className={`cursor-pointer border-b px-4 py-2 hover:bg-gray-100 ${
                      index === highlightedIndex
                        ? "bg-gray-200 dark:bg-gray-600"
                        : ""
                    }`}
                    onMouseDown={() => selectSuggestion(suggestion)}
                  >
                    {suggestion}
                  </li>
                ))}
              </ul>
            )}
          </div> */}
        </div>
        <FloatingImages />
      </div>

      {/* Bottom Content */}
      <div className="flex w-full flex-col items-center justify-center pt-40">
        <span className="text-lucres-800 dark:text-dark-lucres-green-200 text-sm font-medium">
          We are featured on
        </span>
        <div className="mt-10 flex w-full items-center justify-center">
          <FeaturedSlider />
        </div>
      </div>
    </section>
  )
}

export default Hero
