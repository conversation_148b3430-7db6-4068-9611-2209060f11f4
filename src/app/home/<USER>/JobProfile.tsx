'use client'
import React from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import 'swiper/css'
import 'swiper/css/free-mode'
import 'swiper/css/pagination'
import { Autoplay, Pagination, FreeMode } from 'swiper/modules'

import JobCard from './JobCard'
import Tag from '../../../components/Tag'
import Button from '../../../components/Button'
import { useRouter } from 'next/navigation'

export interface Profile {
  Id: number
  Title: string
  Company_Name: string
  Experience_Level: string
  Salary: string
  Skills: string[]
  Location: string
  Duration: string
  Date_Posted: string
  Icon: string
  Role: string
  Languages: string[]
}

const JobProfile: React.FC = () => {
  const router = useRouter()

  const profiles: Profile[] = [
    {
      Id: 1,
      Title: 'Backend Developer',
      Company_Name: 'Vodforce',
      Experience_Level: 'Experienced',
      Salary: '45k PM',
      Skills: ['XML', 'XSLT', 'JSON'],
      Location: 'Greater Noida',
      Duration: 'Full-Time',
      Date_Posted: '2',
      Icon: '/jobprofile/voforce-icon.svg',
      Role: 'Software',
      Languages: ['English'],
    },
    {
      Id: 2,
      Title: 'Frontend Developer',
      Company_Name: 'TechSpark',
      Experience_Level: 'Mid-Level',
      Salary: '40k PM',
      Skills: ['HTML', 'CSS', 'JavaScript'],
      Location: 'Bangalore',
      Duration: 'Full-Time',
      Date_Posted: '3',
      Icon: '/jobprofile/voforce-icon.svg',
      Role: 'IT',
      Languages: ['English', 'Hindi'],
    },
    {
      Id: 3,
      Title: 'Data Scientist',
      Company_Name: 'DataWave',
      Experience_Level: 'Experienced',
      Salary: '65k PM',
      Skills: ['Python', 'R', 'SQL', 'C++'],
      Location: 'Mumbai',
      Duration: 'Full-Time',
      Date_Posted: '1',
      Icon: '/jobprofile/voforce-icon.svg',
      Role: 'Real Estate',
      Languages: ['Hindi'],
    },
    {
      Id: 4,
      Title: 'DevOps Engineer',
      Company_Name: 'CloudNest',
      Experience_Level: 'Experienced',
      Salary: '55k PM',
      Skills: ['AWS', 'Docker', 'CI/CD'],
      Location: 'Hyderabad',
      Duration: 'Full-Time',
      Date_Posted: '5',
      Icon: '/jobprofile/voforce-icon.svg',
      Role: 'Software',
      Languages: ['English'],
    },
    {
      Id: 5,
      Title: 'Mobile App Developer',
      Company_Name: 'Appify',
      Experience_Level: 'Junior',
      Salary: '35k PM',
      Skills: ['Android Studio', 'APIs', 'UI/UX'],
      Location: 'Pune',
      Duration: 'Full-Time',
      Date_Posted: '4',
      Icon: '/jobprofile/voforce-icon.svg',
      Role: 'IT',
      Languages: ['Hindi'],
    },
  ]

  const handleClick = () => {
    router.push('/sign-in')
  }

  return (
    <div className="flex h-full flex-col items-center justify-center py-28">
      <div className="flex w-full flex-col items-center justify-center px-10 xl:w-2/6">
        <Tag theme="primary" TagText="Easy Job Search" />

        <h1 className="font-inter mb-10 px-[30px] pt-5 text-center text-[36px] leading-[43px]">
          Making Jobs as common as a <b>tweet</b>
        </h1>
        <span className="font-inter px-7 text-center text-base font-normal leading-[22.4px] opacity-50">
          Imagine finding and sharing job opportunities with the same ease and speed as posting on
          social media. That's the power of Lucres.
        </span>
      </div>
      {/* <div className="my-20 flex w-full max-w-[1440px] items-center gap-x-4 overflow-hidden px-1 py-4 md:px-2 xl:w-8/12">
        <Swiper
          slidesPerView={1}
          spaceBetween={20}
          loop={true}
          freeMode={true}
          autoplay={{ delay: 1000, disableOnInteraction: false }}
          speed={1000}
          modules={[Autoplay, FreeMode, Pagination]}
          breakpoints={{
            768: { slidesPerView: 2 },
            1024: { slidesPerView: 3 },
            1440: { slidesPerView: 3 },
          }}
        >
          {profiles.map((profile) => (
            <SwiperSlide key={profile.Id}>
              <JobCard profile={profile} />
            </SwiperSlide>
          ))}
        </Swiper>
      </div> */}
      <Button className="mt-10" onClick={handleClick}>
        Search Jobs
      </Button>
    </div>
  )
}

export default JobProfile
