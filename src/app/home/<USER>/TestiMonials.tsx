'use client'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Pagination, Navigation, Autoplay } from 'swiper/modules'
// @ts-ignore
import 'swiper/css'
// @ts-ignore
import 'swiper/css/free-mode'
// @ts-ignore
import 'swiper/css/pagination'

import TestimonialCard from './TestimonialCard'
import Tag from '../../../components/Tag'
import { CaretLeftIcon, CaretRightIcon } from '@phosphor-icons/react'

// Define the testimonial structure
export interface Testimonial {
  text: string
  author: string
  position: string
  image: string
}

// Sample testimonials array
const testimonials: Testimonial[] = [
  {
    text: "“<PERSON><PERSON> makes job hunting feel like interacting on social media. I've earned money just by sharing job listings with my friends!”",
    author: '<PERSON><PERSON><PERSON>',
    position: 'Looking a SDE 2',
    image: '/testimonials/Person1.png',
  },
  {
    text: "“This platform has changed the way I look for jobs. It's so easy and engaging!”",
    author: '<PERSON>',
    position: 'Marketing Manager',
    image: '/testimonials/Person2.png',
  },
  {
    text: '“I love how intuitive and rewarding the job search experience is on Lucres.”',
    author: '<PERSON>',
    position: 'UX Designer',
    image: '/testimonials/Person3.png',
  },
]

const Testimonials: React.FC = () => {
  return (
    <div className="bg-lucres-100 dark:bg-dark-lucres-black-400 p-8 py-36">
      <div className="dark:bg-dark-lucres-black-500 relative mx-auto max-w-full overflow-hidden rounded-[20px] bg-white p-6 md:h-1/2 md:w-[904px] md:p-12">
        <Tag TagText="Words of Love" />
        <h1 className="min-976:text-4xl m-5 text-center text-2xl font-medium">
          What job seekers are saying about us
        </h1>
        <div className="relative mx-auto flex w-full items-start md:w-[600px] md:overflow-hidden">
          <Swiper
            modules={[Pagination, Navigation, Autoplay]}
            navigation={{
              prevEl: '.swiper-button-prev',
              nextEl: '.swiper-button-next',
            }}
            pagination={{
              clickable: true,
              el: '.swiper-pagination',
              type: 'bullets',
              dynamicBullets: true,
            }}
            spaceBetween={0}
            slidesPerView={1}
            loop
            autoplay={{
              delay: 3000,
              disableOnInteraction: false,
            }}
            className="relative w-full"
          >
            {testimonials.map((testimonial, index) => (
              <SwiperSlide key={index}>
                <TestimonialCard testimonial={testimonial} />
              </SwiperSlide>
            ))}
          </Swiper>
        </div>

        <div className="swiper-pagination my-6 md:hidden"></div>

        <div className="translate-y-15 absolute left-1/2 top-72 z-30 hidden md:block">
          <CaretLeftIcon className="swiper-button-prev text-lucres-900! dark:text-dark-lucres-green-100! -left-[320px]! h-5! w-5! translate-y-1/2! absolute top-1/2 cursor-pointer rounded-full p-1 text-center shadow-[0px_4px_8px_0px_rgba(0,0,0,0.06),0px_0px_4px_0px_rgba(0,0,0,0.04)]" />
          <CaretRightIcon className="swiper-button-next text-lucres-900! dark:text-dark-lucres-green-100! -right-[320px]! h-5! w-5! translate-y-1/2! absolute top-1/2 cursor-pointer rounded-full p-1 text-center shadow-[0px_4px_8px_0px_rgba(0,0,0,0.06),0px_0px_4px_0px_rgba(0,0,0,0.04)]" />
        </div>

        <div className="border-lucres-500 min-976:flex absolute -left-7 top-[214px] hidden h-[960px] w-[960px] items-center justify-center rounded-full border border-solid">
          <div className="border-lucres-500 h-[752px] w-[752px] rounded-full border border-solid">
            {/* Rotating images */}
            <div className="absolute left-1/2 top-1/2 origin-center -translate-x-1/2 -translate-y-1/2">
              <div className="animate-clock-wise absolute h-11 w-11">
                <img
                  src={'/hero/boy1.svg'}
                  width={40}
                  height={40}
                  alt="Grin"
                  className="absolute -top-[480px]"
                />
                <img
                  src={'/hero/girl1.svg'}
                  width={44}
                  height={44}
                  alt="Hug"
                  className="absolute -top-[340px] left-[340px] rotate-45"
                />
                <img
                  src={'/hero/boy2.svg'}
                  width={40}
                  height={40}
                  alt="RollingEye"
                  className="absolute left-[480px] rotate-90"
                />
                <img
                  src={'/hero/girl2.svg'}
                  width={44}
                  height={44}
                  alt="Shock"
                  className="rotate-135 absolute left-[340px] top-[340px]"
                />
                <img
                  src={'/hero/boy3.svg'}
                  width={44}
                  height={44}
                  alt="StarStruck"
                  className="absolute top-[480px] rotate-180"
                />
                <img
                  src={'/hero/girl3.svg'}
                  width={44}
                  height={44}
                  alt="Sunglasses"
                  className="rotate-225 absolute -left-[340px] top-[340px]"
                />
                <img
                  src={'/hero/boy2.svg'}
                  width={44}
                  height={44}
                  alt="Think"
                  className="rotate-270 absolute -left-[480px]"
                />
                <img
                  src={'/hero/girl2.svg'}
                  width={44}
                  height={44}
                  alt="Wink"
                  className="rotate-315 absolute -left-[340px] -top-[340px]"
                />
              </div>
              <div className="animate-anti-clock-wise relative h-9 w-9">
                <img
                  src={'/hero/girl3.svg'}
                  width={44}
                  height={44}
                  alt="Brain"
                  className="absolute -top-[376px]"
                />
                <img
                  src={'/hero/boy3.svg'}
                  width={44}
                  height={44}
                  alt="Clown"
                  className="absolute -top-[266px] left-[266px] rotate-45"
                />
                <img
                  src={'/hero/girl1.svg'}
                  width={44}
                  height={44}
                  alt="Earth"
                  className="absolute left-[376px] rotate-90"
                />
                <img
                  src={'/hero/boy1.svg'}
                  width={44}
                  height={44}
                  alt="Ghost"
                  className="rotate-135 absolute left-[266px] top-[266px]"
                />
                <img
                  src={'/hero/girl2.svg'}
                  width={44}
                  height={44}
                  alt="Internet"
                  className="absolute top-[376px] rotate-180"
                />
                <img
                  src={'/hero/boy2.svg'}
                  width={44}
                  height={44}
                  alt="Moon"
                  className="rotate-225 absolute -left-[266px] top-[266px]"
                />
                <img
                  src={'/hero/girl1.svg'}
                  width={44}
                  height={44}
                  alt="Stopwatch"
                  className="rotate-270 absolute -left-[376px]"
                />
                <img
                  src={'/hero/boy1.svg'}
                  width={44}
                  height={44}
                  alt="Tada"
                  className="rotate-315 absolute -left-[266px] -top-[266px]"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Testimonials
