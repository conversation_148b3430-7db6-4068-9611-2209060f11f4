'use client'
import Button from '@/components/Button'
import Tag from '@/components/Tag'
import { useRouter } from 'next/navigation'

const JoinToday = () => {
  const router = useRouter()

  return (
    <div className="bg-lucres-900 dark:bg-dark-lucres-black-500 w-full px-4 py-16 text-white">
      <div className="mx-auto flex w-full max-w-7xl flex-col items-center justify-center gap-y-5 text-center">
        <Tag theme="secondary" TagText="Empower your career" />
        <h1 className="font-inter text-4xl font-medium">Join for free today</h1>
        <p className="font-inter max-w-2xl text-sm font-normal leading-6">
          Supercharge your career while you discover, share, monetise. Join now and start your
          rewarding job search journey!
        </p>
        <Button size="large" theme="opaque" onClick={() => router.push('/sign-in')}>
          Get Started
        </Button>
      </div>
    </div>
  )
}

export default JoinToday
