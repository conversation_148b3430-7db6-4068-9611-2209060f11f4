'use client'
import { Swiper, SwiperSlide } from 'swiper/react'
// @ts-ignore
import 'swiper/css'
// @ts-ignore
import 'swiper/css/free-mode'
// @ts-ignore
import 'swiper/css/pagination'
import { Autoplay, FreeMode, Pagination } from 'swiper/modules'

interface Logo {
  src: string
  width: number
  height: number
}

const featuredLogos: Logo[] = [
  { src: '/hero/Republic.svg', width: 183.29, height: 10 },
  { src: '/hero/ZeeNews.svg', width: 86.81, height: 20 },
  { src: '/hero/ZeeNews.svg', width: 86.81, height: 20 },
  { src: '/hero/Fe.svg', width: 67.5, height: 30 },
  { src: '/hero/ApnNews.svg', width: 175.31, height: 36.06 },
]

const FeaturedSlider: React.FC = () => {
  return (
    <Swiper
      slidesPerView={1}
      spaceBetween={46}
      loop={true}
      freeMode={true}
      autoplay={{ delay: 2500, disableOnInteraction: false }}
      speed={1000}
      modules={[FreeMode, Pagination, Autoplay]}
      breakpoints={{
        768: { slidesPerView: 3 },
        1024: { slidesPerView: 3 },
        1440: { slidesPerView: 4 },
      }}
      className="h-10 w-full xl:w-6/12"
    >
      {featuredLogos.map((logo, index) => (
        <SwiperSlide key={index}>
          <div className="relative flex h-10 items-center justify-center">
            <img
              className="flex items-center justify-center"
              src={logo.src}
              width={logo.width}
              height={logo.height}
              alt="logo"
            />
          </div>
        </SwiperSlide>
      ))}
    </Swiper>
  )
}

export default FeaturedSlider
