import React from 'react'
import { Testimonial } from './TestiMonials'

interface TestimonialCardProps {
  testimonial: Testimonial
  className?: String
}

const TestimonialCard: React.FC<TestimonialCardProps> = ({ testimonial, className }) => {
  return (
    <div
      className={`border-lucres-200 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 relative z-10 mx-auto my-8 flex h-auto w-full flex-col justify-center gap-6 rounded-xl border border-solid bg-white p-5 sm:h-56 sm:w-[468px] sm:p-10 ${className}`}
    >
      <div className="mb-5 text-sm font-medium sm:text-base">
        {testimonial.text ||
          `“Luc<PERSON> makes job hunting feel like interacting on social media. I've earned money just by sharing job listings with my friends!”`}
      </div>
      <div className="flex items-center gap-2">
        <img
          src={testimonial.image || `/testimonials/TestimonialPic.png`}
          width={44}
          height={44}
          alt="TestimonialPic"
        />
        <div className="flex-col ps-0.5">
          <div className="text-sm font-medium sm:text-base">
            {testimonial.author || 'Manjeet Singh'}
          </div>
          <div className="text-lucres-800 text-xs font-normal sm:text-sm">
            {testimonial.position || 'Looking a SDE 2'}
          </div>
        </div>
      </div>
    </div>
  )
}

export default TestimonialCard
