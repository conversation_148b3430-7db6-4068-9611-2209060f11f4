const Envelope = () => {
  return (
    <div className="animate-rotate-envelope relative flex h-36 w-36 -rotate-2 justify-center">
      <img
        src={'/bentogrid/earnwhiletweet/envelope/Base.svg'}
        width={175}
        height={158}
        alt="base"
        className="envelope absolute -bottom-[15px] -right-3 z-10"
      />
      <img
        src={'/bentogrid/earnwhiletweet/envelope/Paper.svg'}
        width={132}
        height={133}
        alt="paper"
        className="animate-rotate-paper absolute bottom-3 left-5 z-10"
      />
      <img
        src={'/bentogrid/earnwhiletweet/envelope/Mask.svg'}
        width={125}
        height={85}
        alt="mask"
        className="absolute -right-[2px] bottom-[6px] z-30"
      />
    </div>
  )
}

export default Envelope
