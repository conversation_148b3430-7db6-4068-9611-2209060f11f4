'use client'
import { useEffect, useState } from 'react'
import Envelope from './Envelope'
import Confetti from 'react-confetti-boom'
import useTabVisibility from '../../../../utils/useTabVisible'

const Bento4 = () => {
  const [confettiDelay, setConfettiDelay] = useState(false)
  const isTabVisible = useTabVisibility()
  useEffect(() => {
    if (isTabVisible) {
      let timer = setTimeout(() => setConfettiDelay(true), 1000)
      return () => clearTimeout(timer)
    } else {
      setConfettiDelay(false)
    }
    return () => setConfettiDelay(false)
  }, [isTabVisible])
  return (
    <>
      <div className="col-span-2 pl-6 lg:pl-0">
        <div className="text-base font-semibold">Earn While You Tweet</div>
        <div>
          <span className="text-sm font-semibold">Monetise your influence. </span>
          When candidate secure jobs through your shared tweets, you receive financial rewards. It's
          that simple!
        </div>
        {/* eslint-disable-next-line */}
        <a href="#" className="font-medium text-blue-600 underline">
          Learn More
        </a>
      </div>
      <div className="min-676:block col-span-2 hidden">
        {isTabVisible && (
          <>
            <div className="bento4-confetti absolute -top-8 right-[450px] z-40 bg-transparent lg:-left-0 lg:top-0 xl:left-[30px]">
              {/* <div className="bg-transparent bento4-confetti absolute z-40 left-1/2 -top-8 transform -translate-x-1/2 "> */}
              {confettiDelay && (
                <>
                  <Confetti
                    mode="boom"
                    particleCount={70}
                    shapeSize={40}
                    colors={['#BB9AB1', '#9CA986', '#F6E6CB', '#ACE1AF', '#A87676']}
                    x={0}
                    y={0.9}
                    deg={325}
                    effectCount={Infinity}
                    launchSpeed={4}
                    effectInterval={6000}
                    spreadDeg={25}
                  />
                  <Confetti
                    mode="boom"
                    particleCount={70}
                    shapeSize={40}
                    colors={['#BB9AB1', '#9CA986', '#F6E6CB', '#ACE1AF', '#A87676']}
                    x={1}
                    y={0.9}
                    deg={215}
                    effectCount={Infinity}
                    launchSpeed={4}
                    effectInterval={6000}
                    spreadDeg={25}
                  />
                </>
              )}
            </div>
            <div className="flex justify-center">
              <Envelope />
            </div>
          </>
        )}
      </div>
    </>
  )
}

export default Bento4
