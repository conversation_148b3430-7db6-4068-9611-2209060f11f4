const monetiseinfluence = () => {
  return (
    <>
      <div className="min-676:block order-2 col-span-4 hidden lg:order-1">
        <div className="relative">
          <img
            src={'/bentogrid/monetiseinfluence/OuterBoundary.svg'}
            width={276}
            height={182}
            alt="left"
            className="absolute left-1/2 top-10 z-10 -translate-x-1/2"
          />
          <div className="absolute left-1/2 top-3 z-10 h-44 -translate-x-1/2 overflow-hidden rounded-xl">
            <div className="animate-scroll-infinite relative -top-3 w-max">
              <img
                src={'/bentogrid/monetiseinfluence/First.svg'}
                width={232}
                height={85}
                alt="first"
                className="mb-[18px]"
              />
              <img
                src={'/bentogrid/monetiseinfluence/Second.svg'}
                width={232}
                height={85}
                alt="second"
                className="mb-[18px]"
              />
              <img
                src={'/bentogrid/monetiseinfluence/Recruiter.svg'}
                width={232}
                height={85}
                alt="second"
                className="mb-[18px]"
              />
              <img
                src={'/bentogrid/monetiseinfluence/First.svg'}
                width={232}
                height={85}
                alt="second"
                className="mb-[18px]"
              />
              <img
                src={'/bentogrid/monetiseinfluence/Second.svg'}
                width={232}
                height={85}
                alt="second"
                className="mb-[18px]"
              />
              <img
                src={'/bentogrid/monetiseinfluence/Recruiter.svg'}
                width={232}
                height={85}
                alt="second"
                className="mb-[18px]"
              />
            </div>
          </div>
        </div>
      </div>
      <div className="order-1 col-span-2 pl-6 lg:order-2 lg:pl-0">
        <div className="my-2 text-base font-semibold">Monetise Your Influence</div>
        <div className="text-sm font-normal">
          Share job listings directly from Lucres to your followers and community. Every shared
          opportunity can earn you rewards when someone gets hired through your shared link.
        </div>
      </div>
    </>
  )
}

export default monetiseinfluence
