// "use client";
// import { useRouter } from "next/navigation";
import Tag from '../../../components/Tag'

const Rewards = () => {
  // const router = useRouter();
  return (
    <div className="bg-lucres-100 dark:bg-dark-lucres-black-300 relative h-full p-8 pt-28 md:h-2/5">
      <div className="group relative m-auto flex h-full w-7/12 flex-col items-center gap-y-3 lg:h-2/5 lg:gap-y-0">
        <Tag TagText="Lucres Rewards" />
        <h1 className="m-5 w-full text-center text-4xl font-medium">Turn your network into cash</h1>
        <div className="relative h-full lg:my-20 lg:h-96">
          <div className="lg:w-84 relative left-1/2 flex -translate-x-1/2 flex-col gap-y-3 px-4 lg:absolute lg:h-96">
            <div className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-300 lg:w-84 -left-64 top-10 m-auto grid h-96 w-72 grid-rows-2 rounded-xl bg-white shadow-[0px_2px_4px_0px_rgba(0,0,0,0.08),0px_0px_6px_0px_rgba(0,0,0,0.02)] duration-300 group-hover:-left-96 lg:absolute lg:-rotate-6 dark:border">
              <div className="row-span-1 overflow-hidden border-b border-solid p-8">
                <img
                  src={'/rewards/reward1/Checkbox.svg'}
                  width={331}
                  height={113}
                  alt="checkbox"
                  className="absolute left-0 rotate-[5deg] lg:scale-90"
                />
              </div>
              <div className="row-span-1 p-8">
                <h2 className="mb-2 text-base font-bold">Join and Set Up Your Profile</h2>
                <p className="text-sm">
                  Sign up on Lucres and complete your profile with your professional details. This
                  helps you connect with relevant job seekers and employers.
                </p>
              </div>
            </div>
            <div className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-300 lg:w-84 z-10 m-auto grid h-96 w-72 grid-rows-2 rounded-xl bg-white shadow-[0px_2px_4px_0px_rgba(0,0,0,0.08),0px_0px_6px_0px_rgba(0,0,0,0.02)] lg:absolute dark:border">
              <div className="row-span-1 border-b border-solid p-8">
                <div className="w-88 absolute -left-2 top-4 grid scale-[80%] grid-cols-2 grid-rows-2 gap-0.5">
                  <img
                    src={'/rewards/reward2/Job1.svg'}
                    width={176}
                    height={72}
                    alt="Job1"
                    className="col-span-1 row-span-1"
                  />
                  <img
                    src={'/rewards/reward2/Job2.svg'}
                    width={174}
                    height={71}
                    alt="Job1"
                    className="col-span-1 row-span-1"
                  />
                  <img
                    src={'/rewards/reward2/Job3.svg'}
                    width={184}
                    height={73}
                    alt="Job1"
                    className="col-span-2 row-span-1 m-auto"
                  />
                </div>
              </div>
              <div className="row-span-1 p-8">
                <h2 className="mb-2 text-base font-bold">Share Job Openings</h2>
                <p className="text-sm">
                  Browse available job openings on the platform. Share these listings with your
                  network through directly through the our platform.
                </p>
              </div>
            </div>
            <div className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-300 lg:w-84 left-64 top-10 m-auto grid h-96 w-72 grid-rows-2 rounded-xl bg-white shadow-[0px_2px_4px_0px_rgba(0,0,0,0.08),0px_0px_6px_0px_rgba(0,0,0,0.02)] duration-300 group-hover:left-96 lg:absolute lg:rotate-6 dark:border">
              <div className="row-span-1 border-b border-solid p-8">
                <img
                  src={'/rewards/Reward3.svg'}
                  width={265}
                  height={153}
                  alt="Reward"
                  className="-rotate-[5deg]"
                />
              </div>
              <div className="row-span-1 p-8">
                <h2 className="mb-2 text-base font-bold">Earn Fixed Rewards</h2>
                <p className="text-sm">
                  As job seekers get hired through your shared listings, you'll earn rewards and
                  cash incentives, turning your networking efforts into real income.
                </p>
              </div>
            </div>
          </div>
        </div>
        {/* <Button theme="transparent" onClick={() => router.push("/sign-in")}>
          Learn More
        </Button> */}
      </div>
    </div>
  )
}

export default Rewards
