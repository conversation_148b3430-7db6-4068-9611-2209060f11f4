import React from 'react'
import Link from 'next/link'
import { AlertData } from './page'
import { CheckCircle } from '@phosphor-icons/react'
import Avatar from '../../components/Avatar/Avatar'

interface AlertCardData {
  alertData: AlertData
  onMarkAsRead: () => void
}

const AlertCard: React.FC<AlertCardData> = ({ alertData, onMarkAsRead }) => {
  const actionRedirectMap: Record<string, (notif: AlertData) => string | undefined> = {
    FOLLOW: (notif) => (notif.userPermalink ? `/${notif.userPermalink}` : undefined),
    LIKE: (notif) => {
      if (!notif.permalink) return undefined
      return notif.postType === 'QUOTE'
        ? `/feed/quote/${notif.permalink}`
        : `/feed/${notif.permalink}`
    },
    COMMENT: (notif) => {
      if (!notif.permalink) return undefined
      return notif.postType === 'QUOTE'
        ? `/feed/quote/${notif.permalink}`
        : `/feed/${notif.permalink}`
    },
    APPLICANT_APPLY: (notif) => (notif.actionId ? `/applicants/${notif.actionId}` : undefined),
    // APPLICANT_SELECTED: (notif) => `/contacts/${notif.permalink}`,
    JOB_POSTED: (notif) => (notif.permalink ? `/job/${notif.permalink}` : undefined),
    REPOST_JOB: (notif) => (notif.permalink ? `/job/${notif.permalink}` : undefined),
    MENTION_ARTICLE: (notif) => (notif.permalink ? `/feed/post/${notif.permalink}` : undefined),
    MENTION_QUOTE: (notif) => (notif.permalink ? `/feed/quote/${notif.permalink}` : undefined),
    MENTION_COMMENT: (notif) => (notif.permalink ? `/feed/quote/${notif.permalink}` : undefined),
    REPOST_QUOTE: (notif) => (notif.permalink ? `/feed/quote/${notif.permalink}` : undefined),
    QUOTE_QUOTE: (notif) => (notif.permalink ? `/feed/${notif.permalink}` : undefined),
    QUOTE_ARTICLE: (notif) => (notif.permalink ? `/feed/quote/${notif.permalink}` : undefined),
    REPOST_ARTICLE: (notif) => (notif.permalink ? `/feed/post/${notif.permalink}` : undefined),
  }

  const actionLink = actionRedirectMap[alertData.action?.toUpperCase() || '']?.(alertData)

  // const linkPath = rawPath
  //   .replace(':userPermalink', alertData.userPermalink)
  //   .replace(':permalink', alertData.permalink || alertData.userPermalink)

  return (
    <div className="flex w-full flex-wrap gap-x-8" onClick={onMarkAsRead}>
      <div
        className={`mb-3 flex w-full items-center justify-between rounded-lg p-3 ${
          alertData.status === 'SENT' ? 'bg-lucres-gray-100 dark:bg-dark-lucres-black-400' : ''
        }`}
      >
        <div className="flex w-full items-center gap-2">
          <div onClick={(e) => e.stopPropagation()}>
            <Avatar
              src={alertData.image}
              alt={`${alertData.name}'s Avatar`}
              size={12}
              className="cursor-pointer object-cover"
            />
          </div>
          <div>
            {/* <div className="text-sm">
              <Link to={`/${alertData.userPermalink}`}>
                <span className="cursor-pointer font-semibold capitalize hover:underline">
                  {alertData.name}
                </span>
              </Link>
              {alertData.permalink ? (
                <Link
                  to={`/${alertData.permalink}`}
                  title={`/${alertData.permalink}`}
                  className="cursor-pointer"
                >
                  {' ' + alertData.action}
                </Link>
              ) : (
                ' ' + alertData.action
              )}
            </div> */}
            <div className="text-sm">
              {alertData.userPermalink && (
                <Link href={`/${alertData.userPermalink}`}>
                  <span className="cursor-pointer font-semibold capitalize hover:underline">
                    {alertData.name}
                  </span>
                </Link>
              )}
              {actionLink ? (
                <Link
                  href={actionLink}
                  className={`hover:text-lucres-gray-800 dark:hover:text-lucres-gray-400 ${
                    alertData.permalink ? 'cursor-pointer' : ''
                  }`}
                >
                  {' '}
                  {alertData.actionMessage}
                </Link>
              ) : (
                <span className="hover:text-lucres-gray-800 dark:hover:text-lucres-gray-400">
                  {' '}
                  {alertData.actionMessage}
                </span>
              )}
            </div>

            <div className="text-lucres-800 text-xs">{alertData.time}</div>
          </div>
        </div>
        {alertData.status === 'SENT' && (
          <button
            onClick={(e) => {
              e.stopPropagation() // Prevent any parent click events
              onMarkAsRead()
            }}
            className="hover:text-lucres-600 dark:hover:text-lucres-500 group relative cursor-pointer"
          >
            <CheckCircle size={24} />
            <span className="absolute bottom-full left-1/2 -translate-x-1/2 transform whitespace-nowrap rounded-sm bg-gray-800 px-2 py-1 text-xs text-white opacity-0 transition-opacity duration-300 group-hover:opacity-100">
              Mark as Read
            </span>
          </button>
        )}
      </div>
    </div>
  )
}

export default AlertCard
