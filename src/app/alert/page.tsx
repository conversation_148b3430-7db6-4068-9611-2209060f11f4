'use client'
import { useState, useEffect, useRef, useCallback } from 'react'
import { Checks } from '@phosphor-icons/react'
import AlertCard from './Card'
import { NotificationService, Notification } from '../../services/NotificationService'
import AlertSkeleton from './AlertSkeleton'
import { useAuth } from '../../context/AuthContext'
import useError from '../../context/ErrorContext'
import NoDataFound from '../../components/NoDataFound/NoDataFound'
// import { getRelativeTime } from '../../utils/commonUtils'
// import { useSimpleRouteGuard } from "../../utils/simpleRouteGuard";

export interface AlertData {
  image?: string
  name?: string
  permalink?: string
  userPermalink?: string
  actionId?: string
  actionMessage?: string
  postType?: string
  time?: string
  action?: string
  id?: string
  status: 'SENT' | 'READ' | 'UNREAD'
}

const getRelativeTime = (createdAt: string): string => {
  // const { handleAppError } = useError()
  // useSimpleRouteGuard(true, "/sign-in");
  const now = new Date()
  const past = new Date(createdAt)
  const diffInSeconds = Math.floor((now.getTime() - past.getTime()) / 1000)

  const minutes = Math.floor(diffInSeconds / 60)
  const hours = Math.floor(diffInSeconds / 3600)
  const days = Math.floor(diffInSeconds / 86400)
  const months = Math.floor(diffInSeconds / (86400 * 30))
  const years = Math.floor(diffInSeconds / (86400 * 365))

  if (minutes < 1) {
    return 'just now'
  } else if (minutes < 60) {
    return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`
  } else if (hours < 24) {
    return `${hours} hour${hours !== 1 ? 's' : ''} ago`
  } else if (days < 30) {
    return `${days} day${days !== 1 ? 's' : ''} ago`
  } else if (months < 12) {
    return `${months} month${months !== 1 ? 's' : ''} ago`
  } else {
    return `${years} year${years !== 1 ? 's' : ''} ago`
  }
}

const Alert = () => {
  const { handleError } = useError()
  const { refreshNotificationCount } = useAuth()
  const [alertData, setAlertData] = useState<AlertData[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [hasNextPage, setHasNextPage] = useState(false)
  const observer = useRef<IntersectionObserver | null>(null)

  const fetchNotifications = async (page: number) => {
    setLoading(true)
    setError(null)
    try {
      const response = await NotificationService.getNotifications('ALL', page, 10)

      const notifications = response.data?.items || []

      const mappedData: AlertData[] = notifications.map((notif: Notification) => {
        const relativeTime = getRelativeTime(notif.createdAt)

        let actionMessage = ''
        switch (notif.action.toUpperCase()) {
          case 'FOLLOW':
            actionMessage = 'started following you'
            break
          case 'LIKE':
            if (notif.postType === 'QUOTE') {
              actionMessage = 'liked your quote'
            } else if (notif.postType === 'Job') {
              actionMessage = 'liked your job'
            } else if (notif.postType === 'ARTICLE') {
              actionMessage = 'liked your article'
            } else {
              actionMessage = 'liked your post'
            }
            break
          case 'COMMENT':
            if (notif.postType === 'QUOTE') {
              actionMessage = 'commented on your quote'
            } else if (notif.postType === 'ARTICLE') {
              actionMessage = 'commented on your article'
            } else {
              actionMessage = 'commented on your post'
            }
            break
          case 'APPLICANT_APPLY':
            actionMessage = 'You have a new applicant'
            break
          case 'APPLICANT_SELECTED':
            actionMessage = 'bought your contact'
            break
          case 'JOB_POSTED':
            actionMessage = 'posted a new job'
            break
          case 'REPOST_JOB':
            actionMessage = 'reposted your job listing'
            break
          case 'MENTION_ARTICLE':
            actionMessage = 'mentioned you in an article'
            break
          case 'MENTION_QUOTE':
            actionMessage = 'mentioned you in their post'
            break
          case 'MENTION_COMMENT':
            actionMessage = 'tagged you in a comment'
            break
          case 'REPOST_QUOTE':
            actionMessage = 'reposted your quote'
            break
          case 'QUOTE_QUOTE':
            actionMessage = 'quoted your quote'
            break
          case 'QUOTE_ARTICLE':
            actionMessage = 'quoted your article'
            break
          case 'REPOST_ARTICLE':
            actionMessage = 'reposted your article'
            break
          default:
            actionMessage = notif.action.toLowerCase()
        }

        return {
          id: notif._id,
          action: notif?.action.toUpperCase(),
          actionMessage: actionMessage,
          actionId: notif?.actionId,
          time: relativeTime,
          name:
            `${notif.otherUser?.givenName || ''} ${notif.otherUser?.familyName || ''}`.trim() ||
            'Unknown',
          image: notif.otherUser?.profileImage?.url || '/dashboard/userPlaceholder.png',
          permalink: notif?.permalink,
          postType: notif?.postType,
          userPermalink: notif.otherUser?.permalink,
          status: notif.status,
        }
      })
      // setAlertData(mappedData)
      if (page === 1) {
        setAlertData(mappedData)
      } else {
        setAlertData((prev) => [...prev, ...mappedData])
      }
      setHasNextPage(response.data.paginator.hasNextPage)
    } catch (err: any) {
      // setError(err || 'Failed to load notifications.')
      handleError(err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchNotifications(page)
  }, [page])

  const lastPostElementRef = useCallback(
    (node: HTMLDivElement) => {
      if (loading) return
      if (observer.current) observer.current.disconnect()
      observer.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasNextPage) {
            setPage((prevPage) => prevPage + 1)
          }
        },
        { threshold: 1.0 },
      )
      if (node) observer.current.observe(node)
    },
    [loading, hasNextPage],
  )

  //  mark all notifications as read
  const handleMarkAllAsRead = async () => {
    try {
      await NotificationService.markAllAsRead()
      // Update the status of all notifications to 'READ' in the local state
      setAlertData((prevAlerts) => prevAlerts.map((alert) => ({ ...alert, status: 'READ' })))
    } catch (err: any) {
      handleError(err)
    } finally {
      refreshNotificationCount()
    }
  }

  // Mark a single notification as read
  const handleMarkAsRead = async (id: string) => {
    try {
      const response = await NotificationService.markAsRead(id)

      setAlertData((prevAlerts) =>
        prevAlerts.map((alert) => (alert.id === id ? { ...alert, status: 'READ' } : alert)),
      )
    } catch (err: any) {
      handleError(err)
    } finally {
      refreshNotificationCount()
    }
  }

  return (
    <section className="font-inter flex w-full items-center justify-center py-12 md:mt-16 lg:px-10 lg:py-0">
      <div className="w-full max-w-[1440px] px-2 lg:px-4 xl:w-9/12">
        <div className="dark:border-dark-lucres-black-300 mx-auto min-h-screen w-full max-w-[1440px] lg:border-x xl:w-9/12">
          <div className="mb-24 flex w-full flex-col items-center md:mt-3">
            <div className="flex w-full max-w-[800px] items-center justify-between px-6">
              <h3 className="my-4 text-xl font-semibold">Notifications</h3>
              <div
                className="text-lucres-gray-700 hover:text-lucres-600 dark:text-lucres-300 dark:hover:text-lucres-500 my-4 flex cursor-pointer gap-1 text-sm capitalize"
                onClick={handleMarkAllAsRead}
              >
                <Checks size={24} />
                mark all read
              </div>
            </div>
            {loading && alertData.length === 0 ? (
              <div className="mx-auto w-full max-w-[800px] px-4">
                {[...Array(5)].map((_, index) => (
                  <AlertSkeleton key={index} />
                ))}
              </div>
            ) : error ? (
              <p className="text-red-500">{error}</p>
            ) : alertData.length > 0 ? (
              <div className="mx-auto w-full max-w-[800px] px-4">
                {alertData.map((alert, index) => (
                  <AlertCard
                    alertData={alert}
                    key={alert.id || index}
                    onMarkAsRead={() => alert.id && handleMarkAsRead(alert.id)}
                  />
                ))}
              </div>
            ) : (
              <NoDataFound subtitle="No notification found." />
            )}

            {hasNextPage && (
              <div ref={lastPostElementRef} className="mx-auto w-full max-w-[800px] px-4">
                {[...Array(5)].map((_, index) => (
                  <AlertSkeleton key={index} />
                ))}
              </div>
            )}

            {hasNextPage && (
              <div ref={lastPostElementRef} className="mx-auto w-full max-w-[800px] px-4">
                {[...Array(5)].map((_, index) => (
                  <AlertSkeleton key={index} />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  )
}

export default Alert

// import { Checks } from '@phosphor-icons/react'
// import AlertCard from './Card'
// import AlertSkeleton from './AlertSkeleton'
// import { NotificationService } from '../../services/NotificationService'
// import { useAuth } from '../../context/AuthContext'

// const Alert = () => {
//   const {
//     notifications,
//     notificationsLoading: loading,
//     notificationsError: error,
//     setNotifications,
//     setUnreadNotificatonCount,
//   } = useAuth()

//   // mark all notifications as read
//   const handleMarkAllAsRead = async () => {
//     try {
//       await NotificationService.markAllAsRead()
//       setNotifications((prev) => prev.map((n) => ({ ...n, status: 'READ' })))
//       setUnreadNotificatonCount(0)
//     } catch (err: any) {
//       console.error(err.message || 'Failed to mark notifications as read.')
//     }
//   }

//   const handleMarkAsRead = async (id: string) => {
//     try {
//       await NotificationService.markAsRead(id)
//       setNotifications((prev) => prev.map((n) => (n.id === id ? { ...n, status: 'READ' } : n)))
//       setUnreadNotificatonCount((prevUnreadCount) => Math.max(0, prevUnreadCount - 1))
//     } catch (err: any) {
//       console.error(err.message || 'Failed to mark notification as read.')
//     }
//   }

//   return (
//     <section className="font-inter flex w-full items-center justify-center py-12 md:mt-16 lg:px-10 lg:py-0">
//       <div className="w-full max-w-[1440px] px-2 lg:px-4 xl:w-9/12">
//         <div className="mx-auto min-h-screen w-full max-w-[1440px] lg:border-x xl:w-9/12 dark:border-dark-lucres-black-300">
//           <div className="mb-24 flex w-full flex-col items-center md:mt-3">
//             <div className="flex w-full max-w-[800px] items-center justify-between px-6">
//               <h3 className="my-4 text-xl font-semibold">Notifications</h3>
//               <div
//                 className="my-4 flex cursor-pointer gap-1 capitalize text-lucres-gray-700 hover:text-lucres-600 dark:text-lucres-300 dark:hover:text-lucres-500"
//                 onClick={handleMarkAllAsRead}
//               >
//                 <Checks size={24} />
//                 mark all read
//               </div>
//             </div>

//             {loading ? (
//               <div className="mx-auto w-full max-w-[800px] px-4">
//                 {[...Array(5)].map((_, index) => (
//                   <AlertSkeleton key={index} />
//                 ))}
//               </div>
//             ) : error ? (
//               <p className="text-red-500">{error}</p>
//             ) : notifications.length > 0 ? (
//               <div className="mx-auto w-full max-w-[800px] px-4">
//                 {notifications.map((alert, index) => (
//                   <AlertCard
//                     alertData={alert}
//                     key={alert.id || index}
//                     onMarkAsRead={() => alert.id && handleMarkAsRead(alert.id)}
//                   />
//                 ))}
//               </div>
//             ) : (
//               <p className="text-gray-500">No Notifications</p>
//             )}
//           </div>
//         </div>
//       </div>
//     </section>
//   )
// }

// export default Alert
