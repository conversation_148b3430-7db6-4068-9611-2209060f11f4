import { Inter, Exo_2, DM_Sans, PT_Serif, Khand, Space_Grotesk } from 'next/font/google'

// Inter - Variable font with italics and weights 100-900
export const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
})

// Exo 2 - Weights 400, 600, 700
export const exo2 = Exo_2({
  subsets: ['latin'],
  weight: ['400', '600', '700'],
  display: 'swap',
  variable: '--font-exo2',
})

// DM Sans - Weights 400, 500, 700
export const dmSans = DM_Sans({
  subsets: ['latin'],
  weight: ['400', '500', '700'],
  display: 'swap',
  variable: '--font-dm-sans',
})

// PT Serif - Weights 400, 700
export const ptSerif = PT_Serif({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
  variable: '--font-pt-serif',
})

// Khand - Weights 300, 400, 500, 600, 700
export const khand = Khand({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  display: 'swap',
  variable: '--font-khand',
})

// Space Grotesk - Variable font with weights 300-700
export const spaceGrotesk = Space_Grotesk({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-space-grotesk',
})
