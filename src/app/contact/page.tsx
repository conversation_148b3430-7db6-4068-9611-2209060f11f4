// import { FacebookLogoIcon, InstagramLogoIcon, TwitterLogoIcon } from '@phosphor-icons/react'
import {
  PhosphorFacebookIcon,
  PhosphorInstagramIcon,
  PhosphorTwitterIcon,
} from '@/components/IconsProvider'
import Button from '../../components/Button'
import Input from '../../components/Input'

const Contact = () => {
  return (
    <div className="m-auto mt-28 w-11/12 max-w-[1440px] lg:w-7/12">
      <div className="flex items-center justify-between">
        <div className="w-9/12 pr-2 text-3xl font-medium xl:w-5/12">
          Get in touch with us. We're here to assist you.
        </div>
        <div className="flex flex-col gap-3">
          <div className="border-lucres-300 dark:border-dark-lucres-black-200 dark:hover:bg-dark-lucres-black-200 flex h-12 w-12 cursor-pointer items-center justify-center rounded-full border border-solid hover:bg-slate-100">
            <PhosphorFacebookIcon size={24} />
          </div>
          <div className="border-lucres-300 dark:border-dark-lucres-black-200 dark:hover:bg-dark-lucres-black-200 flex h-12 w-12 cursor-pointer items-center justify-center rounded-full border border-solid hover:bg-slate-100">
            <PhosphorInstagramIcon size={24} />
          </div>
          <div className="border-lucres-300 dark:border-dark-lucres-black-200 dark:hover:bg-dark-lucres-black-200 flex h-12 w-12 cursor-pointer items-center justify-center rounded-full border border-solid hover:bg-slate-100">
            <PhosphorTwitterIcon size={24} />
          </div>
        </div>
      </div>
      <div className="dark:border-dark-lucres-black-200 my-32 flex w-full flex-col gap-y-6 rounded-xl border p-8 xl:p-28">
        <div className="flex w-full flex-col justify-between gap-4 md:flex-row">
          <Input
            type="text"
            id="name"
            name="name"
            placeholder="John Doe"
            label="Full Name"
            required={true}
          />
          <Input
            type="text"
            id="email"
            name="email"
            placeholder="<EMAIL>"
            label="Email"
            required={true}
          />
        </div>
        <Input
          type="text"
          id="subject"
          name="subject"
          placeholder="Experience/Feedback"
          label="Subject"
          required={true}
        />
        <Input
          type="text"
          id="message"
          name="message"
          placeholder="Start Typing Here"
          label="Message"
          required={false}
        />
        <div className="m-auto w-fit">
          <Button className="px-14">Leave us a Message</Button>
        </div>
      </div>
    </div>
  )
}
export default Contact
