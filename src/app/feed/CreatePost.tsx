import { useCallback, useEffect, useRef, useState } from 'react'
import Button from '../../components/Button'
import Textarea from '../../components/Textarea'
import { useAuth } from '../../context/AuthContext'
import { PostService } from '../../services/PostService'
import Tooltip from '../../components/Tooltip'
import IconButton from '../../components/IconButton'
import { PostCard } from '../../models/Post'
import { useToast } from '../../components/ToastX'
import Overlay from '../../components/Overlay'
import Avatar from '../../components/Avatar/Avatar'
import EmojiPicker from './EmojiPicker'
import useError from '../../context/ErrorContext'
import { ImageIcon, SmileyIcon, SpinnerIcon, WarningIcon, XIcon } from '@phosphor-icons/react'
// import imageCompression from 'browser-image-compression'
// import App from './Test'
// import Picker from 'emoji-picker-react'
// import EmojiPicker from 'emoji-picker-react'
type UploadedImage = {
  publicId: string
  mimetype: string
  width: number
  height: number
  secureUrl: string
}

interface CreatePostProps {
  handleClose: () => void
  showPostModal: boolean
  postData?: PostCard
  fetchFeedData?: (username: string, page: number, options: { refetch: boolean }) => void
  setData?: React.Dispatch<React.SetStateAction<PostCard[]>>

  setPostData?: React.Dispatch<React.SetStateAction<PostCard | undefined>>
  // feedData:PostCard[]
}

const CreatePost: React.FC<CreatePostProps> = ({
  handleClose,
  showPostModal,
  postData,
  fetchFeedData,
  setData,
  setPostData,
}) => {
  const articleRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const postInputRef = useRef<HTMLTextAreaElement | null>(null)
  const emojiButtonRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const { authUserProfile: userProfile } = useAuth()

  const [selectedImages, setSelectedImages] = useState<string[]>([])
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([])
  const [previewImages, setPreviewImages] = useState<(string | UploadedImage)[]>([])

  const [postContent, setPostContent] = useState<string>('')
  const [isUploadingImages, setIsUploadingImages] = useState(false)
  const [deletedImageIds, setDeletedImageIds] = useState<string[]>([])
  const [dragActive, setDragActive] = useState(false)
  const [showConfirmModal, setShowConfirmModal] = useState(false)
  const [addNewImage, setAddNewImage] = useState<UploadedImage[]>([])
  const [openEmojiPicker, setOpenEmojiPicker] = useState(false)
  const ifEditMode = Boolean(postData)
  const debounceDelay = 2000 // 2 seconds
  const toast = useToast()
  const { handleError } = useError()
  const MAX_CHARS = 3000
  useEffect(() => {
    if (showPostModal) {
      document.body.classList.add('overflow-hidden')
    } else {
      document.body.classList.remove('overflow-hidden')
    }
    return () => {
      document.body.classList.remove('overflow-hidden')
    }
  }, [showPostModal])

  useEffect(() => {
    if (postData) {
      setPostContent(postData.article?.content || '')
      setUploadedImages(postData.article?.images || [])
    }
  }, [postData])

  const handleFilesUpload = async (files: File[]) => {
    if (!files.length) return
    const MAX_IMAGES = 10

    // Calculate total potential images
    const totalAfterAddition = previewImages.length + files.length
    // Show toast BEFORE processing if limit exceeded
    if (totalAfterAddition > MAX_IMAGES) {
      // const overflow = totalAfterAddition - MAX_IMAGES;
      toast.error(`Maximum ${MAX_IMAGES} images allowed.`)
      files = files.slice(0, MAX_IMAGES - previewImages.length) // Trim excess
    }

    // Proceed only if files exist after trimming
    if (!files.length) return

    const previewUrls = files.map((file) => URL.createObjectURL(file))
    setPreviewImages((prev) => [...prev, ...previewUrls].slice(0, MAX_IMAGES))

    const formData = new FormData()
    files.forEach((file) => formData.append('images', file))

    try {
      setIsUploadingImages(true)
      const response = await PostService.uploadPostImages(formData)

      setUploadedImages((prev) => [...prev, ...response.data].slice(0, MAX_IMAGES))
      setAddNewImage((prev) => [...prev, ...response.data].slice(0, MAX_IMAGES))
      setSelectedImages((prev) => prev.filter((url) => !previewUrls.includes(url)))
    } catch (err: any) {
      // toast.error(err.message)
      handleError(err)
      setSelectedImages((prev) => prev.filter((url) => !previewUrls.includes(url)))
    } finally {
      setIsUploadingImages(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
    const files = Array.from(e.dataTransfer.files).filter((file) => file.type.startsWith('image/'))
    handleFilesUpload(files)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setDragActive(false)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(true)
  }

  const handleRemoveUploadedImage = (index: number) => {
    const updated = [...uploadedImages]
    const [removed] = updated.splice(index, 1)
    setUploadedImages(updated)
    if (removed?.publicId) {
      setDeletedImageIds((prev) => [...prev, removed.publicId])
    }
  }

  useEffect(() => {
    if (deletedImageIds.length === 0) return

    const timer = setTimeout(async () => {
      try {
        await PostService.removePostImages(deletedImageIds)
        setDeletedImageIds([])
      } catch (err) {
        // toast.error('Failed to delete image(s).')
        handleError(err)
        // console.error('Batch delete failed:', err)
      }
    }, debounceDelay)

    return () => clearTimeout(timer)
  }, [deletedImageIds])

  const handleDiscardPost = async () => {
    try {
      const deletedImageIds = addNewImage.map((img) => img.publicId)
      if (deletedImageIds.length > 0) {
        await PostService.removePostImages(deletedImageIds)
      }
      setUploadedImages(postData?.article?.images || [])
    } catch (err) {
      // console.error('Failed to discard images:', err)
      // toast.error('Failed to discard images.')
      handleError(err)
    } finally {
      setShowConfirmModal(false)
      handleClose()
    }
  }

  const handleSubmit = async () => {
    if (!postContent.trim() && uploadedImages.length === 0) return

    const postPayload = {
      content: postContent.trim(),
      images: uploadedImages,
    }

    try {
      if (ifEditMode && deletedImageIds.length > 0) {
        await PostService.removePostImages(deletedImageIds)
      }

      const response = ifEditMode
        ? postData?.article?.id &&
          (await PostService.updatePost(postPayload, postData?.article?.id))
        : await PostService.createPost(postPayload)

      const updatedArticle = response.data
      if (ifEditMode) {
        setData?.((prev) =>
          prev.map((post) =>
            post.article?.id === updatedArticle.id ? { ...post, article: updatedArticle } : post,
          ),
        )
        setPostData?.(
          (post) =>
            ({
              ...post,
              article: updatedArticle,
            }) as PostCard,
        )
      } else {
        if (userProfile?.username) {
          fetchFeedData?.(userProfile.username, 1, { refetch: true })
        }
      }
      setPostContent('')
      setSelectedImages([])
      setUploadedImages([])
      setAddNewImage([])
      setDeletedImageIds([])
      toast.success(`${ifEditMode ? 'Post updated successfully.' : 'Post created successfully.'}`)
      handleClose()
    } catch (error) {
      toast.error('Failed to submit post.')
      handleError(error)
    }
  }

  const areImagesEqualByPublicId = (a: UploadedImage[], b: UploadedImage[]) => {
    if (a.length !== b.length) return false
    return a.every((img, i) => img.publicId === b[i].publicId)
  }

  const originalContent = postData?.article?.content || ''
  const originalImages = postData?.article?.images || []

  const handleCancel = () => {
    if (ifEditMode) {
      const isContentSame = postContent === postData?.article?.content
      const isImagesSame = areImagesEqualByPublicId(uploadedImages, postData?.article?.images || [])
      if (isContentSame && isImagesSame) {
        handleClose()
        return
      }
    }

    if (uploadedImages.length > 0 || isUploadingImages || postContent.trim().length !== 0) {
      setShowConfirmModal(true)
    } else {
      handleClose()
    }
  }
  // Detect click outside Create Post
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (articleRef.current && !articleRef.current.contains(event.target as Node)) {
        handleCancel()
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [handleCancel])

  useEffect(() => {
    // Combine selected and uploaded images into a single preview list
    setPreviewImages([...uploadedImages, ...selectedImages])
  }, [uploadedImages, selectedImages])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    handleFilesUpload(files)
    e.target.value = ''
  }

  // Click outside handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node

      if (
        containerRef.current &&
        !containerRef.current.contains(target) &&
        emojiButtonRef.current &&
        !emojiButtonRef.current.contains(target)
      ) {
        setOpenEmojiPicker(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const isPostDisabled = ifEditMode
    ? postContent.trim() === originalContent &&
      areImagesEqualByPublicId(uploadedImages, originalImages)
    : postContent.trim() === '' && uploadedImages.length === 0

  const progressWidth = (postContent.length / MAX_CHARS) * 100

  return (
    <div className="flex h-screen  w-full  items-start justify-center overflow-auto">
      <div
        ref={articleRef}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onDragLeave={handleDragLeave}
        className={`border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 z-40!  max-h-4/5  relative mt-20 flex w-full  max-w-2xl  flex-col gap-0 rounded-lg bg-white p-4 pb-0 md:w-8/12 xl:w-9/12 dark:border`}
      >
        <div className="flex flex-col gap-4">
          <div className="flex w-full items-center justify-between">
            <Button
              size="small"
              theme="transparent"
              className="dark:border-dark-lucres-black-200! border-[#E8E8E8]! py-1.5!"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              size="small"
              theme="dark"
              className="py-1.5! text-white!"
              disabled={isPostDisabled || isUploadingImages}
              onClick={handleSubmit}
            >
              {ifEditMode ? 'Update' : 'Post'}
            </Button>
          </div>
        </div>
        <div className="mt-6 flex flex-col ">
          <div className="flex w-full  items-start justify-start gap-x-2">
            <div className="w-12">
              <Avatar
                onClick={() => {}}
                src={userProfile?.profileImage.url}
                alt={`${userProfile?.profileImage.name}'s Avatar`}
                size={10}
                className="cursor-default object-cover"
              />
            </div>
            <Textarea
              theme="transparent"
              id="post"
              name="post"
              ref={postInputRef}
              autoFocus
              height="140px"
              placeholder="What's Happening?"
              maxLength={MAX_CHARS}
              value={postContent}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                setPostContent(e.target.value)
              }
              className="text-lucres-gray-700 placeholder:text-lucres-gray-700 p-1! border-none placeholder:text-sm"
              labelClassName="font-medium!"
            />
            <div ref={containerRef} className="">
              {openEmojiPicker && (
                <div className="left-26 absolute bottom-10  mt-5">
                  <EmojiPicker
                    inputRef={postInputRef as React.RefObject<HTMLTextAreaElement>}
                    setData={setPostContent}
                    closePicker={() => setOpenEmojiPicker(false)}
                  />
                </div>
              )}
            </div>
          </div>
          {previewImages.length > 0 && (
            <div className="relative mt-4 flex h-auto flex-wrap gap-4">
              {previewImages.map((image, index) => {
                const isUploaded = typeof image !== 'string'

                return (
                  <div
                    key={index}
                    className="dark:border-dark-lucres-black-300 group relative w-auto rounded-lg border"
                  >
                    <img
                      src={isUploaded ? image.secureUrl : image}
                      alt=""
                      className="h-16 w-16 rounded-lg object-cover"
                    />
                    {isUploadingImages && !isUploaded && (
                      <div className="absolute inset-0 z-10 rounded-lg bg-black/50"></div>
                    )}
                    {isUploadingImages && !isUploaded && (
                      <div className="absolute right-0 top-0 z-10 animate-spin p-1">
                        <SpinnerIcon size={16} className="text-lucres-green-100" />
                      </div>
                    )}

                    {isUploaded && (
                      <button
                        className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 absolute -right-2 -top-2 z-10 flex h-6 w-6 items-center justify-center rounded-lg border bg-white"
                        onClick={() => handleRemoveUploadedImage(index)}
                      >
                        <XIcon
                          size={14}
                          weight="bold"
                          className="text-lucres-black dark:text-lucres-green-100"
                        />
                      </button>
                    )}
                  </div>
                )
              })}
            </div>
          )}
        </div>
        {/* Progress Bar */}
        <div className="flex w-full items-center gap-x-2 text-sm">
          {/* Original Border (behind progress) */}
          <div className="dark:bg-dark-lucres-black-300 relative flex h-0.5 w-full items-center bg-[#E8E8E8]">
            <div
              className="bg-lucres-700 h-full transition-all duration-200 dark:bg-white"
              style={{ width: `${progressWidth}%` }}
            ></div>
          </div>
          <p>{MAX_CHARS - postContent.length}</p>
        </div>
        <div className="flex h-12 items-start gap-x-2">
          <Tooltip text="Add image(s)" direction="top" classes="whitespace-nowrap">
            <IconButton onClick={() => fileInputRef.current?.click()}>
              <ImageIcon size={24} className="dark:text-dark-lucres-green-100 cursor-pointer" />
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept="image/png, image/jpeg, image/jpg, image/gif" // No image/avif
                multiple
                onChange={handleInputChange}
              />
            </IconButton>
          </Tooltip>
          <div ref={emojiButtonRef} className="hidden sm:block">
            <Tooltip text="Add emoji" direction="top" classes="whitespace-nowrap">
              <IconButton onClick={() => setOpenEmojiPicker(!openEmojiPicker)}>
                <SmileyIcon size={24} className="dark:text-dark-lucres-green-100 cursor-pointer" />
              </IconButton>
            </Tooltip>
          </div>
        </div>
        {dragActive && (
          <div className="dark:bg-dark-lucres-black-500 backdrop-blur-xs absolute inset-0 z-50 flex items-center justify-center rounded-lg bg-white">
            <span className="text-lg font-semibold">Drag your file here to upload</span>
          </div>
        )}
      </div>

      {showConfirmModal && (
        <Overlay
          heading="Discard Post"
          handleClose={() => setShowConfirmModal(false)}
          size="min-h-fit mt-20"
          classes="p-0! "
        >
          <div className="dark:bg-dark-lucres-black-500 w-full rounded-lg bg-white p-4 shadow-lg">
            <div className="flex flex-col items-center">
              <WarningIcon size={32} />
              <h2 className="mt-3 text-center text-lg font-semibold">Discard this post?</h2>
              <p className="text-center text-sm text-gray-500 dark:text-gray-300">
                Are you sure you want to discard this post?
              </p>
            </div>
            <div className="dark:border-t-dark-lucres-black-200 mt-4 flex justify-between gap-4 border-t pt-4">
              <Button
                size="small"
                theme="transparent"
                className="px-4! py-1.5! !border"
                onClick={() => setShowConfirmModal(false)}
              >
                Cancel
              </Button>
              <Button
                size="small"
                theme="dark"
                className="px-4! py-1.5! text-white!"
                onClick={handleDiscardPost}
              >
                Yes, Discard
              </Button>
            </div>
          </div>
        </Overlay>
      )}
    </div>
  )
}

export default CreatePost
