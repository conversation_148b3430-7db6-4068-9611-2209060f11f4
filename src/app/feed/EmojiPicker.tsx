// Named import (recommended)
'use client'
import Picker, { Theme, EmojiStyle } from 'emoji-picker-react'
import { useTheme } from '../../context/ThemeProvider'

// emoji-picker-element will use "Twemoji Mozilla" and fall back to other fonts for non-flag emoji

// Or direct default import (if available)
// import Picker from "emoji-picker-react";

interface EmojiData {
  emoji: string
  unified: string
  names: string[]
}

interface EmojiPickerProps {
  inputRef: React.RefObject<HTMLTextAreaElement>
  setData: (content: string) => void
  closePicker: (openEmojiPicker: boolean) => void
}

const EmojiPicker = ({ inputRef, setData }: EmojiPickerProps) => {
  // const pickerRef = useRef<HTMLDivElement>(null)
  const { theme } = useTheme()
  // const onEmojiClick = (emojiData: EmojiData) => {
  //   if (!inputRef.current) return

  //   const emoji = emojiData.emoji
  //   const currentMessage = inputRef.current.value
  //   const cursor = inputRef.current.selectionStart ?? currentMessage.length

  //   const newText = currentMessage.slice(0, cursor) + emoji + currentMessage.slice(cursor)

  //   setData(newText)

  //   const newCursor = cursor + emoji.length
  //   requestAnimationFrame(() => {
  //     inputRef.current?.focus()
  //     inputRef.current?.setSelectionRange(newCursor, newCursor)
  //   })
  // }
  const onEmojiClick = (emojiData: EmojiData) => {
    const textarea = inputRef.current
    if (!textarea) return

    const emoji = emojiData.emoji
    const cursor = textarea.selectionStart ?? textarea.value.length
    const newText = textarea.value.slice(0, cursor) + emoji + textarea.value.slice(cursor)

    const prevScrollTop = textarea.scrollTop // ✅ Save scroll position

    textarea.value = newText
    setData(newText)

    // Move cursor to after emoji
    const newCursor = cursor + emoji.length

    // Delay focus & cursor move to avoid triggering scroll
    requestAnimationFrame(() => {
      textarea.focus()
      textarea.setSelectionRange(newCursor, newCursor)
      textarea.scrollTop = prevScrollTop // ✅ Restore scroll
    })
  }

  return (
    <div className="absolute z-40">
      <Picker
        onEmojiClick={onEmojiClick}
        emojiStyle={EmojiStyle.NATIVE}
        className="h-[45vh]!"
        theme={theme == 'dark' ? Theme.DARK : Theme.LIGHT}
      />
    </div>
  )
}

export default EmojiPicker
