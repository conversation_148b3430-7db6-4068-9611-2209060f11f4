import { useEffect, useRef, useState } from 'react'
import Button from '../../components/Button'
import {
  DotIcon,
  ImagesIcon,
  PlusIcon,
  QuotesIcon,
  // TextBolder,
  TextIndentIcon,
  TextItalicIcon,
  TextOutdentIcon,
  TextTSlashIcon,
} from '@phosphor-icons/react'
import Input from '../../components/Input'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, FreeMode } from 'swiper/modules'
// @ts-ignore
import 'swiper/css'
// @ts-ignore
import 'swiper/css/free-mode'
// @ts-ignore
import 'swiper/css/pagination'
import PreviewArticle from './PreviewArticle'
function PostArticle({ onClick, showArticleModal }: any) {
  const articleRef = useRef<HTMLDivElement>(null)
  const [selectedImages, setSelectedImages] = useState<string[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [title, setTitle] = useState<string>('')
  const [description, setDescription] = useState<string>('')
  const [showPreview, setShowPreview] = useState<boolean>(false)
  const [formData, setFormData] = useState({
    images: [] as string[],
    title: '',
    description: '',
  })
  // Handle body scroll lock
  useEffect(() => {
    if (showArticleModal) {
      document.body.classList.add('overflow-hidden')
    } else {
      document.body.classList.remove('overflow-hidden')
    }
    return () => {
      document.body.classList.remove('overflow-hidden')
    }
  }, [showArticleModal])
  // Detect click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (articleRef.current && !articleRef.current.contains(event.target as Node)) {
        onClick()
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [onClick])

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      const imageUrls = Array.from(files).map((file) => URL.createObjectURL(file))
      setSelectedImages((prev) => [...prev, ...imageUrls])
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    const files = Array.from(e.dataTransfer.files).filter((file) => file.type.startsWith('image/'))
    const imageUrls = files.map((file) => URL.createObjectURL(file))
    setSelectedImages((prev) => [...prev, ...imageUrls])
  }

  const handleContinue = () => {
    const newFormData = {
      images: selectedImages,
      title,
      description,
    }
    setFormData(newFormData)
    setShowPreview(true)
  }

  return (
    <>
      {showPreview ? (
        <div
          className={`z-51 fixed left-1/2 top-0 mt-4 h-auto w-full max-w-2xl -translate-x-1/2 rounded-lg bg-white md:w-8/12 xl:w-9/12`}
        >
          <PreviewArticle formData={formData} setShowPreview={setShowPreview} onClick={onClick} />
        </div>
      ) : (
        <div
          ref={articleRef}
          className={`z-51 fixed left-1/2 top-0 mt-4 h-4/5 w-full max-w-2xl -translate-x-1/2 rounded-lg bg-white md:w-8/12 xl:w-9/12`}
        >
          <div className="mt-2 flex flex-col gap-4 border-b border-b-[#E8E8E8] p-4">
            <div className="flex w-full items-center justify-between">
              <Button
                size="small"
                theme="transparent"
                className="border-[#E8E8E8]! py-1.5!"
                onClick={onClick}
              >
                Cancel{' '}
              </Button>
              <div className="flex items-center gap-4">
                <Button
                  size="small"
                  theme="transparent"
                  className="border-none! bg-[#E8E8E8]! py-1.5!"
                >
                  Save Draft{' '}
                </Button>
                <Button
                  size="small"
                  theme="dark"
                  className="py-1.5! text-white!"
                  onClick={handleContinue}
                >
                  Continue{' '}
                </Button>
              </div>
            </div>
            <div className="mt-4 flex items-center gap-4">
              <img src="/dashboard/pritesh-avatar.svg" alt="" className="mt-1 h-auto w-8" />
              <div className="flex flex-col">
                <div className="flex items-center">
                  <h3 className="flex items-center font-semibold text-black opacity-75">
                    Pritesh Srivastava
                    <DotIcon />
                  </h3>
                  <span className="text-lucres-gray-700 text-sm">1 month ago</span>
                </div>
                <div className="text-lucres-gray-700 flex items-center text-sm">
                  <span>Bengaluru, India </span>
                  <DotIcon />
                  <span>8 Years of Exp.</span>
                </div>
              </div>
            </div>
          </div>
          {selectedImages.length > 0 ? (
            <div className="m-4 h-60 rounded-md border">
              <Swiper
                slidesPerView={selectedImages.length === 1 ? 1 : 2} // Make 1 image take full width
                spaceBetween={selectedImages.length === 1 ? 0 : 10} // Remove spacing for single image
                speed={1000}
                modules={[Autoplay, FreeMode]}
              >
                {selectedImages.map((image, index) => (
                  <SwiperSlide key={index} className="">
                    <img
                      src={image}
                      alt={`Uploaded ${index + 1}`}
                      className="mx-auto h-60 w-auto rounded-md object-cover p-2"
                    />
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
          ) : (
            <div
              className="text-lucres-gray-700 m-4 flex h-60 cursor-pointer flex-col items-center justify-center gap-4 rounded-md border p-8"
              onClick={() => fileInputRef.current?.click()}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept="image/*"
                multiple
                onChange={handleImageUpload}
              />
              <ImagesIcon size={100} color="#585858" />
              <span>We recommend uploading or dragging in an image that is 1920x1080 pixels</span>
              <Button size="small" theme="translucent">
                <PlusIcon size={16} weight="bold" className="mr-1" />
                Upload from Device
              </Button>
            </div>
          )}
          <div className="m-4 flex flex-col gap-4">
            <Input
              placeholder="Title"
              className="text-[#858585]! placeholder:text-base!"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
            <div className="dark:border-dark-lucres-black-300 flex h-40 flex-col rounded-lg border border-gray-200">
              <div className="flex h-32 gap-x-2 p-2">
                <textarea
                  className="scrollbar-none text-lucres-900 dark:bg-dark-lucres-black-500 dark:text-dark-lucres-green-100 dark:placeholder:text-dark-lucres-green-100 focus:outline-hidden w-full resize-none rounded-lg text-sm placeholder:text-sm placeholder:text-[#858585] placeholder:text-opacity-80"
                  placeholder="Start writing your thoughts here. You can also include @mentions"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
              </div>
              <div className="text-lucres-gray-700 flex items-start gap-4 p-2">
                {/* <TextBolder weight="bold" size={20} /> */}
                <TextTSlashIcon weight="bold" size={20} />
                <TextOutdentIcon weight="bold" size={20} />
                <TextIndentIcon weight="bold" size={20} />
                <TextItalicIcon weight="bold" size={20} />
                <QuotesIcon weight="bold" size={20} />
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default PostArticle
