import { useRouter } from 'next/navigation'
import Button from '../../components/Button'
import IconButton from '../../components/IconButton'
import { XIcon } from '@phosphor-icons/react'

interface ProfileProgressProps {
  percentage: number
  setShowProfileProgress?: (show: boolean) => void
}

const ProfileProgress: React.FC<ProfileProgressProps> = ({
  percentage,
  setShowProfileProgress,
}) => {
  // Convert percentage (0-100) to degrees (0-180) for semi-circle rotation
  const rotationDegrees = (percentage / 100) * 180
  const router = useRouter()
  return (
    <div className="mmd:x-4 dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 relative flex justify-around gap-4 rounded-lg border p-4">
      <div className="flex flex-col items-center justify-center">
        <div className="relative h-16 w-32 overflow-hidden">
          {/* Base (static) semicircle */}
          <img src="/dashboard/dashed-semicircle.svg" alt="Static semicircle" className="w-full" />

          {/* Rotating semicircle */}
          <div
            className="absolute left-0 top-0 flex h-full w-full justify-center"
            style={{
              transform: `rotate(${rotationDegrees + 180}deg)`,
              transformOrigin: 'bottom center',
            }}
          >
            <img
              src="/dashboard/gradient-semicircle.svg"
              alt="Rotating semicircle"
              className="w-full"
            />
          </div>
          <span className="absolute left-1/2 top-8 -translate-x-1/2 transform font-semibold">
            {percentage}%
          </span>
        </div>
        <span className="mt-2 whitespace-nowrap">Profile Completed</span>
      </div>
      <div className="flex flex-col gap-4">
        <span className="text-sm">Finish your profile to get noticed faster!</span>
        <Button theme="transparent" isRectangle size="small" onClick={() => router.push('/resume')}>
          Update Profile
        </Button>
      </div>
      <div
        className="absolute right-2 top-1 xl:hidden"
        onClick={() => setShowProfileProgress?.(false)}
      >
        <IconButton>
          <XIcon size={20} />
        </IconButton>
      </div>
    </div>
  )
}

export default ProfileProgress
