'use client'

import React, { useEffect, useState } from 'react'
import { useRouter, use<PERSON>ara<PERSON>, useSearchParams } from 'next/navigation'

import IconButton from '../../../../components/IconButton'
import SidebarN from '../../../../components/Sidebar'
import QuoteCard from '../../../../components/QuoteCard/QuoteCard'
import PostCardSkeleton from '../../../../components/PostCard/PostCardSketeton'
import NoDataFound from '../../../../components/NoDataFound/NoDataFound'

import { PostService } from '../../../../services/PostService'
import { UserService } from '../../../../services/UserService'

import { useAuth } from '../../../../context/AuthContext'
import useError from '../../../../context/ErrorContext'

import { PermalinkUser } from '../../../../models/User'
import { PostCard as PostCardType } from '../../../../models/Post'
import { ArrowLeftIcon } from '@phosphor-icons/react'
import { useFeed } from '@/context/FeedContext'

const ViewQuote: React.FC = () => {
  const router = useRouter()
  const params = useParams()
  const searchParams = useSearchParams()

  const rawId = params?.id
  const id = Array.isArray(rawId) ? rawId[0] : rawId

  const photoParam = searchParams.get('photo')
  const activeIndex = photoParam !== null ? parseInt(photoParam, 10) : null
  const isImageModal = activeIndex !== null && !isNaN(activeIndex)

  const [loading, setLoading] = useState(true)
  const [userData, setUserData] = useState<PermalinkUser | undefined>()
  const [quoteData, setQuoteData] = useState<PostCardType | undefined>()
  const { authUserProfile: userProfile } = useAuth()
  const { handleError } = useError()
  const { setFeedData } = useFeed()
  const handleBackToFeed = () => router.back()
  useEffect(() => {
    window.scrollTo(0, 0)
  }, [])

  useEffect(() => {
    const fetchQuote = async () => {
      if (!id) return
      setLoading(true)

      try {
        const res = await PostService.getQuoteById(id)
        if (res.status === 'success') {
          const quoteWrapped: PostCardType = {
            id: res.data.id,
            type: 'QUOTE',
            isLiked: res.data.isLiked,
            isReposted: res.data.isReposted,
            quote: { ...res.data },
          }
          setQuoteData(quoteWrapped)
        }
      } catch (err) {
        handleError(err)
        setQuoteData(undefined)
      } finally {
        setLoading(false)
      }
    }

    fetchQuote()
  }, [id])

  useEffect(() => {
    if (userProfile?.permalink) {
      UserService.getUserProfileByPermalink(userProfile.permalink)
        .then((res) => setUserData(res.data))
        .catch((err) => console.error('Error fetching profile data:', err))
    }
  }, [userProfile])

  return (
    <section className="font-inter flex h-full w-full items-center justify-center px-2 py-16 pb-[80px] sm:py-0 md:mt-16 lg:px-10">
      <div className="w-full max-w-[1260px] px-4 md:px-0">
        <div className="hidden lg:block">{userData && <SidebarN data={userData} />}</div>

        <div className="flex items-start gap-4 lg:ms-64 xl:flex-row">
          <div className="md:dark:border-dark-lucres-black-300 flex min-h-screen w-full flex-col gap-5 sm:px-4 lg:max-w-[650px] lg:border-x xl:w-9/12">
            <div className="my-4">
              <div className="flex cursor-pointer items-center gap-4" onClick={handleBackToFeed}>
                <IconButton className="dark:bg-dark-lucres-black-500 w-fit bg-white">
                  <ArrowLeftIcon
                    size={24}
                    className="text-lucres-900 dark:text-dark-lucres-green-100"
                  />
                </IconButton>
                <h3 className="text-lucres-gray-700 hover:text-lucres-600 dark:text-lucres-300 dark:hover:text-lucres-500 font-medium">
                  Back
                </h3>
              </div>

              <div className="py-4">
                {loading ? (
                  <PostCardSkeleton />
                ) : quoteData ? (
                  <QuoteCard
                    quoteData={quoteData}
                    setQuoteData={setQuoteData}
                    isCommentsOpen={true}
                    isViewPost={true}
                    setData={setFeedData}
                  />
                ) : (
                  <NoDataFound subtitle="Quote not found or deleted." />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ViewQuote
