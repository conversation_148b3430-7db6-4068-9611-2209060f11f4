'use client'
import React, { JSX, useCallback, useEffect, useRef, useState } from 'react'

import { ImageIcon as IconImage, SmileyIcon } from '@phosphor-icons/react'
// @ts-ignore
import 'swiper/css'
// @ts-ignore
import 'swiper/css/free-mode'
// @ts-ignore
import 'swiper/css/pagination'
import SidebarN from '../../components/Sidebar'
import Button from '../../components/Button'
import ProfileProgress from './ProfileProgress'
import InterestingTalent from '../../components/InterestingTalent'
import PostArticle from './PostArticle'
import CreatePost from './CreatePost'
import { useRouter } from 'next/navigation'
import { PermalinkUser } from '../../models/User'
import { UserService } from '../../services/UserService'
import { useAuth } from '../../context/AuthContext'
import Avatar from '../../components/Avatar/Avatar'
import { PostService } from '../../services/PostService'
import PostCard from '../../components/PostCard/PostCard'
import PostCardSkeleton from '../../components/PostCard/PostCardSketeton'
import Tooltip from '../../components/Tooltip'
import IconButton from '../../components/IconButton'
import RepostCard from '../../components/PostCard/RepostCard'
import QuoteCard from '../../components/QuoteCard/QuoteCard'
import { PostCard as PostCardType } from '../../models/Post'
import NoDataFound from '../../components/NoDataFound/NoDataFound'
import { useFeed } from '../../context/FeedContext'
import useError from '../../context/ErrorContext'
import JobCard from '../jobs/JobCard'
import { Job } from '../../models/Job'
import TrendingJobs from '../../components/TrendingJobs'
import { useAccount } from '@/context/UserDetailsContext'
import JobCardWrapper from '@/components/PostCard/JobCardWrapper'

const page: React.FC = () => {
  const [showArticleModal, setShowArticleModal] = useState<boolean>(false)
  const [showPostModal, setShowPostModal] = useState<boolean>(false)
  // const [userData, setUserData] = useState<PermalinkUser | undefined>()

  const [isAiResumePromptVisible, setIsAiResumePromptVisible] = useState<boolean>(true)
  const { authUserProfile: userProfile, isAuthenticated } = useAuth()
  const { resumeProgress, setResumeProgress } = useAccount()
  const [showProfileProgress, setShowProfileProgress] = useState<boolean>(true)
  const router = useRouter()
  const { handleError } = useError()
  const {
    feedData,
    setFeedData,
    page,
    setPage,
    hasNextPage,
    setHasNextPage,
    fetchedPages,
    setFetchedPages,
    loading,
    setLoading,
  } = useFeed()
  const observer = useRef<IntersectionObserver | null>(null)

  const fetchFeedData = async (permalink: string, page: number, options = { refetch: false }) => {
    if (!options.refetch && fetchedPages.has(page)) {
      setLoading(false)
      return
    }
    try {
      const response = await PostService.getFeedData(permalink, page)
      const { items, paginator } = response.data
      if (page === 1) {
        setFeedData(items)
      } else {
        setFeedData((prev) => {
          const updatedFeed = [...prev, ...items]
          return updatedFeed
        })
      }
      setHasNextPage(paginator.hasNextPage)
      // Mark this page as fetched
      setFetchedPages((prev) => new Set(prev).add(page))
    } catch (error) {
      // console.error("Error fetching feed data:", error);
      // handleError(error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/sign-in')
      return
    }

    if (userProfile?.permalink) {
      fetchFeedData(userProfile.permalink, page)
    }

    if (page === 1) {
      window.scrollTo(0, 0)
    }
  }, [page, userProfile?.permalink])

  useEffect(() => {
    if (userProfile?.id) {
      const key = `resumeProgress_${userProfile.id}`
      const storedProgress = localStorage.getItem(key)
      if (storedProgress) {
        setResumeProgress(Number(storedProgress))
      } else {
        setResumeProgress(11) // Default value if no progress exists for this user
      }
    }
  }, [userProfile, setResumeProgress])

  const lastPostElementRef = useCallback(
    (node: HTMLDivElement) => {
      if (loading) return
      if (observer.current) observer.current.disconnect()
      observer.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasNextPage) {
            setPage((prevPage) => prevPage + 1)
          }
        },
        { threshold: 0.5 },
      )
      if (node) observer.current.observe(node)
    },
    [loading, hasNextPage],
  )

  const handleApplyJob = (job: Job) => {
    if (!isAuthenticated) router.push('/sign-in')
    else {
      if ((job as any).questionnaire) {
      } else {
      }
    }
  }

  const handleJobCardClick = (job: Job) => {
    router.push(`/job/${job?.job?.permalink}`)
  }

  return (
    <section className="font-inter flex h-full w-full items-center justify-center py-16 pb-[80px] lg:mt-16 lg:px-10 lg:py-0">
      <div className="mx-auto flex  w-full max-w-[1260px]  items-start justify-start px-4 md:px-0">
        <div className="relative hidden lg:block ">
          <SidebarN data={userProfile} />
        </div>
        <div className="flex w-full flex-row items-start gap-x-4  lg:ms-64">
          <div className="md:dark:border-dark-lucres-black-300 flex w-full  flex-col gap-5 gap-y-4 md:border-x md:pt-4 lg:max-w-[650px] xl:w-9/12">
            <div className="-mt-2"></div>
            <div
              className="dark:border-dark-lucres-black-300 flex cursor-pointer flex-col rounded-lg border border-gray-200 md:mx-4"
              onClick={() => setShowPostModal(true)}
            >
              <div className="p-3">
                <div className="flex items-start justify-start gap-x-4">
                  <Avatar
                    src={userProfile?.profileImage.url}
                    alt={`${userProfile?.profileImage.name}'s Avatar`}
                    size={10}
                    className="cursor-pointer object-cover"
                    onClick={() => setShowPostModal(true)}
                  />
                  <div className="text-lucres-gray-700 dark:text-lucres-green-100/70 cursor-pointer">
                    What's Happening?
                  </div>
                </div>
                <div className="ms-14 flex items-end justify-between">
                  <div className="flex items-end gap-x-4">
                    <Tooltip text="Add image(s)" direction="bottom" classes="whitespace-nowrap">
                      <IconButton theme="secondary">
                        <IconImage
                          size={20}
                          className="dark:text-dark-lucres-green-100 cursor-pointer"
                        />
                      </IconButton>
                    </Tooltip>
                    <Tooltip text="Add emoji" direction="bottom" classes="whitespace-nowrap">
                      <IconButton theme="secondary">
                        <SmileyIcon
                          size={20}
                          className="dark:text-dark-lucres-green-100 cursor-pointer"
                        />
                      </IconButton>
                    </Tooltip>
                  </div>

                  <Button size="small" className="px-6! py-2!" theme="dark" disabled>
                    Post
                  </Button>
                </div>
              </div>
            </div>
            {resumeProgress < 100 && showProfileProgress && (
              <div className="block w-full md:px-4 xl:hidden xl:px-0">
                <ProfileProgress
                  percentage={resumeProgress}
                  setShowProfileProgress={setShowProfileProgress}
                />
              </div>
            )}
            {/* {isAiResumePromptVisible && (
                  <div className="relative flex h-full flex-col items-start justify-center gap-4 rounded-lg border bg-linear-to-r from-[#dbf4db] via-[#cbf4de] to-[#d5f0d5] p-3 px-4 md:mx-4 md:flex-row md:items-center dark:border-dark-lucres-black-300 dark:from-dark-lucres-black-500 dark:via-dark-lucres-black-400 dark:to-dark-lucres-black-500">
                    <div className="flex w-11/12  items-center justify-start gap-x-1 md:w-6/12 md:justify-center md:gap-x-4">
                      <img src="/dashboard/dotsCircle.svg" alt="" className="w-10 md:w-16" />
                      <h3 className="text-center font-medium md:text-left xl:text-lg">
                        Build a professional AI resume / profile{' '}
                        <span className="font-semibold">effortlessly</span>
                      </h3>
                    </div>
                    <div className="flex w-full  cursor-pointer items-end justify-center  md:w-4/12 md:justify-end">
                      <button
                        className="rounded-md border border-lucres-gray-700 p-2 hover:text-lucres-gray-700 dark:border-dark-lucres-black-300 dark:hover:text-dark-lucres-green-300"
                        onClick={() => navigate('/ai')}
                        // className='hover:bg-dark-lucres-black-200'
                      >
                        Yes, Let's do this
                      </button>
                      <div className="">
                        <X
                          size={20}
                          weight="bold"
                          onClick={() => setIsAiResumePromptVisible(false)}
                          className="absolute right-3 top-3"
                        />
                      </div>
                    </div>
                  </div>
                )} */}
            <div className="flex w-full flex-col gap-4 md:px-4">
              {loading ? (
                Array.from({ length: 3 }).map((_, id) => <PostCardSkeleton key={id} />)
              ) : feedData.length > 0 ? (
                feedData.map((postData, index) => {
                  const relativeIndex = index % 20
                  const commonProps = {
                    fetchData: fetchFeedData,
                    setData: setFeedData,
                    data: feedData,
                  }

                  let postComponent: JSX.Element | null = null

                  switch (postData.type) {
                    case 'ARTICLE':
                      postComponent = (
                        <PostCard key={postData.id} postData={postData} {...commonProps} />
                      )
                      break
                    case 'REPOST':
                      if (
                        postData.repost?.repostedItem &&
                        ['ARTICLE', 'QUOTE', 'JOB'].includes(postData.repost.type)
                      ) {
                        postComponent = (
                          <RepostCard key={postData.id} repostData={postData} {...commonProps} />
                        )
                      }
                      break
                    case 'QUOTE':
                      postComponent = (
                        <QuoteCard key={postData.id} quoteData={postData} {...commonProps} />
                      )
                      break
                    case 'JOB':
                      const job = postData.job
                      postComponent = (
                        <JobCardWrapper
                          key={postData.id}
                          setData={setFeedData}
                          postData={postData}
                        />
                      )
                      break
                  }

                  return (
                    <React.Fragment key={postData.id}>
                      {postComponent}
                      {relativeIndex === 9 && <TrendingJobs />}
                      {relativeIndex === 18 && <InterestingTalent />}
                    </React.Fragment>
                  )
                })
              ) : (
                <NoDataFound />
              )}
              {hasNextPage && (
                <div
                  ref={lastPostElementRef}
                  className="flex w-full flex-col items-center justify-center"
                >
                  <PostCardSkeleton />
                </div>
              )}
            </div>
          </div>
          {resumeProgress < 100 && showProfileProgress && (
            <div className="hidden  pb-2 pt-6 md:px-4 xl:flex xl:px-0">
              <ProfileProgress
                percentage={resumeProgress}
                setShowProfileProgress={setShowProfileProgress}
              />
            </div>
          )}
        </div>
      </div>
      {/* Article Posting */}
      {showArticleModal && (
        <div className="dark:bg-dark-lucres-black-500 z-50! absolute left-0 top-0 h-screen w-full bg-black bg-opacity-75">
          <PostArticle
            onClick={() => setShowArticleModal(false)}
            showArticleModal={showArticleModal}
          />
        </div>
      )}
      {showPostModal && (
        <div className="dark:bg-dark-white z-50! fixed inset-0 bottom-0 left-0 flex !min-h-screen w-full items-center  justify-center bg-black/75 dark:bg-opacity-50">
          <CreatePost
            handleClose={() => setShowPostModal(false)}
            showPostModal={showPostModal}
            fetchFeedData={fetchFeedData}
            setData={setFeedData}
            // feedData={feedData}
          />
        </div>
      )}
    </section>
  )
}

export default page
