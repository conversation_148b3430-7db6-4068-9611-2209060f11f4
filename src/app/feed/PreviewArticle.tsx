import { DotIcon, XIcon } from '@phosphor-icons/react'
import Input from '../../components/Input'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, FreeMode } from 'swiper/modules'
// @ts-ignore
import 'swiper/css'
// @ts-ignore
import 'swiper/css/free-mode'
// @ts-ignore
import 'swiper/css/pagination'
import Button from '../../components/Button'
interface FormData {
  images: string[]
  title: string
  description: string
}

function PreviewArticle({
  formData,
  setShowPreview,
  onClick,
}: {
  formData: FormData
  setShowPreview: (value: boolean) => void
  onClick: () => void
}) {
  return (
    <div className="flex flex-col gap-2 pb-4">
      <div className="flex w-full justify-between border-b p-4">
        <div className="flex items-start gap-4">
          <img src="/dashboard/pritesh-avatar.svg" alt="" className="mt-1 h-auto w-8" />
          <div className="flex flex-col">
            <div className="flex items-center">
              <h3 className="flex items-center font-semibold text-black opacity-75">
                Pritesh Srivastava
                <DotIcon />
              </h3>
              <span className="text-lucres-gray-700 text-sm">2 Days ago</span>
            </div>
            <div className="text-lucres-gray-700 flex items-center text-sm">
              <span>Bengaluru, India </span>
              <DotIcon />
              <span>8 Years of Exp.</span>
            </div>
            <Input
              className="placeholder:text-lucres-gray-700! border-none! p-0! mt-2"
              placeholder="Tell us about your Article  .."
            />
          </div>
        </div>
        <XIcon size={20} weight="bold" onClick={() => onClick()} className="cursor-pointer" />
      </div>
      <div className="mx-4 rounded-md border">
        <Swiper
          slidesPerView={formData.images.length === 1 ? 1 : 2} // Make 1 image take full width
          spaceBetween={formData.images.length === 1 ? 0 : 10} // Remove spacing for single image
          speed={1000}
          modules={[Autoplay, FreeMode]}
        >
          {formData.images.map((image, index) => (
            <SwiperSlide key={index} className="">
              <img
                src={image}
                alt={`Uploaded ${index + 1}`}
                className="mx-auto h-60 w-auto object-cover"
              />
            </SwiperSlide>
          ))}
        </Swiper>
        <div className="flex flex-col p-4">
          <h3 className="text-lg font-semibold">{formData.title}</h3>
          <div className="flex items-center gap-2">
            <img src="/dashboard/pritesh-avatar.svg" alt="" className="mt-2 h-auto w-6" />
            <div className="mt-2 flex items-center">
              <h3 className="flex items-center font-semibold text-black opacity-75">
                Pritesh Srivastava
                <DotIcon />
              </h3>
              <span className="text-lucres-gray-700 text-sm">2 Days ago</span>
            </div>
          </div>
          <p className="mt-4 text-sm text-[#858585]">{formData.description}</p>
        </div>
      </div>
      <div className="mx-4 flex items-center justify-end">
        <Button size="small" theme="white" onClick={() => setShowPreview(false)}>
          Back to Editor
        </Button>
        <Button size="small" className="py-2!">
          Post
        </Button>
      </div>
    </div>
  )
}

export default PreviewArticle
