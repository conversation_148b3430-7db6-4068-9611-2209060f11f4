import { useState } from 'react'
import Button from '../../components/Button'
import Input from '../../components/Input'
import { useRouter } from 'next/navigation'
import { ArrowLeft } from '@phosphor-icons/react'
import { AuthService } from '../../services/AuthService'

interface ResetPasswordProps {
  onBack: () => void
  otp: string
  email: string
}

// Password validation regex
const PASSWORD_REGEX = /^[^"'\\;#*]+$/

const ResetPassword: React.FC<ResetPasswordProps> = ({ onBack, otp, email }) => {
  const [password, setPassword] = useState<string>('')
  const [confirmPassword, setConfirmPassword] = useState<string>('')
  const [passwordError, setPasswordError] = useState<string>('')
  const router = useRouter()

  const handleError = async () => {
    if (!PASSWORD_REGEX.test(password)) {
      setPasswordError('Special characters like \', ", \\, ;, #, and * are not allowed.')
      return
    }

    setPasswordError('')
  }

  const handlePasswordReset = async () => {
    if (passwordError !== '') return

    try {
      await AuthService.forgotPassword(email, otp, password)
      router.push('/sign-in') // Navigate to sign-in page on success
    } catch (error: any) {
      // Check if there's a backend error message
      if (error.message) {
        setPasswordError(error.message)
      } else if (error.data?.message) {
        setPasswordError(error.data.message)
      } else {
        setPasswordError('Failed to reset password. Please try again.')
      }
    }
  }

  return (
    <div className="w-full max-w-md px-5 py-4">
      {/* <Button
        type="button"
        theme="transparent"
        className="mb-10 flex items-center border-none p-0! text-base! hover:bg-transparent"
        onClick={onBack}
      >
        <ArrowLeft className="mr-1" /> Back
      </Button> */}
      <h1 className="mb-5 text-xl font-medium">Please enter your new password</h1>
      <form>
        <div className="flex flex-col gap-2">
          <Input
            type="password"
            id="password"
            label="New Password"
            placeholder="Enter Password"
            className={`mb-1 w-full rounded-sm border p-2`}
            value={password}
            onChange={(e) => {
              setPassword(e.target.value)
              setPasswordError('')
            }}
            onBlur={handleError}
            maxLength={20}
            required
            error={passwordError}
          />

          <Input
            type="password"
            id="confirm-password"
            label="Re-Enter New Password"
            placeholder="Enter Password"
            className={`mb-1 w-full rounded-sm border p-2`}
            value={confirmPassword}
            onChange={(e) => {
              setConfirmPassword(e.target.value)
            }}
            required
          />
        </div>
        <Button
          disabled={
            !password || !confirmPassword || password !== confirmPassword || passwordError !== ''
          }
          className="disabled:bg-lucres-800 mt-4 w-full rounded-lg disabled:cursor-not-allowed"
          onClick={handlePasswordReset}
        >
          Save New Password
        </Button>
      </form>
    </div>
  )
}

export default ResetPassword
