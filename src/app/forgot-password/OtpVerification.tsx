import { useState } from 'react'
import InputOtp from '../../components/InputOtp'
import Button from '../../components/Button'
import { ArrowLeft } from '@phosphor-icons/react'
import { OtpService } from '../../services/OtpService'

interface OtpVerificationProps {
  email: string
  onNext: (otp: string) => void
  onBack: () => void
}

const OtpVerification: React.FC<OtpVerificationProps> = ({ email, onNext, onBack }) => {
  const [otp, setOtp] = useState<string>('')
  const [error, setError] = useState<string | null>(null)

  const handleResendOtp = async () => {
    try {
      const response = await OtpService.sendOtp(email, 'resend', 'other')
      if (response.status !== 'success') {
        setError('Failed to resend otp')
      }
    } catch (error: any) {
      console.error('Failed to send OTP')
      // Check if there's a backend error message
      if (error.message) {
        setError(error.message)
      } else if (error.data?.message) {
        setError(error.data.message)
      } else {
        setError('Failed to resend OTP. Please try again.')
      }
    }
  }

  const handleOtpVerification = async () => {
    if (otp.length !== 6) return
    try {
      const response = await OtpService.verifyOtp(email, otp, 'signup')

      if (response.status === 'success') {
        onNext(otp) // Move to the next step only if status is 200
      } else {
        setError('Invalid OTP. Please try again.')
      }
    } catch (error: any) {
      // Check if there's a backend error message
      if (error.message) {
        setError(error.message)
      } else if (error.data?.message) {
        setError(error.data.message)
      } else {
        setError('Invalid OTP. Please try again.')
      }
    }
  }

  const handleOtpChange = (otpValue: string) => {
    setOtp(otpValue)
    // Clear error when user starts typing
    if (error) {
      setError(null)
    }
  }

  return (
    <div className="w-full max-w-sm px-5 py-4">
      <Button
        type="button"
        theme="transparent"
        className="items-cexnter p-0! text-base! mb-10 flex border-none hover:bg-transparent"
        onClick={onBack}
      >
        <ArrowLeft className="mr-1" /> Back
      </Button>
      <h1 className="mb-4 text-xl font-semibold">Please enter your verification code.</h1>
      <p className="dark:text-dark-lucres-green-300 mb-4 text-sm text-gray-500">
        We have sent you an verification code to your registered email ID
      </p>
      <form>
        <div className="text-dark-lucres-black-400 my-8">
          <InputOtp
            length={6}
            onSubmit={handleOtpChange}
            resendButtonText="Didn't receive code? Resend"
            onResendOtp={handleResendOtp}
            inputClassName="h-12! w-12! "
          />
        </div>
        {error && <p className="-mt-4 mb-2 text-sm text-red-500">{error}</p>}
        <Button
          disabled={otp.length !== 6}
          className="disabled:bg-lucres-800 w-full rounded-lg disabled:cursor-not-allowed"
          onClick={handleOtpVerification}
        >
          Continue
        </Button>
      </form>
    </div>
  )
}

export default OtpVerification
