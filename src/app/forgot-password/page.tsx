'use client'
import { useState } from 'react'
import ForgotPassword from './ForgotPassword'
import OtpVerification from './OtpVerification'
import ResetPassword from './ResetPassword'

const ForgotPasswordWorkflow: React.FC = () => {
  const [step, setStep] = useState<number>(1)
  const [email, setEmail] = useState<string>('')
  const [otp, setOtp] = useState<string>('')

  const handleNextStep = (nextData?: { email?: string; otp?: string }) => {
    if (nextData?.email) setEmail(nextData.email)
    if (nextData?.otp) setOtp(nextData.otp)
    setStep((prev) => prev + 1)
  }

  const handlePreviousStep = () => {
    setStep((prev) => (prev > 1 ? prev - 1 : prev))
  }

  return (
    <div className="flex min-h-screen items-center justify-center">
      {step === 1 && <ForgotPassword onNext={(email) => handleNextStep({ email })} />}
      {step === 2 && (
        <OtpVerification
          email={email}
          onNext={(otp) => handleNextStep({ otp })}
          onBack={handlePreviousStep}
        />
      )}
      {step === 3 && <ResetPassword onBack={handlePreviousStep} otp={otp} email={email} />}
    </div>
  )
}

export default ForgotPasswordWorkflow
