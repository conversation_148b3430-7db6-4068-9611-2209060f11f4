'use client'
import { useState } from 'react'
import Button from '../../components/Button'
import Input from '../../components/Input'
import { ArrowLeftIcon } from '@phosphor-icons/react'
import { useRouter } from 'next/navigation'
import { OtpService } from '../../services/OtpService'
import { AuthService } from '../../services/AuthService'

interface ForgotPasswordProps {
  onNext: (email: string) => void
}

// Email validation regex
const EMAIL_REGEX = /^[a-zA-Z0-9][a-zA-Z0-9._%+-]*[a-zA-Z0-9]@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

const ForgotPassword: React.FC<ForgotPasswordProps> = ({ onNext }) => {
  const [email, setEmail] = useState<string>('')
  const [error, setError] = useState<string>('')

  const handleSendOtp = async () => {
    if (!EMAIL_REGEX.test(email)) {
      setError('Please enter a valid email address.')
      return
    }

    setError('') // Clear any previous errors

    try {
      const validEmail = await AuthService.validateEmail(email)
      if (validEmail.status !== 'success') {
        const response = await OtpService.sendOtp(email, 'send', 'other')
        onNext(email)
      } else {
        setError("Email doesn't exist. Please enter a registerd email")
      }
    } catch (error: any) {
      console.error('Failed to send OTP')
      // Check if there's a backend error message
      if (error.message) {
        setError(error.message)
      } else if (error.data?.message) {
        setError(error.data.message)
      } else {
        setError('Failed to send OTP. Please try again.')
      }
    }
  }

  const router = useRouter()

  const handleGoBack = () => {
    router.push('/sign-in')
  }

  return (
    <div className="w-full max-w-lg px-5 py-4">
      <div
        className="flex cursor-pointer items-center gap-2 py-3 hover:opacity-50"
        onClick={() => router.back()}
      >
        <ArrowLeftIcon size={18} weight="bold" /> <span className="font-medium">Back</span>
      </div>
      <h1 className="mb-4 text-xl font-semibold">Forgot Password</h1>
      <h3 className="text-dark-lucres-black-100 dark:text-dark-lucres-green-300 mb-3 text-sm leading-4">
        Please enter your registered email ID. We will send you a verification code.
      </h3>
      <form>
        <Input
          type="email"
          placeholder="<EMAIL>"
          label="Enter your email"
          className={`w-full rounded-sm border p-2 ${error ? 'border-red-500' : ''}`}
          value={email}
          onChange={(e) => {
            setEmail(e.target.value)
            setError('')
          }}
          error={error}
        />
        <Button
          disabled={!email || !EMAIL_REGEX.test(email)}
          className="disabled:bg-lucres-800 mt-6 w-full rounded-lg disabled:cursor-not-allowed"
          theme="opaque"
          onClick={handleSendOtp}
        >
          Continue
        </Button>
      </form>
    </div>
  )
}

export default ForgotPassword
