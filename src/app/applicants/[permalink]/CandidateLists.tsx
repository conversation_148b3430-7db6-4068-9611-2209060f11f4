'use client'
import { ArrowLeftIcon, LockOpenIcon, NotePencilIcon } from '@phosphor-icons/react'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Button from '../../../components/Button'
import Radio from '../../../components/Radio'
// import Select from '../../components/Select'
import CandidateCard from './CandidateCard'
import IconButton from '../../../components/IconButton'
import Tooltip from '../../../components/Tooltip'
import { useTheme } from '../../../context/ThemeProvider'
import { JobPreviewDetails } from '../../../models/Job'
import { getRelativeTime } from '../../../utils/commonUtils'
// import { JobService } from '../../services/JobService'
import useError from '../../../context/ErrorContext'
import { JobService } from '../../../services/JobService'
import Select from '../../../components/Select'

interface CandidateListProps {
  isSmallScreen: boolean
  jobDetails: JobPreviewDetails | undefined
  onApplicantSelect: (applicantId: string) => void
  setIsBought: (revealProfile: boolean) => void
  freeUnlockCount: number | undefined
  setApplicantStatus: (applicantStatus: string) => void
  filteredApplications: Array<Record<string, any>>
  setFilteredApplications: React.Dispatch<React.SetStateAction<Array<Record<string, any>>>>
}

const CandidateLists: React.FC<CandidateListProps> = ({
  isSmallScreen,
  jobDetails,
  onApplicantSelect,
  setIsBought,
  freeUnlockCount = 0,
  setApplicantStatus,
  filteredApplications,
  setFilteredApplications,
}) => {
  const router = useRouter()
  const { theme } = useTheme()
  const { handleError } = useError()
  const FREE_UNLOCK_COUNT = 5
  const [selectedApplicantId, setSelectedApplicantId] = useState<string | null>(null)
  const [selectedOption, setSelectedOption] = useState<string>('All')
  const radioButtons = ['All', 'Unlocked', 'Pending', 'Rejected', 'Saved']
  const [selectedSortCriteria, setSelectedSortCriteria] = useState<string>('newest')
  const states = [
    { value: 'newest', label: 'Newest' },
    { value: 'oldest', label: 'Oldest' },
  ]

  const fetchApplications = async () => {
    if (!jobDetails?.id) return
    try {
      let status = ''
      switch (selectedOption) {
        case 'Unlocked':
          status = 'BOUGHT'
          break
        case 'Pending':
          status = 'PENDING'
          break
        case 'Rejected':
          status = 'REJECTED'
          break
        case 'Saved':
          status = 'SAVED'
          break
        case 'All':
          status = ''
          break
        default:
          status = ''
      }
      const sort = selectedSortCriteria === 'newest' ? 'desc' : 'asc'

      const response = await JobService.getJobApplicationsCount(jobDetails.id, status, sort)

      // Ensure we're getting an array
      const applications = Array.isArray(response.data.items) ? response.data.items : []
      setFilteredApplications(applications)
      setApplicantStatus(status)
    } catch (error) {
      handleError(error)
    }
  }

  useEffect(() => {
    if (jobDetails?.id) {
      fetchApplications()
    }
  }, [jobDetails?.id, selectedOption, selectedSortCriteria])

  // Update filtered applications when initial applications change
  // useEffect(() => {
  //   if (initialApplications) {
  //     const newFilteredApplications =
  //       selectedOption === "All"
  //         ? initialApplications
  //         : initialApplications.filter(
  //             (app: any) => app.status === selectedOption.toUpperCase()
  //           );
  //     setFilteredApplications(newFilteredApplications);
  //   }
  // }, [initialApplications, selectedOption, selectedSortCriteria]);

  const handleSortChange = async (value: string) => {
    setSelectedSortCriteria(value)
  }
  const handleRadioChange = (value: string) => {
    setSelectedOption(value)
    setSelectedApplicantId(null)
  }

  const handleEditJob = () => {
    const editJobData = {
      id: jobDetails?.id,
      jobTitle: jobDetails?.title,
      minSalary: jobDetails?.salary?.min?.toString(),
      maxSalary: jobDetails?.salary?.max?.toString(),
      salaryPeriod: jobDetails?.salary?.period,
      numOpenings: jobDetails?.openings,
      location: jobDetails?.location,
      remotePolicy: jobDetails?.employmentType || 'in-office',
      experience: `${jobDetails?.workExperience?.min || 0} - ${
        jobDetails?.workExperience?.max || 0
      } years`,
      jobType: jobDetails?.employmentType?.toLowerCase().replace(/_/g, ' - '),
      requiredSkills: jobDetails?.skills?.map((skill) => skill.name) || [],
      jobDescription: jobDetails?.description,
      industry: jobDetails?.industry,
      companyName: jobDetails?.company?.name,
      website: jobDetails?.company?.websiteUrl,
      companyLogo: jobDetails?.companyLogo,
    }

    const params = new URLSearchParams({
      editMode: 'true',
      jobDetails: JSON.stringify(editJobData),
    })

    router.push(`/post-job?${params.toString()}`)
  }

  const handleApplicantSelect = (applicantion: any) => {
    setSelectedApplicantId(applicantion.id)
    onApplicantSelect(applicantion)
  }

  // const getFilteredApplications = () => {
  //   return filteredApplications;
  // };

  return (
    <div
      className={`scrollbar-none h-screen overflow-auto pb-10 ${
        isSmallScreen ? 'w-full' : 'w-5/12 border-r'
      } dark:border-dark-lucres-black-200 px-6`}
    >
      <div className="flex items-center justify-between py-1">
        <div className="flex cursor-pointer items-center gap-2 py-3" onClick={() => router.back()}>
          <ArrowLeftIcon size={18} weight="bold" /> <span className="font-medium">Back</span>
        </div>
        <Button
          size="small"
          theme="transparent"
          className="min-1140:flex dark:border-dark-lucres-black-200! px-3! py-2! font-medium! hidden rounded-lg text-xs"
        >
          <LockOpenIcon size={22} className="mr-2" />
          {FREE_UNLOCK_COUNT - freeUnlockCount}/5 Reveals Left
        </Button>
      </div>
      <div className="dark:border-dark-lucres-black-200 mb-4 flex cursor-pointer justify-between rounded-lg border p-4">
        <div>
          <div className="font-medium">{jobDetails?.title}</div>
          <div className="text-lucres-800 dark:text-dark-lucres-black-100 flex items-center gap-1 text-xs">
            <span className="whitespace-nowrap">
              Posted {jobDetails?.createdAt && getRelativeTime(jobDetails?.createdAt)}
            </span>
            <div className="border-lucres-800 bg-lucres-800 dark:border-dark-lucres-green-300 dark:bg-dark-lucres-green-300 h-1 w-1 rounded-full border"></div>
            <span>
              {jobDetails?.location?.address.state &&
                jobDetails?.location?.address.state + ' ' + jobDetails?.location?.address.country}
            </span>
          </div>
        </div>
        <div className="flex items-start gap-2">
          {/* <div>
            <Tooltip
              text="Add team member"
              direction="bottom"
              classes="whitespace-nowrap text-center"
            >
              <IconButton className="min-1140:hidden">
                <UserPlus size={22} color={theme === 'dark' ? '#F2F7F3' : '#2D4232'} />
              </IconButton>
            </Tooltip>
            <Button
              size="small"
              theme="transparent"
              className="hidden rounded-lg border-lucres-800 px-3! py-2! text-xs font-medium! min-1140:flex dark:border-dark-lucres-black-400!"
            >
              <UserPlus size={22} className="mr-2" />
              Add team member
            </Button>
          </div> */}
          <div onClick={handleEditJob}>
            <Tooltip text="Edit job" direction="bottom" classes="whitespace-nowrap text-center">
              <IconButton className="min-1140:hidden flex">
                <NotePencilIcon size={22} color={theme === 'dark' ? '#F2F7F3' : '#2D4232'} />
              </IconButton>
            </Tooltip>
            <Button
              size="small"
              theme="transparent"
              className="min-1140:!flex dark:border-dark-lucres-black-200 px-3! py-2! font-medium! !hidden rounded-lg border text-xs"
            >
              <NotePencilIcon size={22} className="mr-2" />
              Edit Job
            </Button>
          </div>
        </div>
      </div>
      <div className="flex flex-wrap gap-2">
        {radioButtons.map((item) => {
          return (
            <Radio
              key={item}
              name={item.toLowerCase()}
              value={item}
              label={item}
              selected={selectedOption}
              setSelected={(value) => handleRadioChange(value)}
              classname={`rounded-full! ml-0! font-medium! ${
                selectedOption === item ? '' : 'text-lucres-800!'
              }`}
            />
          )
        })}
      </div>
      <div className="mt-5 flex w-full flex-wrap items-center justify-end gap-2">
        {/* <div className="flex w-full items-center rounded-full border px-1 dark:border-dark-lucres-black-200">
          <input
            type="text"
            placeholder="Search Candidate"
            className="w-5! grow bg-transparent px-6 outline-hidden placeholder:text-lucres-800"
          />

          <Button theme={'transparent'} size={'small'} className="rounded-full border-none p-2!">
            <MagnifyingGlass size={20} weight="bold" />
          </Button>
        </div>*/}
        <div className="flex w-1/2 items-center gap-2">
          <div className="text-lucres-800 min-w-fit text-sm">Sort by</div>
          <Select
            options={states}
            value={selectedSortCriteria}
            onChange={handleSortChange}
            classes="px-3! py-2! gap-2 text-sm mt-0! font-normal!"
          />
        </div>
      </div>
      <div className="mt-4 flex flex-col gap-2">
        <div className="text-lucres-800 mb-1 text-sm">Today</div>
        <div className="flex flex-col gap-6">
          {filteredApplications.map((application: any, index: any) => (
            <CandidateCard
              application={application}
              jobId={jobDetails?.id || ''}
              key={application.id}
              onSelect={() => handleApplicantSelect(application)}
              setIsBought={setIsBought}
              isActive={selectedApplicantId === application.id}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

export default CandidateLists
