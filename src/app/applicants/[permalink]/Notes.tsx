'use client'
import { useState, useEffect } from 'react'
import { PlusIcon, XIcon } from '@phosphor-icons/react'
import Button from '../../../components/Button'

const colorOptions = ['#F1CE7C', '#EAA988', '#78D5F8', '#D7DB96'] as const
type ColorKey = (typeof colorOptions)[number]

const colorMapping: Record<ColorKey, string> = {
  '#F1CE7C': '#FFEEBC',
  '#EAA988': '#FFDBC9',
  '#78D5F8': '#C7F0FF',
  '#D7DB96': '#FBFFBD',
}

interface Note {
  id: string
  content: string
  color: ColorKey
  creator: string
  date: string
}

const Notes: React.FC = () => {
  const [notes, setNotes] = useState<Note[]>([])
  const [isColorMenuOpen, setIsColorMenuOpen] = useState(false)
  // const [selectedColor, setSelectedColor] = useState<ColorKey | null>(null);

  const creator = '<PERSON><PERSON>'
  const currentDate = new Date().toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
  })

  const handleColorSelect = (color: ColorKey) => {
    // setSelectedColor(color);
    setIsColorMenuOpen(false)
    const newNote: Note = {
      id: Date.now().toString(),
      content: '',
      color,
      creator,
      date: currentDate,
    }
    setNotes([...notes, newNote])
  }

  const handleEditNote = (id: string, content: string) => {
    setNotes((prevNotes) => prevNotes.map((note) => (note.id === id ? { ...note, content } : note)))
  }

  const handleDeleteNote = (id: string) => {
    setNotes(notes.filter((note) => note.id !== id))
  }

  const handleOutsideClick = (e: MouseEvent) => {
    const menu = document.getElementById('color-menu')
    if (menu && !menu.contains(e.target as Node)) {
      setIsColorMenuOpen(false)
    }
  }

  useEffect(() => {
    if (isColorMenuOpen) {
      document.addEventListener('click', handleOutsideClick)
    }
    return () => document.removeEventListener('click', handleOutsideClick)
  }, [isColorMenuOpen])

  return (
    <div className="relative">
      {/* Add Note Button */}
      <div className="ml-2 mt-6 flex">
        <Button
          onClick={(e) => {
            e.stopPropagation()
            setIsColorMenuOpen(!isColorMenuOpen)
          }}
          size="small"
          theme="dark"
          className="p-2! dark:bg-slate-50"
        >
          <PlusIcon
            size={14}
            className="dark:text-dark-lucres-black-400 text-white"
            weight="bold"
          />
        </Button>

        {/* ColorMenu Option*/}
        {isColorMenuOpen && (
          <div id="color-menu" className="z-50 rounded-lg">
            <div className="absolute left-7 ml-4 flex gap-x-3">
              {colorOptions.map((color) => (
                <button
                  key={color}
                  onClick={(e) => {
                    e.stopPropagation()
                    handleColorSelect(color)
                  }}
                  style={{ backgroundColor: color }}
                  className="h-8 w-8 rounded-full border-2 border-white hover:opacity-75"
                />
              ))}
            </div>
          </div>
        )}
      </div>
      {/* Display Notes */}
      <div className="mt-6 flex flex-wrap gap-4">
        {notes.map(({ id, content, color, creator, date }) => (
          <div
            key={id}
            className="w-50 flex h-52 flex-col justify-between rounded-xl p-2"
            style={{ backgroundColor: colorMapping[color] }}
          >
            <div className="flex justify-end">
              <button onClick={() => handleDeleteNote(id)} style={{ color }}>
                <XIcon size={20} />
              </button>
            </div>
            <textarea
              className="focus:outline-hidden h-full w-full resize-none border-none bg-transparent p-2"
              value={content}
              onChange={(e) => handleEditNote(id, e.target.value)}
              placeholder="Write your note here..."
            />
            <div className="flex items-center justify-between text-xs text-[#6A6F74]">
              <span>{creator}</span>
              <span>{date}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default Notes
