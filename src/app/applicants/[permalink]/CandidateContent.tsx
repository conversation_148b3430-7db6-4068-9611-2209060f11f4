'use client'
import React, { useState, useEffect } from 'react'
// import {
//   At,
//   CalendarBlank,
//   ChatCenteredDots,
//   Check,
//   CheckCircle,
//   Dot,
//   Microphone,
//   PaperPlaneRight,
//   Plus,
//   Smiley,
//   TextAa,
//   UserPlus,
// } from '@phosphor-icons/react'
// import Select from '../../components/Select'
// import Resume from '../../components/Resume'
// import Button from '../../components/Button'
// import Notes from './Notes'
import { JobService } from '../../../services/JobService'
import Resume from '../../../components/Resume'
import { useAuth } from '../../../context/AuthContext'

interface CandidateContentProps {
  isSmallScreen?: boolean
  applicantion?: any
  jobId?: string
  isBought: boolean
  freeUnlockCount: number
  setFreeUnlockCount: React.Dispatch<React.SetStateAction<number>>
  setFilteredApplications?: any
}

const CandidateContent: React.FC<CandidateContentProps> = ({
  isSmallScreen = false,
  applicantion,
  jobId,
  isBought,
  // fetchApplicationsCount,
  freeUnlockCount,
  setFreeUnlockCount,
  setFilteredApplications,
}) => {
  const [activeTab, setActiveTab] = useState('Resume')
  // const [selectedSortCriteria, setSelectedSortCriteria] = useState<string>('shortlisted')
  // const [userInput, setUserInput] = useState('')
  // const [messages, setMessages] = useState<string[]>([])
  // const [showNotes, setShowNotes] = useState(false)
  const [applicantDetails, setApplicantDetails] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { authUserProfile } = useAuth()
  const fetchApplicantDetails = async () => {
    if (!applicantion?.id || !jobId) return

    setLoading(true)
    setError(null)
    try {
      const response = await JobService.getApplicantDetails(applicantion.id)
      if (isBought && !applicantion.isExpired) {
        const contactResponse = await JobService.getApplicantContact(applicantion.id)
        setApplicantDetails({ ...response.data, ...contactResponse.data })
      } else {
        setApplicantDetails(response.data)
      }
    } catch (err) {
      setError('Failed to load applicant details')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchApplicantDetails()
  }, [applicantion?.id])

  // const handleSortChange = (value: string) => {
  //   setSelectedSortCriteria(value)
  // }
  // const handleSubmit = (e: React.FormEvent) => {
  //   e.preventDefault()
  //   if (userInput.trim() === '') return
  //   setMessages([...messages, userInput])
  //   setUserInput('')
  // }

  // const states = [
  //   { value: 'shortlisted', label: 'Shortlisted' },
  //   { value: 'interview', label: 'Interview' },
  //   { value: 'hrround', label: 'HR Round' },
  // ]

  // const tabs = ['Resume', 'Screening Questions', 'Notes', 'Message', 'Schedule']
  const tabs = ['Resume']
  // const questions = [
  //   {
  //     id: 1,
  //     question: 'How many years of experience do you have in software development?',
  //     type: 'single-choice',
  //     options: ['Less than 1 year', '1-3 years', '3-5 years', 'More than 5 years'],
  //     answer: '1-3 years',
  //   },
  //   {
  //     id: 2,
  //     question: 'What project management tools have you used?',
  //     type: 'multiple-choice',
  //     options: ['Less than 1 year', '3-5 years', 'More than 5 years', '1-3 years'],
  //     answer: ['Less than 1 year', '1-3 years'],
  //   },
  //   {
  //     id: 3,
  //     question: 'Describe a challenging project you worked on and how you handled it.',
  //     type: 'text',
  //     answer:
  //       'In my previous role as a project manager, I worked on a tight-deadline software implementation. The challenge was coordinating multiple teams across different time zones. I created a detailed project timeline, set up daily standups, and used Jira for task tracking. By maintaining clear communication and prioritizing tasks effectively, we delivered the project on time.',
  //   },
  // ]
  // const [responses, setResponses] = useState(questions)
  return (
    <div
      className={`scrollbar-none relative h-screen w-7/12 overflow-auto ${
        activeTab !== 'Resume' && activeTab !== 'Screening Questions' ? 'fixed! left-1/2' : ''
      }`}
    >
      <div
        className={`w-full px-6 ${
          activeTab === 'Message' && `absolute top-[20vh] flex h-[70vh] flex-col justify-end`
        }`}
      >
        {activeTab === 'Resume' && (
          <>
            {loading ? (
              <div className="flex h-40 items-center justify-center">
                <div className="text-lucres-800">Loading applicant details...</div>
              </div>
            ) : error ? (
              <div className="flex h-40 items-center justify-center">
                <div className="text-red-500">{error}</div>
              </div>
            ) : applicantDetails ? (
              <>
                <Resume
                  className="mx-0 mb-6 mt-4"
                  data={applicantDetails}
                  jobId={jobId}
                  application={applicantion}
                  isBought={isBought}
                  freeUnlockCount={freeUnlockCount}
                  setFreeUnlockCount={setFreeUnlockCount}
                  setApplicantDetails={setApplicantDetails}
                  setFilteredApplications={setFilteredApplications}
                />
              </>
            ) : (
              <div className="flex h-40 items-center justify-center">
                <div className="text-lucres-800">Please select an applicant.</div>
              </div>
            )}
          </>
        )}
        {/* {activeTab === 'Screening Questions' && (
          <div>
            <div className="flex w-full items-center justify-between text-sm">
              <span>80%</span>
              <span>2/3 Completed</span>
            </div>
            <div className="relative mt-2 h-1 rounded-lg bg-lucres-300 dark:bg-dark-lucres-black-300">
              <span
                className="absolute left-0 top-0 h-full rounded-lg bg-linear-to-r from-lucres-400 to-[#E4FFC1]"
                style={{ width: `${90}%` }}
              ></span>
            </div>
            <div className="mx-auto mt-10 flex flex-col gap-6">
              {responses.map((q) => (
                <div key={q.id} className="rounded-md border p-4 dark:border-dark-lucres-black-200">
                  <h4 className="mb-2 text-sm text-gray-600 dark:text-lucres-gray-600">
                    Question {q.id}
                  </h4>
                  <p className="rounded-xs bg-lucres-gray-300 p-2 px-4 font-medium text-lucres-black dark:border dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 dark:text-dark-lucres-green-100">
                    {q.question}
                  </p>

                  {q.type === 'single-choice' && (
                    <div className="mt-3 gap-y-2">
                      {q.options?.map((option) => (
                        <label
                          key={option}
                          className={`flex items-center rounded-md p-2 text-gray-600 ${
                            q.answer === option &&
                            'border border-lucres-400 bg-lucres-400 bg-opacity-25 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-400 dark:bg-opacity-100'
                          }`}
                        >
                          <input
                            type="radio"
                            name={`q${q.id}`}
                            checked={q.answer === option}
                            readOnly
                            className="hidden"
                          />
                          <span
                            className={`flex h-3 w-3 items-center justify-center rounded-full border-2 ${
                              q.answer === option
                                ? 'border-lucres-700 bg-lucres-700'
                                : 'border-gray-400'
                            }`}
                          >
                          </span>
                          <span className="ml-2 text-gray-600 dark:text-lucres-gray-600">
                            {option}
                          </span>
                        </label>
                      ))}
                    </div>
                  )}

                  {q.type === 'multiple-choice' && (
                    <div className="mt-3 gap-y-2">
                      {q.options?.map((option) => (
                        <label
                          key={option}
                          className={`flex cursor-pointer items-center rounded-md p-2 ${
                            q.answer.includes(option)
                              ? 'border border-lucres-400 bg-lucres-400 bg-opacity-25 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-400 dark:bg-opacity-100'
                              : ''
                          }`}
                        >
                          <input
                            type="checkbox"
                            checked={q.answer.includes(option)}
                            readOnly
                            className="hidden"
                          />
                          {!q.answer.includes(option) ? (
                            <span
                              className={`flex h-3 w-3 items-center justify-center rounded-full border-2 border-gray-400`}
                            ></span>
                          ) : (
                            q.answer.includes(option) && (
                              <span className="font-bold text-white">
                                <CheckCircle className="text-[#3f8d51]" size={20} />
                              </span>
                            )
                          )}
                          <span className="ml-2 text-gray-600 dark:text-lucres-gray-600">
                            {option}
                          </span>
                        </label>
                      ))}
                    </div>
                  )}

                  {q.type === 'text' && (
                    <div className="mt-3 rounded-md border p-4 text-gray-600 dark:border-dark-lucres-black-200 dark:text-lucres-gray-600">
                      {q.answer}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )} */}

        {/* <div className="">
          {activeTab === 'Notes' && (
            <>
              {!showNotes && (
                <div className="mx-auto mt-28 max-w-md rounded-2xl border border-lucres-200 bg-linear-to-r from-transparent to-lucres-400 p-6 text-center dark:border-dark-lucres-black-200 dark:to-dark-lucres-black-300">
                  <div className="text-xl font-medium">Be the first one to add a note!</div>
                  <p className="mt-2 text-sm text-lucres-800">
                    Share your thoughts/comments on discovered candidates and @mention teammates for
                    feedback.
                  </p>
                  <div className="m-auto mt-2 flex w-5/6 justify-center gap-4">
                    <Button
                      size="small"
                      className="mt-4 flex gap-2"
                      theme="transparent"
                      isRectangle
                      onClick={() => setShowNotes(true)}
                    >
                      <div className="h-full rounded-full bg-lucres-900 p-1 pr-1 opacity-90">
                        <Plus size={12} color="#fff" weight="bold" />
                      </div>
                      Add a note
                    </Button>
                    <Button className="mt-4 flex gap-2" theme="dark" isRectangle size="small">
                      <div>
                        <UserPlus size={20} />
                      </div>
                      Add team member
                    </Button>
                  </div>
                </div>
              )}
              {showNotes && <Notes />}
            </>
          )}

          {activeTab === 'Message' &&
            messages.map((message, index) => (
              <>
                <div key={index} className="mb-4 flex w-full justify-end gap-4">
                  <span className="w-fit rounded-lg bg-gray-100 px-4 py-2 text-left dark:bg-dark-lucres-black-300">
                    {message}
                  </span>
                  <img
                    src={'/common/avatar.svg'}
                    alt="profile"
                    className="h-7 w-7 rounded-full object-cover"
                  />
                </div>
              </>
            ))}
        </div>

        {activeTab !== 'Resume' && (
          <>
            <div className="h-32 w-full pb-10">
              <form onSubmit={activeTab === 'Message' ? handleSubmit : () => ''} className="flex">
                {activeTab === 'Message' ? (
                  <div className="flex w-full flex-col items-start justify-between gap-2 rounded-lg border border-lucres-300 border-opacity-40 bg-transparent px-4 py-3 placeholder-lucres-300 placeholder:text-sm focus:outline-hidden dark:border-dark-lucres-black-200 dark:placeholder-lucres-800">
                    <textarea
                      className="scrollbar-none m-0 w-full resize-none border-none bg-transparent px-0 py-0 outline-hidden"
                      placeholder="Write message"
                      value={userInput}
                      onChange={(e) => setUserInput(e.target.value)}
                    ></textarea>
                    <div className="flex h-7 w-full items-center justify-between">
                      <div className="flex h-full items-center">
                        <div className="h-full cursor-pointer border-r border-lucres-300 pr-1">
                          <Button
                            theme="transparent"
                            size={'small'}
                            className="rounded-full! bg-lucres-900! p-1! opacity-90"
                          >
                            <Plus size={20} color="#fff" weight="bold" />
                          </Button>
                        </div>
                        <div className="flex h-full cursor-pointer items-center border-r border-lucres-300 px-2">
                          <Microphone
                            size={20}
                            color="#8f8f8f"
                            className="opacity-60 hover:opacity-100"
                            weight="bold"
                          />
                        </div>
                        <div className="flex h-full cursor-pointer items-center gap-2 border-r border-lucres-300 px-2">
                          <Smiley
                            size={20}
                            color="#8f8f8f"
                            className="opacity-60 hover:opacity-100"
                            weight="bold"
                          />
                          <At
                            size={20}
                            color="#8f8f8f"
                            className="opacity-60 hover:opacity-100"
                            weight="bold"
                          />
                          <TextAa
                            size={20}
                            color="#8f8f8f"
                            className="opacity-60 hover:opacity-100"
                            weight="bold"
                          />
                        </div>
                        <div className="flex h-full cursor-pointer items-center px-2">
                          <ChatCenteredDots
                            size={20}
                            color="#8f8f8f"
                            className="opacity-60 hover:opacity-100"
                            weight="bold"
                          />
                        </div>
                      </div>
                      <button className="h-full cursor-pointer" type="submit">
                        <PaperPlaneRight
                          size={20}
                          color="#8f8f8f"
                          weight="duotone"
                          className="opacity-60 hover:opacity-100"
                        />
                      </button>
                    </div>
                  </div>
                ) : null}
              </form>
            </div>
          </>
        )}*/}
      </div>
    </div>
  )
}

export default CandidateContent
