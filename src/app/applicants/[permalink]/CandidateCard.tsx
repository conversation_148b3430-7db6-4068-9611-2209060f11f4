'use client'
import { BookmarkSimpleIcon, DotIcon, LockIcon } from '@phosphor-icons/react'
import {
  capitalizeWords,
  formatToReadableDate,
  getRelativeTime,
  getTimeDifference,
  truncateText,
} from '../../../utils/commonUtils'
// import Tag from '../../components/Tag'
import { useState } from 'react'
import Avatar from '../../../components/Avatar/Avatar'
import useError from '../../../context/ErrorContext'
import { JobService } from '../../../services/JobService'
import { useToast } from '../../../components/ToastX'
import Tooltip from '../../../components/Tooltip'

interface CandidateCardProps {
  application: any
  jobId: string
  isSmallScreen?: boolean
  onSelect?: () => void
  setIsBought: (revealProfile: boolean) => void
  isActive?: boolean
}

const CandidateCard: React.FC<CandidateCardProps> = ({
  application,
  onSelect,
  setIsBought,
  isActive,
  jobId,
}) => {
  const { handleError } = useError()
  const toast = useToast()
  const isProfileRevealed = application.status === 'BOUGHT' && !application.isExpired
  const [isSaved, setIsSaved] = useState(application.status === 'SAVED')
  const currentDate: any = new Date()
  const appliedDate: any = new Date(application.appliedDate)
  const diff = Math.floor((currentDate - appliedDate) / (1000 * 60 * 60 * 24))

  // function formatDate(isoString: any) {
  //   if (!isoString) return
  //   const date = new Date(isoString)
  //   const day = date.getUTCDate()
  //   const month = date.toLocaleString('default', { month: 'long', timeZone: 'UTC' })
  //   const year = date.getUTCFullYear()

  //   const getOrdinal = (n: any) => {
  //     if (n > 3 && n < 21) return `${n}th`
  //     switch (n % 10) {
  //       case 1:
  //         return `${n}st`
  //       case 2:
  //         return `${n}nd`
  //       case 3:
  //         return `${n}rd`
  //       default:
  //         return `${n}th`
  //     }
  //   }

  //   return `${getOrdinal(day)} ${month.toLowerCase()} ${year}` || ''
  // }

  const handleClick = () => {
    if (onSelect) {
      onSelect()
      // setRevealProfile(isProfileRevealed)
      setIsBought(application.status === 'BOUGHT' && application.isExpired === false)
    }
  }

  const saveApplicant = async () => {
    try {
      const response = await JobService.applicantStatus(jobId, application?.id, 'SAVED')
      if (response.data.status === 'success') {
        toast.success('Applicant saved')
        setIsSaved(true)
      }
    } catch (error) {
      handleError(error)
    }
  }

  return (
    <div
      className={`hover:bg-lucres-gray-50 dark:border-dark-lucres-black-200 dark:dark:hover:bg-dark-lucres-black-400 flex cursor-pointer flex-col gap-4 rounded-lg border p-4 ${
        isActive ? 'bg-lucres-gray-50 dark:bg-dark-lucres-black-400' : ''
      } `}
      onClick={handleClick}
    >
      <div className="flex justify-between">
        <div className="flex items-center gap-4">
          <Avatar
            src={application.applicant.profileImage.url}
            alt="avatar"
            width={48}
            height={48}
            className=""
          />
          <div className={`flex flex-col ${!isProfileRevealed && 'gap-2'} `}>
            {isProfileRevealed ? (
              <span className="text-lg font-medium">
                {capitalizeWords(truncateText(application.applicant.name, 20).text)}
              </span>
            ) : (
              <span
                className={`bg-lucres-gray-50 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-400 -mb-1 -ml-1 flex w-fit items-center gap-2 whitespace-nowrap rounded-3xl border px-4 py-2 text-sm opacity-80 ${
                  isActive ? 'dark:bg-dark-lucres-black-500 bg-white' : ''
                }`}
              >
                <LockIcon size={20} />
                Profile Locked
              </span>
            )}
            <div
              className={`flex flex-col items-center opacity-90 sm:flex-row sm:items-center ${
                isProfileRevealed && 'text-lucres-800 opacity-100! text-sm'
              }`}
            >
              <span className="flex">
                {' '}
                {application.applicant?.location.address
                  ? truncateText(
                      `${application.applicant?.location?.address?.state} ${application.applicant?.location?.address?.country}`,
                      20,
                    ).text
                  : 'Remote'}
              </span>
              {(application.applicant?.location?.address?.country ||
                !application.applicant?.location?.address) && <DotIcon size={14} weight="bold" />}
              <span>
                {`${
                  application.applicant.totalExperience > 0
                    ? `${application.applicant.totalExperience} Years of Exp.`
                    : 'Fresher'
                }`}{' '}
              </span>
            </div>
          </div>
        </div>

        <div className="flex gap-2">
          {/* <ClipboardText size={22} /> */}
          {!isSaved ? (
            <span className="dark:hover:bg-dark-lucres-black-500 h-fit rounded-full p-2 hover:bg-white">
              <Tooltip text="Save Applicant" direction="bottom">
                <BookmarkSimpleIcon
                  size={24}
                  onClick={(e) => {
                    e.stopPropagation
                    saveApplicant()
                  }}
                />
              </Tooltip>
            </span>
          ) : (
            <span className="h-fit rounded-full p-2 opacity-50">
              <BookmarkSimpleIcon size={24} />
            </span>
          )}
          {/* <Tag
            TagText={application.status}
            ClassName={`text-[10px]! font-normal py-1! px-2 border-lucres-200! dark:border-dark-lucres-black-200! ${application.status === 'PENDING' ? 'border-yellow-500! text-yellow-500! dark:border-yellow-500!' : application.status === 'BOUGHT' ? 'border-green-500! text-green-500! dark:border-green-500!' : application.status === 'Rejcted' ? 'border-red-500! text-red-500! dark:border-red-500!' : ''}`}
          /> */}
        </div>
      </div>
      {application.latestExperience.designation && (
        <div>
          <span className="text-lucres-800 text-sm">Recent Experience</span>
          <h3 className="mt-1 flex items-center font-medium">
            {application?.latestExperience?.designation?.name} <DotIcon size={14} weight="bold" />
            <span className="font-normal">{application?.latestExperience?.company?.name}</span>
          </h3>
          <div>
            <span className="text-lucres-800 flex text-sm">
              {formatToReadableDate(application.latestExperience.startDate, false, false)} -{' '}
              {application.latestExperience.isCurrent
                ? 'Present'
                : formatToReadableDate(application.latestExperience.endDate, false, false)}
              <DotIcon size={14} weight="bold" />{' '}
              {getTimeDifference(
                application.latestExperience.startDate,
                application.latestExperience.endDate,
              )}
            </span>
          </div>
        </div>
      )}
      <div className="">
        <span className="text-lucres-800 text-sm">Educations</span>
        <div className="flex items-center gap-2">
          <h3 className="mt-1 flex items-center font-medium">
            {application?.latestEducation?.degree?.name} <DotIcon size={14} weight="bold" />
            <span className="font-normal">{application.latestEducation.institution?.name} </span>
            <DotIcon size={14} weight="bold" />{' '}
            <span className="text-lucres-800 text-sm">
              {' '}
              {application?.latestEducation?.endYear}{' '}
            </span>
          </h3>
        </div>
      </div>
      <div className="flex flex-wrap gap-4">
        {application.applicant.skills.slice(0, 3).map((item: any, index: number) => (
          <span
            key={index}
            className={`bg-lucres-200 dark:bg-dark-lucres-black-200 flex w-fit items-center gap-2 rounded-lg px-4 py-2 text-sm`}
          >
            {item.name}
          </span>
        ))}
      </div>
      <div className="text-lucres-800 text-sm">
        <div>Applied {getRelativeTime(application.createdAt)}</div>
        {/* <Button
          theme="transparent"
          className={`rounded-lg p-3! font-normal! ${isActive ? 'bg-white' : ''}`}
          onClick={handleClick}
        >
          <div className="flex cursor-pointer">
            <ArrowsOutSimple size={20} className={`mr-2`} /> View Application
          </div>
        </Button> */}
        {/* <div className="flex gap-2">
          <Button
            theme="transparent"
            className="rounded-lg p-2! font-normal!"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex cursor-pointer items-center">
              <BookmarkSimple size={18} />
            </div>
          </Button>
          {application.status !== 'BOUGHT' && (
            <Button
              theme="transparent"
              className="rounded-lg p-2! font-normal!"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex cursor-pointer items-center">
                <LockOpen size={18} />
              </div>
            </Button>
          )}
        </div> */}
      </div>
    </div>
  )
}

export default CandidateCard
