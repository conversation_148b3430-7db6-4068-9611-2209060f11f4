'use client'

import Candidate<PERSON><PERSON> from './CandidateLists'
import Candidate<PERSON>ontent from './CandidateContent'
import { useEffect, useState } from 'react'
import useError from '../../../context/ErrorContext'
import { JobPreviewDetails } from '../../../models/Job'
import { useParams } from 'next/navigation'
import { PostService } from '../../../services/PostService'

const JobData = () => {
  const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth < 1024)
  const { handleError } = useError()
  const [job, setJob] = useState<JobPreviewDetails>()
  const [loading, setLoading] = useState<boolean>(true)
  const [selectedApplicantion, setSelectedApplicantion] = useState<string | null>(null)
  const [filteredApplications, setFilteredApplications] = useState<Array<Record<string, any>>>([])
  const [isBought, setIsBought] = useState(false)
  const params = useParams()
  const permalink = params?.permalink as string
  const [applicantStatus, setApplicantStatus] = useState<string>('')
  const [freeUnlockCount, setFreeUnlockCount] = useState<number>(0)
  const fetchJobData = async () => {
    if (!permalink) return
    try {
      // const jobDetails = await PostService.getJobByPermalink(permalink);
      const jobDetails = await PostService.getJobById(permalink)
      setJob(jobDetails.data)
      setFreeUnlockCount(jobDetails.data.freeUnlockCount)
      setLoading(false)
    } catch (error) {
      console.error('Error:', error)
      handleError(error)
    }
  }

  useEffect(() => {
    fetchJobData()
  }, [permalink])

  useEffect(() => {
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth < 1024)
    }
    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  const handleApplicantSelect = (applicantion: any) => {
    setSelectedApplicantion(applicantion)
  }

  return (
    <div className="dark:border-dark-lucres-black-200 flex h-full overflow-hidden border-t pt-16">
      <CandidateLists
        isSmallScreen={isSmallScreen}
        jobDetails={job}
        onApplicantSelect={handleApplicantSelect}
        setIsBought={setIsBought}
        freeUnlockCount={freeUnlockCount}
        setApplicantStatus={setApplicantStatus}
        filteredApplications={filteredApplications}
        setFilteredApplications={setFilteredApplications}
      />
      {!isSmallScreen && (
        <CandidateContent
          isSmallScreen={isSmallScreen}
          applicantion={selectedApplicantion || undefined}
          jobId={job?.id}
          isBought={isBought}
          // fetchApplicationsCount={fetchApplicationsCount}
          freeUnlockCount={freeUnlockCount || 0}
          setFreeUnlockCount={setFreeUnlockCount}
          setFilteredApplications={setFilteredApplications}
        />
      )}
    </div>
  )
}
export default JobData
