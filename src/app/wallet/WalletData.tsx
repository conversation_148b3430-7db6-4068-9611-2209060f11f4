'use client'
// import Button from "../../../components/Button";
import Button from '../../components/Button'
import Select from '../../components/Select'
import { useCallback, useEffect, useRef, useState } from 'react'
import { UserService } from '../../services/UserService'
import { useToast } from '../../components/ToastX'
import IconButton from '../../components/IconButton'
import Input from '../../components/Input'
import WalletSkeleton from './WalletSkelaton'
import { Wallet } from '../../models/User'
import TransactionDetaailsCard from './TransactionDetaailsCard'
import { ArrowLeftIcon, CurrencyInrIcon } from '@phosphor-icons/react'

function WalletData() {
  const [selectedAccess, setSelectedAccess] = useState<string>('')
  const [walletData, setWalletData] = useState<Wallet | null>(null)

  const [isWithdrawPanelOpen, setIsWithdrawPanelOpen] = useState<boolean>(false)
  const [loading, setLoading] = useState(false)
  const [upiAddress, setUpiAddress] = useState('')
  const [amount, setAmount] = useState('')
  const [description, setDescription] = useState('')
  const [page, setPage] = useState<number>(1)
  const [hasNextPage, setHasNextPage] = useState(false)
  const [isupiAddressFound, setIsupiAddressFound] = useState(false)
  const observer = useRef<IntersectionObserver | null>(null)
  const [expandedTransactionId, setExpandedTransactionId] = useState<string | null>(null)
  const [errors, setErrors] = useState({
    upiNo: '',
    amount: '',
    description: '',
  })
  const toast = useToast()
  const accessOptions = [
    { value: '', label: 'All' },
    { value: 'CREDIT', label: 'Money Earned' },
    { value: 'DEBIT', label: 'Money withdrawal' },
  ]

  const handleAccessChange = (value: string) => {
    setSelectedAccess(value)
  }

  const validate = () => {
    let valid = true
    const newErrors = { upiNo: '', amount: '', description: '' }

    // Validate UPI
    if (!upiAddress.trim()) {
      newErrors.upiNo = 'UPI address is required.'
      valid = false
    } else if (!/^[\w.-]+@[\w.-]+$/.test(upiAddress)) {
      newErrors.upiNo = 'Enter a valid UPI address.'
      valid = false
    }

    // Validate Amount (should already be filled and disabled)
    if (!amount || parseFloat(amount) <= 0) {
      newErrors.amount = 'Amount must be greater than 0.'
      valid = false
    }

    // Description (optional validation)
    // if (!description.trim()) {
    //   newErrors.description = 'Description is required.'
    //   valid = false
    // }

    setErrors(newErrors)
    return valid
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (validate()) {
      try {
        if (!isupiAddressFound) {
          await UserService.updateUpiAddress(upiAddress)
        }
        const formData = {
          amount: parseFloat(amount),
          discription: description,
        }
        const response = await UserService.withdrawWalletAmmount(formData)
        setIsWithdrawPanelOpen(false)
      } catch (error) {
        toast.error('withdraw amount failed')
      }
    }
  }

  const getWalletTransactions = async (page: number, type: string) => {
    setLoading(true)
    try {
      const response = await UserService.getWalletTransactions(page, type)
      if (page === 1) {
        setWalletData(response.data)
      } else {
        setWalletData((prev) =>
          prev
            ? {
                ...prev,
                transactions: {
                  ...prev.transactions,
                  items: [...prev.transactions.items, ...response.data.transactions.items],
                  paginator: response.data.transactions.paginator,
                },
              }
            : response.data,
        )
      }

      setHasNextPage(response.data.transactions.paginator.hasNextPage)
    } catch (error) {
    } finally {
      setLoading(false)
    }
  }
  useEffect(() => {
    // setLoading(true); // <- Add this line to ensure new pages trigger loading
    getWalletTransactions(page, selectedAccess)
  }, [page, selectedAccess])

  const handleShowWithdrawForm = async () => {
    if (walletData && walletData?.wallet?.amount >= 35) {
      setIsWithdrawPanelOpen(true)
      try {
        const res = await UserService.getUpiAddress()
        setIsupiAddressFound(true)
        setUpiAddress(res.data.upi_address)
      } catch (error) {}
    } else {
      toast.error('Sorry, you do not have enough balance to withdraw.')
      setIsupiAddressFound(false)
    }
  }

  const lastPostElementRef = useCallback(
    (node: HTMLDivElement) => {
      if (loading) return
      if (observer.current) observer.current.disconnect()
      observer.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasNextPage) {
            setPage((prevPage) => prevPage + 1)
          }
        },
        { threshold: 0.5 },
      )
      if (node) observer.current.observe(node)
    },
    [loading, hasNextPage],
  )

  const toggleAccordion = (transactionId: string) => {
    setExpandedTransactionId((prev) => (prev === transactionId ? null : transactionId))
  }
  return (
    <section className="font-inter flex h-full w-full items-center justify-center lg:py-0">
      {isWithdrawPanelOpen ? (
        <div className="flex w-full max-w-[1440px] flex-col px-4 pt-6 sm:px-8 lg:gap-4">
          <div
            className="flex cursor-pointer items-center gap-4"
            onClick={() => setIsWithdrawPanelOpen(false)}
          >
            <IconButton theme="secondary" className="dark:bg-dark-lucres-black-500 w-fit bg-white">
              <ArrowLeftIcon
                size={24}
                className="text-lucres-black dark:text-dark-lucres-green-100"
              />
            </IconButton>
            <h3 className="text-lucres-gray-700 hover:text-lucres-600 dark:text-lucres-300 dark:hover:text-lucres-500 font-medium">
              Back
            </h3>
          </div>
          <div className="flex w-full flex-col items-start justify-between">
            {/* <div className="font-semibold">Withdraw Money</div> */}
            <form onSubmit={handleSubmit} className="mt-8 w-full">
              <div className="flex flex-col gap-5">
                <div className="flex-col items-start gap-5 lg:flex">
                  <div className="flex w-full justify-between gap-5">
                    <Input
                      label="UPI address"
                      // theme="gray"
                      value={upiAddress}
                      error={errors.upiNo}
                      disabled={isupiAddressFound}
                      required
                      onChange={(e) => setUpiAddress(e.target.value)}
                    />
                    <Input
                      label="Amount"
                      // theme="gray"
                      value={amount}
                      required
                      onChange={(e) => setAmount(e.target.value)}
                      error={errors.amount}
                    />
                  </div>
                  <Input
                    label="Description"
                    // theme="gray"
                    value={description}
                    error={errors.description}
                    onChange={(e) => setDescription(e.target.value)}
                  />
                </div>
                <div className="flex w-full items-center justify-between">
                  <span className="text-lucres-gray-700 dark:text-dark-lucres-green-100 relative ps-2">
                    {' '}
                    <span className="absolute -top-0.5 left-0 text-red-400"> *</span>A transaction
                    fee of Rs. 5 will be deducted
                  </span>
                  <Button size="small" isRectangle theme="translucent">
                    Withdraw
                  </Button>
                </div>
              </div>
            </form>
          </div>
        </div>
      ) : (
        <>
          {loading && walletData?.transactions?.items?.length === 0 ? (
            <div className="w-full">
              <WalletSkeleton />
            </div>
          ) : (
            <div className="flex w-full max-w-[1440px] flex-col pt-6">
              <div className="flex flex-col px-4 lg:gap-4">
                <div className="flex w-full items-center justify-between">
                  <div className="text-lucres-black dark:text-lucres-green-100 font-semibold">
                    Wallet
                  </div>
                </div>
                <div className="bg-lucres-50 dark:bg-dark-lucres-black-400 mt-4 flex h-20 w-full items-center justify-between rounded-xl p-4 ps-0">
                  <div className="flex items-center">
                    <img src="/wallet/rupee.svg" alt="" className="h-20" />
                    <div className="text-lucres-black dark:text-lucres-green-100">
                      <span>Your Balance is</span>
                      <h3 className="flex items-center text-3xl font-medium">
                        <CurrencyInrIcon size={28} weight="bold" />
                        {walletData?.wallet?.amount}
                      </h3>
                    </div>
                  </div>

                  <Button theme="dark" isRectangle size="small" onClick={handleShowWithdrawForm}>
                    Withdraw
                  </Button>
                </div>
                <div className="mt-8 flex w-full flex-col items-center gap-4 sm:mt-0">
                  <div className="flex w-full justify-start">
                    <div className="w-48">
                      <Select
                        options={accessOptions}
                        value={selectedAccess}
                        onChange={handleAccessChange}
                        placeholder="All"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="text-lucres-black dark:text-lucres-green-100 mt-4 w-full text-lg">
                {walletData?.transactions?.items.map((transaction) => (
                  <TransactionDetaailsCard
                    key={transaction.id}
                    transaction={transaction}
                    toggleAccordion={toggleAccordion}
                    isExpanded={expandedTransactionId === transaction.id}
                  />
                ))}
              </div>
              {hasNextPage && (
                <div ref={lastPostElementRef} className="flex flex-col items-center justify-center">
                  <WalletSkeleton />
                </div>
              )}
            </div>
          )}
        </>
      )}
    </section>
  )
}

export default WalletData
