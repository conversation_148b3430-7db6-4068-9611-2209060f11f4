const WalletSkeleton = () => {
  return (
    <div className="flex w-full max-w-[1440px] animate-pulse flex-col pt-6 lg:gap-4 lg:px-0">
      <div className="dark:bg-dark-lucres-black-400 flex h-20 w-full items-center justify-between rounded-xl bg-gray-100 p-4 ps-0">
        <div className="flex items-center gap-4">
          <div className="dark:bg-dark-lucres-black-200 h-16 w-16 rounded-full bg-gray-300" />
          <div className="space-y-2">
            <div className="dark:bg-dark-lucres-black-200 h-4 w-32 rounded-sm bg-gray-300" />
            <div className="dark:bg-dark-lucres-black-200 h-6 w-40 rounded-sm bg-gray-300" />
          </div>
        </div>

        <div className="dark:bg-dark-lucres-black-200 h-8 w-24 rounded-sm bg-gray-300" />
      </div>
    </div>
  )
}

export default WalletSkeleton
