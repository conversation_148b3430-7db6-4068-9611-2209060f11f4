import { CaretDown, CaretUp, CurrencyInr } from '@phosphor-icons/react'
import React from 'react'
import { formatToReadableDate } from '../../utils/commonUtils'

const TransactionDetaailsCard = ({ transaction, isExpanded, toggleAccordion }: any) => {
  return (
    <div className="text-lucres-gray-700 dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 dark:text-dark-lucres-green-100 flex h-full w-full cursor-pointer flex-col items-center justify-between border-b text-xs md:text-sm dark:border">
      <div
        className="dark:hover:bg-dark-lucres-black-400 flex w-full items-center justify-between p-4 hover:bg-gray-100"
        onClick={() => toggleAccordion(transaction.id)}
      >
        <div className="text-lucres-gray-700 dark:text-dark-lucres-green-100 flex w-5/12 flex-col">
          <span className="text-base font-semibold">
            {transaction.type === 'DEBIT' ? 'Withdrawn amount' : 'Referral points earned'}
          </span>
          <span className="text-lucres-gray-500">
            {transaction.type === 'CREDIT' && <span>{transaction.job.title}</span>}
          </span>
        </div>
        <div className="flex w-40 items-center">
          {' '}
          <CurrencyInr weight="bold" className="text-sm" />
          {transaction.amount}
        </div>
        <div className="w-40">
          {transaction.status === 'INITIATED' ? 'Processed' : 'Processing'}
        </div>
        <div className="flex w-56 items-center justify-between">
          {formatToReadableDate(transaction.createdAt, false)}
          <div>
            {isExpanded ? (
              <CaretUp size={24} className="w-4 cursor-pointer md:w-6" />
            ) : (
              <CaretDown
                size={24}
                className="w-4 cursor-pointer md:w-6"
                // onClick={() => order.id && toggleAccordion(order.id)}
              />
            )}
          </div>
        </div>
      </div>

      {isExpanded && (
        <div className="dark:border-y-dark-lucres-black-300 w-full gap-8 border-y text-sm sm:flex">
          <div className="text-lucres-gray-700 dark:text-dark-lucres-green-100 flex w-full flex-col gap-2 p-4">
            <div className="text-lucres-gray-700 dark:text-dark-lucres-green-100 h-fit w-full max-w-xs rounded-lg p-2 text-xs sm:pb-0">
              <div className="mb-1 flex w-full items-center justify-between">
                <div>Transaction no.</div>
                <div>Rs. {transaction.transNo}</div>
              </div>
              <div className="my-1 flex w-full items-center justify-between">
                <div>Date</div>
                {formatToReadableDate(transaction.createdAt, true)}
              </div>
              <div className="my-1 flex w-full items-center justify-between">
                <div>Status</div>
                {transaction.status === 'INITIATED' ? 'Processed' : 'Processing'}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default TransactionDetaailsCard
