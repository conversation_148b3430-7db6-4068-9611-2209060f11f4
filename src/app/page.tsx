'use client'
import { usePathname } from 'next/navigation'
import Footer from '@/components/Footer'
import Home from './home/<USER>'
import { useAuth } from '@/context/AuthContext'
import Feed from './feed/page'
import { FeedProvider } from '@/context/FeedContext'

// Routes that should show footer
const FOOTER_ROUTES = ['/', '/about', '/contact', '/blogs', '/jobs']

const HomeRoute = () => {
  const pathname = usePathname()
  const { isAuthenticated } = useAuth()
  const showFooter = FOOTER_ROUTES.includes(pathname.toLowerCase())

  return isAuthenticated ? (
    <FeedProvider>
      <Feed />
    </FeedProvider>
  ) : (
    <>
      <Home />
      {showFooter && <Footer />}
    </>
  )
}

export default function Page() {
  return <HomeRoute />
}
