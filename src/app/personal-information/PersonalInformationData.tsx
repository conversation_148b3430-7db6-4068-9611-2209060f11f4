'use client'
import React, { useEffect, useRef, useState } from 'react'
import ImageCropModal from '../../components/ImageCropModal'
import Input from '../../components/Input'
import Button from '../../components/Button'
import Select from '../../components/Select'
import {
  NotePencilIcon,
  SignOutIcon,
  WarningIcon,
  XIcon,
  ArrowsClockwiseIcon,
  MapPinIcon,
  MagnifyingGlassIcon,
} from '@phosphor-icons/react'
import { UserService } from '../../services/UserService'
import { UserProfile, UserContact, PermalinkUser } from '../../models/User'
import { convertToTitleCase } from '../../utils/commonUtils'
import Toast, { ToastData, ToastProps } from '../../components/Toast'
import { handleFileChange, handleCropComplete } from '../../utils/imageUtils'
import { useAuth } from '../../context/AuthContext'
import Avatar from '../../components/Avatar/Avatar'
import { useRouter, useSearchParams } from 'next/navigation'
import Overlay from '../../components/Overlay'
import { OtpService } from '../../services/OtpService'
import InputOtp from '../../components/InputOtp'
import { useToast } from '../../components/ToastX'
import { AuthService } from '../../services/AuthService'
import useError from '../../context/ErrorContext'
import { useAccount } from '../../context/UserDetailsContext'
import { format } from 'date-fns'
import DatePicker from '../../components/DatePicker'
import { LocationService } from '../../services/LocationService'

interface PersonalInformationDataProps {
  refreshUserData?: () => void
}

// Custom hook for Google Places AutocompleteService
function usePlacesAutocomplete() {
  const [suggestions, setSuggestions] = useState<any[]>([])
  const serviceRef = useRef<any>(null)

  const fetchSuggestions = (input: string) => {
    if (!window.google || !window.google.maps || !window.google.maps.places) {
      return
    }
    if (!serviceRef.current) {
      serviceRef.current = new window.google.maps.places.AutocompleteService()
    }
    if (input.length < 3) {
      setSuggestions([])
      return
    }
    serviceRef.current.getPlacePredictions({ input }, (predictions: any[] | null) => {
      setSuggestions(predictions || [])
    })
  }

  return { suggestions, fetchSuggestions, setSuggestions }
}

// Helper to format Location object as a string for input display
function formatLocationString(location: any) {
  if (!location || !location.address) return ''
  const { area, city, state, country } = location.address
  return [area, city, state, country].filter(Boolean).join(', ')
}

const PersonalInformationData: React.FC<PersonalInformationDataProps> = ({ refreshUserData }) => {
  const { setAuthUserProfile, authUserProfile, logout } = useAuth()
  const { setPersonalInformation, setContactDetails } = useAccount()
  const router = useRouter()
  const toast = useToast()
  const { handleError } = useError()
  // Separate states for profile and contact
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [originalUserProfile, setOriginalUserProfile] = useState<UserProfile | null>(null)

  const [userContact, setUserContact] = useState<UserContact | null>(null)
  const [originalUserContact, setOriginalUserContact] = useState<UserContact | null>(null)

  const [isUserProfileEditingEnabled, setIsUserProfileEditingEnabled] = useState(false)
  const [isUserContactEditingEnabled, setIsUserContactEditingEnabled] = useState(false)
  const [isVerifyModal, setIsVerifyModal] = useState(false)
  const [otp, setOtp] = useState<string>('')
  const [error, setError] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [isInfoUpdated, setIsInfoUpdated] = useState(false)
  const [isPersonalInfoUpdated, setIsPersonalInfoUpdated] = useState(false)
  const [isContactInfoUpdated, setIsContactInfoUpdated] = useState(false)
  const [allowReachRecruiters, setAllowReachRecruiters] = useState<boolean>(
    authUserProfile?.preferences?.allowRecruiterContact ?? false,
  )
  const [initialRender, setInitialRender] = useState(true)
  // Validation errors for each field
  const [errors, setErrors] = useState<Partial<Record<string, string>>>({})

  // Update error messages from API responses
  const [personalUpdateError, setPersonalUpdateError] = useState<string>('')
  const [contactUpdateError, setContactUpdateError] = useState<string>('')

  const fileInputRef = useRef<HTMLInputElement | null>(null)
  const [isCropModalOpen, setIsCropModalOpen] = useState(false)
  const [imageSrc, setImageSrc] = useState<string | null>(null)
  const [isPhotoUpdated, setIsPhotoUpdated] = useState(false)
  const [authUser, setAuthUser] = useState(authUserProfile)
  const [logoutModal, setLogoutModal] = useState(false)
  const [password, setPassword] = useState<string>('')
  const [isRegeneratingPassword, setIsRegeneratingPassword] = useState(false)
  const [sendingPasswordOtp, setSendingPasswordOtp] = useState(false)
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false)
  const [isGettingLocation, setIsGettingLocation] = useState(false)
  const [showLocationSuggestions, setShowLocationSuggestions] = useState(false)
  const {
    suggestions: locationSuggestions,
    fetchSuggestions,
    setSuggestions,
  } = usePlacesAutocomplete()
  const [locationInput, setLocationInput] = useState('')
  const isUserTypingRef = useRef(false)
  const locationDebounceTimeout = useRef<ReturnType<typeof setTimeout> | null>(null)

  useEffect(() => {
    // Check URL params for edit mode instead of router state
    const urlParams = new URLSearchParams(window.location.search)
    const editMode = urlParams.get('editMode')
    const fromResume = urlParams.get('fromResume')

    if (fromResume && editMode) {
      setIsUserProfileEditingEnabled(true)
    }
  }, [])

  const [toasts, setToasts] = useState<ToastData[]>([])
  const [toastPosition, setToastPosition] = useState<ToastProps['position']>('bottom-right')
  const handleShowToast = (
    type: 'success' | 'info' | 'error' | 'loading',
    message: string,
    timeout: number = 3000,
  ): number => {
    const id = Date.now()
    const newToast = { id, type, message, timeout }
    setToasts((prev) => [...prev, newToast])

    // Auto-remove non-loading toasts
    if (type !== 'loading') {
      setTimeout(() => {
        removeToast(id)
      }, timeout)
    }

    return id
  }

  const removeToast = (id: number) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id))
  }

  // Define field names for personal and contact sections
  const personalFields: string[] = [
    'givenName',
    'familyName',
    'title',
    'headline',
    'birthDate',
    'location', // combined location field
    'nationality', // new nationality field
    'gender',
  ]
  const contactFields: string[] = ['primaryEmail', 'contactNo', 'password'] // contactNo maps to secondaryPhone

  function mergeUserProfile(prev: PermalinkUser, next: UserProfile): PermalinkUser {
    return {
      ...prev,
      ...next,
      givenName: next.givenName ?? prev.givenName,
      familyName: next.familyName ?? prev.familyName,
      birthDate: next.birthDate ?? prev.birthDate,
      gender: next.gender ?? prev.gender,
      isActive: next.isActive ?? prev.isActive,
      profileImage: next.profileImage ?? prev.profileImage,
      coverImage: next.coverImage ?? prev.coverImage,
      permalink: next.permalink ?? prev.permalink,
      headline: next.headline ?? prev.headline,
      address: next.address ?? prev.address,
      location: next.location ?? prev.location,
      profileCompletion: next.profileCompletion ?? prev.profileCompletion,
      profileMetrics: next.profileMetrics ?? prev.profileMetrics,
      isUserVerified: next.isUserVerified ?? prev.isUserVerified,
      hasNewMessage: next.hasNewMessage ?? prev.hasNewMessage,
      subscription: next.subscription ?? prev.subscription,
      recruiterTrial: next.recruiterTrial ?? prev.recruiterTrial,

      // AuthUser-only properties retained from previous
      companyName: prev.companyName,
      isSearchActive: prev.isSearchActive,
      followersCount: prev.followersCount,
      followingCount: prev.followingCount,
      preferences: prev.preferences,
      postsCount: prev.postsCount,
      isContactRestricted: prev.isContactRestricted,
      isFollowing: prev.isFollowing,
      isFollower: prev.isFollower,
      isContactPurchased: prev.isContactPurchased,
    }
  }

  const formatSelectedDate = (date: string | undefined) => {
    if (!date) return ''
    try {
      return format(new Date(date), 'dd/MM/yyyy')
    } catch {
      return ''
    }
  }

  const validateField = (field: string, value: string): string => {
    const trimmedValue = value.trim()
    if (!trimmedValue) {
      return 'This field is required.'
    }
    switch (field) {
      case 'givenName':
      case 'familyName':
        if (!/^[a-zA-Z]{3,20}$/.test(trimmedValue)) {
          return 'Must be 3-20 letters.'
        }
        break
      case 'headline':
        if (trimmedValue.length > 100) {
          return 'Max 100 characters.'
        }
        break
      case 'title':
        if (!/^[a-zA-Z0-9\s]{3,50}$/.test(trimmedValue)) {
          return 'Must be 3-50 characters, letters, numbers, or spaces.'
        }
        break
      case 'birthDate':
        // Additional birthDate validation could be added here
        break
      case 'location':
        if (trimmedValue.length > 200) {
          return 'Max 200 characters.'
        }
        break
      case 'nationality':
        if (!/^[a-zA-Z\s]{2,50}$/.test(trimmedValue)) {
          return 'Must be 2-50 letters and spaces.'
        }
        break
      case 'gender':
        if (!['MALE', 'FEMALE', 'OTHER'].includes(trimmedValue)) {
          return 'Invalid gender value.'
        }
        break
      case 'primaryEmail':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(trimmedValue)) {
          return 'Invalid email address.'
        }
        break
      case 'contactNo':
        if (!/^\d{10}$/.test(trimmedValue)) {
          return 'Contact number must be 10 digits.'
        }
        break
      case 'password':
        if (trimmedValue.length < 1) {
          return 'Password is required'
        }
        if (/^[^"'\\;#*]+$/.test(trimmedValue)) {
          return 'Special characters like \', ", \\, ;, #, and * are not allowed.'
        }
        break
      default:
        break
    }
    return ''
  }

  // Update handler for both profile and contact
  const handleUserChange = (field: string, value: string) => {
    // Clear backend errors when user starts typing
    if (personalFields.includes(field)) {
      setPersonalUpdateError('')
    } else if (contactFields.includes(field)) {
      setContactUpdateError('')
    }

    if (personalFields.includes(field)) {
      if (!userProfile) return
      let updatedProfile = { ...userProfile }
      if (
        [
          'givenName',
          'familyName',
          'headline',
          'title',
          'birthDate',
          'gender',
          'nationality',
        ].includes(field)
      ) {
        updatedProfile = { ...updatedProfile, [field]: value }
      } else if (field === 'location') {
        updatedProfile = {
          ...updatedProfile,
          address: { ...updatedProfile.address, line1: value },
          location: {
            ...updatedProfile.location,
            address: {
              ...updatedProfile.location.address,
              city: '',
              state: '',
              country: '',
            },
          },
        }
      }

      setUserProfile(updatedProfile)
      // Only show custom validation errors if there are no backend errors
      if (!personalUpdateError) {
        const errorMessage = validateField(field, value)
        setErrors((prev) => ({ ...prev, [field]: errorMessage }))
      } else {
        // Clear field-specific errors when there's a backend error
        setErrors((prev) => ({ ...prev, [field]: '' }))
      }
    } else if (contactFields.includes(field)) {
      if (!userContact) return
      let updatedContact = { ...userContact }
      if (field === 'primaryEmail') {
        updatedContact.primaryEmail = value
      } else if (field === 'contactNo') {
        updatedContact.primaryPhone = value
      } else if (field === 'password') {
        setPassword(value)
      }
      setUserContact(updatedContact)
      // Only show custom validation errors if there are no backend errors
      if (!contactUpdateError) {
        const errorMessage = validateField(field, value)
        setErrors((prev) => ({ ...prev, [field]: errorMessage }))
      } else {
        // Clear field-specific errors when there's a backend error
        setErrors((prev) => ({ ...prev, [field]: '' }))
      }
    }
  }

  const cancelPersonalEdit = () => {
    if (userProfile && originalUserProfile) {
      setUserProfile({ ...originalUserProfile })
      const newErrors = { ...errors }
      personalFields.forEach((field) => delete newErrors[field])
      setErrors(newErrors)
    }
    setIsUserProfileEditingEnabled(false)
    setIsPersonalInfoUpdated(false)
    setPersonalUpdateError('')
  }

  const cancelContactEdit = () => {
    if (userContact && originalUserContact) {
      setUserContact({ ...originalUserContact })
      setPassword('')
      setIsRegeneratingPassword(false)
      const newErrors = { ...errors }
      contactFields.forEach((field) => delete newErrors[field])
      setErrors(newErrors)
    }
    setIsUserContactEditingEnabled(false)
    setIsVerifyModal(false)
    setIsContactInfoUpdated(true)
    setContactUpdateError('')
    setError('')
  }

  const handlePersonalUpdate = async () => {
    if (!userProfile || !originalUserProfile) return

    // If there's a backend error, don't proceed with validation
    if (personalUpdateError) {
      return
    }

    let isValid = true
    const newErrors: Partial<Record<string, string>> = {}

    personalFields.forEach((field) => {
      let value = '' // Default to empty string

      if (
        [
          'givenName',
          'familyName',
          'headline',
          'title',
          'birthDate',
          'gender',
          'nationality',
        ].includes(field)
      ) {
        value = (userProfile[field as keyof UserProfile] as string) ?? ''
      } else if (field === 'location') {
        value = userProfile?.address?.line1 ?? ''
      }

      const errorMessage = validateField(field, value)
      if (errorMessage) {
        isValid = false
        newErrors[field] = errorMessage
      }
    })

    setErrors((prev) => ({ ...prev, ...newErrors }))

    if (isValid) {
      try {
        const response = await UserService.updateUserProfile(userProfile)
        setIsUserProfileEditingEnabled(false)
        setOriginalUserProfile(userProfile)
        setAuthUser((prev: PermalinkUser | null) =>
          prev ? mergeUserProfile(prev, response.data.user) : response.data.user,
        )
        setPersonalUpdateError('')
        setIsPersonalInfoUpdated(true)
        setIsInfoUpdated(true)
      } catch (error: any) {
        // Check if there's a backend error message
        if (error.message) {
          setPersonalUpdateError(error.message)
        } else {
          setPersonalUpdateError('Error updating profile')
        }
      }
    }
  }

  const handleContactUpdate = async (resend?: boolean) => {
    if (!userContact?.primaryPhone) return

    // If there's a backend error, don't proceed with validation
    if (contactUpdateError) {
      return
    }

    let isValid = true
    const newErrors: Partial<Record<string, string>> = {}

    const field = contactFields[1]
    if (field) {
      let value = ''
      if (field === 'contactNo') {
        value = userContact.primaryPhone || ''
      }
      const errorMessage = validateField(field, value)
      if (errorMessage) {
        isValid = false
        newErrors[field] = errorMessage
      }
    }

    setErrors((prev) => ({ ...prev, ...newErrors }))

    if (isValid) {
      try {
        const response = await OtpService.sendMobileOtp(
          userContact.primaryPhone,
          resend ? 'resend' : 'send',
          'other',
        )
        setIsVerifyModal(true)
        // setIsUserContactEditingEnabled(false)
        setContactUpdateError('')
      } catch (error: any) {
        // Check if there's a backend error message
        if (error.message) {
          setContactUpdateError(error.message)
        } else {
          setContactUpdateError('Error updating contact')
        }
      }
    }
  }

  const handleUpdatePrimaryPhone = async () => {
    if (!userContact?.primaryEmail) return
    if (otp.length !== 6) return
    setLoading(true)
    try {
      const response = await UserService.updatePrimaryPhone(userContact?.primaryPhone, otp)

      if (response.status === 'success') {
        setOriginalUserContact(userContact)
        setIsUserContactEditingEnabled(false)
        setIsVerifyModal(false)
        setIsContactInfoUpdated(true)
        setError('') // Clear any existing errors
      } else {
        throw response
      }
    } catch (error: any) {
      // Check if there's a backend error message
      if (error.message) {
        setError(error.message)
      } else if (error.data?.message) {
        setError(error.data.message)
      } else {
        setError('Invalid OTP. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  const handleToggle = async () => {
    if (allowReachRecruiters === undefined) return
    try {
      const response = await UserService.recruiterContact(allowReachRecruiters)

      if (response.status === 'success') {
        toast.success(response.message)
      } else {
        throw response
      }
    } catch (error: any) {
      // Check if there's a backend error message
      if (error.message) {
        setError(error.message)
      } else if (error.data?.message === 'CONTACT_PHONE_UPDATE_LIMIT_EXCEEDED') {
        setError('Phone number update limit reached.')
      } else {
        setError('Invalid OTP. Please try again.')
      }
    }
  }

  useEffect(() => {
    if (initialRender) {
      setInitialRender(false)
      return
    }
    handleToggle()
  }, [allowReachRecruiters])

  // Photo update handlers remain largely the same
  const handlePhotoUpdate = () => {
    fileInputRef.current?.click()
  }

  const onFileSelected = (image: string) => {
    setImageSrc(image)
    setIsPhotoUpdated(true)
    setIsCropModalOpen(true)
  }

  const onCropSuccess = async () => {
    await fetchUserData()
    setIsPhotoUpdated(false)
    refreshUserData && refreshUserData()
    // handleShowToast('success', 'Image uploaded successfully')
    setIsCropModalOpen(false)
  }

  const onCropError = (error: string) => {
    // handleShowToast('error', error)
    setIsPhotoUpdated(false)
  }

  // Fetch profile and contact data separately and set state accordingly.
  const fetchUserData = async () => {
    try {
      const [profileData, contactData] = await Promise.all([
        UserService.getUserProfile(),
        UserService.getUserContact(),
      ])
      const profile = profileData.data
      const contact = contactData.data.contact

      setUserProfile(profile)
      setAuthUser((prev: PermalinkUser | null) =>
        prev ? mergeUserProfile(prev, profile) : profile,
      )
      setPersonalInformation(profile)
      setOriginalUserProfile(profile)
      setContactDetails(contact)
      setUserContact(contact)
      setOriginalUserContact(contact)
    } catch (error) {}
  }

  const handleSendPasswordOtp = async (resend?: boolean) => {
    setSendingPasswordOtp(true)
    try {
      if (!userContact?.primaryEmail) return
      const response = await OtpService.sendOtp(
        userContact?.primaryEmail,
        resend ? 'resend' : 'send',
        'other',
      )
      setIsRegeneratingPassword(true)
      if (response.status === 'success') {
      } else {
        throw response
      }
    } catch (error: any) {
      // Check if there's a backend error message
      if (error.message) {
        handleError(error)
      } else {
        handleError(new Error('Failed to send OTP'))
      }
    } finally {
      setSendingPasswordOtp(false)
    }
  }

  const handleResetPassword = async () => {
    if (!password) {
      setErrors((prev) => ({ ...prev, password: 'Password is required' }))
    } else {
      setErrors((prev) => ({ ...prev, password: '' }))
    }
    return
    if (otp.length !== 6) return
    setLoading(true)
    try {
      const response = await UserService.resetPassword(password, otp)

      if (response.status === 'success') {
        toast.success('Password Updated successfully')
        setOriginalUserContact(userContact)
        setIsUserContactEditingEnabled(false)
        setIsVerifyModal(false)
        setIsRegeneratingPassword(false)
        setIsContactInfoUpdated(true)
        setError('') // Clear any existing errors
      } else {
        throw response
      }
    } catch (error: any) {
      // Check if there's a backend error message
      if (error.message) {
        setError(error.message)
      } else if (error.data?.message) {
        setError(error.data.message)
      } else {
        setError('Invalid OTP. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  const handleOtpChange = (otpValue: string) => {
    setOtp(otpValue)
    // Clear error when user starts typing
    if (error) {
      setError('')
    }
  }

  const handleLocationUpdate = (locationData: any) => {
    if (!userProfile) return

    // The locationData from google/service has address, latitude, longitude
    const { address, latitude, longitude } = locationData

    setUserProfile({
      ...userProfile,
      address: {
        ...userProfile.address,
        line1: formatLocationString(locationData), // For display in non-edit mode
      },
      location: {
        ...userProfile.location,
        address: address,
        latitude: latitude,
        longitude: longitude,
      },
    })

    setLocationInput(formatLocationString(locationData))
    isUserTypingRef.current = false
  }

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser.')
      return
    }

    setIsGettingLocation(true)

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords

        try {
          const location = await LocationService.getLocationFromCoords(latitude, longitude)
          if (location) {
            handleLocationUpdate(location)
            isUserTypingRef.current = false
          } else {
            alert('Could not get location details')
          }
        } catch (error) {
          console.error('Error getting location details:', error)
          alert('Error getting location details')
        } finally {
          setIsGettingLocation(false)
        }
      },
      (error) => {
        setIsGettingLocation(false)
        switch (error.code) {
          case error.PERMISSION_DENIED:
            alert('Location access denied by user.')
            break
          case error.POSITION_UNAVAILABLE:
            alert('Location information is unavailable.')
            break
          case error.TIMEOUT:
            alert('Location request timed out.')
            break
          default:
            alert('An unknown error occurred while getting location.')
            break
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000,
      },
    )
  }

  useEffect(() => {
    if (userProfile?.location) {
      setLocationInput(formatLocationString(userProfile.location))
    } else if (userProfile?.address?.line1) {
      setLocationInput(userProfile.address.line1)
    }
  }, [userProfile?.location, userProfile?.address?.line1])

  useEffect(() => {}, [userContact])
  const handleLogout = () => {
    logout(true)
    router.push('/')
  }

  useEffect(() => {
    const updateToastPosition = () => {
      if (window.innerWidth < 1024) {
        setToastPosition('bottom-center')
      } else {
        setToastPosition('bottom-right')
      }
    }

    updateToastPosition() // Call initially
    window.addEventListener('resize', updateToastPosition)
    return () => window.removeEventListener('resize', updateToastPosition)
  }, [])

  useEffect(() => {
    setAuthUserProfile(authUser)
  }, [authUser])

  useEffect(() => {
    fetchUserData()
  }, [])

  useEffect(() => {}, [otp])

  return (
    <>
      {isCropModalOpen && imageSrc && (
        <ImageCropModal
          type="profile"
          image={imageSrc}
          onCropComplete={(croppedFile) =>
            handleCropComplete(
              croppedFile,
              UserService.uploadPhoto,
              onCropSuccess,
              onCropError,
              'profile',
              handleShowToast,
              removeToast,
            )
          }
          onClose={() => (setIsCropModalOpen(false), setIsPhotoUpdated(false))}
        />
      )}
      <div className="w-full px-4 pb-6 pt-10 sm:px-0">
        <Toast toasts={toasts} removeToast={removeToast} position={toastPosition} />
        {/* Photo Upload Section */}
        <div className="mb-10 flex items-start gap-5">
          <div className="flex items-center gap-4 sm:w-1/2">
            {userProfile?.profileImage?.url && (
              <Avatar
                src={userProfile.profileImage.url}
                alt="profile"
                size={20}
                className="cursor-pointer"
              />
            )}
            <Button
              size="small"
              isRectangle
              className="bg-lucres-gray-100! dark:bg-dark-lucres-black-300! flex-1"
              onClick={handlePhotoUpdate}
              disabled={isPhotoUpdated}
            >
              Upload Photo
            </Button>
            <input
              type="file"
              ref={fileInputRef}
              style={{ display: 'none' }}
              onChange={(e) => handleFileChange(e, onFileSelected, handleShowToast)}
              accept="image/*"
            />
          </div>
        </div>

        {/* Personal Information Section */}
        <div className="mb-12">
          <div className="mb-8 flex items-center justify-between">
            <div className="text-lg font-semibold">Personal Information</div>
            {isUserProfileEditingEnabled ? (
              <XIcon size={26} className="cursor-pointer" onClick={cancelPersonalEdit} />
            ) : (
              <NotePencilIcon
                size={26}
                className="cursor-pointer"
                onClick={() => {
                  setIsUserProfileEditingEnabled(true)
                  setIsPersonalInfoUpdated(false)
                }}
              />
            )}
          </div>
          <form onSubmit={(e) => e.preventDefault()}>
            <div className="flex flex-col gap-5">
              <div className="items-start gap-5 lg:flex">
                <Input
                  label="First Name"
                  theme="gray"
                  value={userProfile?.givenName ? convertToTitleCase(userProfile.givenName) : ''}
                  disabled={!isUserProfileEditingEnabled}
                  className={!isUserProfileEditingEnabled ? 'p-0! bg-transparent' : ''}
                  error={personalUpdateError || errors.givenName}
                  onChange={(e) => handleUserChange('givenName', e.target.value)}
                />
                <Input
                  label="Last Name"
                  theme="gray"
                  value={userProfile?.familyName ? convertToTitleCase(userProfile.familyName) : ''}
                  disabled={!isUserProfileEditingEnabled}
                  className={!isUserProfileEditingEnabled ? 'p-0! bg-transparent' : ''}
                  error={personalUpdateError || errors.familyName}
                  onChange={(e) => handleUserChange('familyName', e.target.value)}
                />
              </div>
              <div className="items-start gap-5 lg:flex">
                <Input
                  label="Headline"
                  theme="gray"
                  value={userProfile?.headline || ''}
                  disabled={!isUserProfileEditingEnabled}
                  className={!isUserProfileEditingEnabled ? 'p-0! pb-1! bg-transparent' : ''}
                  error={personalUpdateError || errors.headline}
                  onChange={(e) => handleUserChange('headline', e.target.value)}
                />

                <Input
                  label="Job Title"
                  theme="gray"
                  value={userProfile?.title || ''}
                  disabled={!isUserProfileEditingEnabled}
                  className={!isUserProfileEditingEnabled ? 'p-0! pb-1! bg-transparent' : ''}
                  error={personalUpdateError || errors.title}
                  onChange={(e) => handleUserChange('title', e.target.value)}
                />
              </div>
              <div className="items-start gap-5 lg:flex">
                {/* <Input
                  label="Date of Birth"
                  type="date"
                  theme="gray"
                  value={
                    userProfile?.birthDate
                      ? userProfile.birthDate.includes('T')
                        ? userProfile.birthDate.split('T')[0]
                        : userProfile.birthDate
                      : ''
                  }
                  disabled={!isUserProfileEditingEnabled}
                  placeholder="Select your date of birth"
                  className="p-0! pointer-events-none  flex-1 bg-transparent"
                  error={personalUpdateError || errors.birthDate}
                  onChange={(e) => handleUserChange('birthDate', e.target.value)}
                /> */}
                {/* Date of Birth  */}
                <div className="relative flex-1">
                  <div
                    className="flex-1 cursor-pointer"
                    onClick={() => isUserProfileEditingEnabled && setIsDatePickerOpen(true)}
                  >
                    <Input
                      label="Date of Birth"
                      theme="gray"
                      value={formatSelectedDate(userProfile?.birthDate)}
                      disabled
                      className={`pointer-events-none flex-1 ${!isUserProfileEditingEnabled ? 'p-0! pb-1! bg-transparent' : ''}`}
                      error={personalUpdateError || errors.birthDate}
                    />
                  </div>
                  {isDatePickerOpen && (
                    <div className="absolute left-0 z-10 w-full">
                      <DatePicker
                        selectedDate={
                          userProfile?.birthDate ? new Date(userProfile.birthDate) : null
                        }
                        onDateChange={(newDate) => {
                          handleUserChange('birthDate', format(newDate, 'yyyy-MM-dd'))
                          setIsDatePickerOpen(false)
                        }}
                        isOpen={isDatePickerOpen}
                        onClose={() => setIsDatePickerOpen(false)}
                        maxDate={new Date()}
                      />
                    </div>
                  )}
                </div>
                {/* Gender  */}
                <div className="flex-1">
                  {isUserProfileEditingEnabled ? (
                    <Select
                      label="Gender"
                      value={userProfile?.gender || ''}
                      options={[
                        { value: 'MALE', label: 'Male' },
                        { value: 'FEMALE', label: 'Female' },
                        { value: 'OTHER', label: 'Other' },
                      ]}
                      onChange={(value) => handleUserChange('gender', value)}
                      error={personalUpdateError || errors.gender}
                    />
                  ) : (
                    <Input
                      label="Gender"
                      theme="gray"
                      value={
                        userProfile?.gender
                          ? userProfile.gender.charAt(0).toUpperCase() +
                            userProfile.gender.slice(1).toLowerCase()
                          : ''
                      }
                      disabled
                      className="p-0! bg-transparent"
                      error={personalUpdateError || errors.gender}
                    />
                  )}
                </div>
              </div>
              <div className="items-start gap-5 lg:flex">
                <div className="relative w-full flex-1 lg:w-1/2">
                  {isUserProfileEditingEnabled ? (
                    <>
                      <div className="absolute left-3 top-11 z-10 flex items-center">
                        <MagnifyingGlassIcon
                          size={18}
                          weight="bold"
                          className="border-lucres-300 dark:border-lucres-200 dark:border-x-dark-lucres-black-100 border-r pr-1"
                        />
                      </div>
                      <Input
                        label="Location"
                        placeholder="e.g. Area, City, State, Country"
                        name="location"
                        autoComplete="off"
                        value={locationInput}
                        onChange={(e) => {
                          isUserTypingRef.current = true
                          const query = e.target.value
                          setLocationInput(query)
                          if (locationDebounceTimeout.current) {
                            clearTimeout(locationDebounceTimeout.current)
                          }
                          if (query.length > 2) {
                            locationDebounceTimeout.current = setTimeout(() => {
                              if (isUserTypingRef.current) {
                                fetchSuggestions(query)
                                setShowLocationSuggestions(true)
                              }
                            }, 300)
                          } else {
                            setSuggestions([])
                            setShowLocationSuggestions(false)
                          }
                        }}
                        onBlur={() => {
                          setTimeout(() => setShowLocationSuggestions(false), 200)
                        }}
                        onFocus={() => {
                          if (locationInput.length > 2 && locationSuggestions.length > 0) {
                            setShowLocationSuggestions(true)
                          }
                        }}
                        error={personalUpdateError || errors.location}
                        theme="gray"
                        className="pl-10"
                      />
                      {showLocationSuggestions && locationSuggestions.length > 0 && (
                        <ul className="border-lucres-300 dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 absolute z-20 mt-1 max-h-48 w-full overflow-auto rounded-md border bg-white shadow-lg">
                          {locationSuggestions.map((suggestion) => (
                            <li
                              key={suggestion.place_id}
                              className="hover:bg-lucres-green-200 dark:hover:bg-dark-lucres-black-400 cursor-pointer px-4 py-3 hover:bg-opacity-30"
                              onClick={async () => {
                                // Fetch place details using Google Maps JS API PlacesService
                                if (
                                  window.google &&
                                  window.google.maps &&
                                  window.google.maps.places
                                ) {
                                  const service = new window.google.maps.places.PlacesService(
                                    document.createElement('div'),
                                  )
                                  service.getDetails(
                                    { placeId: suggestion.place_id },
                                    (place: any, status: any) => {
                                      if (
                                        status ===
                                          window.google.maps.places.PlacesServiceStatus.OK &&
                                        place
                                      ) {
                                        // Parse address components
                                        let country = ''
                                        let state = ''
                                        let city = ''
                                        let area = ''
                                        if (place.address_components) {
                                          for (const component of place.address_components) {
                                            const types = component.types
                                            if (types.includes('country'))
                                              country = component.long_name
                                            else if (types.includes('administrative_area_level_1'))
                                              state = component.long_name
                                            else if (
                                              types.includes('locality') ||
                                              types.includes('administrative_area_level_2')
                                            )
                                              city = component.long_name
                                            else if (
                                              types.includes('sublocality') ||
                                              types.includes('neighborhood')
                                            )
                                              area = component.long_name
                                          }
                                        }
                                        const locationObj = {
                                          address: { country, state, city, area },
                                          latitude: place.geometry?.location?.lat() || 0,
                                          longitude: place.geometry?.location?.lng() || 0,
                                        }
                                        handleLocationUpdate(locationObj)
                                      } else {
                                        // fallback: just set the description as city
                                        const locationObj = {
                                          address: {
                                            country: '',
                                            state: '',
                                            city: suggestion.description,
                                            area: '',
                                          },
                                          latitude: 0,
                                          longitude: 0,
                                        }
                                        handleLocationUpdate(locationObj)
                                      }
                                    },
                                  )
                                } else {
                                  // fallback: just set the description as city
                                  const locationObj = {
                                    address: {
                                      country: '',
                                      state: '',
                                      city: suggestion.description,
                                      area: '',
                                    },
                                    latitude: 0,
                                    longitude: 0,
                                  }
                                  handleLocationUpdate(locationObj)
                                }
                                setShowLocationSuggestions(false)
                              }}
                            >
                              <div className="flex items-center gap-2">
                                <MapPinIcon
                                  size={14}
                                  className="text-lucres-500 dark:text-dark-lucres-green-200"
                                />
                                <div>
                                  <div className="text-lucres-900 font-medium dark:text-white">
                                    {suggestion.structured_formatting?.main_text ||
                                      suggestion.structured_formatting?.mainText ||
                                      suggestion.description}
                                  </div>
                                  <div className="text-lucres-600 dark:text-dark-lucres-green-100 text-xs">
                                    {suggestion.structured_formatting?.secondary_text ||
                                      suggestion.structured_formatting?.secondaryText ||
                                      ''}
                                  </div>
                                </div>
                              </div>
                            </li>
                          ))}
                        </ul>
                      )}
                      <button
                        type="button"
                        onClick={getCurrentLocation}
                        disabled={isGettingLocation}
                        className={`mt-2 flex items-center gap-1 rounded px-2 py-1 text-xs font-medium transition-colors ${
                          isGettingLocation
                            ? 'cursor-not-allowed bg-gray-200 text-gray-500'
                            : 'bg-lucres-100 text-lucres-700 hover:bg-lucres-200 dark:bg-dark-lucres-black-300 dark:text-dark-lucres-green-200 dark:hover:bg-dark-lucres-black-200'
                        }`}
                      >
                        <MapPinIcon size={12} weight={isGettingLocation ? 'regular' : 'bold'} />
                        {isGettingLocation ? 'Getting...' : 'Use Current Location'}
                      </button>
                    </>
                  ) : (
                    <Input
                      label="Location"
                      theme="gray"
                      value={userProfile?.address?.line1 || ''}
                      disabled
                      className={'p-0! pb-1! bg-transparent'}
                      error={personalUpdateError || errors.location}
                      onChange={(e) => handleUserChange('location', e.target.value)}
                      placeholder="Enter your full address"
                    />
                  )}
                </div>
                <div className="mt-4 w-full lg:mt-0 lg:w-1/2">
                  <Input
                    label="Nationality"
                    theme="gray"
                    value={userProfile?.nationality || ''}
                    disabled={!isUserProfileEditingEnabled}
                    className={!isUserProfileEditingEnabled ? 'p-0! pb-1! bg-transparent' : ''}
                    error={personalUpdateError || errors.nationality}
                    onChange={(e) => handleUserChange('nationality', e.target.value)}
                    placeholder="Enter your nationality"
                  />
                </div>
              </div>
            </div>
            {(isUserProfileEditingEnabled ||
              (isPersonalInfoUpdated &&
                new URLSearchParams(window.location.search).get('fromResume'))) && (
              <>
                <div dir="rtl" className="mt-8">
                  {isPersonalInfoUpdated &&
                  new URLSearchParams(window.location.search).get('fromResume') ? (
                    <Button size="small" onClick={() => router.push('/resume')} className="px-4!">
                      <div className="flex items-center gap-2" dir="ltr">
                        <span>Back to resume</span>
                      </div>
                    </Button>
                  ) : (
                    <Button size="small" onClick={handlePersonalUpdate}>
                      Update
                    </Button>
                  )}
                </div>
              </>
            )}
          </form>
        </div>

        {/* Contact Information Section */}
        <div className="mb-8">
          <div className="mb-8 flex items-center justify-between">
            <div className="text-lg font-semibold">Contact Information</div>
            {isUserContactEditingEnabled ? (
              <XIcon size={26} className="cursor-pointer" onClick={cancelContactEdit} />
            ) : (
              <NotePencilIcon
                size={26}
                className="cursor-pointer"
                onClick={() => {
                  setIsUserContactEditingEnabled(true)
                  setIsContactInfoUpdated(false)
                  setError('')
                  setContactUpdateError('')
                  setIsVerifyModal(false)
                }}
              />
            )}
          </div>
          <form onSubmit={(e) => e.preventDefault()}>
            <div className="flex flex-col gap-5">
              <div className="items-start gap-5 lg:flex">
                <Input
                  label="Contact Number"
                  theme="gray"
                  maxLength={10}
                  value={userContact?.primaryPhone ? userContact?.primaryPhone : ''}
                  disabled={!isUserContactEditingEnabled || isRegeneratingPassword}
                  className={!isUserContactEditingEnabled ? 'p-0! bg-transparent' : ''}
                  error={contactUpdateError || errors.contactNo}
                  onChange={(e) => handleUserChange('contactNo', e.target.value)}
                />
                <Input
                  label="Email Id"
                  theme="gray"
                  value={userContact?.primaryEmail || ''}
                  disabled={true}
                  className={'p-0! bg-transparent'}
                  error={contactUpdateError || errors.primaryEmail}
                  onChange={(e) => handleUserChange('primaryEmail', e.target.value)}
                />
              </div>
              {!isVerifyModal && (
                <div className="w-full items-center gap-5 lg:flex">
                  <Input
                    label="Password"
                    type={isRegeneratingPassword ? 'password' : 'text'}
                    theme="gray"
                    value={isRegeneratingPassword ? password : '*********'}
                    disabled={!isRegeneratingPassword || !isUserContactEditingEnabled}
                    componentClass="w-1/2!"
                    className={`${!isUserContactEditingEnabled ? 'p-0! bg-transparent' : ''}`}
                    error={contactUpdateError || errors.password}
                    onChange={(e) => handleUserChange('password', e.target.value)}
                  />
                  {isUserContactEditingEnabled && !isRegeneratingPassword && (
                    <div
                      className="hover:text-lucres-500 flex w-1/2 cursor-pointer items-center gap-3"
                      onClick={() => handleSendPasswordOtp()}
                    >
                      <ArrowsClockwiseIcon
                        size={22}
                        className={`${sendingPasswordOtp && 'animate-spin'}`}
                      />{' '}
                      Regenrate
                    </div>
                  )}
                </div>
              )}
              {(isVerifyModal || isRegeneratingPassword) && (
                <div className="flex w-fit flex-col justify-center">
                  <span className="mb-2 text-sm">
                    {isRegeneratingPassword
                      ? 'OTP sent to ur registered email address '
                      : 'Please Enter OTP'}
                  </span>
                  <InputOtp
                    length={6}
                    onSubmit={handleOtpChange}
                    isResend={false}
                    onResendOtp={() => {
                      isRegeneratingPassword
                        ? handleSendPasswordOtp(true)
                        : handleContactUpdate(true)
                    }}
                    inputClassName="h-10! w-10!"
                    className="items-start! justify-start!"
                    isButton
                    error={error || ''}
                    onClick={
                      isRegeneratingPassword ? handleResetPassword : handleUpdatePrimaryPhone
                    }
                  />
                </div>
              )}
            </div>
            {(isUserContactEditingEnabled ||
              (isContactInfoUpdated &&
                new URLSearchParams(window.location.search).get('fromResume'))) &&
              !isVerifyModal &&
              !isRegeneratingPassword && (
                <div dir="rtl" className="mt-8">
                  {isContactInfoUpdated &&
                  new URLSearchParams(window.location.search).get('fromResume') ? (
                    <Button size="small" onClick={() => router.push('/resume')} className="px-4!">
                      <div className="flex items-center gap-2" dir="ltr">
                        <span> Back to resume</span>
                      </div>
                    </Button>
                  ) : (
                    <Button size="small" onClick={() => handleContactUpdate()}>
                      Update
                    </Button>
                  )}
                </div>
              )}
          </form>
        </div>
      </div>
      <div className="mb-5 flex items-center gap-2 px-4 sm:px-0">
        <div className="flex items-center gap-2">
          <div
            className={`relative flex h-6 w-12 cursor-pointer items-center rounded-full transition-colors ${
              !allowReachRecruiters
                ? 'bg-dark-lucres-green-200 dark:bg-dark-lucres-black-200'
                : 'bg-lucres-400 dark:bg-lucres-500'
            }`}
            onClick={() => {
              setAllowReachRecruiters(!allowReachRecruiters)
            }}
            role="switch"
            aria-checked={allowReachRecruiters}
            aria-label="Switch recruiter contact preferance"
          >
            <div
              className={`absolute inset-y-0 left-0 top-0.5 h-5 w-5 rounded-full bg-white transition-transform ${
                !allowReachRecruiters ? '' : 'translate-x-7'
              }`}
            />
          </div>
          <span className="text-sm font-medium">Want recruiters to contact you?</span>
        </div>
      </div>
      <div className=" px-4 sm:px-0">
        <Button
          isRectangle
          size="small"
          theme="translucent"
          className="mb-10"
          onClick={() => setLogoutModal(true)}
        >
          <SignOutIcon size={24} className="mr-2" /> Logout from all Devices
        </Button>
      </div>
      {logoutModal && (
        <Overlay
          heading="Logout from all Devices"
          handleClose={() => setLogoutModal(false)}
          size="min-h-fit mt-20"
          classes="p-0! "
        >
          <div className="dark:bg-dark-lucres-black-500 w-full rounded-lg bg-white p-4 shadow-lg">
            <div className="flex flex-col items-center">
              <WarningIcon size={32} />
              <h2 className="mt-3 text-center text-lg font-semibold">Logout Globally</h2>
              <p className="text-center text-sm text-gray-500 dark:text-gray-300">
                Are you sure you want to logout globally?
              </p>
            </div>
            <div className="dark:border-t-dark-lucres-black-200 mt-4 flex justify-between gap-4 border-t pt-4">
              <Button
                size="small"
                theme="transparent"
                className="px-4! py-1.5! !border"
                onClick={() => setLogoutModal(false)}
              >
                Cancel
              </Button>
              <Button
                size="small"
                theme="dark"
                className="px-4! py-1.5! text-white!"
                onClick={handleLogout}
              >
                Confirm Logout
              </Button>
            </div>
          </div>
        </Overlay>
      )}
    </>
  )
}

export default PersonalInformationData
