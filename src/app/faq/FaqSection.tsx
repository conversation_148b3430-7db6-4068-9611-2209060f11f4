'use client'

import { useState } from 'react'
import { Plus, Minus } from '@phosphor-icons/react'

export type FaqItem = {
  question: string
  answer: string
}

type FaqSubSection = {
  title: string
  faqs?: FaqItem[]
  content?: string
}

type FaqSectionType = {
  section: string
  faqs?: FaqItem[]
  subSections?: FaqSubSection[]
}

type FaqSectionProps = {
  title: string
  faqs: FaqItem[]
  isSubsection?: boolean
}

export function FaqSection({ title, faqs, isSubsection = false }: FaqSectionProps) {
  return (
    <div
      className={`${isSubsection ? 'border-lucres-200 dark:border-lucres-700 border-l-2 pl-4' : ''}`}
    >
      {title && (
        <h3 className="text-lucres-800 dark:text-lucres-200 mb-4 text-xl font-medium">{title}</h3>
      )}
      <div className="space-y-4">
        {faqs.map((faq, index) => (
          <FaqItem key={index} question={faq.question} answer={faq.answer} />
        ))}
      </div>
    </div>
  )
}

function FaqItem({ question, answer }: FaqItem) {
  const [isOpen, setIsOpen] = useState(false)
  const itemId = `faq-${question.replace(/\s+/g, '-').toLowerCase()}`

  return (
    <div
      className="border-lucres-300 dark:border-lucres-600/80 group mb-4 border-b pb-4 last:mb-0 last:border-b-0 last:pb-0"
      id={itemId}
    >
      <button
        className="flex w-full items-center justify-between text-left"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-controls={`${itemId}-content`}
      >
        <h4 className="text-lucres-900 dark:text-lucres-100 group-hover:text-lucres-700 dark:group-hover:text-lucres-300 text-lg font-medium transition-colors">
          {question}
        </h4>
        <span className="text-lucres-500 dark:text-lucres-400 ml-4 flex-shrink-0">
          {isOpen ? <Minus size={20} weight="bold" /> : <Plus size={20} weight="bold" />}
        </span>
      </button>
      <div
        id={`${itemId}-content`}
        className={`overflow-hidden transition-all duration-200 ease-in-out ${isOpen ? 'mt-3' : 'max-h-0'}`}
        aria-hidden={!isOpen}
      >
        <div className="prose dark:prose-invert text-lucres-700 dark:text-lucres-300 max-w-none">
          {answer.split('\n').map((paragraph, i) => (
            <p key={i} className="mb-3 last:mb-0">
              {paragraph}
            </p>
          ))}
        </div>
      </div>
    </div>
  )
}
