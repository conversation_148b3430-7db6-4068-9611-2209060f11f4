'use client'

import { useState } from 'react'
import { PlusIcon, MinusIcon } from '@phosphor-icons/react'
import Footer from '@/components/Footer'

type FaqItem = {
  question: string
  answer: string
}

type FaqSubSection = {
  title: string
  faqs?: FaqItem[]
  content?: string
}

type FaqSection = {
  section: string
  faqs?: FaqItem[]
  subSections?: FaqSubSection[]
}

const faqData: FaqSection[] = [
  {
    section: 'What is lucres?',
    faqs: [
      {
        question: 'How does <PERSON><PERSON> benefit you?',
        answer:
          '<PERSON><PERSON> provides a community-led professional network, facilitating faster connections with like-minded individuals, access to relevant job opportunities, and quick interaction with recruiters. Recruiters can enjoy a seamless, cost-efficient recruiting experience.',
      },
      {
        question: 'Is <PERSON><PERSON> free to use?',
        answer:
          'Yes, <PERSON><PERSON> is free for users. There are no charges for creating an account, building your profile, or applying to jobs listed on the platform.',
      },
      {
        question: 'Is <PERSON><PERSON> limited to certain job types or industries?',
        answer:
          "No, <PERSON><PERSON> caters to a diverse range of job types and industries. Whether you're seeking full-time roles, freelance work, internships, or specialised positions, <PERSON><PERSON> has opportunities across various fields.",
      },
    ],
  },
  {
    section: 'Sign up',
    subSections: [
      {
        title: 'Account and Profile',
        faqs: [
          {
            question: 'Is there any age requirement to use Lucres?',
            answer: 'Yes, you need to be 18 years old or above to use Lucres.',
          },
          {
            question: 'What details can I display on my profile? Why is it important?',
            answer:
              "Your profile on Lucres functions as your digital resume, so it's crucial to maintain it updated. It encompasses your introduction, experience, education, skills, languages, and achievements. This is the information that recruiters can view, so ensure it highlights everything important.",
          },
          {
            question: "How can I enhance my profile's visibility to recruiters?",
            answer:
              'Keeping your profile updated with your latest experiences, skills, and achievements can significantly improve visibility to recruiters on Lucres.',
          },
          {
            question: 'Is verifying my details mandatory?',
            answer:
              'Yes, verifying your details is mandatory and contributes to maintaining Lucres as a secure platform.',
          },
          {
            question: 'Do I need to pay a membership fee to create an account on Lucres?',
            answer: 'Nope! Lucres and all the features on the platform are completely free to use.',
          },
          {
            question: 'How can I create my account on Lucres?',
            answer:
              "It's easy! Just click on the sign up button and fill in all your details - name, email id, mobile number etc. Once the details are filled in, just click the button to submit.",
          },
        ],
      },
      {
        title: 'How to build your profile',
        faqs: [
          {
            question:
              "I'm lost! How do I navigate through the different tabs and screen on the website?",
            answer:
              "There are 3 sections you need to know of on the website: Board: This is your personalised feed and the first thing you will see by default on your homepage. You can find all your posts and your friend's posts here. Jobs: This is where you can see all the available job postings that you can apply to. You can also see and track any job postings that you have created. Careers: This is where you need to go to create an online resume that recruiters can view once you apply to jobs. Put in everything you need to make the best impression.",
          },
          {
            question: 'What information do I need to fill up in the careers page?',
            answer:
              "This page is your online resume, so it's extremely important that you keep this page upto date and complete. To make sure you have an edge over other candidates, make sure every section is filled up. Introduction: Write a small, introductory paragraph about yourself. Experience: Note down your past work experiences, job roles etc. here. Education: This is where you can put in all of your past work experience. Skills and Interests: Note down all your skills and your expertise level in each one. Competency: This is a drop down menu from where you can can select the top 3 competencies within your role/industry. Languages: The drop down menu allows you to choose the languages you are well versed in. Achievements: This is where you can shine! Feel free to showcase all the achievements from work, college and school.",
          },
        ],
      },
      {
        title: 'Verifying your account',
        faqs: [
          {
            question: 'Is verifying my details mandatory?',
            answer:
              'Yep! Verfiying your account is neccessary and also helps keep Lucres a safe place.',
          },
        ],
      },
      {
        title: 'Building your network',
        faqs: [
          {
            question: 'How can I find my contacts on Lucres?',
            answer:
              'There are two ways to find friends on Lucres: 1. You can find your friends automatically when you login through your Gmail or Facebook account. OR 2. Take advantage of our great referral program and ask your friends to join you on the platform.',
          },
          {
            question: 'How do I delete my account?',
            answer:
              'Your account page has a deactivate button. Simply click on the button to delete your account.',
          },
        ],
      },
    ],
  },
  {
    section: 'Using the Platform',
    subSections: [
      {
        title: 'Posts',
        faqs: [
          {
            question: 'How do I post on my feed?',
            answer: "Use the 'Post' button at the top of the Board to share images and/or text.",
          },
        ],
      },
      {
        title: 'Finding Candidates',
        faqs: [
          {
            question: 'How do I post a job?',
            answer:
              "The top right corner of your screen has a 'Post Job' option at all times. After our team has reviewed and verified all the job details, the job post will be available publicly. Once posted, you can track it in the 'Jobs Posted' section.",
          },
          {
            question: "I've recieved some applications for my job posting. What do I do now?",
            answer:
              "In the 'Jobs Posted' tab in the Jobs section, view how many people have applied and all their details. Once you find suitable candidates, click on the 'Cart' icon to purchase their contact information and get connected to them.",
          },
          {
            question: "What is the 'Repost' feature, and how does it benefit me?",
            answer:
              'Reposting a job on your profile shares the job post with everyone in your network. If any applicants from your network get shortlisted by the recruiter, you receive a reward! You earn 75% of the revenue for every candidate shortlisted. Read More',
          },
        ],
      },
      {
        title: 'Finding jobs',
        faqs: [
          {
            question: 'How do I find suitable job roles?',
            answer:
              "The 'Jobs' tab on the top right panel of your screen will open up the page with all open roles available at the moment. There are filters that you can use to narrow down your search.",
          },
          {
            question: "I've applied. What happens next?",
            answer:
              "Your application is already enroute to the recuiter. If your profile is the right fit, you will be shortlisted and the recruiter will reach out to you. You can track the status of all the jobs you've applied to through th 'Jobs Applied' tab in the Jobs section.",
          },
          {
            question:
              'How can I be sure that the job postings on Lucres are verified or of high quality?',
            answer:
              'Before the job posts become available publicly, our internal team carefully goes through and verifies each one.',
          },
          {
            question: 'Can I customise my job search on Lucres?',
            answer:
              'Yes, Lucres offers personalised job recommendations based on your preferences, ensuring a more tailored and efficient job search experience.',
          },
          {
            question: 'Can I connect with recruiters directly on Lucres?',
            answer:
              'Yes, Lucres facilitates direct connections with recruiters, enabling seamless communication and networking opportunities.',
          },
        ],
      },
    ],
  },
  {
    section: 'Privacy and Security',
    subSections: [
      {
        title: 'Privacy & Security',
        faqs: [
          {
            question: 'Is my information visible to everyone?',
            answer:
              'Only limited information, such as your name, profile picture, cover picture, and shared posts, are publicly visible on your profile. Your contact information is shared with recruiters only if they intend to reach out to you.',
          },
          {
            question: 'Why do you need my address details?',
            answer:
              'We understand your concern. We only collect personal details for billing and taxation reasons.',
          },
          {
            question: 'Is my information secure?',
            answer:
              'Absolutely! We intend to keep Lucres a safe place for all. However do keep in mind that we will share your contact details with recruiters who wish to reach out to you.',
          },
        ],
      },
    ],
  },
  {
    section: 'Payments and Withdrawals',
    subSections: [
      {
        title: 'Payments & Withdrawals',
        faqs: [
          {
            question: 'Where can I see my balance?',
            answer:
              'You can view your wallet balance and all transaction details from the wallet tab of your account details on the extreme right of your page.',
          },
          {
            question: 'How do I withdraw my rewards/amount earned?',
            answer:
              "The withdraw button is accessible on the bottom of the screen in the wallet section. Fill in your account details, and you'll recieve the amount within 24-48 hours.",
          },
          {
            question: 'Where can I view my transaction status?',
            answer:
              "When you click on the 'Wallet' tab (located on the same page as your account details on the extreme right of your page), you can view your wallet balance and transaction details.",
          },
        ],
      },
    ],
  },
  {
    section: 'Feedback and Support',
    subSections: [
      {
        title: 'Feedback & Support',
        faqs: [
          {
            question: 'I want to suggest some features to improve the website, can I?',
            answer:
              'Absolutely! We are constantly trying to make the website better. Please drop us a note at the email id - <EMAIL>',
          },
          {
            question: 'I want to raise a complaint.',
            answer:
              'Oops, please drop us a note at the email id - <EMAIL> and we will look into complaint at the earliest.',
          },
        ],
      },
    ],
  },
  {
    section: 'Repost',
    subSections: [
      {
        title: 'Repost',
        content: `
          <div class="space-y-3 text-sm text-gray-800 dark:text-gray-200">
            <p>The repost section on Lucres.com allows users to earn money by sharing job postings. Here's how it works:</p>
            
            <ul class="list-disc pl-5 space-y-1">
              <li>When you repost a job and a candidate applies through your repost, the recruiter might purchase the candidate's contact information</li>
              <li>The recruiter pays ₹49 for the contact</li>
              <li>You receive 75% of this amount (₹36.75) for each successful referral</li>
              <li>This revenue-sharing incentive applies only when a recruiter pays for a contact</li>
            </ul>
            
            <p>For more details about our repost model, please visit our <a href="/blog/repost-model" class="text-lucres-600 dark:text-lucres-400 hover:underline">blog post</a>.</p>
          </div>
        `,
      },
    ],
  },
  {
    section: 'Pricing and Refund Policy',
    subSections: [
      {
        title: 'Pricing & Refund Policy',
        content: `
          <div class="space-y-3 text-sm text-gray-800 dark:text-gray-200">
            <p>Thank you for choosing Lucres! We aim to provide you with a seamless and rewarding experience on our platform. Please read our refund policy carefully to understand how it applies to your purchases and interactions on Lucres.</p>
            
            <div class="space-y-1">
              <h4 class="font-medium">Pricing:</h4>
              <ul class="list-disc pl-5 space-y-1">
                <li><strong>Email ID Purchase:</strong> ₹8/+GST - Access to candidate's email address</li>
                <li><strong>Full Contact Purchase:</strong> ₹49/+GST - Comprehensive contact details including email, phone, and additional contact information</li>
              </ul>
            </div>
            
            <div class="space-y-1">
              <h4 class="font-medium">General Refund Terms:</h4>
              <p>Lucres strives to provide accurate and valuable candidate contacts. However, we do not guarantee the outcome of any engagement between recruiters and candidates.</p>
              <p>In the event of technical errors or platform issues, Lucres will assess refund requests on a case-by-case basis.</p>
              <p>Once the refund is initiated from Lucres Private Limited, it will be credited to your bank account within 5-7 business working days.</p>
            </div>
            
            <div class="space-y-1">
              <h4 class="font-medium">Contact Us:</h4>
              <p>If you have any questions or concerns about our refund policy, please feel free to contact our customer support team:</p>
              <ul class="list-disc pl-5 space-y-1">
                <li>Phone: <a href="tel:+91**********" class="text-lucres-600 dark:text-lucres-400 hover:underline">+91 **********</a></li>
                <li>Email: <a href="mailto:<EMAIL>" class="text-lucres-600 dark:text-lucres-400 hover:underline"><EMAIL></a></li>
                <li>Address: Lucres Private Limited, #11-14, 2nd floor, Shinde Complex, Neeligin Road, Hubli, Karnataka - 580029</li>
              </ul>
            </div>
            
            <div class="space-y-1">
              <h4 class="font-medium">Changes to this Policy:</h4>
              <p>Lucres reserves the right to modify or amend this refund policy at any time. Users will be notified of any changes through our platform or via the email address associated with their account.</p>
              <p>By using Lucres services, you agree to abide by the terms outlined in this refund policy.</p>
            </div>
            
            <p>Thank you for being a part of the Lucres community!</p>
          </div>
        `,
      },
    ],
  },
  {
    section: 'Others',
    subSections: [
      {
        title: 'Other Questions',
        faqs: [
          {
            question: "I don't live in India, can I still use the platform?",
            answer:
              "Unfortunately, Lucres is only available in India at the moment, but we'll be expanding to more countries super soon.",
          },
          {
            question: "I'd love to partner with you! Where can I reach out?",
            answer:
              'Awesome. We love collaborations and partnerships. Please write to <NAME_EMAIL>.',
          },
        ],
      },
    ],
  },
]

const FaqItem = ({ question, answer }: { question: string; answer: string }) => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div className="border-lucres-200 dark:border-lucres-800 border-b py-3">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex w-full items-start justify-between gap-3 text-left"
        aria-expanded={isOpen}
      >
        <span className="text-lucres-900 dark:text-lucres-100 text-sm font-medium">{question}</span>
        <span className="text-lucres-500 flex-shrink-0">
          {isOpen ? <MinusIcon size={16} /> : <PlusIcon size={16} />}
        </span>
      </button>
      {isOpen && (
        <div className="prose prose-sm mt-2 max-w-none text-sm text-gray-800 dark:text-gray-200">
          {answer}
        </div>
      )}
    </div>
  )
}

export default function FaqPage() {
  const [activeSection, setActiveSection] = useState(0)
  const activeSectionData = faqData[activeSection]

  // Helper function to render HTML content
  const renderHTML = (htmlContent: string) => ({
    __html: htmlContent,
  })

  return (
    <div className="flex min-h-screen flex-col">
      <div className="dark:bg-dark-lucres-black-500 flex-grow bg-white pb-24 pt-24 sm:pb-32 sm:pt-32">
        <div className="mx-auto max-w-5xl px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-lucres-900 mb-2 text-3xl font-bold dark:text-white">
              Frequently Asked Questions
            </h1>
            <p className="text-lucres-700 dark:text-lucres-400 mb-6 text-lg">
              We help employers and candidates find the right fit.
            </p>
          </div>

          <div className="flex flex-col gap-6 md:flex-row">
            {/* Sidebar */}
            <div className="w-full flex-shrink-0 md:w-56">
              <nav className="space-y-1">
                {faqData.map((section, index) => (
                  <button
                    key={section.section}
                    onClick={() => setActiveSection(index)}
                    className={`w-full rounded-md px-3 py-2 text-left text-sm transition-colors ${
                      activeSection === index
                        ? 'bg-lucres-100 dark:bg-lucres-900/30 text-lucres-900 font-medium dark:text-white'
                        : 'text-lucres-700 dark:text-lucres-400 hover:bg-lucres-50 dark:hover:bg-lucres-900/50'
                    }`}
                  >
                    {section.section}
                  </button>
                ))}
              </nav>
            </div>

            {/* Main Content */}
            <div className="flex-1">
              <h2 className="text-lucres-900 mb-4 text-lg font-medium dark:text-white">
                {activeSectionData.section}
              </h2>

              {/* Main Section FAQs */}
              {activeSectionData.faqs && (
                <div className="space-y-1">
                  {activeSectionData.faqs.map((faq, index) => (
                    <FaqItem key={index} question={faq.question} answer={faq.answer} />
                  ))}
                </div>
              )}

              {/* Subsections */}
              {activeSectionData.subSections?.map((subsection, index) => (
                <div key={index} className="mt-6">
                  {activeSectionData.subSections && activeSectionData.subSections.length > 1 && (
                    <h3 className="text-lucres-700 dark:text-lucres-green-300 mb-3 text-base font-medium">
                      {subsection.title}
                    </h3>
                  )}
                  {subsection.content && (
                    <div
                      className="prose prose-sm mb-3 max-w-none text-gray-800 dark:text-gray-200"
                      dangerouslySetInnerHTML={renderHTML(subsection.content)}
                    />
                  )}
                  <div className="space-y-1">
                    {subsection.faqs?.map((faq, faqIndex) => (
                      <FaqItem key={faqIndex} question={faq.question} answer={faq.answer} />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  )
}
