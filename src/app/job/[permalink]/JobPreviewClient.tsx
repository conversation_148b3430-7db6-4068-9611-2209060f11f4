'use client'
import {
  ArrowLeftIcon,
  BriefcaseIcon,
  DotIcon,
  DotsThreeVerticalIcon,
  IdentificationCardIcon,
  MoneyIcon,
  UsersIcon,
  WarningIcon,
} from '@phosphor-icons/react'
import Button from '../../../components/Button'
import Tooltip from '../../../components/Tooltip'
import IconButton from '../../../components/IconButton'
import { JobPreviewDetails } from '../../../models/Job'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { PostService } from '../../../services/PostService'
import Avatar from '../../../components/Avatar/Avatar'
import {
  calculateSalaryRange,
  capitalizeWords,
  convertToTitleCase,
  truncateText,
} from '../../../utils/commonUtils'
import JobPreviewSkeleton from './JobPreviewSkeleton'
import BookmarkIcon from '../../../components/BookmarkIcon'
import { useAuth } from '../../../context/AuthContext'
import { useToast } from '../../../components/ToastX'
import { BookmarkService } from '../../../services/BookmarkService'
import useError from '../../../context/ErrorContext'
import { JobService } from '../../../services/JobService'
import { CareerService } from '../../../services/CareerService'
import { useAccount } from '../../../context/UserDetailsContext'
import Overlay from '../../../components/Overlay'
import SafeHtml from '../../../components/SafeHtml'

interface JobDescriptionProps {
  jobDescription?: string
}

const JobDescription: React.FC<JobDescriptionProps> = ({ jobDescription }) => {
  if (!jobDescription) return <p>No job details available.</p>

  // Split sections based on "**" used for bold text
  const formattedDescription = jobDescription
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold formatting
    .replace(/\n\n/g, '<br/><br/>') // Preserve paragraph breaks

  return (
    <div className="w-full break-words">
      <h2 className="my-2 font-semibold">Job Description</h2>
      {formattedDescription && <SafeHtml html={formattedDescription} />}
    </div>
  )
}

const JobPreviewClient = ({ job: initialJob }: { job: JobPreviewDetails }) => {
  const router = useRouter()
  const { authUserProfile, isAuthenticated } = useAuth()
  const { contactDetails } = useAccount()
  const { handleError } = useError()
  const [job, setJob] = useState<JobPreviewDetails>(initialJob)
  const [applied, setIsApplied] = useState<boolean>(initialJob?.isApplied)
  const [loading, setLoading] = useState<boolean>(false)
  const toast = useToast()
  const [showMissingDetailsModal, setShowMissingDetailsModal] = useState(false)
  const [missingDetails, setMissingDetails] = useState<string[]>([])
  // console.log(initialJob)

  const validateResume = async () => {
    if (!authUserProfile?.permalink) {
      toast.error('Please complete your profile before applying')
      return false
    }

    try {
      const resumeResponse = await CareerService.getCareerByPermalink(authUserProfile.permalink)
      if (resumeResponse.status !== 'success' || !resumeResponse.data) {
        toast.error('Please complete your resume before applying')
        return false
      }

      const resumeData = resumeResponse.data
      const missing = []

      // Check education
      if (!resumeData.educations || resumeData.educations.length === 0) {
        missing.push('education details')
      }

      // Check skills
      if (!resumeData.skills || resumeData.skills.length === 0) {
        missing.push('skills')
      }

      // Check phone number
      if (!contactDetails.primaryPhone) {
        missing.push('phone number')
      }

      if (missing.length > 0) {
        setMissingDetails(missing)
        setShowMissingDetailsModal(true)
        return false
      }

      return true
    } catch (error) {
      console.error('Error validating resume:', error)
      toast.error('Failed to validate resume')
      return false
    }
  }

  const handleApply = async () => {
    if (!job?.id) return
    if (!authUserProfile?.id) return
    const userId = authUserProfile.id

    try {
      const isValid = await validateResume()
      if (!isValid) return

      // If all required details are present, proceed with job application
      const response = await JobService.applyJob(job?.id, userId)

      if (response.data.status === 'success') {
        setIsApplied(true)
        toast.success('Job Applied Successfully')
      } else {
        throw new Error(response.data.message || 'Cannot apply to the job at this moment')
      }
    } catch (error: any) {
      handleError(error)
    }
  }

  const handleBookmarkToggle = async (newBookmarked: boolean) => {
    if (!isAuthenticated) {
      toast.error('Please log in to bookmark.')
      router.push('/login')
      return
    }

    if (!job?.id) return

    try {
      if (newBookmarked) {
        const res = await BookmarkService.addBookmark(job.id, 'JOBS_SRP')
        if (res.status === 'success') {
          toast.success(res.message || 'Bookmark added.')
          setJob((prev) => (prev ? { ...prev, isBookmark: true } : prev))
        } else {
          throw new Error(res.message)
        }
      } else {
        const res = await BookmarkService.deleteBookmark(job?.id, 'JOBS_SRP')
        if (res.status === 'success') {
          toast.success(res.message || 'Bookmark removed.')
          setJob((prev) => (prev ? { ...prev, isBookmark: false } : prev))
        } else {
          throw new Error(res.message)
        }
      }
    } catch (error: any) {
      handleError(error)
      throw error // Re-throw to trigger UI rollback
    }
  }

  return (
    <>
      {loading ? (
        <JobPreviewSkeleton />
      ) : (
        <div className="m-auto mb-10 mt-20 flex w-11/12 max-w-[780px] flex-col items-center justify-center lg:mt-16 lg:w-full lg:py-4">
          <div className="flex w-full cursor-pointer items-center justify-between">
            <div className="flex items-center gap-x-2" onClick={() => router.back()}>
              <IconButton className="dark:bg-dark-lucres-black-500 w-fit bg-white">
                <ArrowLeftIcon
                  size={24}
                  className="text-lucres-900 dark:text-dark-lucres-green-100"
                />
              </IconButton>
              <h3 className="text-lucres-gray-700 hover:text-lucres-600 dark:text-lucres-300 dark:hover:text-lucres-500 font-medium">
                Back
              </h3>
            </div>
            <div className="flex items-center justify-between gap-x-2">
              <Button
                theme={'translucent'}
                size={'small'}
                disabled={applied}
                className="whitespace-nowrap text-xs"
                onClick={handleApply}
              >
                {applied ? 'Applied' : 'Apply'}
              </Button>
              <BookmarkIcon
                onBookmarkToggle={handleBookmarkToggle}
                isBookmarked={job?.isBookmark}
                tooltipText="Save Job"
              />
              <Tooltip text={'More'} classes="whitespace-nowrap text-center" direction="bottom">
                <IconButton>
                  <DotsThreeVerticalIcon
                    size={24}
                    className="text-lucres-900 dark:text-dark-lucres-green-100"
                  />
                </IconButton>
              </Tooltip>
            </div>
          </div>
          <div className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 relative mt-8 flex w-full flex-col justify-center gap-y-4 rounded-xl border border-[#F5F5F5] p-6">
            <div className="flex w-full items-start gap-x-4">
              <Avatar
                src={job?.companyLogo?.url}
                alt={`${job?.companyLogo?.name}'s Avatar`}
                size={10}
                className="cursor-pointer object-cover"
              />
              <div className="flex flex-col">
                <div className="flex flex-col sm:flex-row sm:items-center">
                  <span className="flex items-center text-lg font-bold">
                    {job?.title && convertToTitleCase(job?.title)}
                    <DotIcon weight="bold" size={14} className="hidden sm:block" />
                  </span>
                  <span className="text-lucres-900 dark:text-dark-lucres-green-100 text-base">
                    {job?.company?.name
                      ? convertToTitleCase(job?.company?.name)
                      : job?.company?.name}
                  </span>
                </div>
                <span className="dark:text-dark-lucres-green-100">
                  {' '}
                  {job?.workMode === 'REMOTE'
                    ? 'Remote'
                    : typeof job?.location === 'string'
                      ? truncateText(job.location, 20).text
                      : truncateText(
                          job?.location?.address.state + ', ' + job?.location?.address.country,
                          20,
                        ).text}{' '}
                  {job?.workMode === 'HYBRID' && '(Hybrid)'}
                </span>
              </div>
            </div>
            <div className="text-lucres-900 dark:text-dark-lucres-green-100 mb-2 flex w-full flex-wrap items-start gap-4 ps-1 text-sm font-medium md:flex-row md:items-center md:gap-4 lg:gap-6">
              <div className="flex items-center gap-x-2">
                <UsersIcon size={24} className="text-lucres-900 dark:text-dark-lucres-green-100" />
                <span className="whitespace-nowrap text-sm font-medium">
                  {job?.openings} Openings
                </span>
              </div>
              <div className="flex items-center gap-x-2">
                <BriefcaseIcon
                  size={24}
                  className="text-lucres-900 dark:text-dark-lucres-green-100"
                />
                <span>{`${job?.workExperience.min}-${job?.workExperience.max} Years`}</span>
              </div>
              <div className="flex items-center gap-x-2">
                <MoneyIcon size={24} className="text-lucres-900 dark:text-dark-lucres-green-100" />
                <span>{job?.salary ? calculateSalaryRange(job.salary) : 'Not specified'}</span>
                <span className="text-lucres-gray-700 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-300 dark:text-dark-lucres-green-100 inline-block rounded-[4px] border px-3 py-1 text-xs font-normal dark:border">
                  {capitalizeWords(job?.salary?.period || '')}
                </span>
              </div>
              <div className="flex items-center gap-x-2">
                <IdentificationCardIcon
                  size={24}
                  className="text-lucres-900 dark:text-dark-lucres-green-100"
                />
                <span>
                  {job?.employmentType &&
                    capitalizeWords(
                      job?.employmentType
                        .replace(/[^a-zA-Z0-9]/g, ' ')
                        .replace(/\s+/g, ' ')
                        .trim(),
                    )}
                </span>
              </div>
            </div>
            <div className="flex flex-col gap-y-2">
              <span className="font-semibold">Required Skills</span>
              <div className="flex flex-wrap items-start justify-start gap-2 ps-1">
                {job?.skills.map((skill: any, i: number) => (
                  <p
                    key={i}
                    className="bg-lucres-gray-300 text-lucres-gray-700 hover:text-dark-lucres-green-900 dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 dark:text-dark-lucres-green-100 dark:hover:bg-dark-lucres-black-300 dark:hover:text-dark-lucres-green-400 inline-block cursor-pointer rounded-[4px] px-4 py-1 text-xs font-normal hover:bg-gray-200/75 dark:border"
                  >
                    {convertToTitleCase(skill.name)}
                  </p>
                ))}
              </div>
            </div>
            <JobDescription jobDescription={job?.description} />
          </div>
          <div className="my-16 flex flex-col items-center">
            <span>This Job is posted by</span>
            <Avatar
              src={job?.jobPoster?.profileImage?.url}
              alt={`${job?.companyLogo?.name}'s Avatar`}
              size={12}
              className="mt-4 cursor-pointer object-cover"
            />
            <span
              className="mt-4 cursor-pointer text-lg font-medium hover:underline"
              onClick={() => router.push(`/${job?.jobPoster?.permalink}`)}
            >
              {convertToTitleCase(job?.jobPoster?.givenName || '')}{' '}
              {convertToTitleCase(job?.jobPoster?.familyName || '')}
            </span>
            <span className="w-80 text-center">{job?.jobPoster?.headline}</span>
          </div>
        </div>
      )}

      {showMissingDetailsModal && (
        <Overlay
          heading="Complete Your Resume"
          handleClose={() => setShowMissingDetailsModal(false)}
          size="min-h-fit mt-20"
          classes="p-0!"
        >
          <div className="dark:bg-dark-lucres-black-500 w-full rounded-lg bg-white p-4 shadow-lg">
            <div className="flex flex-col items-center">
              <WarningIcon size={32} className="text-yellow-500" />
              <h2 className="mt-3 text-center text-lg font-semibold">Complete Your Resume</h2>
              <p className="mt-2 text-center text-sm text-gray-500 dark:text-gray-300">
                Please add the following details:
              </p>
              <div>
                {missingDetails.map((detail, index) => (
                  <div key={index} className="capitalize">
                    {detail}
                  </div>
                ))}
              </div>
            </div>
            <div className="dark:border-t-dark-lucres-black-200 mt-6 flex justify-between gap-4 border-t pt-4">
              <Button
                size="small"
                theme="transparent"
                className="px-4! py-1.5! !border"
                onClick={() => setShowMissingDetailsModal(false)}
              >
                Cancel
              </Button>
              <Button
                size="small"
                theme="dark"
                className="px-4! py-1.5! text-white!"
                onClick={() => {
                  setShowMissingDetailsModal(false)
                  router.push('/resume')
                }}
              >
                Go to Resume
              </Button>
            </div>
          </div>
        </Overlay>
      )}
    </>
  )
}

export default JobPreviewClient
