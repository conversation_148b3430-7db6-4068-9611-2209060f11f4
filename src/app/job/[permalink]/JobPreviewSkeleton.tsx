const JobPreviewSkeleton: React.FC = () => {
  return (
    <div className="m-auto flex max-w-[780px] animate-pulse flex-col items-center justify-center md:mt-16 lg:py-4">
      <div className="flex w-full items-center justify-between">
        <div className="flex items-center gap-x-2">
          <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300" />
          <div className="dark:bg-dark-lucres-black-400 h-4 w-28 rounded-sm bg-gray-300" />
        </div>
        <div className="flex items-center gap-x-2">
          <div className="dark:bg-dark-lucres-black-400 h-8 w-20 rounded-lg bg-gray-300" />
          <div className="dark:bg-dark-lucres-black-400 h-8 w-8 rounded-full bg-gray-300" />
          <div className="dark:bg-dark-lucres-black-400 h-8 w-8 rounded-full bg-gray-300" />
        </div>
      </div>

      <div className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 relative mt-8 w-full rounded-xl border border-[#F5F5F5] p-6">
        <div className="flex items-start gap-x-4">
          <div className="dark:bg-dark-lucres-black-400 h-10 w-10 rounded-full bg-gray-300" />
          <div className="flex flex-col gap-y-2">
            <div className="dark:bg-dark-lucres-black-400 h-5 w-40 rounded-sm bg-gray-300" />
            <div className="dark:bg-dark-lucres-black-400 h-4 w-28 rounded-sm bg-gray-300" />
            <div className="dark:bg-dark-lucres-black-400 h-4 w-32 rounded-sm bg-gray-300" />
          </div>
        </div>

        <div className="mb-2 mt-6 flex flex-wrap items-center gap-4 ps-1 text-sm">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="flex items-center gap-x-2">
              <div className="dark:bg-dark-lucres-black-400 h-5 w-5 rounded-full bg-gray-300" />
              <div className="dark:bg-dark-lucres-black-400 h-4 w-24 rounded-sm bg-gray-300" />
            </div>
          ))}
        </div>

        <div className="mt-4 flex flex-col gap-y-2">
          <div className="dark:bg-dark-lucres-black-400 h-4 w-32 rounded-sm bg-gray-300" />
          <div className="flex flex-wrap gap-2 ps-1">
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className="dark:bg-dark-lucres-black-400 h-6 w-20 rounded-sm bg-gray-300"
              />
            ))}
          </div>
        </div>

        <div className="mt-6">
          <div className="dark:bg-dark-lucres-black-400 mb-2 h-4 w-36 rounded-sm bg-gray-300" />
          <div className="space-y-2">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="dark:bg-dark-lucres-black-400 h-4 w-full rounded-sm bg-gray-300"
              />
            ))}
          </div>
        </div>
      </div>

      <div className="mt-8 flex flex-col items-center">
        <div className="dark:bg-dark-lucres-black-400 h-4 w-40 rounded-sm bg-gray-300" />
        <div className="dark:bg-dark-lucres-black-400 mt-4 h-24 w-24 rounded-full bg-gray-300" />
        <div className="dark:bg-dark-lucres-black-400 mt-4 h-5 w-40 rounded-sm bg-gray-300" />
        <div className="dark:bg-dark-lucres-black-400 mt-1 h-4 w-28 rounded-sm bg-gray-300" />
      </div>
    </div>
  )
}

export default JobPreviewSkeleton
