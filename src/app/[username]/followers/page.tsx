'use client'
import React, { useEffect, useState } from 'react'
import { FollowersList, FollowItem, PermalinkUser } from '../../../models/User'
import { UserService } from '../../../services/UserService'
import { useRouter, useSearchParams, usePathname } from 'next/navigation'
import { useAuth } from '../../../context/AuthContext'
import Avatar from '../../../components/Avatar/Avatar'
import { Paginator } from '../../../models/Paginator'
import Pagination from '../../../components/Pagination'
import { capitalizeWords, truncateText } from '../../../utils/commonUtils'
import FollowButton from '../../../components/FollowButton'
import useError from '@/context/ErrorContext'
import NoDataFound from '@/components/NoDataFound/NoDataFound'

const FollowersPage: React.FC = () => {
  const { handleError } = useError()
  const [userFollowersData, setUserFollowersData] = useState<FollowersList | undefined>()
  const [followersPage, setFollowersPage] = useState<number>(1)
  const [followersPaginator, setFollowersPaginator] = useState<Paginator | null>(null)

  const router = useRouter()
  const searchParams = useSearchParams()
  const pathname = usePathname()
  const { authUserProfile: userProfile, refreshUserProfile } = useAuth()

  // Extract username from pathname
  const username = pathname.split('/')[1]
  const isOtherUser = username && username !== userProfile?.username

  const fetchFollowersData = async (page: number = 1) => {
    try {
      const params = new URLSearchParams(searchParams)
      params.set('page', page.toString())
      params.set('limit', '16')
      router.replace(`${pathname}?${params.toString()}`)
      const data = isOtherUser
        ? await UserService.getOtherUsersFollowers(username!, page)
        : await UserService.getUserFollowers(page)
      const { items, paginator: pg } = data?.data
      setUserFollowersData({ data })
      setFollowersPaginator(pg || null)
      setFollowersPage(page)
    } catch (error) {
      handleError(error)
    }
  }

  useEffect(() => {
    const pageParam = searchParams.get('page')
    const page = pageParam ? Number(pageParam) : 1

    if (username) {
      fetchFollowersData(page)
    }
  }, [username, pathname, searchParams])

  const handleFollowerPageChange = (newPage: number) => {
    if (newPage !== followersPage) {
      fetchFollowersData(newPage)
    }
  }

  const handleUnFollowUser = async (id: string, entityType: string) => {
    try {
      const res = await UserService.handleUnFollowUser(id, entityType)
      console.log('HANDLEUNFOLLOWUSER', res)
      if (res.status === 'success') {
        await refreshUserProfile()
      } else {
        throw new Error(res)
      }
    } catch (error) {
      handleError(error)
    }
  }
  //! This is followers page
  const handleFollowUser = async (id: string, entityType: string) => {
    try {
      console.log('PAGEENTITYTYPE', entityType)
      const res = await UserService.handleFollowUser(id, entityType)
      console.log('This is called', res)
      if (res.status === 'success') {
        await refreshUserProfile()
      } else {
        throw new Error(res)
      }
    } catch (error) {
      handleError(error)
    }
  }
  // MODULE: FOLLOW/UNFOLLOW
  return (
    <div className="flex flex-col items-center justify-center">
      <div className="flex w-full flex-wrap gap-x-4 p-4">
        {userFollowersData?.data?.data?.length ? (
          userFollowersData.data?.data?.map((follower: FollowItem) => (
            <div
              key={follower.id}
              className="border-lucres-200 dark:border-dark-lucres-black-300 mb-4 flex basis-[calc(50%-0.5rem)] rounded-lg border p-2 px-4"
            >
              <div className="flex w-full items-center justify-between gap-2">
                <div className="flex w-full gap-2">
                  <Avatar
                    src={follower?.entity?.profileImage?.url}
                    alt={`${follower?.entity?.profileImage?.name}'s Avatar`}
                    size={12}
                    className="min-w-10 cursor-pointer object-cover"
                  />
                  <div className="flex-1">
                    <div className="mb-1 flex w-full items-center justify-between">
                      <div onClick={() => router.push(`/${follower.entity.username}`)}>
                        <div className="cursor-pointer text-sm hover:underline">
                          {capitalizeWords(
                            truncateText(
                              `${follower?.entity?.givenName} ${follower?.entity?.familyName}` ||
                                '',
                              15,
                            ).text,
                          )}
                        </div>

                        <div className="text-lucres-800 text-xs">@{follower?.entity?.username}</div>
                      </div>
                      <FollowButton
                        id={follower.id}
                        size="small"
                        className="px-6! py-2! text-xs"
                        handleFollow={() =>
                          handleFollowUser(follower?.entity.id, follower?.followeeType)
                        }
                        handleUnFollow={() =>
                          handleUnFollowUser(follower?.entity.id, follower?.followeeType)
                        }
                        isInitiallyFollowing={follower?.isFollowing}
                        disabled={follower?.entity?.username === userProfile?.username}
                      >
                        {follower.isFollowing ? 'Following' : 'Follow'}
                      </FollowButton>
                    </div>
                    <div className="text-lucres-800 text-xs">
                      {truncateText(follower?.entity?.headline, 30).text}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="m-auto w-fit">
            <NoDataFound />
          </div>
        )}
      </div>
      {followersPaginator && followersPaginator?.pageCount > 1 && (
        <div className="py-6">
          <Pagination paginator={followersPaginator} onPageChange={handleFollowerPageChange} />
        </div>
      )}
    </div>
  )
}

export default FollowersPage
