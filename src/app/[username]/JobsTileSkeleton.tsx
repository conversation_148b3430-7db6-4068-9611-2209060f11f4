const JobTileSkeleton = () => {
  return (
    <div className="dark:border-dark-lucres-black-200 mb-3 flex animate-pulse flex-col justify-between rounded-lg border px-2 py-4">
      <div className="flex flex-col items-center justify-between gap-2 md:flex-row md:items-start">
        <div className="w-full md:w-auto">
          <div className="mb-2 flex items-center gap-2">
            <div className="dark:bg-dark-lucres-black-200 h-5 w-40 rounded-sm bg-gray-300" />
            <div className="dark:bg-dark-lucres-black-200 h-5 w-5 rounded-sm bg-gray-300" />
          </div>
          <div className="flex items-center gap-2 text-xs">
            <div className="dark:bg-dark-lucres-black-200 h-3 w-28 rounded-sm bg-gray-300" />
            <div className="hidden items-center gap-1 sm:flex">
              <div className="dark:bg-dark-lucres-black-200 h-3 w-3 rounded-full bg-gray-300" />
              <div className="dark:bg-dark-lucres-black-200 h-3 w-28 rounded-sm bg-gray-300" />
            </div>
          </div>
        </div>

        <div className="mt-4 flex items-start gap-2 md:mt-0">
          <div className="dark:bg-dark-lucres-black-200 h-9 w-40 rounded-sm bg-gray-300" />
          <div className="dark:bg-dark-lucres-black-200 h-9 w-24 rounded-full bg-gray-300" />
        </div>
      </div>
    </div>
  )
}

export default JobTileSkeleton
