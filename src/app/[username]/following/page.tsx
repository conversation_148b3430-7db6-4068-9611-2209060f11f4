'use client'
import React, { useEffect, useState } from 'react'
import { FollowingsList, FollowItem, PermalinkUser } from '../../../models/User'
import { UserService } from '../../../services/UserService'
import { useRouter, useSearchParams, usePathname } from 'next/navigation'
import { useAuth } from '../../../context/AuthContext'
import Avatar from '../../../components/Avatar/Avatar'
import { Paginator } from '../../../models/Paginator'
import Pagination from '../../../components/Pagination'
import { capitalizeWords, truncateText } from '../../../utils/commonUtils'
import FollowButton from '../../../components/FollowButton'
import NoDataFound from '@/components/NoDataFound/NoDataFound'
import useError from '@/context/ErrorContext'

const FollowingPage: React.FC = () => {
  const { handleError } = useError()
  const [userFollowingData, setUserFollowingData] = useState<FollowingsList | undefined>()
  const [followingPage, setFollowingPage] = useState<number>(1)
  const [followingPaginator, setFollowingPaginator] = useState<Paginator | null>(null)

  const router = useRouter()
  const searchParams = useSearchParams()
  const pathname = usePathname()
  const { authUserProfile: userProfile, refreshUserProfile } = useAuth()

  // Extract username from pathname
  const username = pathname.split('/')[1]
  const isOtherUser = username && username !== userProfile?.username

  const fetchFollowingData = async (page: number = 1) => {
    try {
      const params = new URLSearchParams(searchParams)
      params.set('page', page.toString())
      params.set('limit', '16')
      router.replace(`${pathname}?${params.toString()}`)
      const response = isOtherUser
        ? await UserService.getOtherUsersFollowing(username!, page)
        : await UserService.getUserFollowing(page)
      const { data, paginator: pg } = response
      console.log('USERSERVICE RESPONSE', { data })
      setUserFollowingData({ data })
      setFollowingPaginator(pg || null)
      setFollowingPage(page)
    } catch (error) {
      console.error('Error fetching following data:', error)
    }
  }

  useEffect(() => {
    const pageParam = searchParams.get('page')
    const page = pageParam ? Number(pageParam) : 1

    if (username) {
      fetchFollowingData(page)
    }
  }, [username, pathname, searchParams])

  const handleFollowingPageChange = (newPage: number) => {
    if (newPage !== followingPage) {
      fetchFollowingData(newPage)
    }
  }

  const handleUnFollowUser = async (id: string, entityType: string) => {
    try {
      const res = await UserService.handleUnFollowUser(id, entityType)
      if (res.status === 'success') {
        await refreshUserProfile()
      } else {
        throw new Error(res)
      }
    } catch (error) {
      handleError(error)
    }
  }
  //! This is following Page
  const handleFollowUser = async (id: string, entityType: string) => {
    try {
      const res = await UserService.handleFollowUser(id, entityType)
      if (res.status === 'success') {
        await refreshUserProfile()
      } else {
        throw new Error(res)
      }
    } catch (error) {
      handleError(error)
    }
  }

  return (
    <div className="flex flex-col items-center justify-center">
      <div className="flex w-full flex-wrap gap-x-4 p-4">
        {userFollowingData?.data?.length ? (
          userFollowingData.data.map((following: FollowItem) => (
            <div
              key={following.id}
              className="border-lucres-200 dark:border-dark-lucres-black-300 mb-4 flex basis-[calc(50%-0.5rem)] rounded-lg border p-2 px-4"
            >
              <div className="flex w-full items-center justify-between gap-2">
                <div className="flex w-full gap-2">
                  <Avatar
                    src={following?.entity?.profileImage?.url}
                    alt={`${following.entity.givenName}'s Avatar`}
                    size={12}
                    className="min-w-10 cursor-pointer object-cover"
                  />
                  <div className="flex-1">
                    <div className="mb-1 flex w-full items-center justify-between">
                      <div onClick={() => router.push(`/${following.entity.username}`)}>
                        <div className="cursor-pointer text-sm hover:underline">
                          {capitalizeWords(
                            truncateText(
                              `${following?.entity?.givenName} ${following?.entity?.familyName}` ||
                                '',
                              15,
                            ).text,
                          )}
                        </div>
                        <div className="text-lucres-800 text-xs">
                          @{following?.entity?.username}
                        </div>
                      </div>
                      {username && username !== userProfile?.username ? (
                        <FollowButton
                          id={following.id}
                          size="small"
                          className="px-6! py-2! text-xs"
                          disabled={following.entity.username === userProfile?.username}
                          isInitiallyFollowing={following.isFollowing}
                          handleFollow={() =>
                            handleFollowUser(following.entity.id, following?.followingType)
                          }
                          handleUnFollow={() =>
                            handleUnFollowUser(following.entity.id, following?.followingType)
                          }
                        >
                          {following.isFollowing ? 'Following' : 'Follow'}
                        </FollowButton>
                      ) : (
                        <FollowButton
                          size="small"
                          id={following.id}
                          className="px-6! py-2! text-xs"
                          isInitiallyFollowing={true}
                          handleFollow={() =>
                            handleFollowUser(following.entity.id, following?.followingType)
                          }
                          handleUnFollow={() =>
                            handleUnFollowUser(following.entity.id, following?.followingType)
                          }
                        >
                          Following
                        </FollowButton>
                      )}
                    </div>

                    <div className="text-lucres-800 text-xs">
                      {truncateText(following?.entity?.headline, 30).text}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="m-auto w-fit">
            <NoDataFound />
          </div>
        )}
      </div>
      {followingPaginator && followingPaginator.pageCount > 1 && (
        <div className="py-6">
          <Pagination paginator={followingPaginator} onPageChange={handleFollowingPageChange} />
        </div>
      )}
    </div>
  )
}

export default FollowingPage
