'use client'
import { useEffect, useState, useMemo } from 'react'
import Radio from '../../components/Radio'
import Button from '../../components/Button'
import { ArrowUpRightIcon, DotIcon, MagnifyingGlassIcon, WarningIcon } from '@phosphor-icons/react'
import Select from '../../components/Select'
import { useRouter, useParams } from 'next/navigation'
import { JobService } from '../../services/JobService'
import useError from '../../context/ErrorContext'
import { useAuth } from '../../context/AuthContext'
import { capitalizeWords, getRelativeTime } from '../../utils/commonUtils'
import JobTileSkeleton from './JobsTileSkeleton'
import NoDataFound from '../../components/NoDataFound/NoDataFound'
import { useToast } from '../../components/ToastX'
import { CareerService } from '../../services/CareerService'
import { useAccount } from '../../context/UserDetailsContext'
import Overlay from '../../components/Overlay'

const JobPosted = () => {
  const router = useRouter()
  const { contactDetails } = useAccount()
  const { authUserProfile } = useAuth()
  const toast = useToast()
  // const { username } = useParams<{ username: string }>()
  const params = useParams<{ username: string }>()
  const username = useMemo(() => params.username, [params])

  const { handleError } = useError()
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [selectedOption, setSelectedOption] = useState<string>('Open')
  const [selectedStates, setSelectedStates] = useState<string>('Open')
  const [cardData, setCardData] = useState<Array<Record<string, any>>>([])
  const [isJobAvailable, setIsJobAvailable] = useState(true)
  const [missingDetails, setMissingDetails] = useState<string[]>([])
  const [showModal, setShowModal] = useState(false)
  const [loading, setLoading] = useState<boolean>(false)
  const states = [
    { value: 'Open', label: 'Open' },
    { value: 'Closed', label: 'Closed' },
  ]

  const isPersonalProfile = username === authUserProfile?.username
  const fetchJobsPosted = async (username: string, status?: 'open' | 'close') => {
    try {
      setLoading(true)
      setIsJobAvailable(true)
      const profileData = await JobService.getGobByUser(username, status)
      setCardData(profileData.data.items)
      if (!profileData.data.items || profileData.data.items.length === 0) {
        setIsJobAvailable(false)
        setCardData([])
      }
    } catch (error: any) {
      // console.error('Error:', error.data.message)
      if (error.data.message === 'No Jobs available with provided details.') {
        setIsJobAvailable(false)
        setCardData([])
      } else handleError(error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (selectedOption === 'Closed' && username) {
      fetchJobsPosted(username, 'close')
    } else if (username) {
      fetchJobsPosted(username)
    }
  }, [username, selectedOption])

  const isJobClosed = (job: any) => {
    // If we're on the Closed tab, all jobs should be considered closed
    if (selectedOption === 'Closed') return true

    // Check various properties that might indicate a job is closed
    return (
      job.isExpired ||
      job.status === 'CLOSED' ||
      job.status === 'closed' ||
      job.jobStatus === 'CLOSED' ||
      job.jobStatus === 'closed'
    )
  }

  const handleRadioChange = (value: string) => {
    setSelectedOption(value)
  }

  const handleStateChange = async (jobId: string, newValue: string) => {
    try {
      const response = await JobService.deleteJob(jobId)
      if (response.status === 'success') {
        // If changing from open to closed and we're on the open tab, remove the job from the list
        if (selectedOption === 'Open' && newValue === 'Closed') {
          setCardData((prev) => prev.filter((job) => job.id !== jobId))
        } else {
          // Otherwise, just update the job status
          setCardData((prev) =>
            prev.map((job) =>
              job.id === jobId ? { ...job, isExpired: newValue !== 'Open' } : job,
            ),
          )
        }
        toast.success(`Job marked as ${newValue}`)
      } else throw response
    } catch (error) {
      handleError(error)
      toast.error('Failed to update job status')
    }
  }

  const validateResume = async () => {
    if (!authUserProfile?.permalink) {
      toast.error('Please complete your profile before applying')
      return false
    }

    try {
      const resumeResponse = await CareerService.getCareerByPermalink(authUserProfile.permalink)
      if (resumeResponse.status !== 'success' || !resumeResponse.data) {
        toast.error('Please complete your resume before applying')
        return false
      }

      const resumeData = resumeResponse.data
      const missing = []

      // Check education
      if (!resumeData.educations || resumeData.educations.length === 0) {
        missing.push('education details')
      }

      // Check skills
      if (!resumeData.skills || resumeData.skills.length === 0) {
        missing.push('skills')
      }

      // Check phone number
      if (!contactDetails.primaryPhone) {
        missing.push('phone number')
      }

      if (missing.length > 0) {
        setMissingDetails(missing)
        setShowModal(true)
        return false
      }

      return true
    } catch (error) {
      console.error('Error validating resume:', error)
      toast.error('Failed to validate resume')
      return false
    }
  }

  const handleApply = async (id: string) => {
    if (!authUserProfile?.id) return
    const userId = authUserProfile.id

    try {
      const isValid = await validateResume()
      if (!isValid) return

      // If all required details are present, proceed with job application
      const response = await JobService.applyJob(id, userId)

      if (response.data.status === 'success') {
        toast.success('Job Applied Successfully')
        // Update the specific job's isApplied status
        setCardData((prev) =>
          prev.map((job) => (job.id === id ? { ...job, isApplied: true } : job)),
        )
      } else {
        throw new Error(response.data.message || 'Cannot apply to the job at this moment')
      }
    } catch (error: any) {
      handleError(error)
    }
  }

  const filteredJobs = cardData.filter((job) => {
    const searchLower = searchQuery.toLowerCase()
    const matchesSearch =
      job.title?.toLowerCase().includes(searchLower) ||
      job.description?.toLowerCase().includes(searchLower)

    // Only apply client-side status filtering if we're not already filtering by API
    // When selectedOption is 'Closed', the API should return only closed jobs
    // When selectedOption is 'Open', the API should return only open jobs
    // So we don't need additional client-side filtering for status
    return matchesSearch
  })

  const handleClick = (data: any) => {
    if (selectedOption === 'Closed') {
      toast.error('This job is closed')
    } else if (data.jobStatus === 'PENDING') {
      toast.error('This job is not approved yet')
    } else if (isPersonalProfile && typeof window !== 'undefined' && window.innerWidth < 1024) {
      // If screen width is less than 1024px and it's a personal profile
      toast.info('Please use a larger screen to view job details')
    } else router.push(isPersonalProfile ? `/applicants/${data.id}` : `/job/${data.permalink}`)
  }

  return (
    <div className="m-4 pb-10">
      <div className="flex w-full flex-wrap justify-between gap-2">
        <div className="flex gap-2">
          {isPersonalProfile && (
            <>
              <Radio
                key="Open"
                name="open"
                value="Open"
                label="Open"
                selected={selectedOption}
                setSelected={(value) => handleRadioChange(value)}
                classname={`rounded-full! ml-0! text-sm! font-medium! ${
                  selectedOption === 'Open' ? '' : 'text-lucres-800!'
                }`}
              />
              <Radio
                key="Closed"
                name="closed"
                value="Closed"
                label="Closed"
                selected={selectedOption}
                setSelected={(value) => handleRadioChange(value)}
                classname={`rounded-full! ml-0! text-sm! font-medium! ${
                  selectedOption === 'Closed' ? '' : 'text-lucres-800!'
                }`}
              />
              {/* <Radio
            key="Draft"
            name="draft"
            value="Draft"
            label="Draft"
            selected={selectedOption}
            setSelected={(value) => handleRadioChange(value)}
            classname={`rounded-full! ml-0! text-sm! font-medium! ${
              selectedOption === 'Draft' ? '' : 'text-lucres-800!'
            }`}
          /> */}
            </>
          )}
        </div>
        <div className="dark:border-dark-lucres-black-200 flex h-10 w-96 min-w-60 max-w-96 items-center rounded-full border px-1 lg:w-72">
          <input
            type="text"
            placeholder="Search Jobs"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="placeholder:text-lucres-800 w-5! outline-hidden grow bg-transparent px-6"
          />

          <Button theme={'transparent'} size={'small'} className="p-1! rounded-full border-none">
            <MagnifyingGlassIcon size={20} weight="bold" />
          </Button>
        </div>
      </div>
      {isJobAvailable ? (
        <div className="mt-5">
          {loading
            ? Array.from({ length: 5 }).map((_, index) => <JobTileSkeleton key={index} />)
            : filteredJobs.map((data, index) => (
                <div
                  key={index}
                  className="hover:bg-lucres-gray-50 dark:border-dark-lucres-black-200 dark:hover:bg-dark-lucres-black-400 mb-3 flex cursor-pointer flex-col justify-between rounded-lg border px-4 py-2"
                  onClick={() => handleClick(data)}
                >
                  <div className="flex flex-col   justify-between gap-2 md:flex-row md:items-start">
                    <div>
                      <div className="flex gap-1 whitespace-nowrap font-medium">
                        {data?.title}
                        {isPersonalProfile && <ArrowUpRightIcon size={24} className="inline" />}
                      </div>
                      <div className="text-lucres-800 dark:text-dark-lucres-black-100 flex items-center text-xs">
                        <span>Posted {getRelativeTime(data?.createdAt)}</span>
                        <DotIcon size={14} weight="bold" className="hidden sm:block" />
                        <span className="flex items-center">
                          {data?.location?.address?.state &&
                            data?.location?.address?.state +
                              ', ' +
                              data?.location?.address?.country}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-center gap-2">
                      {!isPersonalProfile ? (
                        <div>
                          <Button
                            size="small"
                            theme="translucent"
                            className="px-3! py-2! font-normal! min-w-24"
                            disabled={data?.isApplied}
                            onClick={(e) => {
                              e.stopPropagation()
                              handleApply(data?.id)
                            }}
                          >
                            {data?.isApplied ? 'Applied' : 'Apply'}
                          </Button>
                        </div>
                      ) : (
                        <>
                          {selectedOption !== 'Closed' && (
                            <div>
                              <Button
                                size="small"
                                theme="translucent"
                                className="px-3! py-2! font-normal! min-w-40"
                              >
                                {data?.jobStatus === 'PENDING'
                                  ? 'Pending'
                                  : data?.applicationsCount + ' New Applications'}
                              </Button>
                            </div>
                          )}
                          <Select
                            options={states}
                            value={isJobClosed(data) ? 'Closed' : 'Open'}
                            onChange={(value) => handleStateChange(data.id, value)}
                            classes="px-3! py-2! gap-1 font-normal! rounded-full!"
                            disabled={selectedOption === 'Closed'}
                          />
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
        </div>
      ) : (
        <NoDataFound subtitle="No resume data available." />
      )}
      {showModal && (
        <Overlay
          heading={'Complete Your Resume'}
          handleClose={() => setShowModal(false)}
          size="min-h-fit mt-20"
          classes="p-0!"
        >
          <div className="dark:bg-dark-lucres-black-500 w-full rounded-lg bg-white p-4 shadow-lg">
            <div className="flex flex-col items-center">
              <WarningIcon size={32} className="text-yellow-500" />
              <p className={`mt-3 text-center text-sm`}>Please add the following details:</p>
              <div className="mt-1 flex flex-col items-center text-sm">
                {missingDetails.map((detail, index) => (
                  <div key={index} className="capitalize">
                    {detail}
                  </div>
                ))}
              </div>
            </div>
            <div className="dark:border-t-dark-lucres-black-200 mt-6 flex justify-between gap-4 border-t pt-4">
              <Button
                size="small"
                theme="transparent"
                className="px-4! py-1.5! !border"
                onClick={() => setShowModal(false)}
              >
                Cancel
              </Button>
              <Button
                size="small"
                theme="dark"
                className="px-4! py-1.5! text-white!"
                onClick={() => {
                  setShowModal(false)
                  router.push('/resume')
                }}
              >
                Go to Resume
              </Button>
            </div>
          </div>
        </Overlay>
      )}
    </div>
  )
}

export default JobPosted
