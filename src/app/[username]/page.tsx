'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { UserService } from '../../services/UserService'
import { useAuth } from '../../context/AuthContext'
import { PermalinkUser } from '../../models/User'
import useError from '../../context/ErrorContext'
import Resume from '../../components/Resume'

const Profile = () => {
  const { username } = useParams<{ username: string }>()
  const { authUserProfile, isAuthenticated } = useAuth()
  const { handleError } = useError()
  const [userData, setUserData] = useState<PermalinkUser | undefined>()
  const [loading, setLoading] = useState(true)

  const fetchProfileData = async (permalink: string) => {
    if (!isAuthenticated) return
    try {
      setLoading(true)
      const profileData = await UserService.getUserProfileByPermalink(permalink)
      setUserData(profileData.data)
    } catch (error) {
      console.error('Error:', error)
      handleError(error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (username) {
      fetchProfileData(username)
    }
  }, [username])

  if (loading || !userData) {
    return null // You might want to show a loading skeleton here
  }

  return <Resume data={userData} />
}

export default Profile
