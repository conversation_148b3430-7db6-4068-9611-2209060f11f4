import React from 'react'

const ProfileSkeleton = () => {
  return (
    <>
      <div className="w-full animate-pulse md:ms-64">
        <div className="border-lucres-300 dark:border-dark-lucres-black-200 w-full md:border-x lg:w-9/12 lg:max-w-[650px]">
          {/* Banner */}
          <div className="dark:bg-dark-lucres-black-300 aspect-10/3 hidden w-full bg-gray-200 md:block" />

          {/* Tabs */}
          <div className="border-b-lucres-200 dark:border-b-dark-lucres-black-200 flex w-full gap-2 overflow-auto border-b-2 text-center md:px-2">
            {['Resume', 'Posts', 'Jobs Posted'].map((tab) => (
              <div
                key={tab}
                className="w-28 border-b-[3px] border-transparent px-1 py-2 text-sm font-medium text-gray-300 dark:text-gray-600"
              >
                {tab}
              </div>
            ))}{' '}
          </div>

          {/* Resume Section */}
          <div className="space-y-4 p-4">
            {/* Name & Headline */}
            <div className="dark:bg-dark-lucres-black-300 h-6 w-1/2 rounded-sm bg-gray-300" />
            <div className="dark:bg-dark-lucres-black-400 h-4 w-1/3 rounded-sm bg-gray-200" />

            {/* Summary */}
            <div className="mt-4 space-y-2">
              <div className="dark:bg-dark-lucres-black-300 h-4 w-full rounded-sm bg-gray-200" />
              <div className="dark:bg-dark-lucres-black-300 h-4 w-5/6 rounded-sm bg-gray-200" />
              <div className="dark:bg-dark-lucres-black-300 h-4 w-4/6 rounded-sm bg-gray-200" />
            </div>

            {/* Experience Section */}
            <div className="mt-6 space-y-3">
              {[...Array(2)].map((_, i) => (
                <div key={i} className="space-y-2">
                  <div className="dark:bg-dark-lucres-black-400 h-5 w-1/3 rounded-sm bg-gray-300" />
                  <div className="dark:bg-dark-lucres-black-300 h-4 w-3/4 rounded-sm bg-gray-200" />
                  <div className="dark:bg-dark-lucres-black-300 h-4 w-2/3 rounded-sm bg-gray-200" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default ProfileSkeleton
