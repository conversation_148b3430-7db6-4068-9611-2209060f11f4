'use client'
import React, { useEffect, useRef, useState } from 'react'
import { useRouter, useParams, usePathname } from 'next/navigation'
import Button from '../../components/Button'
import { Camera, CameraIcon } from '@phosphor-icons/react'
import { UserService } from '../../services/UserService'
import Sidebar from '../../components/SidebarMobile'
import SidebarN from '../../components/Sidebar'
import Tooltip from '../../components/Tooltip'
import { useAuth } from '../../context/AuthContext'
import { handleCropComplete, handleFileChange } from '../../utils/imageUtils'
import { PermalinkUser } from '../../models/User'
import Toast, { ToastData, ToastProps } from '../../components/Toast'
import ProfileSkeleton from './ProfileSkeleton'
import FollowButton from '../../components/FollowButton'
import ImageCropModal from '../../components/ImageCropModal'
import useError from '../../context/ErrorContext'

interface ProfileLayoutProps {
  children: React.ReactNode
}

const ProfileLayout: React.FC<ProfileLayoutProps> = ({ children }) => {
  const router = useRouter()
  const pathname = usePathname()
  const { handleError } = useError()
  const { username } = useParams<{ username: string }>()
  const [activeTab, setActiveTab] = useState('')
  const [userData, setUserData] = useState<PermalinkUser | undefined>()
  const [isCropModalOpen, setIsCropModalOpen] = useState(false)
  const [loading, setLoading] = useState(true)
  const [imageSrc, setImageSrc] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement | null>(null)
  const { authUserProfile: userProfile, isAuthenticated, refreshUserProfile } = useAuth()
  const [isPersonalProfile, setIsPersonalProfile] = useState<boolean>()
  const [toastPosition, setToastPosition] = useState<ToastProps['position']>('bottom-right')
  const [toasts, setToasts] = useState<ToastData[]>([])

  const handleShowToast = (
    type: 'success' | 'info' | 'error' | 'loading',
    message: string,
    timeout: number = 3000,
  ): number => {
    const id = Date.now()
    const newToast = { id, type, message, timeout }
    setToasts((prev) => [...prev, newToast])

    if (type !== 'loading') {
      setTimeout(() => {
        removeToast(id)
      }, timeout)
    }

    return id
  }

  const removeToast = (id: number) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id))
  }

  useEffect(() => {
    if (pathname.endsWith('/jobs-posted')) {
      setActiveTab('Jobs Posted')
    } else if (pathname.endsWith('/posts')) {
      setActiveTab('Posts')
    } else if (pathname.endsWith('/followers')) {
      setActiveTab('Followers')
    } else if (pathname.endsWith('/following')) {
      setActiveTab('Following')
    } else {
      setActiveTab('Resume')
    }
  }, [pathname])

  const handleBannerClick = () => {
    fileInputRef.current?.click()
  }

  const onFileSelected = (image: string) => {
    setImageSrc(image)
    setIsCropModalOpen(true)
  }

  const onCropSuccess = async () => {
    if (userProfile?.permalink) {
      await fetchProfileData(userProfile.permalink)
    }
    setIsCropModalOpen(false)
  }

  const onCropError = (error: string) => {
    console.error('Error:', error)
  }

  const fetchProfileData = async (permalink: string) => {
    try {
      // setLoading(true);
      const profileData = await UserService.getUserProfileByPermalink(permalink)
      setUserData(profileData.data)
    } catch (error) {
      console.error('Error:', error)
      handleError(error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    const updateToastPosition = () => {
      if (window.innerWidth < 1024) {
        setToastPosition('bottom-center')
      } else {
        setToastPosition('bottom-right')
      }
    }

    updateToastPosition()
    window.addEventListener('resize', updateToastPosition)
    return () => window.removeEventListener('resize', updateToastPosition)
  }, [])

  useEffect(() => {
    if (username) {
      fetchProfileData(username)
    }
  }, [username, userProfile?.permalink])

  useEffect(() => {
    if (userData?.username && userProfile?.username) {
      setIsPersonalProfile(userData.username === userProfile.username)
    }
  }, [userData?.username, userProfile?.username])

  const handleUnFollowUser = async (id: string) => {
    try {
      const entityType = localStorage.getItem("x-active-entity-type");
      const res = await UserService.handleUnFollowUser(id, entityType as string);
      if (res.status === 'success') {
        await refreshUserProfile()
        if (userData?.permalink) {
          fetchProfileData(userData.permalink)
        }
      } else {
        throw new Error(res)
      }
    } catch (error) {
      handleError(error)
      throw error
    }
  }

  const handleFollowUser = async (id: string) => {
    try {
      const entityType = localStorage.getItem("x-active-entity-type");
      const res = await UserService.handleFollowUser(id, entityType as string);
      if (res.status === 'success') {
        await refreshUserProfile()
        if (userData?.permalink) {
          fetchProfileData(userData.permalink)
        }
      } else {
        throw new Error(res)
      }
    } catch (error) {
      throw error
    }
  }

  // if (loading || !userData) {
  //   return <ProfileSkeleton />;
  // }

  return (
    <>
      {isCropModalOpen && imageSrc && (
        <ImageCropModal
          type="cover"
          image={imageSrc}
          onCropComplete={(croppedFile) =>
            handleCropComplete(
              croppedFile,
              UserService.uploadPhoto,
              onCropSuccess,
              onCropError,
              'cover',
              handleShowToast,
              removeToast,
            )
          }
          onClose={() => setIsCropModalOpen(false)}
        />
      )}
      <section className="font-inter flex h-full w-full flex-col items-center justify-center pb-14 pt-12 lg:mt-16 lg:px-10 lg:py-0">
        <Toast toasts={toasts} removeToast={removeToast} position={toastPosition} />
        <div className="w-full md:hidden">
          <Sidebar key={JSON.stringify(userData)} data={userData} />
        </div>

        <div className="mx-auto flex   w-full max-w-[1260px] items-start justify-start">
          <div className="relative hidden lg:block">
            <SidebarN key={JSON.stringify(userData)} data={userData} />
          </div>
          {loading && !userData ? (
            <ProfileSkeleton />
          ) : (
            <div className=" mt-2 flex w-full flex-row items-start gap-x-4 lg:ms-64 lg:mt-0 ">
              <div className="border-lucres-300 dark:border-dark-lucres-black-200 min-h-screen w-full md:border-x lg:max-w-[650px] xl:w-9/12">
                <div
                  className={`${
                    isPersonalProfile && 'group'
                  } aspect-10/3 hidden w-full flex-col justify-between ${
                    pathname.endsWith('/followers') || pathname.endsWith('/following')
                      ? ''
                      : 'md:flex'
                  }`}
                >
                  <div
                    className="mb-2 flex h-full w-full items-start justify-end gap-4 p-6"
                    style={{
                      backgroundImage: `url('${
                        userData?.coverImage?.url || '/dashboard/banner.svg'
                      }')`,
                      backgroundSize: 'cover',
                      backgroundRepeat: 'no-repeat',
                    }}
                  >
                    {isAuthenticated && (
                      <>
                        {isPersonalProfile ? (
                          <>
                            <Tooltip
                              text="Change Banner"
                              direction="bottom"
                              classes="whitespace-nowrap"
                            >
                              <Button
                                theme="white"
                                className="bg-lucres-200 p-2! text-[#666666] opacity-0 duration-700 hover:bg-slate-100 hover:text-[#444444] group-hover:opacity-80"
                                onClick={handleBannerClick}
                              >
                                <CameraIcon size={28} weight="fill" />
                              </Button>
                            </Tooltip>
                          </>
                        ) : (
                          <div className="pointer-events-auto">
                            <FollowButton
                              id={userData?.id ?? ''}
                              size="small"
                              theme="white"
                              className="px-6! py-2!"
                              isInitiallyFollowing={userData?.isFollowing}
                              handleFollow={() => handleFollowUser(userData?.id ?? '')}
                              handleUnFollow={() => handleUnFollowUser(userData?.id ?? '')}
                            >
                              {userData?.isFollowing ? 'Following' : 'Follow'}
                            </FollowButton>
                          </div>
                        )}
                      </>
                    )}

                    <input
                      type="file"
                      ref={fileInputRef}
                      style={{ display: 'none' }}
                      onChange={(e) => handleFileChange(e, onFileSelected, handleShowToast)}
                      accept="image/*"
                    />
                  </div>
                </div>

                <div
                  className={`border-b-lucres-200 dark:border-b-dark-lucres-black-200  flex w-full gap-2 overflow-auto whitespace-nowrap border-b-2 text-center md:px-2 ${
                    pathname.endsWith('/followers') || pathname.endsWith('/following')
                      ? 'hidden'
                      : ''
                  }`}
                >
                  <div
                    className={`w-28 cursor-pointer px-1 py-1 ${
                      activeTab === 'Resume' &&
                      'border-lucres-400! dark:border-lucres-500 font-medium'
                    } border-b-[3px] border-transparent`}
                    onClick={() => router.push(`/${userData?.username}`)}
                  >
                    Resume
                  </div>
                  {isAuthenticated && (
                    <div
                      className={`w-28 cursor-pointer px-1 py-1 ${
                        activeTab === 'Posts' &&
                        'border-lucres-400! dark:border-lucres-500 font-medium'
                      } border-b-[3px] border-transparent`}
                      onClick={() => router.push(`/${userData?.username}/posts`)}
                    >
                      Posts
                    </div>
                  )}
                  <div
                    className={`w-28 cursor-pointer px-1 py-1 ${
                      activeTab === 'Jobs Posted' &&
                      'border-lucres-400! dark:border-lucres-500 font-medium'
                    } border-b-[3px] border-transparent`}
                    onClick={() => router.push(`/${userData?.username}/jobs-posted`)}
                  >
                    Jobs Posted
                  </div>
                </div>

                {/* Follow tabs - only show when on follow pages */}
                {(pathname.endsWith('/followers') || pathname.endsWith('/following')) && (
                  <div className="border-lucres-300 dark:border-dark-lucres-black-200 mx-auto flex w-full items-center justify-start gap-4 border-b pt-6">
                    <span
                      className={`cursor-pointer px-4 pb-2 ${
                        activeTab === 'Followers'
                          ? 'border-lucres-500 border-b-2 font-semibold'
                          : ''
                      }`}
                      onClick={() => router.push(`/${userData?.username}/followers`)}
                    >
                      Followers
                    </span>
                    <span
                      className={`cursor-pointer px-4 pb-2 ${
                        activeTab === 'Following'
                          ? 'border-lucres-500 border-b-2 font-semibold'
                          : ''
                      }`}
                      onClick={() => router.push(`/${userData?.username}/following`)}
                    >
                      Following
                    </span>
                  </div>
                )}

                {/* Render the page content */}
                {children}
              </div>
            </div>
          )}
        </div>
      </section>
    </>
  )
}

export default ProfileLayout
