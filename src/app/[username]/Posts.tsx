import { useCallback, useEffect, useRef, useState } from 'react'
import { PostService } from '../../services/PostService' // adjust the path if needed
import { useAuth } from '../../context/AuthContext'
import PostCard from '../../components/PostCard/PostCard'
import PostCardSkeleton from '../../components/PostCard/PostCardSketeton'
import RepostCard from '../../components/PostCard/RepostCard'
import { useRouter, useParams } from 'next/navigation'
import QuoteCard from '../../components/QuoteCard/QuoteCard'
import JobCard from '../jobs/JobCard'
import NoDataFound from '../../components/NoDataFound/NoDataFound'
import { PostCard as PostCardType } from '../../models/Post'
import { Job } from '../../models/Job'
import useError from '../../context/ErrorContext'
import JobCardWrapper from '@/components/PostCard/JobCardWrapper'

const Posts: React.FC = () => {
  const [boardData, setBoardData] = useState<PostCardType[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [page, setPage] = useState(1)
  const [hasNextPage, setHasNextPage] = useState(false)
  const { authUserProfile: userProfile } = useAuth()
  const router = useRouter()
  const observer = useRef<IntersectionObserver | null>(null)
  const param = useParams()
  const usernameParam = param?.username
  const username = Array.isArray(usernameParam) ? usernameParam[0] : usernameParam

  const { handleError } = useError()
  const fetchBoardData = async (username: string, page: number) => {
    try {
      // setLoading(true)
      if (userProfile?.username && username) {
        const response = await PostService.getBoardData(username, page)
        if (page === 1) {
          setBoardData(response.data.items)
        } else {
          setBoardData((prev) => [...prev, ...response.data.items])
        }
        setHasNextPage(response.data.paginator.hasNextPage)
      }
    } catch (error: any) {
      // console.error('Failed to fetch board data', error?.message)
      handleError(error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (username) {
      setBoardData([])
      setPage(1)
      setHasNextPage(false)
      fetchBoardData(username, 1)
    }
  }, [username])

  useEffect(() => {
    if (page > 1 && username) {
      fetchBoardData(username, page)
    }
  }, [page])

  const lastPostElementRef = useCallback(
    (node: HTMLDivElement) => {
      if (loading) return
      if (observer.current) observer.current.disconnect()
      observer.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasNextPage) {
            setPage((prevPage) => prevPage + 1)
          }
        },
        { threshold: 1.0 },
      )
      if (node) observer.current.observe(node)
    },
    [loading, hasNextPage],
  )

  const handleJobCardClick = (job: Job) => {
    router.push(`/job/${job?.job?.permalink}`)
  }

  return (
    <div className="w-full max-w-[700px] p-4">
      {loading && boardData.length === 0 ? ( // First load
        <div className="flex flex-col gap-4">
          {[...Array(3)].map((_, index) => (
            <PostCardSkeleton key={index} />
          ))}
        </div>
      ) : (
        <div className="flex w-full flex-col gap-4">
          {boardData.length > 0 ? (
            boardData.map((postData) => {
              if (postData.type === 'ARTICLE') {
                return (
                  <PostCard
                    key={postData.id}
                    postData={postData}
                    fetchData={fetchBoardData}
                    setData={setBoardData}
                    isProfilePage={true}
                    data={boardData}
                  />
                )
              }
              if (
                postData.type === 'REPOST' &&
                postData.repost?.repostedItem &&
                postData.repost?.type === 'ARTICLE'
              ) {
                return (
                  <RepostCard
                    key={postData.repost.id}
                    repostData={postData}
                    fetchData={fetchBoardData}
                    setData={setBoardData}
                    isProfilePage={true}
                    data={boardData}
                  />
                )
              }
              if (
                postData.type === 'REPOST' &&
                postData.repost?.repostedItem &&
                postData.repost?.type === 'QUOTE'
              ) {
                return (
                  <RepostCard
                    key={postData.id}
                    repostData={postData}
                    fetchData={fetchBoardData}
                    setData={setBoardData}
                    isProfilePage={true}
                    data={boardData}
                  />
                )
              }
              if (postData.type === 'QUOTE') {
                return (
                  <QuoteCard
                    key={`${postData.id}`}
                    quoteData={postData}
                    setData={setBoardData}
                    data={boardData}
                  />
                )
              }
              if (
                postData.type === 'REPOST' &&
                postData.repost?.repostedItem &&
                postData.repost?.type === 'JOB'
              ) {
                return (
                  <RepostCard
                    key={`${postData.id}`}
                    repostData={postData}
                    fetchData={fetchBoardData}
                    setData={setBoardData}
                    data={boardData}
                  />
                )
              }
              if (postData.type === 'JOB') {
                const job = postData.job
                return (
                  <JobCardWrapper key={postData.id} setData={setBoardData} postData={postData} />
                  // <JobCard
                  //   key={postData.id}
                  //   onClick={handleJobCardClick}
                  //   job={{
                  //     job: {
                  //       ...job,

                  //       name: job.company.name,
                  //       img: job?.user?.companyLogo?.url || '',
                  //       headline: `${job.designation || 'Unknown'}, ${
                  //         job.company?.name || 'Unknown'
                  //       }`,
                  //       location: `${job.location?.address?.city || ''}, ${
                  //         job.location?.address?.country || ''
                  //       }`,
                  //       skills: job.skills?.map((s: string | { name: string }) =>
                  //         typeof s === 'string' ? s : s.name,
                  //       ),
                  //       description:
                  //         job.description.length > 100
                  //           ? job.description.slice(0, 100) + '...'
                  //           : job.description,
                  //     },
                  //     isRePosted: postData.isReposted,
                  //     isApplied: postData.isApplied,
                  //     isLiked: postData.isLiked,
                  //   }}
                  // />
                )
              }

              return null
            })
          ) : (
            <div>
              <NoDataFound />
            </div>
          )}

          {hasNextPage && (
            <div ref={lastPostElementRef} className="flex flex-col items-center justify-center">
              <PostCardSkeleton />
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default Posts
