import Button from '../../components/Button'

const Dashboard = () => {
  // const cards = [
  //   {
  //     title: '1 Active Job',
  //     action: 'ArrowUpRight',
  //     content: {
  //       header: 'Latest Jobs',
  //       job: {
  //         title: 'Senior Product Designer',
  //         postedDaysAgo: '3 Days Ago',
  //       },
  //     },
  //   },
  //   {
  //     title: 'Scheduled Interviews',
  //     action: 'ArrowUpRight',
  //     content: {
  //       header: 'This Week',
  //       interviews: [
  //         {
  //           name: '<PERSON><PERSON><PERSON>',
  //           image: '/hero/boy1.svg',
  //           role: 'Graphic Designer',
  //           dateTime: '24 May 2024, 3:30 PM',
  //         },
  //         {
  //           name: '<PERSON><PERSON>',
  //           image: '/hero/boy1.svg',
  //           role: 'Graphic Designer',
  //           dateTime: '24 May 2024, 3:30 PM',
  //         },
  //       ],
  //     },
  //   },
  //   {
  //     title: '1 New Message',
  //     action: 'ArrowUpRight',
  //     content: {
  //       header: 'Unread Message',
  //       messages: [
  //         {
  //           name: '<PERSON><PERSON><PERSON>',
  //           role: 'Graphic Designer',
  //           messagePreview: 'Hello P<PERSON>h ...',
  //         },
  //       ],
  //     },
  //   },
  //   {
  //     title: 'Team Actions',
  //     action: 'ArrowUpRight',
  //     content: {
  //       header: 'Latest Activities',
  //       activities: [
  //         {
  //           user: 'You',
  //           action: 'scheduled a call with Prakash',
  //           timeAgo: '5 Min Ago',
  //         },
  //         {
  //           user: 'Sameer',
  //           action: 'scheduled a call with Prakash',
  //           timeAgo: '12 Min Ago',
  //         },
  //         {
  //           user: 'Radhika',
  //           action: 'Rejected an Application',
  //           timeAgo: '5 Min Ago',
  //         },
  //         {
  //           user: 'Radhika',
  //           action: 'Posted a job UI/UX Intern',
  //           timeAgo: '5 Min Ago',
  //         },
  //       ],
  //     },
  //   },
  // ]

  return (
    <div className="px-7 pb-10">
      <div className="border-lucres-200 to-lucres-400 dark:border-dark-lucres-black-200 bg-linear-to-r flex rounded-lg border from-transparent px-2 py-6">
        <div className="mr-2">
          <img
            src={'/company/dashboard/CompanyCircle.svg'}
            width={224}
            height={120}
            alt="CompanyEntry"
          />
        </div>
        <div className="flex h-full flex-1 flex-col justify-between gap-2">
          <div className="text-2xl font-semibold">
            <span className="text-lucres-600">Congrats!</span> You have got 4 candidates with exact
            match.
          </div>
          <div className="text-lucres-800 dark:text-dark-lucres-black-100 text-lg">
            Reach out to candidates that meet your job requirements.
          </div>
          <Button className="bg-lucres-900 text-lucres-50! py-3! mt-5 w-40 rounded-lg text-base font-semibold">
            Review Candidates
          </Button>
        </div>
      </div>
      <div className="flex gap-7"></div>
    </div>
  )
}

export default Dashboard
