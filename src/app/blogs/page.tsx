'use client'
import { SparkleIcon } from '@phosphor-icons/react'
import Tag from '../../components/Tag'
import BlogCard from './BlogCard'
import { useState } from 'react'
import ViewBlog from './ViewBlog'

export type BlogCardData = {
  id: number
  image: string
  time: number
  heading: string
  data: string
  genre: string
}

const IconTag = (): React.JSX.Element => {
  return (
    <div className="flex items-center gap-x-2">
      <SparkleIcon size={16} weight="fill" />
      <span>Blogs</span>
    </div>
  )
}

const page = (): React.JSX.Element => {
  const [readmore, setReadMore] = useState(false)
  const [blog, setBlog] = useState<BlogCardData | undefined>()
  const CardData: BlogCardData[] = [
    {
      id: 1,
      image: '/blogs/blogthumbnail/Thumbnail1.png',
      time: 5,
      heading: 'Tips for Building an Impressive Profile on Lucres',
      data: 'Creating a standout profile is crucial for attracting top employers. This guide provides step-by-step tips on highlighting your skills, experience, and unique strengths to make a lasting impression.',
      genre: 'Profile Building',
    },
    {
      id: 2,
      image: '/blogs/blogthumbnail/Thumbnail2.png',
      time: 5,
      heading: 'Maximizing Your Network on Lucres',
      data: 'Networking is a key component of career growth. Explore effective strategies to expand your professional network, engage meaningfully with connections, and turn those relationships into career opportunities.',
      genre: 'Networking',
    },
    {
      id: 3,
      image: '/blogs/blogthumbnail/Thumbnail3.png',
      time: 5,
      heading: 'The Benefits of Sharing Job Posts on Lucres',
      data: 'Sharing job opportunities on Lucres not only helps your network but also rewards you financially. Learn how to share job posts effectively, engage your followers, and maximize your earnings.',
      genre: 'Job Search tips',
    },
    {
      id: 4,
      image: '/blogs/blogthumbnail/Thumbnail4.png',
      time: 5,
      heading: 'How to Navigate Job Search During Uncertain Times',
      data: 'Economic downturns and uncertain times can make job searching challenging. This article offers practical tips and strategies to stay resilient, proactive, and successful in your job hunt, no matter the economic climate.',
      genre: 'Insights',
    },
    {
      id: 5,
      image: '/blogs/blogthumbnail/Thumbnail5.png',
      time: 5,
      heading: 'Chemical Engineering Dropout to Tech Entrepreneur',
      data: "Discover the inspiring journey of Suhas Bonageri, who transitioned from leading his family's pesticide manufacturing company to founding Lucres, a revolutionary job search platform. Learn about the challenges he overcame and the vision that drives Lucres today.",
      genre: 'Featured',
    },
  ]

  const active = 'bg-black dark:bg-dark-lucres-black-200 text-white rounded-md'
  const handleReadMore = (blog: BlogCardData) => {
    setReadMore(true)
    setBlog(blog)
  }
  return (
    <>
      {readmore && blog ? (
        <ViewBlog blog={blog} handleReadMore={handleReadMore} />
      ) : (
        <div className="bg-lucres-100 dark:bg-dark-lucres-black-500 py-24">
          <>
            <Tag TagText={<IconTag />} theme="primary" />
            <div className="m-auto my-5 w-full text-center md:w-9/12 lg:w-7/12 xl:w-1/3">
              <div>
                <h1 className="text-3xl font-medium">Insights, Tips, and Success Stories</h1>
                <p className="dark:text-dark-lucres-green-300 m-auto my-4 px-10 text-lg text-gray-900 text-opacity-70">
                  Stay informed with the latest trends, news, and insights from various industries
                  to keep your knowledge up-to-date.
                </p>
              </div>
            </div>
            <div className="scrollbar-none border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 m-auto mt-10 flex w-11/12 gap-2 overflow-x-scroll whitespace-nowrap rounded-md bg-white p-1 shadow-[0px_4px_10px_0px_rgba(17,17,17,0.05)] lg:w-fit lg:overflow-x-hidden dark:border">
              <div className={`px-[10px] py-[6px] ${active}`}>View all</div>
              <div className="px-[10px] py-[6px]">Profile Building</div>
              <div className="px-[10px] py-[6px]">Networking</div>
              <div className="px-[10px] py-[6px]">Job Searching Tips</div>
              <div className="px-[10px] py-[6px]">Success Stories</div>
              <div className="px-[10px] py-[6px]">Industry Insights</div>
            </div>
          </>
          <div className="xl:w-1154 m-auto mt-24 grid w-11/12 gap-2 md:grid-cols-2 xl:grid-cols-3">
            {CardData.map((data) => (
              <BlogCard cardData={data} key={data.id} handleReadMore={handleReadMore} />
            ))}
          </div>
        </div>
      )}
    </>
  )
}

export default page
