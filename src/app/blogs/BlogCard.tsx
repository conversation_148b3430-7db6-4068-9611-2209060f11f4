import { CaretRightIcon } from '@phosphor-icons/react'
import Button from '../../components/Button'
import Tag from '../../components/Tag'
import { BlogCardData } from './page'

interface BlogProps {
  cardData: BlogCardData
  handleReadMore: (blog: BlogCardData) => void
}

const BlogCard: React.FC<BlogProps> = ({ cardData, handleReadMore }) => {
  const landscape = cardData.id === 1 ? true : false
  return (
    <div
      className={`dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 w-fit overflow-hidden rounded-xl border border-solid border-[rgba(17,17,17,0.1)] bg-white ${
        landscape && 'mb-8 flex flex-col md:col-span-2 md:flex-row xl:col-span-3'
      }`}
    >
      <div className={`w-full`}>
        {/* <Image src={cardData.image} alt='Blog Thumbnail' width={landscape ? 769 : 368} height={407} /> */}
        <img src={cardData.image} alt="Blog Thumbnail" className="h-full w-full" />
      </div>
      <div className={`${landscape ? 'p-6 py-6 md:w-1/2 md:px-5 xl:w-3/12 xl:px-6' : 'p-6'} `}>
        <div>
          <div className="flex w-fit items-center justify-between gap-x-2 whitespace-nowrap text-sm font-medium">
            <Tag TagText={cardData.genre} theme="tertiary" ClassName="py-1.5!" />
            <div>
              <span>{cardData.time} </span> min read
            </div>
          </div>
          <div className="my-5">
            <h1 className="mb-4 text-base font-medium">{cardData.heading}</h1>
            <p className="text-lucres-950 dark:text-dark-lucres-green-200 text-sm">
              {cardData.data}{' '}
            </p>
          </div>
        </div>
        <Button
          theme="transparent"
          size="small"
          className="px-4! mt-4"
          onClick={() => handleReadMore(cardData)}
        >
          Read More
          <CaretRightIcon className="ml-1 h-4 w-4 px-[2px]" />
        </Button>
      </div>
    </div>
  )
}

export default BlogCard
