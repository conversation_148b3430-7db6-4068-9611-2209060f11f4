import {
  ClockIcon,
  FacebookLogoIcon,
  HandWavingIcon,
  HeartIcon,
  InstagramLogoIcon,
  LinkedinLogoIcon,
  PlusCircleIcon,
  UserIcon,
} from '@phosphor-icons/react'
import Tag from '../../components/Tag'
import Input from '../../components/Input'
import BlogCard from './BlogCard'
import { BlogCardData } from './page'

interface BlogProps {
  blog: BlogCardData
  handleReadMore: (blog: BlogCardData) => void
}

const ViewBlog: React.FC<BlogProps> = ({ blog, handleReadMore }) => {
  const CardData: BlogCardData[] = [
    {
      id: 2,
      image: '/blogs/blogthumbnail/Thumbnail2.png',
      time: 5,
      heading: 'Maximizing Your Network on Lucres',
      data: 'Networking is a key component of career growth. Explore effective strategies to expand your professional network, engage meaningfully with connections, and turn those relationships into career opportunities.',
      genre: 'Networking',
    },
    {
      id: 3,
      image: '/blogs/blogthumbnail/Thumbnail3.png',
      time: 5,
      heading: 'The Benefits of Sharing Job Posts on Lucres',
      data: 'Sharing job opportunities on Lucres not only helps your network but also rewards you financially. Learn how to share job posts effectively, engage your followers, and maximize your earnings.',
      genre: 'Job Search tips',
    },
  ]

  return (
    <div className="mt-16 pb-8 lg:mt-0 lg:pb-0 lg:pt-16">
      <div className="bg-lucres-100 dark:bg-dark-lucres-black-500 flex h-40 w-full items-start justify-center pt-4 md:h-60 lg:h-72">
        <div className="flex w-11/12 max-w-7xl flex-col items-start justify-center gap-4 text-sm text-[#1F2937] lg:w-9/12">
          <div className="flex items-center gap-2">
            <span className="cursor-pointer text-[#4B5563]">Home</span>
            <span>/</span>
            <span className="cursor-pointer">Blogs</span>
          </div>
          <img
            src="/blogs/blogthumbnail/Thumbnail6.svg"
            alt=""
            className="h-1/2 w-full object-cover"
          />
        </div>
      </div>
      <div className="mt-10 flex w-full items-center justify-center md:mt-20 xl:mt-60">
        <div className="flex max-w-7xl flex-col items-center justify-center gap-8 p-8 lg:w-7/12">
          <div className="flex w-full flex-col gap-2">
            <Tag TagText={blog.genre} theme="tertiary" ClassName="py-1.5! m-0! " />
            <h1 className="text-2xl font-semibold">{blog.heading}</h1>
            <div className="flex items-center gap-4 text-sm text-[#8F8F8F]">
              <span>By Dharmesh Patel</span>
              <span className="flex items-center gap-1">
                <ClockIcon /> {blog.time} min read
              </span>
            </div>
          </div>
          <div className="dark:text-dark-lucres-green-100 w-full font-medium text-[#1B1819] opacity-75 dark:opacity-100">
            Are you considering a career as a video editor? If your answer is yes, then I can assure
            you that you are on the right part. The life of a video editor is especially rewarding.
            It allows you to use their creativity and technical skills. Whether you love making
            films or telling stories, video editing is a very important skill in today's market,
            especially with the trend of content creation.
            <br /> Join me as we discuss the world of a video editor. we will look at the
            responsibilities of a video editor, the steps needed to become a video editor, and the
            essential techniques you'll need to master.
          </div>

          <div className="flex w-full flex-col gap-2">
            <span className="dark:text-dark-lucres-green-100 font-semibold text-black opacity-75 dark:opacity-100">
              Let's look at the Key responsibilities of a video editor
            </span>
            <ul className="dark:text-dark-lucres-green-100 list-disc ps-6 font-medium text-[#1B1819] opacity-75 dark:opacity-100">
              <li> Reviewing raw footage and selecting the best shots.</li>
              <li>Trimming segments to create seamless transitions.</li>
              <li>Adding music, dialogue, graphics, and special effects.</li>
              <li>Collaborating with directors and producers to achieve the desired outcome.</li>
              <li>Ensuring the final product is polished and meets technical standards.</li>
            </ul>
          </div>
          <div className="bg-lucres-100 dark:bg-dark-lucres-black-300 flex w-full items-start gap-4 rounded-lg p-8 py-4">
            <img src="/blogs/blogthumbnail/light.svg" alt="" className="mt-0.5 w-auto" />
            <p className="dark:text-dark-lucres-green-100 w-full font-medium text-black opacity-75 lg:w-11/12 dark:opacity-100">
              Related How to Become a Product Marketer: A Step-by-Step Guide to Successfully Launch
              Your Product Marketing Career [2024].
            </p>
          </div>
          <div className="flex w-full flex-col gap-2">
            <span className="dark:text-dark-lucres-green-100 font-semibold text-black opacity-75 dark:opacity-100">
              What is video Editing?
            </span>
            <p className="dark:text-dark-lucres-green-100 font-medium text-[#1B1819] opacity-75 dark:opacity-100">
              Video editing allows people to be creative and use technical skills in a meaningful
              way. Whether it is by choice or one's love for making masterpieces or telling stories,
              video editing is an important skill in today's digital world. In this discussion,
              we'll look at the basic steps needed for one to become a video editor.
            </p>
          </div>
          <div className="bg-lucres-100 dark:bg-dark-lucres-black-300 dark:text-dark-lucres-green-100 flex w-full items-start gap-4 rounded-lg p-8 py-4">
            <img src="/blogs/blogthumbnail/light.svg" alt="" className="mt-0.5 w-auto" />
            <p className="dark:text-dark-lucres-green-100 w-full font-medium text-black opacity-75 lg:w-11/12 dark:opacity-100">
              Related How to Become a Product Marketer: A Step-by-Step Guide to Successfully Launch
              Your Product Marketing Career [2024].
            </p>
          </div>

          <div className="dark:border-t-dark-lucres-black-300 flex w-full items-center justify-between border-t pt-8">
            <div className="flex items-center gap-1">
              <HeartIcon size={24} color="#536471" />
              <span className="text-[#536471]">84</span>
            </div>
            <div className="flex items-center gap-2 text-[#8F8F8F]">
              <span>Share</span>
              <span>
                <FacebookLogoIcon size={24} weight="fill" color="#8F8F8F" />
              </span>
              <span>
                <InstagramLogoIcon size={24} color="#8F8F8F" />
              </span>
              <span>
                <LinkedinLogoIcon size={24} weight="fill" color="#8F8F8F" />
              </span>
            </div>
          </div>
          <div className="flex w-full flex-col gap-2">
            <h2 className="dark:text-dark-lucres-green-100 text-xl font-semibold text-[#1B1819] opacity-75 dark:opacity-100">
              Tell us if this Article was helpful?
            </h2>
            <div className="mt-4 flex w-full items-center gap-4">
              <span className="rounded-full bg-gray-300 p-0.5">
                <UserIcon size={24} weight="fill" color="#fff" />
              </span>

              <Input className="py-2!" placeholder="Tell us your thoughts" />
              <div className="flex items-center gap-4">
                <HeartIcon size={24} weight="fill" color="red" />
                <HandWavingIcon size={24} color="orange" weight="fill" />
                <PlusCircleIcon size={24} color="#999999" />
              </div>
            </div>
            <div className="mt-8 flex w-full flex-col gap-4">
              <div className="flex items-start gap-4">
                <img src="/dashboard/pritesh-avatar.svg" alt="" className="mt-1 h-auto w-10" />
                <div className="flex flex-col">
                  <h3 className="dark:text-dark-lucres-green-100 font-semibold text-black opacity-75 dark:opacity-100">
                    Saad Rashid
                  </h3>
                  <span className="text-sm text-[#8F8F8F]">1 month ago</span>
                  <p className="dark:text-dark-lucres-green-100 text-black opacity-75 dark:opacity-100">
                    This article is really insightful. Sharing further.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <img src="/dashboard/pritesh-avatar.svg" alt="" className="mt-1 h-auto w-10" />
                <div className="flex flex-col">
                  <h3 className="dark:text-dark-lucres-green-100 font-semibold text-black opacity-75 dark:opacity-100">
                    Taniya Kashya
                  </h3>
                  <span className="text-sm text-[#8F8F8F]">1 month ago</span>
                  <p className="dark:text-dark-lucres-green-100 text-black opacity-75 dark:opacity-100">
                    Just got placed with this hack. Thanks!
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="bg-lucres-100 dark:bg-dark-lucres-black-300 flex w-full items-center justify-center">
        <div className="border-t-dark-lucres-black-300 flex w-full max-w-7xl flex-col gap-8 p-8 lg:w-7/12 dark:border-t">
          <div className="flex items-start gap-4">
            <img src="/dashboard/pritesh-avatar.svg" alt="" className="mt-1 h-auto w-10" />
            <div className="flex flex-col">
              <h3 className="dark:text-dark-lucres-green-100 font-semibold text-black opacity-75 dark:opacity-100">
                Saad Rashid
              </h3>
              <span className="text-sm text-[#8F8F8F]">1 month ago</span>
              <p className="dark:text-dark-lucres-green-100 text-black opacity-75 dark:opacity-100">
                Designer/writer. Brand, product, systems.
              </p>
            </div>
          </div>
          <div className="flex w-full flex-col gap-8">
            <h3 className="dark:text-dark-lucres-green-100 text-xl font-semibold text-[#1B1819] opacity-75 dark:opacity-100">
              Related Content
            </h3>
            <div className="grid w-full grid-cols-1 gap-4 lg:grid-cols-2">
              {CardData.map((data) => (
                <BlogCard cardData={data} key={data.id} handleReadMore={handleReadMore} />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ViewBlog
