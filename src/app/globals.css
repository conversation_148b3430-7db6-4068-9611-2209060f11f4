@import 'tailwindcss';

@layer base {
  button {
    @apply cursor-pointer;
  }
}

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --width-30: 120px;
  --width-50: 200px;
  --width-84: 336px;
  --width-86: 344px;
  --width-88: 352px;
  --width-90: 360px;
  --width-92: 368px;
  --width-1154: 1154px;

  --color-lucres-50: #f5faef;
  --color-lucres-100: #f2f7f3;
  --color-lucres-200: #eaeceb;
  --color-lucres-300: #bec4bf;
  --color-lucres-400: #b6e777;
  --color-lucres-500: #9bc95f;
  --color-lucres-600: #7ca14c;
  --color-lucres-700: #3f8d51;
  --color-lucres-800: #8f8f8f;
  --color-lucres-900: #2d4232;
  --color-lucres-950: #1b1819;
  --color-lucres-1000: #293b2d;
  --color-lucres-green-100: #f2f7f3;
  --color-lucres-green-200: #b6e777;
  --color-lucres-green-300: #649427;
  --color-lucres-green-400: #2d4232;
  --color-lucres-gray-50: #f8f8f8;
  --color-lucres-gray-100: #f5f5f5;
  --color-lucres-gray-150: #f2f3f5;
  --color-lucres-gray-200: #e8e8e8;
  --color-lucres-gray-300: #eeeeee;
  --color-lucres-gray-400: #c7c7c7;
  --color-lucres-gray-500: #919191;
  --color-lucres-gray-600: #8f8f8f;
  --color-lucres-gray-700: #585858;
  --color-lucres-gray-800: #767676;
  --color-lucres-black: #212121;
  --color-lucres-template: #e8e8e833;
  --color-dark-lucres-green-50: #f5faef;
  --color-dark-lucres-green-100: #f2f7f3;
  --color-dark-lucres-green-200: #eaeceb;
  --color-dark-lucres-green-300: #bec4bf;
  --color-dark-lucres-green-400: #b6e777;
  --color-dark-lucres-green-500: #9bc95f;
  --color-dark-lucres-green-600: #7ca14c;
  --color-dark-lucres-green-700: #3f8d51;
  --color-dark-lucres-green-800: #397f49;
  --color-dark-lucres-green-900: #327141;
  --color-dark-lucres-black-100: #9ca3af;
  --color-dark-lucres-black-200: #40444b;
  --color-dark-lucres-black-300: #2f3136;
  --color-dark-lucres-black-400: #292b2f;
  --color-dark-lucres-black-500: #202225;
  --color-dark-lucres-template: #fefefe;

  --breakpoint-min-1140: 1140px;
  --breakpoint-min-976: 976px;
  --breakpoint-min-676: 676px;

  --background-image-gradient-radial: radial-gradient(var(--tw-gradient-stops));
  --background-image-gradient-conic: conic-gradient(
    from 180deg at 50% 50%,
    var(--tw-gradient-stops)
  );

  --animate-pop: pop 0.3s ease-in-out;
  --animate-shrink: shrink 0.3s ease-in-out;
  --animate-pop-follow: popFollow 0.3s ease-in-out;
  --animate-shrink-follow: shrinkFollow 0.3s ease-in-out;
  --animate-jelly-select: jellySelect 0.7s ease;
  --animate-jelly-unselect: jellyUnselect 0.7s ease;
  --animate-up-voted: upVoted 0.3s ease;
  --animate-down-voted: downVoted 0.3s ease;
  --animate-slide-left: slideLeft 0.5s ease;
  --animate-button-animation: buttonAnimation 1s linear;
  --animate-scroll-infinite: scrollInfinite 10s linear infinite;
  --animate-rotate-envelope: rotateEnvelope 6s linear infinite;
  --animate-rotate-paper: rotatePaper 6s linear infinite;
  --animate-discoverjobs-main: discoverjobsMain 4s linear infinite;
  --animate-discoverjobs-back: discoverjobsBack 4s linear infinite;
  --animate-discoverjobs-small-plus-right: discoverjobsSmallPlusRight 4s linear infinite;
  --animate-discoverjobs-small-plus-left: discoverjobsSmallPlusLeft 4s linear infinite;
  --animate-discoverjobs-big-plus-right: discoverjobsBigPlusRight 4s linear infinite;
  --animate-discoverjobs-big-plus-left: discoverjobsBigPlusLeft 4s linear infinite;
  --animate-expandyournetwork-wrapper-animation: expandyournetworkWrapperAnimation 4s linear
    infinite;
  --animate-expandyournetwork-main-image-zoom: expandyournetworkMainImageZoom 4s linear infinite;
  --animate-expandyournetwork-down-image: expandyournetworkDownImage 4s linear infinite;
  --animate-expandyournetwork-up-image: expandyournetworkUpImage 4s linear infinite;
  --animate-expandyournetwork-first-plus: expandyournetworkFirstPlus 4s linear infinite;
  --animate-expandyournetwork-second-plus: expandyournetworkSecondPlus 4s linear infinite;
  --animate-expandyournetwork-third-plus: expandyournetworkThirdPlus 4s linear infinite;
  --animate-clock-wise: clockWise 50s linear infinite;
  --animate-anti-clock-wise: antiClockWise 50s linear infinite;

  --ease-toast-enter: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-toast-exit: cubic-bezier(0.55, 0.055, 0.675, 0.19);

  --font-inter: var(--font-inter);
  --font-exo: var(--font-exo2);
  --font-dm-sans: var(--font-dm-sans);
  --font-pt-serif: var(--font-pt-serif);
  --font-khand: var(--font-khand);
  --font-grotesk: var(--font-space-grotesk);

  @keyframes pop {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.3);
    }
    100% {
      transform: scale(1);
    }
  }
  @keyframes shrink {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(0.9);
    }
    100% {
      transform: scale(1);
    }
  }
  @keyframes popFollow {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }
  @keyframes shrinkFollow {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(0.9);
    }
    100% {
      transform: scale(1);
    }
  }
  @keyframes jellySelect {
    0%,
    100% {
      transform: scaleX(1) translateX(0);
    }
    25% {
      transform: scaleX(1.1) translateX(5%);
    }
    50% {
      transform: scaleX(0.9) translateX(-2%);
    }
    75% {
      transform: scaleX(1.05) translateX(3%);
    }
  }
  @keyframes jellyUnselect {
    0%,
    100% {
      transform: scaleX(1) translateX(0);
    }
    25% {
      transform: scaleX(0.9) translateX(-2%);
    }
    50% {
      transform: scaleX(1.1) translateX(5%);
    }
    75% {
      transform: scaleX(0.95) translateX(-3%);
    }
  }
  @keyframes upVoted {
    0% {
      transform: translateY(120%);
      opacity: 0%;
    }
    100% {
      transform: translateY(0) opacity(100%);
      opacity: 100%;
    }
  }
  @keyframes downVoted {
    0% {
      transform: translateY(-100%);
      opacity: 0%;
    }
    100% {
      transform: translateY(0) opacity(100%);
      opacity: 100%;
    }
  }
  @keyframes slideLeft {
    0% {
      transform: translateX(200%);
    }
    100% {
      transform: translateX(0);
    }
  }
  @keyframes buttonAnimation {
    from {
      transform: translateX(0) skewX(-45deg);
    }
    to {
      transform: translateX(1000px) skewX(-45deg);
    }
  }
  @keyframes rotateEnvelope {
    10%,
    85% {
      transform: rotate(-2deg);
    }
    20%,
    75% {
      transform: rotate(4deg);
    }
  }
  @keyframes rotatePaper {
    10%,
    85% {
      transform: rotate(0deg) translateY(0px);
    }
    20%,
    75% {
      transform: rotate(4deg) translateY(-20px);
    }
  }
  @keyframes scrollInfinite {
    0%,
    23% {
      transform: translateY(0px);
    }
    33%,
    57% {
      transform: translateY(-103px);
    }
    67%,
    90% {
      transform: translateY(-206px);
    }
    100% {
      transform: translateY(-309px);
    }
  }
  @keyframes expandyournetworkWrapperAnimation {
    10%,
    85% {
      transform: translate(0%, 0px);
    }
    20%,
    75% {
      transform: translate(0%, 0px);
    }
  }
  @keyframes expandyournetworkMainImageZoom {
    10%,
    85% {
      transform: translateY(0px) scale(100%) rotate(0deg);
    }
    20%,
    75% {
      transform: translateY(-15px) scale(130%) rotate(-2deg);
    }
  }
  @keyframes expandyournetworkDownImage {
    10%,
    85% {
      transform: translateY(0px);
    }
    20%,
    75% {
      transform: translateY(-24px);
    }
  }
  @keyframes expandyournetworkUpImage {
    10%,
    85% {
      transform: translateY(0px);
    }
    20%,
    75% {
      transform: translateY(24px);
    }
  }
  @keyframes expandyournetworktopImage {
    0% {
      opacity: 0%;
    }
    20%,
    80% {
      opacity: 40%;
    }
  }
  @keyframes expandyournetworkFirstPlus {
    10%,
    85% {
      transform: translate(0px, -30px);
      opacity: 0;
    }
    20%,
    75% {
      transform: translate(180px, -70px);
      opacity: 1;
    }
  }

  @keyframes expandyournetworkSecondPlus {
    10%,
    85% {
      transform: translate(0px, -40px);
      opacity: 0;
    }
    20%,
    75% {
      transform: translate(-180px, -40px);
      opacity: 1;
    }
  }
  @keyframes expandyournetworkThirdPlus {
    10%,
    85% {
      transform: translatex(-16px);
      opacity: 0;
    }
    20%,
    75% {
      transform: translatex(-156px);
      opacity: 1;
    }
  }
  @keyframes discoverjobsMain {
    10%,
    85% {
      transform: rotate(0) translateY(0);
    }
    20%,
    75% {
      transform: rotate(-3deg) translateY(-10px);
    }
  }
  @keyframes discoverjobsBack {
    10%,
    85% {
      transform: translate(0, 0);
    }
    20%,
    75% {
      transform: translate(20px, 12px);
    }
  }
  @keyframes monetiseinfluenceUp {
    10%,
    85% {
      transform: translate(-50%, 0);
    }
    20%,
    75% {
      transform: translate(-50%, -100px);
    }
  }
  @keyframes monetiseinfluenceGroupDown {
    10%,
    85% {
      transform: translateY(-320px);
    }
    20%,
    75% {
      transform: translateY(-120px);
    }
  }
  @keyframes monetiseinfluenceSingleDown {
    10%,
    85% {
      transform: translateY(-350px);
    }
    20%,
    75% {
      transform: translateY(-220px);
    }
  }
  @keyframes discoverjobsSmallPlusRight {
    10%,
    85% {
      transform: translate(0px, -30px);
      opacity: 0;
    }
    20%,
    75% {
      transform: translate(180px, -70px);
      opacity: 1;
    }
  }
  @keyframes discoverjobsSmallPlusLeft {
    10%,
    85% {
      transform: translate(0px, -40px);
      opacity: 0;
    }
    20%,
    75% {
      transform: translate(-180px, -40px);
      opacity: 1;
    }
  }
  @keyframes discoverjobsBigPlusRight {
    10%,
    85% {
      transform: translate(16px, 0px);
      opacity: 0;
    }
    20%,
    75% {
      transform: translate(156px, -40px);
      opacity: 1;
    }
  }
  @keyframes discoverjobsBigPlusLeft {
    10%,
    85% {
      transform: translatex(-16px);
      opacity: 0;
    }
    20%,
    75% {
      transform: translatex(-156px);
      opacity: 1;
    }
  }
  @keyframes clockWise {
    to {
      transform: rotate(360deg);
    }
  }
  @keyframes antiClockWise {
    to {
      transform: rotate(-360deg);
    }
  }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@utility moveLeftRight {
  animation: moveLeftRight 3s infinite;
}

@utility moveLeftRightWithoutScale {
  animation: moveLeftRightWithoutScale 3s infinite;
}

@utility moveLeftRightGirl1 {
  animation: moveLeftRightGirl1 3s infinite;
}

@utility moveLeftRightGirl2 {
  animation: moveLeftRightGirl2 3s infinite;
}

@utility moveLeftRightboy3 {
  animation: moveLeftRightboy3 3s infinite;
}

@utility moveLeftRightgirl3 {
  animation: moveLeftRightgirl3 3s infinite;
}

@utility moveLeftRightrs1 {
  animation: moveLeftRightrs1 3s infinite;
}

@utility moveLeftRightrs2 {
  animation: moveLeftRightrs2 3s infinite;
}

@utility moveLeftRightrs3 {
  animation: moveLeftRightrs3 3s infinite;
}

@layer utilities {
  *,
  ::after,
  ::before {
    font-family:
      var(--font-inter),
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      Oxygen,
      Ubuntu,
      Cantarell,
      'Open Sans',
      'Helvetica Neue',
      sans-serif;
  }

  .rsw-toolbar {
    background-color: #9bc95f !important;
    color: #ced4da !important;
  }

  /* In your global CSS file */
  .scrollbar-none::-webkit-scrollbar {
    display: none; /* Hide scrollbar for Chrome, Safari and Opera */
  }
  .no-scroll {
    overflow: hidden;
  }

  .scrollbar-none {
    -ms-overflow-style: none; /* Hide scrollbar for Internet Explorer and Edge */
    scrollbar-width: none; /* Hide scrollbar for Firefox */
  }

  body {
    font-family: 'Inter', serif;
    overflow-x: hidden !important;
    /* padding-top: 4rem; */
    /* color: #2d4232; */
    color: #000;
  }

  html.dark body {
    background-color: #202225; /* Dark theme background color */
    color: #f2f7f3; /* Dark theme text color */
  }
  .container {
    position: relative;
    display: inline-block;
  }
  .custom-company-pagination .swiper-company-pagination-bullet {
    width: 8px;
    height: 8px;
    background-color: #fff; /* Default color */
    opacity: 1;
    border-radius: 50%;
    margin-right: 20px;
    transition: all 0.3s ease;
  }

  .custom-company-pagination .swiper-company-pagination-bullet-active {
    width: 8px;
    height: 8px;
    background-color: #649427; /* Active color */
    /* transform: scale(1.2); Enlarge active dot */
  }
  .custom-trending-job-pagination .swiper-trending-job-pagination-bullet {
    width: 6px;
    height: 6px;
    background-color: #d9d9d9; /* Default color */
    opacity: 1;
    border-radius: 50%;
    margin-right: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .custom-trending-job-pagination .swiper-trending-job-pagination-bullet-active {
    width: 6px;
    height: 6px;
    background-color: #649427; /* Active color */
    /* transform: scale(1.2); Enlarge active dot */
  }
  .image {
    border-radius: 10%;
    display: block;
    position: relative;
    z-index: 2;
  }

  .glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(143, 228, 14, 0.7), transparent);
    animation: glowing 2s infinite ease-in-out;
    z-index: 1;
  }

  .perspective-1000 {
    perspective: 1000px;
  }
  @keyframes glowing {
    0% {
      box-shadow:
        0 0 20px rgba(173, 255, 47, 0.7),
        0 0 40px rgba(173, 255, 47, 0.7);
    }
    50% {
      box-shadow:
        0 0 60px rgba(173, 255, 47, 0.9),
        0 0 80px rgba(173, 255, 47, 0.9);
    }
    100% {
      box-shadow:
        0 0 20px rgba(173, 255, 47, 0.7),
        0 0 40px rgba(173, 255, 47, 0.7);
    }
  }

  /* Removing the default appearance */
  .thumb,
  .thumb::-webkit-slider-thumb {
    -webkit-appearance: none;
    -webkit-tap-highlight-color: transparent;
  }

  .thumb {
    pointer-events: none;
    position: absolute;
    height: 0;
    outline: none;
  }

  /* For Chrome browsers */
  .thumb::-webkit-slider-thumb {
    border: 2px solid #9bc95f;
    border-radius: 50%;
    box-shadow: 0 0 1px 1px #ced4da;
    background-color: #fff;
    cursor: pointer;
    height: 10px;
    width: 10px;
    margin-top: 4px;
    pointer-events: all;
    position: relative;
    z-index: 50;
  }

  /* For Firefox browsers */
  .thumb::-moz-range-thumb {
    background-color: #9bc95f;
    border: none;
    border-radius: 50%;
    box-shadow: 0 0 1px 1px #9bc95f;
    cursor: pointer;
    height: 18px;
    width: 18px;
    margin-top: 4px;
    pointer-events: all;
    position: relative;
  }

  .bento4-confetti canvas {
    width: 485px !important;
    height: 310px !important;
  }

  .translate-x-full {
    transform: translateX(100%);
    opacity: 0;
  }
}

@layer utilities {
  @keyframes moveLeftRight {
    0%,
    25%,
    100% {
      transform: scale(1) translateX(0);
    }
    50%,
    75% {
      transform: scale(1.1) translateX(-40px);
    }
  }

  @keyframes moveLeftRightWithoutScale {
    0%,
    25%,
    100% {
      transform: translateX(0);
    }
    50%,
    75% {
      transform: translateX(60px);
    }
  }

  @keyframes moveLeftRightGirl1 {
    0%,
    25%,
    100% {
      transform: translateX(0);
    }
    50%,
    75% {
      transform: translateX(-60px);
    }
  }

  @keyframes moveLeftRightGirl2 {
    0%,
    25%,
    100% {
      transform: translateX(0);
    }
    50%,
    75% {
      transform: translateX(-20px) scale(2.5) translateY(10px);
    }
  }

  @keyframes moveLeftRightboy3 {
    0%,
    25%,
    100% {
      transform: translateX(0);
    }
    50%,
    75% {
      transform: translateX(-40px);
    }
  }

  @keyframes moveLeftRightgirl3 {
    0%,
    25%,
    100% {
      transform: translateX(0);
    }
    50%,
    75% {
      transform: translateX(40px);
    }
  }

  @keyframes moveLeftRightrs1 {
    0%,
    25%,
    100% {
      transform: translateX(0);
    }
    50%,
    75% {
      transform: translateX(60px) translateY(-20px) scale(1.2);
    }
  }

  @keyframes moveLeftRightrs2 {
    0%,
    25%,
    100% {
      transform: translateX(0);
    }
    50%,
    75% {
      transform: translateX(60px) translateY(-80px);
    }
  }

  @keyframes moveLeftRightrs3 {
    0%,
    25%,
    100% {
      opacity: 1;
      transform: translate(0, 0);
    }
    50%,
    75% {
      opacity: 0;
      transform: translate(40px, -60px);
    }
  }
}

.custom-bullet {
  width: 8px;
  height: 8px;
  /* background-color: #000;  */
  border: 1px solid gray;
  border-radius: 50%;
  margin: 0 4px;
  transition:
    transform 0.3s ease,
    background-color 0.3s ease;
}

.custom-bullet-active {
  border: none;
  background-color: #9bc95f;
}

/* custom date picker  */
.rdp-root {
  margin-top: -17px;
  --rdp-dropdown-gap: 1rem !important;
}
.rdp-month {
  --rdp-background-color: green;
  --rdp-text-color: #000;
  --rdp-accent-color: #000;
  --rdp-day-selected-background-color: #000;
  --rdp-day-selected-color: #fff;
  --rdp-day-today-color: #000;
  --rdp-outline-color: #e6e6e6;
  --rdp-selected-border: none;
  width: 265px;
  margin: 0 0.25rem;
  font-size: 0.875rem;
  /* padding: 2px; */
}

.rdp-selected .rdp-day_button {
  background-color: #333;
  color: #fff;
  border-radius: 8px;
}

.rdp-day_button {
  --rdp-day_button-width: 34px;
  --rdp-day_button-height: 34px;
}
.rdp-month_grid {
  margin: 0 -11px;
}
.rdp-today {
  color: #2d4232 !important ;
}
.dark .rdp-today {
  color: white !important ;
}

.rdp-months_dropdown .rdp-years_dropdown {
  top: 100% !important;
  left: 0 !important;
  transform: none !important;
}

.rdp-dropdown:focus-visible ~ .rdp-caption_label {
  outline: none !important;
}
.rdp-chevron {
  gap: 0.5rem;
  white-space: nowrap;
  color: #333;
  width: 20px;
  height: 20px;
  border: 1px solid var(--input);
  border-radius: 0.375rem;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.05);
  fill: black !important;
  transition:
    color 0.2s,
    background-color 0.2s,
    opacity 0.2s;
}

.dark .rdp-chevron {
  fill: white !important;
}
/* Disabled state */
.rdp-chevron:disabled {
  pointer-events: none;
  opacity: 0.5;
}

.dark .rdp-root {
  --rdp-background-color: #181818;
  --rdp-text-color: #fff;
  --rdp-accent-color: #fff;
  --rdp-day-selected-background-color: #fff;
  --rdp-day-selected-color: #000;
  --rdp-day-today-color: #fff;
  --rdp-outline-color: #333;
}
