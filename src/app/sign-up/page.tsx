'use client'
import { useState, useEffect } from 'react'
import SignupLayout from './SignupLayout'
import UserEmail from './UserEmail'
import EmailVerification from './VerifyEmail'
import UserDetails from './UserDetails'

const OTP_EXPIRATION_TIME = 5 * 60

interface SignupFormData {
  email: string
  username?: string
  firstName?: string
  lastName?: string
  otp?: string
  profileImage?: Record<string, any>
  googleAccessToken?: string
}

const SignupFlow: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState<SignupFormData>({
    email: '',
    username: '',
    firstName: '',
    lastName: '',
  })

  const [timeLeft, setTimeLeft] = useState(0)
  const [isExpired, setIsExpired] = useState(false)

  useEffect(() => {
    let timer: NodeJS.Timeout
    if (timeLeft > 0) {
      timer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            clearInterval(timer)
            setIsExpired(true)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => clearInterval(timer)
  }, [timeLeft])

  const handleNext = (data?: Partial<typeof formData>, startTimer?: boolean) => {
    // Update form data
    setFormData((prev) => ({ ...prev, ...data }))

    // Move to next step and start timer if needed
    setCurrentStep((prevStep) => prevStep + 1)
    if (startTimer) {
      setTimeLeft(OTP_EXPIRATION_TIME)
    }
  }

  const restartSignup = () => {
    setCurrentStep(0)
    setFormData({
      email: '',
      username: '',
      firstName: '',
      lastName: '',
      otp: '',
    })
    setTimeLeft(0)
    setIsExpired(false)
  }

  const renderStep = () => {
    if (isExpired) return null
    switch (currentStep) {
      case 0:
        return <UserEmail onNext={handleNext} />
      case 1:
        return <EmailVerification email={formData.email} onNext={handleNext} />
      case 2:
        return formData.otp && <UserDetails email={formData.email} otp={formData.otp} />
      default:
        return null
    }
  }

  return (
    <>
      {currentStep < 3 ? (
        <SignupLayout isExpired={isExpired} restartSignup={restartSignup}>
          {renderStep()}
        </SignupLayout>
      ) : (
        renderStep()
      )}
    </>
  )
}

export default SignupFlow
