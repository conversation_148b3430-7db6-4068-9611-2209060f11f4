import { useState } from 'react'
import Input from '../../components/Input'
import Button from '../../components/Button'
import { OtpService } from '../../services/OtpService'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { AuthService } from '../../services/AuthService'
import { useGoogleLogin } from '@react-oauth/google'
import { useAuth } from '../../context/AuthContext'
import { SignupRequest } from '../../models/AuthModels'

interface UserEmailProps {
  onNext: (
    data: {
      email: string
      firstName?: string
      lastName?: string
      profileImage?: Record<string, any>
      googleAccessToken?: string
    },
    startTimer?: boolean,
    targetStep?: number,
  ) => void
}

// Email validation regex
const EMAIL_REGEX = /^[a-zA-Z0-9][a-zA-Z0-9._%+-]*[a-zA-Z0-9]@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

const UserEmail: React.FC<UserEmailProps> = ({ onNext }) => {
  const [email, setEmail] = useState('')
  const [error, setError] = useState('')
  const { googleSignUp, googleLogin, signUp } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)

  const handleContinue = async () => {
    if (!EMAIL_REGEX.test(email)) {
      setError('Please enter a valid email address.')
      return
    }

    setError('') // Clear any previous errors

    try {
      const validEmail = await AuthService.validateEmail(email)
      if (validEmail.status === 'success') {
        const response = await OtpService.sendOtp(email, 'send', 'signup')
        onNext({ email }, true) // Start timer on OTP generation
      } else {
        setError(validEmail.message || 'Email already exists, please use a different email')
      }
    } catch (error: any) {
      console.error('Email already exists')
      // Check if there's a backend error message
      if (error.message) {
        setError(error.message)
      } else if (error.data?.message) {
        setError(error.data.message)
      } else {
        setError('Email already exists, please use a different email')
      }
    }
  }

  const handleGoogleSignUp = useGoogleLogin({
    onSuccess: async (response) => {
      try {
        setLoading(true)
        const googleResponse = await googleSignUp(response.code)
        if (googleResponse.data.user.username) {
          throw new Error("You're already registered. Try logging in instead.")
        }
        if (googleResponse.status === 'success') {
          // Directly call signup API with Google data
          const localPart = googleResponse.data.user.email?.split('@')[0].toLowerCase() || ''
          const alphanumericLocal = localPart.replace(/[^a-z0-9]/gi, '') //only alpha numeric
          const baseUsername = alphanumericLocal.slice(0, 4) // take first 4
          const randomSuffix = Math.random().toString(36).substring(2, 8) // 6 characters
          const userData: SignupRequest = {
            username: baseUsername + randomSuffix || '',
            givenName: googleResponse.data.user.givenName || '',
            familyName: googleResponse.data.user.familyName || '',
            primaryEmail: googleResponse.data.user.email,
            profileImage: googleResponse.data.user.profileImage || {},
            googleAccessToken: googleResponse.data.googleAccessToken || '',
          }

          const signupResponse = await signUp(userData)
          if (signupResponse.status === 'success') {
            router.push('/ai')
          } else {
            // Check if there's a backend error message
            if (signupResponse.message) {
              throw new Error(signupResponse.message)
            } else {
              throw new Error('Signup failed. Try again.')
            }
          }
        } else {
          throw new Error('Google Sign-in failed. Try again.')
        }
      } catch (error: any) {
        console.error('Google Sign-in Error:', error)
        // Check if there's a backend error message
        if (error.message) {
          setError(error.message)
        } else if (error.data?.message) {
          setError(error.data.message)
        } else {
          setError('Google Sign-in failed')
        }
      } finally {
        setLoading(false)
      }
    },
    onError: (error) => {
      console.error('Google Sign-in flow failed:', error)
      setError('Google Sign-in was cancelled or failed. Please try again.')
    },
    flow: 'auth-code',
  })

  return (
    <div className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 mx-auto my-4 w-full max-w-sm rounded-xl bg-white px-8 py-6 dark:border">
      <h3 className="text-dark-lucres-black-100 mb-6 text-sm font-medium uppercase">sign up</h3>

      <div className="mb-5">
        <Button
          theme="transparent"
          size="medium"
          className="border-lucres-300! w-full rounded-lg"
          // className="flex w-full items-center justify-center rounded-lg border-gray-300! py-3! transition-colors hover:bg-gray-50"
          onClick={handleGoogleSignUp}
          disabled={loading}
        >
          <img src="/signin/Avatar.svg" alt="Google" className="mr-2 h-5 w-5" />
          {loading ? 'Signing Up...' : 'Sign Up'}
        </Button>
      </div>

      <div className="my-7 text-center">
        <p className="text-lucres-800 dark:text-dark-lucres-green-300 leading-4">or</p>
      </div>
      <form>
        <div className="mb-4">
          <Input
            type="email"
            id="email"
            value={email}
            onChange={(e) => {
              setEmail(e.target.value)
              setError('')
            }}
            className={`w-full rounded-lg border px-4 py-3`}
            placeholder="<EMAIL>"
            label="Signup with your Email"
            error={error}
          />
        </div>

        <Button
          disabled={!email || !EMAIL_REGEX.test(email)}
          className="w-full rounded-lg disabled:cursor-not-allowed"
          theme="opaque"
          onClick={handleContinue}
        >
          Continue
        </Button>
      </form>

      <div className="mt-4 text-center">
        <p className="text-lucres-800 dark:text-dark-lucres-green-300 mb-12 text-xs">
          Already using Lucres?{' '}
          <Link
            href="/sign-in"
            className="text-lucres-600 dark:text-dark-lucres-green-400 font-semibold hover:underline"
          >
            Sign In
          </Link>
        </p>
      </div>

      <div className="-mx-5 mb-2 text-center">
        <p className="dark:text-dark-lucres-green-300 text-xs text-gray-500">
          By continuing you agree to our{' '}
          <a href="#" className="text-blue-500 hover:underline">
            privacy policy
          </a>{' '}
          and{' '}
          <a href="#" className="text-blue-500 hover:underline">
            terms of use
          </a>
        </p>
      </div>
    </div>
  )
}

export default UserEmail
