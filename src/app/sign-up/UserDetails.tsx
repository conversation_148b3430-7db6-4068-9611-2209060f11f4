import { useState } from 'react'
import Input from '../../components/Input'
import Button from '../../components/Button'
import { useAuth } from '../../context/AuthContext'
import { useRouter } from 'next/navigation'
import { SignupRequest } from '../../models/AuthModels'
import { AuthService } from '../../services/AuthService'
import { CheckCircleIcon } from '@phosphor-icons/react'

interface UserDetailsProps {
  email: string
  otp: string
}

const UserPersonalDetails: React.FC<UserDetailsProps> = ({ email, otp }) => {
  const { signUp } = useAuth()
  const router = useRouter()

  const [formData, setFormData] = useState({
    username: '',
    firstName: '',
    lastName: '',
    password: '',
  })

  const [fieldErrors, setFieldErrors] = useState<{
    username?: string
    firstName?: string
    lastName?: string
    password?: string
  }>({})

  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [checking, setChecking] = useState<boolean>(false)
  const [validUsername, setValidUsername] = useState<boolean>(false)
  const [usernameError, setUsernameError] = useState<string | null>(null)
  const [isFocused, setIsFocused] = useState<boolean>()

  const validateUsername = async (username: string) => {
    setChecking(true)
    setUsernameError('')
    try {
      const validUsername = await AuthService.validateUsername(username)
      if (validUsername.status === 'success') {
        setValidUsername(true)
      } else {
        // Check if there's a backend error message
        if (validUsername.message) {
          setUsernameError(validUsername.message)
          setFieldErrors((prev) => ({
            ...prev,
            username: validUsername.message,
          }))
        } else {
          setUsernameError('Invalid Username')
          setFieldErrors((prev) => ({
            ...prev,
            username: 'Invalid Username',
          }))
        }
      }
    } catch (error: any) {
      console.error('Username is already taken')
      // Check if there's a backend error message
      if (error.message) {
        setUsernameError(error.message)
        setFieldErrors((prev) => ({
          ...prev,
          username: error.message,
        }))
      } else if (error.data?.message) {
        setUsernameError(error.data.message)
        setFieldErrors((prev) => ({
          ...prev,
          username: error.data.message,
        }))
      } else {
        setUsernameError('Username is already taken')
        setFieldErrors((prev) => ({
          ...prev,
          username: 'Username is already taken',
        }))
      }
    } finally {
      setChecking(false)
    }
  }

  const validateField = (field: string, value: string) => {
    const usernameRegex = /^[a-zA-Z]{3,20}$/
    const nameRegex = /^[a-zA-Z]{3,20}$/
    const passwordRegex = /^[^"'\\;#*]+$/

    let error = ''
    switch (field) {
      case 'username':
        error = !usernameRegex.test(value)
          ? 'Username must be 3-20 letters and contain only alphabets.'
          : ''

        if (error === '') validateUsername(value)
        break
      case 'firstName':
        error = !nameRegex.test(value)
          ? 'First name must be 3-20 letters and contain only alphabets.'
          : ''
        break
      case 'lastName':
        error = !nameRegex.test(value)
          ? 'Last name must be 3-20 letters and contain only alphabets.'
          : ''
        break
      case 'password':
        if (value.length < 1) {
          return 'Password is required'
        }
        error = !passwordRegex.test(value)
          ? 'Special characters like \', ", \\, ;, #, and * are not allowed.'
          : ''
        break
      default:
        error = ''
    }

    setFieldErrors((prev) => ({ ...prev, [field]: error }))
    return error
  }

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    setFieldErrors((prev) => ({ ...prev, [field]: '' })) // Clear error when user types
    // Clear general error when user starts typing
    if (error) {
      setError('')
    }
  }

  const handleContinue = async (e: React.FormEvent) => {
    e.preventDefault() // Prevent form submission and page reload
    setError('') // Clear general error before validation

    let hasError = false
    const newErrors: typeof fieldErrors = {}

    // Validate all fields
    Object.keys(formData).forEach((field) => {
      const error = validateField(field, formData[field as keyof typeof formData])
      if (error) {
        hasError = true
        newErrors[field as keyof typeof fieldErrors] = error
      }
    })

    if (hasError) {
      setFieldErrors(newErrors)
      return
    }

    setLoading(true)

    try {
      const userData: SignupRequest = {
        username: formData.username.toLowerCase(),
        givenName: formData.firstName.trim(),
        familyName: formData.lastName.trim(),
        password: formData.password,
        primaryEmail: email,
        verificationCode: otp,
      }

      const response = await signUp(userData)

      if (response.status === 'success') {
        router.push('/ai')
      } else {
        // Check if there's a backend error message
        if (response.message) {
          setError(response.message)
        } else {
          setError('Signup failed. Try again.')
        }
      }
    } catch (error: any) {
      // Check if there's a backend error message
      if (error.message) {
        setError(error.message)
      } else if (error.data?.message) {
        setError(error.data.message)
      } else {
        setError('Signup failed. Please check your details and try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 mx-auto mt-3 min-h-fit w-full max-w-sm rounded-xl bg-white px-5 py-7 dark:border">
      <h3 className="text-dark-lucres-black-100 text-sm font-medium uppercase">Sign up</h3>
      <h2 className="mb-3 text-2xl font-medium">We are getting one step closer!</h2>

      <form onSubmit={handleContinue}>
        <div className="mb-6">
          <Input
            type="text"
            id="username"
            value={formData.username}
            onChange={(e) => handleChange('username', e.target.value)}
            onBlur={() => {
              setIsFocused(false)
              validateField('username', formData.username)
            }}
            placeholder="Enter username"
            label="Username *"
            onFocus={() => setIsFocused(true)}
            error={fieldErrors.username || usernameError || ''}
            icon={
              !isFocused &&
              ((checking && (
                <span
                  className={`text-lucres-600 dark:text-lucres-400 inline-block h-2 w-2 animate-spin rounded-full border-2 border-current border-t-transparent p-2`}
                ></span>
              )) ||
                (validUsername && !usernameError && !fieldErrors.username && (
                  <CheckCircleIcon size={24} className="text-lucres-600 dark:text-lucres-400" />
                )))
            }
          />
        </div>

        <div className="flex flex-col gap-4">
          <div className="flex items-start justify-between gap-x-3 lg:w-full">
            <div className="flex-1">
              <Input
                type="text"
                id="firstName"
                placeholder="John"
                value={formData.firstName}
                onChange={(e) => handleChange('firstName', e.target.value)}
                onBlur={() => validateField('firstName', formData.firstName.trim())}
                label="First Name *"
                error={fieldErrors.firstName}
              />
            </div>

            <div className="flex-1">
              <Input
                type="text"
                id="lastName"
                placeholder="Doe"
                value={formData.lastName}
                onChange={(e) => handleChange('lastName', e.target.value)}
                onBlur={() => validateField('lastName', formData.lastName.trim())}
                label="Last Name *"
                error={fieldErrors.lastName}
              />
            </div>
          </div>

          <Input
            type="password"
            id="password"
            value={formData.password}
            onChange={(e) => handleChange('password', e.target.value)}
            onBlur={() => validateField('password', formData.password)}
            placeholder="Enter password"
            label="Password *"
            maxLength={20}
            error={fieldErrors.password}
          />
        </div>

        {error && <p className="mt-1 text-sm text-red-500">{error}</p>}

        <Button type="submit" disabled={loading} className="mt-4 w-full px-6 py-3">
          {loading ? 'Signing Up...' : 'Save & Continue'}
        </Button>
      </form>
    </div>
  )
}

export default UserPersonalDetails
