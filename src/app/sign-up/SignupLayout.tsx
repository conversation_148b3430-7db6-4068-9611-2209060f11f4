import { CheckIcon } from '@phosphor-icons/react'
import FormHeader from '../../components/FormHeader'
import Button from '../../components/Button'

interface SignupLayoutProps {
  children: React.ReactNode
  isExpired: boolean
  restartSignup: () => void
}

const SignupLayout: React.FC<SignupLayoutProps> = ({ children, isExpired, restartSignup }) => {
  return (
    <>
      {isExpired && (
        // <Modal>
        <div className="z-50! absolute left-0 top-0 h-full w-full bg-black bg-opacity-75">
          <div className="flex h-full w-full items-center justify-center">
            <div className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 flex h-1/3 w-11/12 flex-col items-center justify-center gap-4 rounded-md bg-white p-4 text-center md:w-8/12 lg:w-5/12 dark:border">
              <h3 className="text-xl font-semibold">Session Expired</h3>
              <p className="text-lucres-gray-500">
                Your session expired, please restart the signup process.
              </p>
              <div className="mt-2 flex items-center gap-4">
                <Button
                  size="small"
                  theme="dark"
                  isRectangle
                  onClick={restartSignup}
                  className="bg-lucres-black dark:bg-dark-lucres-green-400 dark:text-lucres-black text-white"
                >
                  Restart
                </Button>
              </div>
            </div>
          </div>
        </div>
        // </Modal>
      )}
      <header className="bg-lucres-900 dark:bg-dark-lucres-black-500 fixed left-0 top-0 z-10 hidden w-full xl:block">
        <div className="mx-auto flex max-w-7xl items-center justify-between px-4 pt-4">
          <FormHeader />
        </div>
      </header>
      <div className="bg-lucres-900 dark:bg-dark-lucres-black-500 relative flex h-screen w-full items-center justify-center py-3">
        <div className="flex h-full w-full max-w-7xl flex-1 items-center gap-x-10 p-4 lg:items-start lg:ps-16 lg:pt-16">
          {/* Children Props of  Signup Flow  */}
          <div className="mx-auto w-full max-w-md flex-1">{children}</div>

          {/* Right Content */}
          {/* <div className="hidden flex-col justify-start gap-y-4 px-1 pt-8 text-left text-white lg:flex">
            <h1 className="w-80 text-4xl leading-[45px] xl:w-96">
              We are Making Job Hunting as{" "}
              <span className="font-semibold tracking-tighter text-lime-300">
                Simple as Tweeting 
              </span>
            </h1>
            <ul className="flex flex-col gap-y-4 ps-2 text-base">
              <li className="flex items-center justify-start gap-x-1">
                <CheckIcon size={24} /> Join 15000+ users today
              </li>
              <li className="flex items-center justify-start gap-x-1">
                <CheckIcon size={24} />
                Build AI Resume for free
              </li>
              <li className="flex items-center justify-start gap-x-1">
                <CheckIcon size={24} /> Post Unlimited Jobs for free
              </li>
            </ul>
          </div> */}
          <div className="hidden flex-col justify-start gap-y-4 px-1 pt-8 text-left text-white lg:flex">
            <h1 className="w-80 text-4xl leading-[45px] xl:w-96">
              Making Job Hunting as{' '}
              <span className="font-medium tracking-tighter text-lime-300">Common as Tweet</span>
            </h1>
            <ul className="flex flex-col gap-y-4 ps-2 text-base">
              <li className="flex items-center justify-start gap-x-1">
                <CheckIcon size={24} /> Join the fastest growing networking website today
              </li>
              <li className="flex items-center justify-start gap-x-1">
                <CheckIcon size={24} /> Build AI Resume for free
              </li>
              <li className="flex items-center justify-start gap-x-1">
                <CheckIcon size={24} /> Post Unlimited Jobs for free
              </li>
            </ul>
          </div>
        </div>
        <div className="relative hidden h-full w-96 xl:block">
          {/* Blur Circle Behind the Image */}
          <span className="dark:bg-dark-lucres-black-100 fixed bottom-[30%] right-[3%] z-0 h-96 w-96 overflow-hidden rounded-full bg-lime-400 bg-opacity-30 opacity-30 blur-2xl dark:blur-3xl"></span>

          {/* Image Layer on Top */}
          <img
            src="/signin/randomimg1.svg"
            alt="SignIn User Image"
            className="fixed bottom-0 right-6 z-10 w-96"
          />
        </div>
      </div>
    </>
  )
}

export default SignupLayout
