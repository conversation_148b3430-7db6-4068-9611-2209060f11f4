import { useState } from 'react'
import InputOtp from '../../components/InputOtp'
import Button from '../../components/Button'
import { OtpService } from '../../services/OtpService'

interface EmailVerificationProps {
  email: string
  onNext: (data: { otp: string }) => void
}

const maskEmail = (email: string): string => {
  const [username, domain] = email.split('@')
  const maskedUsername = username.slice(0, 3) + '****'
  return `${maskedUsername}@${domain}`
}

const EmailVerification: React.FC<EmailVerificationProps> = ({ email, onNext }) => {
  const [otp, setOtp] = useState<string>('')
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  const handleOtpVerification = async () => {
    if (otp.length !== 6) return

    setLoading(true)
    setError(null)

    try {
      const response = await OtpService.verifyOtp(email, otp, 'signup')

      if (response.status === 'success') {
        onNext({ otp }) // Move to the next step only if status is 200
      } else {
        setError('Invalid OTP. Please try again.')
      }
    } catch (error: any) {
      // Check if there's a backend error message
      if (error.message) {
        setError(error.message)
      } else if (error.data?.message) {
        setError(error.data.message)
      } else {
        setError('Invalid OTP. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  const handleResendOtp = async () => {
    try {
      const response = await OtpService.sendOtp(email, 'resend', 'signup')
      if (response.status !== 'success') {
        setError('Failed to resend otp')
      }
    } catch (error: any) {
      console.error('Failed to send OTP')
      // Check if there's a backend error message
      if (error.message) {
        setError(error.message)
      } else if (error.data?.message) {
        setError(error.data.message)
      } else {
        setError('Failed to resend OTP. Please try again.')
      }
    }
  }

  const handleOtpChange = (otpValue: string) => {
    setOtp(otpValue)
    // Clear error when user starts typing
    if (error) {
      setError(null)
    }
  }

  return (
    <div className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 mx-auto mt-3 min-h-fit w-full max-w-sm rounded-xl bg-white px-6 py-8 pb-40 dark:border">
      <div>
        <h3 className="text-dark-lucres-black-100 mb-2 text-sm font-medium uppercase">Sign Up</h3>

        <h1 className="mb-1 text-xl font-semibold">Email ID Verification</h1>
        <p className="dark:text-dark-lucres-green-300 pb-6 text-sm text-gray-500">
          Enter the 6-digit verification code sent to your email ID{' '}
          <span className="font-medium">{maskEmail(email)}</span>
        </p>
      </div>
      <form>
        <div className="text-dark-lucres-black-400 my-6">
          <InputOtp
            length={6}
            onSubmit={handleOtpChange}
            resendButtonText="Didn't receive code? Resend"
            onResendOtp={handleResendOtp}
            inputClassName="h-12! w-12!"
          />
        </div>

        {error && <p className="mb-2 text-sm text-red-500">{error}</p>}

        <Button
          disabled={otp.length !== 6 || loading}
          className="disabled:bg-lucres-800 w-full rounded-lg disabled:cursor-not-allowed"
          onClick={handleOtpVerification}
        >
          {loading ? 'Verifying...' : 'Continue'}
        </Button>
      </form>
    </div>
  )
}

export default EmailVerification
