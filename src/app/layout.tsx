import type { Metadata } from 'next'
import './globals.css'
import { ThemeProvider } from '../context/ThemeProvider'
import NavigationWrapper from '../components/NavigationWrapper'
import Footer from '../components/Footer'
import { ErrorProvider } from '../context/ErrorContext'
import { ToastProvider } from '../components/ToastX'
// import { BoardProvider } from '../context/BoardContext';
// import { JobProvider } from '../context/JobProvider';
// import { TalentProvider } from '../context/TalentProvider';
// import { GoogleOAuthProvider } from '@react-oauth/google';
import { AuthProvider } from '../context/AuthContext'
import { AccountProvider } from '../context/UserDetailsContext'
import { ColorProvider } from '../context/ColorContext'
import RouteProtection from '../components/RouteProtection'
import { googleOauth } from '../config/defaultEnv'
import { inter, exo2, dmSans, ptSerif, khand, spaceGrotesk } from './fonts'
import { GoogleOAuthProvider } from '@react-oauth/google'
import Script from 'next/script'

export const metadata: Metadata = {
  title: 'Lucres | Making Jobs as common as a tweet',
  description: 'Job and talent platform',
  icons: {
    icon: [{ url: '/favicon.svg', sizes: '512x512' }],
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html
      lang="en"
      className={` ${inter.variable}  ${exo2.variable} ${dmSans.variable} ${ptSerif.variable} ${khand.variable} ${spaceGrotesk.variable}`}
    >
      <body>
        <Script
          src="https://maps.googleapis.com/maps/api/js?key=AIzaSyD0lhuMByLNeBhx5pPajCrXYSTqVHQcEnA&libraries=places"
          strategy="afterInteractive"
        />
        <ThemeProvider>
          <ToastProvider>
            <ErrorProvider>
              {/* <BoardProvider> */}
              {/* <JobProvider> */}
              {/* <TalentProvider> */}
              <GoogleOAuthProvider clientId={googleOauth.web.client_id}>
                <AuthProvider>
                  <AccountProvider>
                    <ColorProvider>
                      {/* <RouteProtection> */}
                      <NavigationWrapper />
                      {children}
                      {/* </RouteProtection> */}
                    </ColorProvider>
                  </AccountProvider>
                </AuthProvider>
              </GoogleOAuthProvider>
              {/* </TalentProvider> */}
              {/* </JobProvider> */}
              {/* </BoardProvider> */}
            </ErrorProvider>
          </ToastProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
