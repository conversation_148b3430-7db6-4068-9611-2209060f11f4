import { MouseEvent, ReactElement, useEffect, useState } from 'react'
import { BriefcaseIcon, DotIcon, MapPinIcon } from '@phosphor-icons/react'
import { useRouter } from 'next/navigation'
import Button from '../../components/Button'
import Tooltip from '../../components/Tooltip'
import IconButton from '../../components/IconButton'
import { FollowingsList, TalentUser } from '../../models/User'
import { convertToTitleCase, truncateText } from '../../utils/commonUtils'
import Avatar from '../../components/Avatar/Avatar'
import { UserService } from '../../services/UserService'
import FollowButton from '../../components/FollowButton'
import { useToast } from '../../components/ToastX'
import CopyLinkIcon from '../../components/CopyLinkIcon'
import MessageTalent from './MessageTalent'
import { BookmarkService } from '../../services/BookmarkService'
import { useAuth } from '../../context/AuthContext'
import BookmarkIcon from '../../components/BookmarkIcon'
// import Overlay from '../../components/Overlay'
import ResumeDownload from '../../components/ResumeDownload'
import { templates } from '../resume/ResumeTemplates/templates'
import { useTheme } from '@/context/ThemeProvider'
// import Pricing from '../pricing/Pricing'

interface TalentCardProps {
  user: any
  onSave?: (user: TalentUser) => void
  // onDownload?: (user: TalentUser) => void
  saved?: boolean
  following?: boolean
  fetchProfileData: (username: string) => void
  onSkillTagClick?: (skill: string) => void
  activeSaved?: boolean
  setShowPricing?: any
}
//! Here talent card component is receiving different props from talent and saved section to fix this I have unified the responses by creating a file in utils of talent directory
const TalentCard: React.FC<TalentCardProps> = ({
  user,
  onSave,
  // onDownload,
  saved,
  following,
  onSkillTagClick,
  fetchProfileData,
  activeSaved,
  setShowPricing,
  // showResumeDownoadModal,setShowResumeDownloadModal
}) => {
  // console.log('TALENT USER DATA', user)
  const router = useRouter()
  const { isAuthenticated, refreshUserProfile, authUserProfile, setAuthUserProfile } = useAuth()
  const toast = useToast()
  const [isFollowing, setIsFollowing] = useState(following)
  const [messageUser, setMessageUser] = useState<boolean>(false)
  const [userFollowingData, setUserFollowingData] = useState<FollowingsList | undefined>()
  const [showFullDescription, setShowFullDescription] = useState(false)
  const [isActiveSubscription, setIsActiveSubscription] = useState(true)
  const [showResumeDownoadModal, setShowResumeDownloadModal] = useState(false)
  const { theme } = useTheme()
  // const [resumeData,setResumeData] = useState([])
  // const [showPricing, setShowPricing] = useState(false)
  const totalCredits =
    authUserProfile?.subscription.status === 'ACTIVE' && authUserProfile?.subscription.resumesTotal
  const remainingCredits =
    authUserProfile?.subscription.status === 'ACTIVE' &&
    authUserProfile?.subscription.resumesRemaining
  // const fetchFollowingData = async () => {
  //   try {
  //     const followingData = await UserService.getUserFollowing()
  //     setUserFollowingData(followingData.data.items)
  //   } catch (error) {
  //     console.error('Error:', error)
  //   }
  // }

  const handleUnFollowUser = async (id: string) => {
    try {
      const entityType = localStorage.getItem('x-active-entity-type')
      const res = await UserService.handleUnFollowUser(id, entityType as string)
      console.log('HANDLEUNFOLLOWERUSER', res)
      if (res.status === 'success') {
        await refreshUserProfile()
        if (user?.user?.permalink) {
          fetchProfileData(user.user.permalink)
        }
      } else {
        throw new Error(res)
      }
    } catch (error) {
      throw error
      // toast.error(`User Unfollow Failed`)
    }
  }

  const handleFollowUser = async (id: string) => {
    try {
      const entityType = localStorage.getItem('x-active-entity-type')
      const res = await UserService.handleFollowUser(id, entityType as string)
      // console.log('RESPONSENOWFOLLOWUSER', res)
      if (res.status === 'success') {
        await refreshUserProfile()
        if (user?.user?.permalink) {
          fetchProfileData(user.user.permalink)
        }
      } else {
        throw new Error(res)
      }
    } catch (error) {
      throw error
      // toast.error(`User Follow Failed`)
    }
  }
// This is showing on UI follow or following
  useEffect(() => {
    if (Array.isArray(userFollowingData)) {
      // console.log('USERFOLLOWINGDATA', userFollowingData)
      const following = userFollowingData.some((data) => data.user?.user?.id === user?.user.id)
      setIsFollowing(following)
    }
  }, [userFollowingData, user.id])

  const handleMessage = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/sign-in')
    } else {
      setMessageUser(true)
    }
  }

  const handleBookmarkToggle = async (newBookmarked: boolean) => {
    if (!isAuthenticated) {
      toast.error('Please log in to bookmark.')
      router.push('/login')
      return
    }

    try {
      if (newBookmarked) {
        const res = await BookmarkService.addBookmark(user?.user.id, 'TALENT_SRP')
        if (res.status === 'success') {
          toast.success(res.message || 'Bookmark added.')
          onSave && onSave({ ...user, isBookmark: true })
        } else {
          throw new Error(res.message)
        }
      } else {
        const res = await BookmarkService.deleteBookmark(user?.user.id, 'TALENT_SRP')
        if (res.status === 'success') {
          toast.success(res.message || 'Bookmark removed.')
          onSave && onSave({ ...user, isBookmark: false })
        } else {
          throw new Error(res.message)
        }
      }
    } catch (error: any) {
      toast.error(error?.message || error?.response?.data?.message || 'Failed to bookmark.')
      throw error // Re-throw to trigger UI rollback
    }
  }

  // const handleConfirmDownloadResume = () => {
  //   if (!isAuthenticated) {
  //     navigate('/sign-in')
  //   } else {
  //     setShowResumeDownloadModal(true)

  //   }
  // }

  //

  // const handleDownloadResume = async (talentId: string) => {
  //   try {
  //     const response = await UserService.getActiveSubscription(talentId)
  //   } catch (error: any) {
  //     if (error.data.messageType === 'NO_ACTIVE_SUBSCRIPTION') {
  //       setIsActiveSubscription(false)
  //     }
  //   }
  //   setShowResumeDownloadModal(false)
  // }

  // const handleShowPricing = () => {
  //   setShowPricing(true)
  // }

  // useEffect(()=>{

  //  const getResumeData = async()=>{
  //       const res = await CareerService.getCareerByPermalink(user.user.username)
  //         const personalDetails =  {
  //         name: "John Doe",
  //         profilePicture: "https://res.cloudinary.com/lucres/image/upload/v1744785063/lucres/n4uvYs6uuK--cropped.png.png",
  //         contactNumber: "",
  //         email: "<EMAIL>",
  //         location: "Pune",
  //         nationality: "Indian",
  //         profile: "Software Engineer",
  //         dateOfBirth: ""
  //     }
  //       setResumeData({personalDetails,...res.data})
  //  }
  //  getResumeData()
  // },[showResumeDownoadModal])

  // Find the selected template object based on templateId
  const selectedTemplateObj = templates[0]

  // Get the pdfComponent for the selected template
  const PdfComponent = selectedTemplateObj.pdfComponent
  const filename = `${user?.user.name?.replace(/\s+/g, '_')}_Resume.pdf`
  // const filename = `${user.user.name}_resume.pdf`

  //     const handleDownload = async () => {
  //             const careerRes = await CareerService.getCareerByPermalink(user.user.username)
  //             const userRes = await UserService.getUserProfileByPermalink(user.user.username)
  //         const personalDetails =  {
  //         name:  convertToTitleCase(user.user.name),
  //         profilePicture: user.user.img,
  //         contactNumber: "",
  //         email: "<EMAIL>",
  //         location: userRes.data.location.address.city,
  //         nationality: userRes.data.nationality,
  //         profile: user.user.headline,
  //         dateOfBirth: ''
  //     }
  //       const resumeData = {personalDetails,...careerRes.data}
  //   const blob = await pdf(<PdfComponent data={resumeData} />).toBlob();

  //   const url = URL.createObjectURL(blob);
  //   const link = document.createElement('a');
  //   link.href = url;
  //   link.download = filename;
  //   document.body.appendChild(link);
  //   link.click();
  //   document.body.removeChild(link);
  //   URL.revokeObjectURL(url);
  // };

  //   const handleDownload = async (talentId: string) => {
  //     try {
  //       const response = await UserService.purchaseResume(talentId)
  //       if (authUserProfile) {
  //         const authUser = await UserService.getUserProfileByPermalink(authUserProfile?.username)
  //         setAuthUserProfile(authUser.data)
  //       }
  //       setShowResumeDownloadModal(false)

  //       // toast.success('Resume Downloaded')
  //     } catch (error: any) {
  //       if (
  //         error.data.messageType === 'NO_ACTIVE_SUBSCRIPTION' ||
  //         error.data.messageType === 'NO_RESUME_REMAINING'
  //       ) {
  //         setShowPricing(true)
  //       }
  //     }
  //   }

  // const handleDownload = async (talentId: string) => {
  //   try {
  //     // Step 1: Attempt purchase
  //     const response = await UserService.purchaseResume(talentId)
  //     if (authUserProfile) {
  //       const authUser = await UserService.getUserProfileByPermalink(authUserProfile?.username)
  //       setAuthUserProfile(authUser.data)
  //     }
  //     setShowResumeDownloadModal(false)
  //     // toast.success('Resume Downloaded');

  // Step 2: Generate PDF and trigger download
  // const careerRes = await CareerService.getCareerByPermalink(user.user.username)
  // const userRes = await UserService.getUserProfileByPermalink(user.user.username)
  // // setAuthUserProfile(userRes.data)

  // const personalDetails = {
  //   name: convertToTitleCase(user.user.name),
  //   profilePicture: user.user.img,
  //   contactNumber: '',
  //   email: '<EMAIL>',
  //   location: userRes.data.location.address.city,
  //   nationality: userRes.data.nationality,
  //   profile: user.user.headline,
  //   dateOfBirth: '',
  // }

  //     const resumeData = { personalDetails, ...careerRes.data }
  //     const blob = await pdf(<PdfComponent data={resumeData} />).toBlob()

  //     const url = URL.createObjectURL(blob)
  //     const link = document.createElement('a')
  //     link.href = url
  //     link.download = filename
  //     document.body.appendChild(link)
  //     link.click()
  //     document.body.removeChild(link)
  //     URL.revokeObjectURL(url)
  //   } catch (error: any) {
  //     if (
  //       error.data?.messageType === 'NO_ACTIVE_SUBSCRIPTION' ||
  //       error.data?.messageType === 'NO_RESUME_REMAINING'
  //     ) {
  //       setShowPricing(true)
  //     } else {
  //       console.error('Download failed:', error)
  //     }
  //   }
  // }

  //   const handleDownload = (talentId: string) => {
  //   downloadResume({
  //     userId:talentId,
  //     flow:'Talent',
  //     authUserProfile,
  //     filename: filename,
  //     setAuthUserProfile,
  //     setShowResumeDownloadModal,
  //     setShowPricing,
  //     PdfComponent,
  //     username:user.username
  //   })
  // }
  // console.log('USER JUST BEFORE RETURN', user)
  return (
    <div
      className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 relative flex cursor-pointer flex-col justify-center gap-2 rounded-xl border px-4 py-3 md:mx-4 md:p-3"
      onClick={() => router.push(`/${user?.user.username}`)}
    >
      <div className="flex items-start justify-between md:items-center">
        <div className="flex flex-col items-start gap-3 gap-x-2 md:flex-row md:items-center">
          <div onClick={(e) => e.stopPropagation()}>
            <Avatar
              src={user?.user?.profileImg}
              alt={`${user?.user?.profileImgName}'s Avatar`}
              size={12}
              className="cursor-pointer object-cover"
            />
          </div>
          <div className="flex flex-col">
            <div className="flex flex-col items-start lg:flex-row lg:items-center">
              <div className="cursor-pointer whitespace-nowrap text-xl font-bold hover:underline">
                {truncateText(convertToTitleCase(user?.user.name), 20).text}
              </div>
              <DotIcon size={14} weight="bold" className="hidden lg:block" />
              <div className="text-lucres-gray-600 flex items-center text-base">{`@${user?.user.username}`}</div>
            </div>
            <span className="dark:text-dark-lucres-green-200 text-sm">
              {truncateText(user?.user.headline, 45).text}
            </span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {/* <Button
            size={'small'}
            theme="transparent"
            onClick={handleMessage}
            className="whitespace-nowrap rounded-full! border-gray-300! p-2! text-lucres-black! dark:border-dark-lucres-black-300! dark:text-dark-lucres-green-100!"
          >
            <ChatTeardropDots size={24} />
          </Button> */}
          <div onClick={(e) => e.stopPropagation()}>
            <FollowButton
              id={user?.user.idxId || user?.user.id}
              size="small"
              isInitiallyFollowing={isFollowing}
              className="px-6! py-2!"
              handleFollow={() => handleFollowUser(user?.user.id)}
              handleUnFollow={() => handleUnFollowUser(user?.user.id)}
            >
              {isFollowing ? 'Following' : 'Follow'}
            </FollowButton>
          </div>
        </div>
      </div>

      <div className="flex flex-wrap gap-x-5 gap-y-2">
        <div className="flex items-center gap-x-2">
          <BriefcaseIcon size={24} className=" " />
          <span className="whitespace-nowrap text-sm font-medium">{user?.user.workExperience}</span>
        </div>
        {/* Change this condition later */}(
        <div className="flex items-center gap-x-2">
          <MapPinIcon size={24} className=" " />
          <span className="whitespace-nowrap text-sm font-medium">{user?.user.location}</span>
        </div>
        )
      </div>

      <div className="mb-2 text-sm">{`${truncateText(user?.user.aboutMe, 50).text}`}</div>
      {/* Because there is diff in response */}
      <div className="flex w-full flex-wrap items-start justify-start gap-2 sm:w-9/12">
        {user?.user.skills?.slice(0, 5).map((skill: any, i: any) => (
          <span
            key={i}
            onClick={(e) => {
              e.stopPropagation()
              onSkillTagClick && onSkillTagClick(skill)
            }}
            className="bg-lucres-gray-300 text-lucres-gray-700 hover:text-dark-lucres-green-900 dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 dark:hover:bg-dark-lucres-black-300 dark:hover:text-dark-lucres-green-400 inline-block cursor-pointer rounded-[4px] px-4 py-1 text-xs font-normal hover:bg-gray-200/75 dark:border dark:text-white"
          >
            {convertToTitleCase(skill)}
          </span>
        ))}
      </div>

      <div className="flex w-full items-center justify-between">
        <div className="flex gap-x-1">
          <div className="flex items-center">
            <Tooltip text={'Download'} classes="whitespace-nowrap text-center" direction="bottom">
              <IconButton
                onClick={(e) => {
                  e.stopPropagation()
                  if (authUserProfile?.subscription.status === 'ACTIVE') {
                    setShowResumeDownloadModal(true)
                  } else {
                    setShowPricing(true)
                  }
                  // handleDownload(user.user.id)
                }}
              >
                {/* <DownloadSimple size={24} className=" " /> */}
                {theme === 'dark' ? (
                  <img
                    src="/common/download-profile-icon-dark.svg"
                    alt="download"
                    className="w-[19px]"
                  />
                ) : (
                  <img
                    src="/common/download-profile-icon-light.svg"
                    alt="download"
                    className="w-[19px]"
                  />
                )}
              </IconButton>
            </Tooltip>
            <span className="text-sm">{user.user.downloadsCount}</span>
            <ResumeDownload
              userId={user?.user.id}
              setShowPricing={setShowPricing}
              username={user?.user.name}
              showResumeDownloadModal={showResumeDownoadModal}
              setShowResumeDownloadModal={setShowResumeDownloadModal}
            />
          </div>

          <CopyLinkIcon toCopy={`${location.origin}/${user?.user.username}`} />
        </div>
        <BookmarkIcon
          onBookmarkToggle={handleBookmarkToggle}
          isBookmarked={activeSaved}
          tooltipText="Save Talent"
        />
      </div>

      {messageUser && <MessageTalent handleClose={() => setMessageUser(false)} user={user.user} />}
      {/* {showResumeDownoadModal && (
        <Overlay
          handleClose={() => setShowResumeDownloadModal(false)}
          heading={'Download Resume'}
          size="min-h-fit mt-20"
          classes="p-8! relative!"
        >
          <div className="w-full rounded-lg   bg-white   dark:bg-dark-lucres-black-500">
            <div className=" flex flex-col items-center ">
              <p className=" text-start text-sm text-gray-500 dark:text-gray-300">
                Are you sure you want to download this Resume?
              </p>
              <div className="flex w-full justify-center text-sm">
                <Credits
                  credits={remainingCredits}
                  totalcredits={totalCredits}
                  sqSize={14}
                  strokeWidth={3}
                />
              </div>
            </div>
            <div className="mt-4  flex justify-center gap-4">
              <Button
                size="small"
                theme="transparent"
                className="!border px-4! py-1.5!"
                onClick={() => setShowResumeDownloadModal(false)}
              >
                Cancel
              </Button>
              <Button
                size="small"
                theme="dark"
                className="px-4! py-1.5! text-white!"
                // onClick={() => handleDownload(user.id)}
              >
                Yes, Download
              </Button>
            </div>
          </div>
        </Overlay>
      )} */}
    </div>
  )
}

export default TalentCard
