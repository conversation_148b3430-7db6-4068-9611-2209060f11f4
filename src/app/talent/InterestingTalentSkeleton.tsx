import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, FreeMode, Pagination, Navigation } from 'swiper/modules'

const InterestingTalentSkeleton = ({ slidesCount = 4 }) => {
  return (
    <div className="w-full md:px-4">
      <Swiper
        slidesPerView={1}
        slidesPerGroup={2}
        spaceBetween={10}
        speed={1000}
        modules={[Autoplay, FreeMode, Pagination, Navigation]}
        breakpoints={{
          768: { slidesPerView: 2 },
          1024: { slidesPerView: 2.5 },
          1440: { slidesPerView: 2.5 },
        }}
        pagination={{
          clickable: true,
          el: '.custom-trending-job-pagination',
          bulletClass: 'swiper-trending-job-pagination-bullet',
          bulletActiveClass: 'swiper-trending-job-pagination-bullet-active',
        }}
      >
        {[...Array(slidesCount)].map((_, index) => (
          <SwiperSlide key={index}>
            <div className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 flex w-full animate-pulse items-start justify-between rounded-xl border px-6 py-4">
              <div className="flex items-start gap-x-4">
                <div className="dark:bg-dark-lucres-black-300 h-10 w-10 rounded-full bg-gray-300" />
                <div className="space-y-2">
                  <div className="dark:bg-dark-lucres-black-300 h-4 w-32 rounded-sm bg-gray-300" />
                  <div className="dark:bg-dark-lucres-black-300 h-3 w-40 rounded-sm bg-gray-200" />
                  <div className="dark:bg-dark-lucres-black-300 h-3 w-24 rounded-sm bg-gray-200" />
                </div>
              </div>
              <div className="dark:bg-dark-lucres-black-300 h-6 w-6 rounded-md bg-gray-300" />
            </div>
          </SwiperSlide>
        ))}
        <div className="custom-trending-job-pagination mt-4 flex justify-center"></div>
      </Swiper>
    </div>
  )
}

export default InterestingTalentSkeleton
