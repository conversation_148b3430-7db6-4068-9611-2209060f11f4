'use client'
import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { SrpService } from '../../../services/SrpService'
import { useAuth } from '../../../context/AuthContext'
import { TalentUser, TalentCard as TalentCardType } from '../../../models/User'
import TalentCardSkeleton from '../TalentCardSkeleton'
import TalentCard from '../TalentCard'
import { UserService } from '../../../services/UserService'
import NoDataFound from '../../../components/NoDataFound/NoDataFound'
import useError from '../../../context/ErrorContext'
import Pricing from '../../../components/Pricing'
import { useBodyScrollLock } from '../../../utils/useBodyScrollLock'
import { useToast } from '../../../components/ToastX'
import { normalizedSavedProfilesData } from '../utils/unifiedDataForComp'

const SavedTalent: React.FC = () => {
  const { isAuthenticated, authUserProfile, setAuthUserProfile } = useAuth()
  const { handleError } = useError()
  const router = useRouter()
  const toast = useToast()

  const [activeTab, setActiveTab] = useState('Saved')
  const [selectedSkills, setSelectedSkills] = useState<string[]>([])
  const [showPricing, setShowPricing] = useState(false)
  const [selectedLocations, setSelectedLocations] = useState<string[]>([])
  const [selectedWorkExperience, setSelectedWorkExperience] = useState<string | undefined>(
    undefined,
  )
  const [savedTalents, setSavedTalents] = useState<TalentCardType[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [showFilters, setShowFilters] = useState<boolean>(false)

  const fetchProfileData = async (permalink: string) => {
    try {
      await UserService.getUserProfileByPermalink(permalink)
    } catch (error) {
      console.error('Error:', error)
    }
  }

  const fetchSavedTalent = async () => {
    setLoading(true)
    try {
      const response = await SrpService.getSavedBookmark('TALENT_SRP')
      console.log(response)
      if (response.status === 'error') throw response
      setSavedTalents(response.data.items || [])
    } catch (err: any) {
      // if (err.message !== 'No bookmarks found with provided details.') handleError(err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchSavedTalent()
  }, [activeTab])

  const handleResetFilters = () => {
    setSelectedSkills([])
    setSelectedLocations([])
    setSelectedWorkExperience(undefined)
    fetchSavedTalent()
  }

  const handleSkillTagClick = (skill: string) => {
    setSelectedSkills((prevSkills) =>
      prevSkills.includes(skill) ? prevSkills : [...prevSkills, skill],
    )
  }

  const handlePurchaseSuccess = async () => {
    if (authUserProfile) {
      const authUser = await UserService.getUserProfileByPermalink(authUserProfile.username)
      setAuthUserProfile(authUser.data)
    }
  }

  useBodyScrollLock(showPricing)

  return (
    <section className="font-inter flex h-full w-full py-12 lg:mt-12  lg:py-0">
      <div className="flex w-full items-start justify-center   px-2 lg:max-w-[79%] lg:justify-end  lg:px-4  xl:max-w-[70%] ">
        <div className="dark:border-dark-lucres-black-300 min-h-screen w-full max-w-[700px] pb-8 md:border-x lg:pb-0 ">
          {isAuthenticated && (
            <div className="mx-auto flex w-full items-center justify-between px-6 pt-6 sm:w-9/12">
              <span
                className={`cursor-pointer pb-2 ${
                  activeTab === 'Talent' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => router.push('/talent')}
              >
                Talent
              </span>
              <span
                className={`cursor-pointer pb-2 ${
                  activeTab === 'Saved' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => setActiveTab('Saved')}
              >
                Saved Profiles
              </span>
            </div>
          )}
          <div
            className={`mb-6 flex w-full flex-col items-center border-t sm:items-start md:px-6 ${
              isAuthenticated ? '' : 'md:pt-3'
            } dark:border-dark-lucres-black-300`}
          ></div>
          <div className="flex w-full flex-col gap-y-4">
            {loading ? (
              <>
                {Array.from({ length: 10 }).map((_, index) => (
                  <TalentCardSkeleton key={index} />
                ))}
              </>
            ) : savedTalents.length ? (
              savedTalents.map((item, index) => {
                const user = normalizedSavedProfilesData(item);
                return (
                  <div key={index} className="w-full">
                    <TalentCard
                      user={user}
                      saved={true}
                      activeSaved={true}
                      following={item.isFollowing}
                      fetchProfileData={fetchProfileData}
                      onSkillTagClick={handleSkillTagClick}
                      setShowPricing={setShowPricing}
                    />
                  </div>
                )
              })
            ) : (
              <NoDataFound />
            )}
          </div>
        </div>
      </div>
      {showPricing && (
        <div className="z-999! fixed left-0 top-0 h-screen w-screen">
          <Pricing
            handleClose={() => setShowPricing(false)}
            flow="TALENT"
            handleSuccess={handlePurchaseSuccess}
          />
        </div>
      )}
    </section>
  )
}

export default SavedTalent
