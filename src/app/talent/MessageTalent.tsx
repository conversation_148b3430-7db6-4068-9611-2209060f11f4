'use client'
import {
  DotIcon,
  FilmStripIcon,
  ImageIcon,
  MapPinIcon,
  SmileyIcon,
  TextBIcon,
  TextItalicIcon,
} from '@phosphor-icons/react'
import React, { useEffect, useState } from 'react'
import IconButton from '../../components/IconButton'
import { convertToTitleCase } from '../../utils/commonUtils'
import { TalentUser } from '../../models/User'
import Input from '../../components/Input'
import Textarea from '../../components/Textarea'
import Credits from './Credits'
import Button from '../../components/Button'
import emptyBox from './empty-box.svg'
import Avatar from '../../components/Avatar/Avatar'
import Overlay from '../../components/Overlay'

interface MessageProps {
  handleClose: () => void
  user: TalentUser
}

const MessageTalent: React.FC<MessageProps> = ({ handleClose, user }) => {
  const [credits] = useState<number>(20)
  const [showGetCreditsModal, setShowGetCreditsModal] = useState<boolean>(false)
  const [formData, setFormData] = useState({ subject: '', message: '' })

  const totalCredits = 12

  useEffect(() => {
    if (credits === 0) {
      setShowGetCreditsModal(true)
    }
  }, [credits])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
  }

  return (
    <Overlay
      handleClose={handleClose}
      heading={showGetCreditsModal ? '' : `New Message for ${convertToTitleCase(user.name)}`}
      classes={showGetCreditsModal ? 'p-0!' : ''}
    >
      {showGetCreditsModal ? (
        <div className="flex w-full flex-col items-center">
          <div className="dark:bg-dark-lucres-black-200 relative flex h-40 w-full items-center justify-center rounded-sm bg-[#ECFFD5] p-4">
            <img src={emptyBox} alt="Empty Box" className="mx-auto h-auto w-8/12" />
          </div>
          <div className="flex w-9/12 flex-col items-center">
            <div className="mt-20 text-center">
              <h3 className="dark:text-lucres-black text-lg font-semibold">
                You're Out of <span className="text-lucres-600">Credits!</span>
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                You've reached your messaging limit & resume downloads. Upgrade now to continue
                connecting with top talent.
              </p>
            </div>
            <div className="mt-2 flex gap-4 py-4 pb-8">
              <Button size="small" theme="opaque" isRectangle>
                Get More Credits
              </Button>
              <Button size="small" theme="transparent" isRectangle>
                Explore Plans
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <>
          <div className="flex flex-col items-start gap-3 gap-x-4 md:flex-row md:items-center">
            <Avatar
              src={user.img}
              alt={`${user.name}'s Avatar`}
              size={16}
              className="cursor-pointer object-cover"
            />
            <div className="flex flex-col">
              <span className="text-lucres-900 dark:text-dark-lucres-green-100 whitespace-nowrap font-bold">
                {convertToTitleCase(user.name)}
              </span>
              <div className="text-lucres-900 dark:text-dark-lucres-green-200 flex items-center text-sm">
                <span>{convertToTitleCase(user.location ?? '')}</span>
                <DotIcon weight="bold" />
                <span>{user.workExperience} of Exp.</span>
              </div>
            </div>
          </div>

          <form className="flex flex-col gap-4" onSubmit={handleSubmit}>
            <Input
              type="text"
              theme="gray"
              id="subject"
              name="subject"
              label="Subject"
              labelClassName="font-medium!"
              value={formData.subject}
              onChange={handleChange}
            />
            <Textarea
              theme="gray"
              id="message"
              name="message"
              label="Message"
              labelClassName="font-medium!"
              value={formData.message}
              onChange={handleChange}
            />

            <div className="flex w-full items-center justify-between">
              <div className="flex items-center gap-4">
                <ImageIcon size={20} className="dark:text-dark-lucres-green-100 cursor-pointer" />
                <FilmStripIcon
                  size={20}
                  className="dark:text-dark-lucres-green-100 cursor-pointer"
                />
                <SmileyIcon size={20} className="dark:text-dark-lucres-green-100 cursor-pointer" />
                <MapPinIcon size={20} className="dark:text-dark-lucres-green-100 cursor-pointer" />
                |
                <TextBIcon
                  size={20}
                  weight="bold"
                  className="dark:text-dark-lucres-green-100 cursor-pointer"
                />
                <TextItalicIcon
                  size={20}
                  weight="bold"
                  className="dark:text-dark-lucres-green-100 cursor-pointer"
                />
              </div>
              <Credits sqSize={16} strokeWidth={3} credits={credits} totalcredits={totalCredits} />
            </div>

            <div className="flex w-full justify-end">
              <Button
                size="small"
                isRectangle
                type="submit"
                disabled={formData.message === '' || formData.subject === ''}
              >
                Send Message
              </Button>
            </div>
          </form>
        </>
      )}
    </Overlay>
  )
}

export default MessageTalent
