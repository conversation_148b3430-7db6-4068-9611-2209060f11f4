import React from 'react'
import MultiSelect from '../../components/MultiSelect'
import Select from '../../components/Select'
import Button from '../../components/Button'
import { useAuth } from '../../context/AuthContext'

interface TalentFilterProps {
  selectedSkills: string[]
  setSelectedSkills: React.Dispatch<React.SetStateAction<string[]>>
  selectedLocations: string[]
  setSelectedLocations: React.Dispatch<React.SetStateAction<string[]>>
  selectedExperience: string | undefined
  setSelectedExperience: React.Dispatch<React.SetStateAction<string | undefined>>
  handleResetFilters: () => void
}

const TalentFilter: React.FC<TalentFilterProps> = ({
  selectedSkills,
  setSelectedSkills,
  selectedLocations,
  setSelectedLocations,
  selectedExperience,
  setSelectedExperience,
  handleResetFilters,
}) => {
  const { isAuthenticated } = useAuth()

  const experienceOptions = [
    { value: 'Fresher', label: 'Fresher' },
    { value: '1-3 years', label: '1-3 years' },
    { value: '3-6 years', label: '3-6 years' },
    { value: '6-9 years', label: '6-9 years' },
    { value: '9-12 years', label: '9-12 years' },
    { value: '>12 years', label: '>12 years' },
  ]
  const skillsOptions = [
    { value: 'Html', label: 'Html' },
    { value: 'CSS', label: 'CSS' },
    { value: 'JavaScript', label: 'JavaScript' },
    { value: 'React', label: 'React' },
  ]
  const locationOptions = [
    { value: 'Pune', label: 'Pune' },
    { value: 'Mumbai', label: 'Mumbai' },
    { value: 'Goa', label: 'Goa' },
    { value: 'Chennai', label: 'Chennai' },
  ]

  return (
    <div className="flex w-full flex-col items-end gap-4">
      <div
        className={`flex w-full items-center justify-between ${!isAuthenticated ? 'md:pt-5' : 'md:mt-0 md:pt-3'}`}
      >
        <div>Filters</div>
        <Button
          theme="transparent"
          className="border-lucres-300/40 dark:border-dark-lucres-black-200 px-4! py-1! rounded-lg border text-sm hover:bg-opacity-50"
          onClick={handleResetFilters}
        >
          Reset
        </Button>
      </div>
      <MultiSelect
        classes="p-2! pl-0!"
        parentClassName="p-0"
        options={skillsOptions}
        value={selectedSkills}
        onChange={setSelectedSkills}
        placeholder="Skills"
        type="text"
      />
      <MultiSelect
        type="text"
        classes="p-2! pl-0!"
        parentClassName="p-0 "
        options={locationOptions}
        value={selectedLocations}
        onChange={setSelectedLocations}
        placeholder="Locations"
      />
      <Select
        classes="p-2!"
        parentClassName="p-0"
        options={experienceOptions}
        value={selectedExperience}
        onChange={(value) => setSelectedExperience(value)}
        placeholder="Experience"
      />
    </div>
  )
}

export default TalentFilter
