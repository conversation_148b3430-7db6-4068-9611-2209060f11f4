'use client'
import React, { useEffect, useState, useRef } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { SrpService } from '../../../services/SrpService'
import Pagination from '../../../components/Pagination'
import { useAuth } from '../../../context/AuthContext'
import { Paginator } from '../../../models/Paginator'
import { TalentUser, TalentCard as TalentCardType } from '../../../models/User'
import TalentCardSkeleton from '../TalentCardSkeleton'
import TalentCard from '../TalentCard'
import { Funnel, FunnelIcon, X, XIcon } from '@phosphor-icons/react'
import TalentFilters from '../TalentFilter'
import { UserService } from '../../../services/UserService'
import NoDataFound from '../../../components/NoDataFound/NoDataFound'
import useError from '../../../context/ErrorContext'
import Pricing from '../../../components/Pricing'
import { useBodyScrollLock } from '../../../utils/useBodyScrollLock'
import { useToast } from '../../../components/ToastX'
import { generateTalentFilterUrl, parseTalentFilterUrl } from '../utils/urlUtils'
import { useDebouncedTalentFilters } from '../utils/useDebouncedTalentFilters'
import { TalentFilterState } from '../utils/urlUtils'

function isEqual(a: any, b: any) {
  return JSON.stringify(a) === JSON.stringify(b)
}

const defaultFilterState: TalentFilterState = {
  selectedSkills: [],
  selectedLocations: [],
  selectedWorkExperience: undefined,
  currentPage: 1,
}

// Helper to wrap set functions to match React.Dispatch<SetStateAction<T>>
function toDispatch<T>(setter: (value: T) => void): React.Dispatch<React.SetStateAction<T>> {
  return (valueOrUpdater) => {
    setter(valueOrUpdater as T)
  }
}

const TalentWithFilters: React.FC = () => {
  const { isAuthenticated, authUserProfile, setAuthUserProfile } = useAuth()
  const { handleError } = useError()
  const pathname = usePathname()
  const router = useRouter()
  const toast = useToast()

  const [activeTab, setActiveTab] = useState('Talent')
  const [showPricing, setShowPricing] = useState(false)
  const [savedTalents, setSavedTalents] = useState<TalentCardType[]>([])
  const [talents, setTalents] = useState<Array<TalentCardType>>([])
  const [paginator, setPaginator] = useState<Paginator | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [showFilters, setShowFilters] = useState<boolean>(false)
  const {
    filters,
    setSelectedSkills,
    setSelectedLocations,
    setSelectedWorkExperience,
    setCurrentPage,
    resetFilters,
    debouncedFilters,
  } = useDebouncedTalentFilters(defaultFilterState, 300)

  const isSyncingFromUrl = useRef(false)

  // Parse URL parameters on component mount or navigation
  useEffect(() => {
    const filterState = parseTalentFilterUrl(pathname)
    if (
      !isEqual(filterState, {
        selectedSkills: debouncedFilters.selectedSkills,
        selectedLocations: debouncedFilters.selectedLocations,
        selectedWorkExperience: debouncedFilters.selectedWorkExperience,
        currentPage: debouncedFilters.currentPage,
      })
    ) {
      isSyncingFromUrl.current = true
      setSelectedSkills(debouncedFilters.selectedSkills)
      setSelectedLocations(debouncedFilters.selectedLocations)
      setSelectedWorkExperience(debouncedFilters.selectedWorkExperience)
      setCurrentPage(debouncedFilters.currentPage)
    }
  }, [pathname])

  // Update URL when filters change, but not if just synced from URL
  useEffect(() => {
    if (isSyncingFromUrl.current) {
      isSyncingFromUrl.current = false
      return
    }
    const newPath = generateTalentFilterUrl({
      selectedSkills: debouncedFilters.selectedSkills,
      selectedLocations: debouncedFilters.selectedLocations,
      selectedWorkExperience: debouncedFilters.selectedWorkExperience,
      currentPage: debouncedFilters.currentPage,
    })
    if (pathname !== newPath) {
      router.replace(newPath)
    }
  }, [debouncedFilters, pathname])

  const fetchProfileData = async (permalink: string) => {
    try {
      await UserService.getUserProfileByPermalink(permalink)
    } catch (error) {
      console.error('Error:', error)
    }
  }

  // Fetch talents when debounced filters change
  useEffect(() => {
    setLoading(true)
    const experienceRanges: Record<string, { min: number; max: number }[]> = {
      Fresher: [{ min: 0, max: 1 }],
      '1-3 years': [{ min: 1, max: 3 }],
      '3-6 years': [{ min: 3, max: 6 }],
      '6-9 years': [{ min: 6, max: 9 }],
      '9-12 years': [{ min: 9, max: 12 }],
      '>12 years': [{ min: 12, max: 50 }],
    }
    const filtersForApi: any = {}
    if (debouncedFilters.selectedSkills.length)
      filtersForApi.skills = debouncedFilters.selectedSkills
    if (debouncedFilters.selectedLocations.length)
      filtersForApi.locations = debouncedFilters.selectedLocations
    if (debouncedFilters.selectedWorkExperience)
      filtersForApi.workExperience = experienceRanges[debouncedFilters.selectedWorkExperience]
    SrpService.getTalents(debouncedFilters.currentPage, 25, filtersForApi)
      .then((response) => {
        // console.log("DYNAMIC ROUTE", response);
        const { items, paginator: pg } = response.data
        setTalents(items || [])
        setPaginator(pg || null)
      })
      .catch(handleError)
      .finally(() => setLoading(false))
  }, [debouncedFilters, handleError])

  const handlePageChange = (newPage: number) => {
    if (newPage !== filters.currentPage) {
      setCurrentPage(newPage)
    }
  }

  const handleResetFilters = () => {
    resetFilters()
  }

  const handleSkillTagClick = (skill: string) => {
    if (!debouncedFilters.selectedSkills.includes(skill)) {
      setSelectedSkills([...debouncedFilters.selectedSkills, skill])
    }
  }

  const handlePurchaseSuccess = async () => {
    if (authUserProfile) {
      const authUser = await UserService.getUserProfileByPermalink(authUserProfile.username)
      setAuthUserProfile(authUser.data)
    }
  }

  useBodyScrollLock(showPricing)

  return (
    <section className="font-inter flex h-full w-full py-12 lg:mt-12  lg:py-0">
      <div className="flex w-full items-start justify-center   px-2 lg:max-w-[79%] lg:justify-end  lg:px-4  xl:max-w-[70%] ">
        <div className="dark:border-dark-lucres-black-300 min-h-screen w-full max-w-[700px] pb-8 md:border-x lg:pb-0 ">
          {isAuthenticated && (
            <div className="mx-auto flex w-full items-center justify-between px-6 pt-6 sm:w-9/12">
              <span
                className={`cursor-pointer pb-2 ${
                  activeTab === 'Talent' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => setActiveTab('Talent')}
              >
                Talent
              </span>
              <span
                className={`cursor-pointer pb-2 ${
                  activeTab === 'Saved' ? 'border-lucres-500 border-b-2 font-semibold' : ''
                }`}
                onClick={() => router.push('/talent/saved')}
              >
                Saved Profiles
              </span>
            </div>
          )}
          <div
            className={`mb-6 flex w-full flex-col items-center border-t sm:items-start md:px-6 ${
              isAuthenticated ? '' : 'md:pt-3'
            } dark:border-dark-lucres-black-300`}
          ></div>
          <div className="flex w-full flex-col gap-y-4">
            {loading ? (
              <>
                {Array.from({ length: 10 }).map((_, index) => (
                  <TalentCardSkeleton key={index} />
                ))}
              </>
            ) : talents.length ? (
              talents.map((item, index) => (
                <div key={index} className="w-full">
                  <TalentCard
                    user={item}
                    activeSaved={item.isBookmark}
                    following={item.isFollowing}
                    fetchProfileData={fetchProfileData}
                    onSkillTagClick={handleSkillTagClick}
                    setShowPricing={setShowPricing}
                  />
                </div>
              ))
            ) : (
              <NoDataFound />
            )}
          </div>
          {paginator && paginator.pageCount > 1 && (
            <div className="py-6">
              <Pagination paginator={paginator} onPageChange={handlePageChange} />
            </div>
          )}
          <span
            onClick={() => setShowFilters(!showFilters)}
            className="bg-lucres-green-200 dark:bg-dark-lucres-black-300 fixed bottom-24 right-11 z-50 flex h-14 w-14 cursor-pointer items-center justify-center rounded-full transition-transform duration-200 ease-in-out lg:hidden"
          >
            <span
              className={`transform transition-all duration-300 ease-in-out ${
                showFilters ? 'rotate-90 scale-100 opacity-100' : 'absolute scale-50 opacity-0'
              }`}
            >
              <XIcon size={24} />
            </span>
            <span
              className={`transform transition-all duration-300 ease-in-out ${
                showFilters ? 'absolute scale-50 opacity-0' : 'scale-100 opacity-100'
              }`}
            >
              <FunnelIcon size={24} />
            </span>
          </span>
          {showFilters && (
            <div
              className="fixed left-0 top-0 z-40 flex h-screen w-full items-center justify-center rounded-lg bg-black bg-opacity-40 lg:hidden"
              onClick={() => setShowFilters(false)}
            >
              <div
                className="dark:bg-dark-lucres-black-500 flex w-9/12 flex-col items-end justify-start gap-4 rounded-lg bg-white px-8 py-6"
                onClick={(e) => e.stopPropagation()}
              >
                <TalentFilters
                  selectedSkills={debouncedFilters.selectedSkills}
                  setSelectedSkills={toDispatch(setSelectedSkills)}
                  selectedLocations={debouncedFilters.selectedLocations}
                  setSelectedLocations={toDispatch(setSelectedLocations)}
                  selectedExperience={debouncedFilters.selectedWorkExperience}
                  setSelectedExperience={toDispatch(setSelectedWorkExperience)}
                  handleResetFilters={handleResetFilters}
                />
              </div>
            </div>
          )}
        </div>
      </div>
      <div className="dark:bg-dark-lucres-black-500 hidden flex-col items-start justify-start gap-4 rounded-lg bg-white p-4 pb-8 pt-4 lg:flex">
        <TalentFilters
          selectedSkills={debouncedFilters.selectedSkills}
          setSelectedSkills={toDispatch(setSelectedSkills)}
          selectedLocations={debouncedFilters.selectedLocations}
          setSelectedLocations={toDispatch(setSelectedLocations)}
          selectedExperience={debouncedFilters.selectedWorkExperience}
          setSelectedExperience={toDispatch(setSelectedWorkExperience)}
          handleResetFilters={handleResetFilters}
        />
      </div>
      {showPricing && (
        <div className="z-999! fixed left-0 top-0 h-screen w-screen">
          <Pricing
            handleClose={() => setShowPricing(false)}
            flow="TALENT"
            handleSuccess={handlePurchaseSuccess}
          />
        </div>
      )}
    </section>
  )
}

export default TalentWithFilters
