import { FC } from 'react'

interface Props {
  strokeWidth?: number
  sqSize?: number
  credits: any
  totalcredits: any
  classes?: string
  creditsLeftRequired?: boolean
}

const Credits: FC<Props> = (props) => {
  const {
    strokeWidth = 8,
    sqSize = 40,
    credits,
    totalcredits,
    classes,
    creditsLeftRequired = true,
  } = props
  const radius = (sqSize - strokeWidth) / 2
  const viewBox = `0 0 ${sqSize} ${sqSize}`
  const dashArray = radius * Math.PI * 2
  const dashOffset = dashArray - (dashArray * (credits || 0)) / (totalcredits || 1)

  return (
    <div className={`flex items-center gap-2 text-sm font-medium ${classes}`}>
      <svg width={sqSize} height={sqSize} viewBox={viewBox}>
        <circle
          className="fill-none stroke-gray-200"
          cx={sqSize / 2}
          cy={sqSize / 2}
          r={radius}
          strokeWidth={`${strokeWidth}px`}
        />
        {credits !== 0 && (
          <circle
            className="stroke-lucres-green-300 dark:stroke-dark-lucres-green-600 fill-none transition-all delay-200 ease-in"
            cx={sqSize / 2}
            cy={sqSize / 2}
            r={radius}
            strokeWidth={`${strokeWidth}px`}
            transform={`rotate(-90 ${sqSize / 2} ${sqSize / 2})`}
            style={{
              strokeDasharray: dashArray,
              strokeDashoffset: dashOffset,
            }}
          />
        )}
      </svg>
      <div>
        {credits}/{totalcredits} {creditsLeftRequired && 'Credits Left'}
      </div>
    </div>
  )
}

export default Credits
