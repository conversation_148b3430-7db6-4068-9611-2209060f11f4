export const normalizedTalentData = (item: any) => {
    const user = item.user ?? item ?? {};
    console.log(user);
    return {
        user: {
            aboutMe: user.aboutMe,
            createdAt: user.createdAt,
            downloadsCount: user.downloadsCount,
            headline: user.headline,
            id: user.id,
            profileImg: user.img,
            location: user.location,
            name: user.name,
            skills: user.skills,
            username: user.username,
            workExperience: user.workExperience,
        }
    }
}

export const normalizedSavedProfilesData = (item: any) => {
    const user = item.user ?? item ?? {};
    console.log("SAVEDPROFILEDATA", user);
    return {
        user: {
            aboutMe: user.entity.aboutMe,
            createdAt: user.entity.createdAt,
            downloadsCount: user.entity.downloadsCount,
            headline: user.entity.headline,
            id: user.entity.id,
            profileImg: user.entity.profileImg.url,
            profileImgName: user.entity.profileImg.name,
            location: user.entity.location,
            name: user.entity.name,
            skills: user.entity.skills,
            username: user.entity.username,
            workExperience: user.entity.workExperience,
            idxId: user.id,
            isContactRestricted: user.isContactRestricted,
            isFollowing: user.isFollowing,
            type: user.Type,
            updatedAt: user.updatedAt,
        }
    }
}