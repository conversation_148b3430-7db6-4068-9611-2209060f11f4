import { useRouter } from 'next/navigation'
import Button from '../../components/Button'

interface ConfirmationModalProps {
  onClick: () => void
}

function ConfirmationModal({ onClick }: ConfirmationModalProps) {
  const router = useRouter()
  const handleNavigateToFeed = () => {
    router.push('/feed')
  }
  return (
    <div className="flex h-full w-full items-center justify-center">
      <div className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 flex h-1/3 w-11/12 flex-col items-center justify-center gap-4 rounded-md bg-white p-4 text-center md:w-8/12 lg:w-5/12 dark:border">
        <h3 className="text-xl font-semibold">Skip for now?</h3>
        <p className="text-lucres-gray-500">
          Your AI resume will be ready in minutes. You can still create your AI resume from your
          profile section.
        </p>
        <div className="mt-2 flex items-center gap-4">
          <Button size="small" theme="transparent" isRectangle onClick={onClick}>
            Cancel
          </Button>
          <Button
            size="small"
            theme="dark"
            isRectangle
            className="bg-lucres-black dark:bg-dark-lucres-green-400 dark:text-lucres-black text-white"
            onClick={handleNavigateToFeed}
          >
            Yes, Skip for now
          </Button>
        </div>
      </div>
    </div>
  )
}

export default ConfirmationModal
