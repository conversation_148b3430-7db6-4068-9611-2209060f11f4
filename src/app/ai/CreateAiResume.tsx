import { useState, useEffect, useRef } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import Button from '../../components/Button'
import { <PERSON>U<PERSON>, <PERSON>rkle, StopCircle } from '@phosphor-icons/react'
import Input from '../../components/Input'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, FreeMode, Pagination, Navigation } from 'swiper/modules'
// @ts-ignore
import 'swiper/css'
// @ts-ignore
import 'swiper/css/free-mode'
// @ts-ignore
import 'swiper/css/pagination'
import { AiResumeService } from '../../services/AIResumeService'

interface Message {
  sender: 'AI' | 'User'
  text: string
  timestamp: string
}

// Define the input configuration interface
interface InputConfig {
  messageButtons?: string[]
  showTextInput?: boolean
  inputButtons?: string[]
}

/**
 * Determines the input configuration based on the bot's question.
 * @param message - The bot's last message.
 * @returns An object specifying the input configuration.
 */
const getInputType = (message: string): InputConfig => {
  if (message.includes('Shall we get started?')) {
    return { messageButtons: ['Yes', 'No'] }
  } else if (message.includes('Are you a fresher or experienced professional?')) {
    return { messageButtons: ['Fresher', 'Experienced'] }
  } else if (message.includes('Any other roles to include?')) {
    return { messageButtons: ['Skip'], showTextInput: true }
  } else if (message.includes('Any other education qualification to include?')) {
    return { messageButtons: ['Skip'], showTextInput: true }
  } else if (message.includes('Any certifications or extra training you’d like to highlight?')) {
    return { messageButtons: ['Skip'], showTextInput: true }
  } else if (message.includes('Do you have a LinkedIn profile or portfolio link I can add?')) {
    return { messageButtons: ['Skip'], showTextInput: true }
  } else if (message.includes("You're all set! Let's head to the Careers page.")) {
    return {} // No input needed, conversation is complete
  } else {
    return { showTextInput: true }
  }
}

function CreateAiResume() {
  const [messages, setMessages] = useState<Message[]>([])
  const [userInput, setUserInput] = useState<string>('')
  const [loading, setLoading] = useState<boolean>(false)
  const [createResume, setCreateResume] = useState<boolean>(false)
  const [isGenerating, setIsGenerating] = useState<boolean>(false)
  const router = useRouter()
  const chatContainerRef = useRef<HTMLDivElement>(null)
  const inputContainerRef = useRef<HTMLDivElement>(null)

  const swiperItems = [
    'Checking Profile',
    'Finding Suitable Keywords',
    'Analyzing',
    'Gathering Insights',
    'Compiling Results',
  ]
  const [currentIndex, setCurrentIndex] = useState<number>(0)

  // Fetch chat history on mount
  useEffect(() => {
    const fetchChatHistory = async () => {
      try {
        const response = await AiResumeService.getChatHistory()
        const chatHistory = response.data.chatHistory.map((msg: any) => ({
          sender: msg.role === 'BOT' ? 'AI' : 'User',
          text: msg.message,
          timestamp: msg.timestamp,
        }))
        setMessages(chatHistory)
      } catch (error) {
        console.error('Error fetching chat history:', error)
      }
    }
    fetchChatHistory()
  }, [])

  // Scroll to bottom when messages update
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: 'smooth',
      })
    }
  }, [messages])

  // Focus the input when conditions are met
  useEffect(() => {
    if (
      messages.length > 0 &&
      messages[messages.length - 1].sender === 'AI' &&
      !isGenerating &&
      getInputType(messages[messages.length - 1].text).showTextInput &&
      inputContainerRef.current
    ) {
      const inputElement = inputContainerRef.current.querySelector('input')
      if (inputElement) {
        inputElement.focus()
      }
    }
  }, [messages, isGenerating])

  // Set createResume to true after 2.5 seconds when isGenerating is true
  useEffect(() => {
    if (isGenerating) {
      const timer = setTimeout(() => {
        setCreateResume(true)
      }, 2500)
      return () => clearTimeout(timer)
    }
  }, [isGenerating])

  // Send user message to the backend
  const sendMessage = async (message: string) => {
    setLoading(true)
    try {
      // Send the user's message to the backend
      const response = await AiResumeService.sendUserInput(message)

      const userMessage: Message = {
        sender: 'User',
        text: message,
        timestamp: new Date().toISOString(),
      }

      const botReply: Message = {
        sender: 'AI',
        text: response.data.reply,
        timestamp: new Date().toISOString(),
      }

      // Add both user message and bot reply
      setMessages((prev) => [...prev, userMessage, botReply])

      // Check if the conversation is done
      if (response.data.done) {
        setIsGenerating(true)
      }
    } catch (error) {
      console.error('Error sending message:', error)
    } finally {
      setLoading(false)
    }
  }

  // Handle text input submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (userInput.trim() === '' || loading) return
    sendMessage(userInput)
    setUserInput('')
  }

  // Handle button clicks
  const handleButtonClick = (option: string) => {
    if (loading) return
    sendMessage(option)
  }

  return (
    <div className="flex h-full min-h-full w-full items-center justify-center lg:py-12">
      <div className="mt-20 flex w-full flex-col items-center justify-center lg:w-7/12">
        <div className="relative">
          <div className="h-34 w-34 container mb-3">
            <div className="glow"></div>
            <img className="image" src="/dashboard/Ai.svg" alt="AiSvg" />
          </div>
        </div>

        {!createResume && (
          <h1 className="text-center text-2xl">
            Build a professional AI <br /> Resume / Profile
            <span className="font-semibold"> effortlessly</span>
          </h1>
        )}

        {createResume ? (
          <div className="h-24 w-full">
            <Swiper
              slidesPerView={3}
              direction="vertical"
              spaceBetween={5}
              autoplay={{ disableOnInteraction: false }}
              centeredSlides={true}
              onSlideChange={(swiper) => setCurrentIndex(swiper.realIndex)}
              onReachEnd={() => router.push('/resume')}
              className="flex h-full flex-col items-center justify-center text-center"
              speed={200}
              modules={[Autoplay, FreeMode, Pagination, Navigation]}
            >
              {swiperItems.map((item, index) => (
                <SwiperSlide
                  key={index}
                  className={`transition-all duration-300 ${
                    index === currentIndex ? 'text-lg opacity-75' : 'opacity-50'
                  }`}
                >
                  <div>{item}</div>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        ) : (
          <div className="mb-20 mt-10 h-full w-full lg:mb-0">
            <div
              ref={chatContainerRef}
              className="scrollbar-none flex h-[40vh] w-full flex-col justify-start gap-y-8 overflow-y-scroll rounded-lg p-4 lg:h-[40vh]"
            >
              {messages.map((msg, index) => (
                <div key={index} className="flex flex-col gap-y-2">
                  <div
                    className={`flex items-start gap-x-2 ${
                      msg.sender === 'AI' ? 'justify-start' : 'justify-end'
                    }`}
                  >
                    {msg.sender === 'AI' && (
                      <span className="dark:bg-dark-lucres-black-300 flex h-9 w-9 shrink-0 items-center justify-center rounded-full bg-gray-100">
                        <Sparkle size={24} />
                      </span>
                    )}
                    <div
                      className={`${
                        msg.sender === 'AI'
                          ? 'dark:bg-dark-lucres-black-200 max-w-[90%] break-words bg-white p-2'
                          : 'dark:bg-dark-lucres-black-300 w-fit max-w-[80%] break-words bg-gray-100 px-4 py-2'
                      } rounded-lg`}
                    >
                      {msg.text}
                    </div>
                    {msg.sender === 'User' && (
                      <img
                        src="/common/avatar.svg"
                        alt="profile"
                        className="h-7 w-7 rounded-full object-cover"
                      />
                    )}
                  </div>

                  {/* Render message buttons below the message */}
                  {msg.sender === 'AI' &&
                    index === messages.length - 1 &&
                    !isGenerating &&
                    (() => {
                      const inputConfig = getInputType(msg.text)
                      if (inputConfig.messageButtons && inputConfig.messageButtons.length > 0) {
                        return (
                          <div className="flex gap-x-3 pl-12">
                            {inputConfig.messageButtons.map((option, idx) => (
                              <Button
                                key={idx}
                                onClick={() => handleButtonClick(option)}
                                disabled={loading}
                                theme="translucent"
                                className="rounded-md!"
                                size="small"
                              >
                                {option}
                              </Button>
                            ))}
                          </div>
                        )
                      }
                      return null
                    })()}
                </div>
              ))}
            </div>

            {/* Input Area: Show text input and/or input buttons */}
            <div className="mt-4">
              {messages.length > 0 &&
                messages[messages.length - 1].sender === 'AI' &&
                !createResume && (
                  <>
                    {isGenerating ? (
                      <div className="flex items-center gap-x-3">
                        <div className="relative flex h-3 w-3 items-center justify-center rounded-full border-2">
                          <div className="border-lucres-900 absolute h-3 w-3 animate-spin rounded-full border-2 border-l-transparent border-t-transparent"></div>
                        </div>
                        <span className="font-small text-base">
                          Thank you for giving your information we are Generating your resume 🎉
                        </span>
                      </div>
                    ) : (
                      (() => {
                        const inputConfig = getInputType(messages[messages.length - 1].text)
                        if (inputConfig.showTextInput) {
                          return (
                            <form onSubmit={handleSubmit} className="flex">
                              <div
                                ref={inputContainerRef}
                                className="border-lucres-300 mb-3 flex w-full items-center justify-between gap-x-2 rounded-lg border border-opacity-40 bg-transparent px-4 py-3"
                              >
                                <Input
                                  type="text"
                                  className="m-0! px-0! py-0! border-none"
                                  placeholder="Send a message"
                                  value={userInput}
                                  onChange={(e) => setUserInput(e.target.value)}
                                  disabled={loading}
                                />
                                {inputConfig.inputButtons &&
                                  inputConfig.inputButtons.length > 0 && (
                                    <div className="flex gap-x-2">
                                      {inputConfig.inputButtons.map((option, idx) => (
                                        <Button
                                          key={idx}
                                          onClick={() => handleButtonClick(option)}
                                          disabled={loading}
                                          theme="translucent"
                                          className="rounded-md!"
                                          size="small"
                                        >
                                          {option}
                                        </Button>
                                      ))}
                                    </div>
                                  )}
                                <button
                                  type="button"
                                  onClick={() => {
                                    if (userInput.trim() !== '' && !loading) {
                                      sendMessage(userInput)
                                      setUserInput('')
                                    }
                                  }}
                                  className="bg-lucres-900 flex h-6 w-6 cursor-pointer items-center justify-center rounded-[4px] p-0.5 text-white"
                                  disabled={loading}
                                >
                                  {loading ? (
                                    <StopCircle size={24} weight="fill" />
                                  ) : (
                                    <ArrowUp size={24} />
                                  )}
                                </button>
                              </div>
                            </form>
                          )
                        }
                        return null
                      })()
                    )}
                  </>
                )}
            </div>

            <span className="mt-2 flex w-full items-center justify-center text-center text-xs opacity-75">
              Since AI can make mistakes, check important info.
            </span>
          </div>
        )}
      </div>
    </div>
  )
}

export default CreateAiResume
