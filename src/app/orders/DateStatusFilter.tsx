import Radio from '../../components/Radio'

interface Props {
  status: string
  setStatus: (value: string) => void
  fromDate: string
  toDate: string
  setFromDate: (val: string) => void
  setToDate: (val: string) => void
  fetchOrdersByFilter: (status: string, fromDate: string, toDate: string) => void
}

const DateStatusFilter: React.FC<Props> = ({
  status,
  setStatus,
  fromDate,
  toDate,
  setFromDate,
  setToDate,
  fetchOrdersByFilter,
}) => {
  function formatDate(date: string): string {
    if (!date || !date.includes('-')) return ''
    const [year, month, day] = date.split('-')
    return `${day}/${month}/${year}`
  }

  const getFormattedDateRange = () => {
    const formattedFrom = fromDate ? formatDate(fromDate) : ''
    const formattedTo = toDate ? formatDate(toDate) : ''
    return { formattedFrom, formattedTo }
  }

  const handleRadioChange = (value: string) => {
    setStatus(value)
    const { formattedFrom, formattedTo } = getFormattedDateRange()
    fetchOrdersByFilter(value, formattedFrom, formattedTo)
  }
  const handleFromDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFromDate = e.target.value
    setFromDate(newFromDate)

    if (!newFromDate) {
      // From date cleared, don't send toDate
      fetchOrdersByFilter(status, '', '')
      return
    }

    const formattedFrom = formatDate(newFromDate)
    const formattedTo = toDate
      ? formatDate(toDate)
      : formatDate(new Date().toISOString().split('T')[0])

    fetchOrdersByFilter(status, formattedFrom, formattedTo)
  }

  const handleToDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newToDate = e.target.value
    setToDate(newToDate)

    if (!newToDate) {
      // To date cleared
      if (fromDate) {
        const formattedFrom = formatDate(fromDate)
        const formattedTo = formatDate(new Date().toISOString().split('T')[0]) // fallback to today
        fetchOrdersByFilter(status, formattedFrom, formattedTo)
      } else {
        fetchOrdersByFilter(status, '', '')
      }
      return
    }

    const formattedFrom = fromDate ? formatDate(fromDate) : '05/06/2020' // fallback if fromDate is not set
    const formattedTo = formatDate(newToDate)

    fetchOrdersByFilter(status, formattedFrom, formattedTo)
  }

  return (
    <div className="mt-4 flex w-full flex-wrap items-center justify-between gap-5 px-6">
      <div className="flex gap-2">
        {['ALL', 'SUCCESS', 'NEW_ORDER'].map((val) => (
          <Radio
            key={val}
            name="status"
            value={val}
            label={val === 'ALL' ? 'All' : val === 'SUCCESS' ? 'Success' : 'Failed'}
            selected={status}
            setSelected={handleRadioChange}
            classname={`rounded-full! ml-0! text-sm! font-medium! ${
              status === val ? '' : 'text-lucres-800!'
            }`}
          />
        ))}
      </div>

      <div className="relative flex gap-4">
        <input
          type="date"
          value={fromDate}
          onChange={handleFromDateChange}
          className="bg-lucres-gray-300 dark:bg-dark-lucres-black-300 rounded-md p-2"
        />
        <input
          type="date"
          value={toDate}
          onChange={handleToDateChange}
          className="bg-lucres-gray-300 dark:bg-dark-lucres-black-300 rounded-md p-2"
        />
      </div>
    </div>
  )
}

export default DateStatusFilter
