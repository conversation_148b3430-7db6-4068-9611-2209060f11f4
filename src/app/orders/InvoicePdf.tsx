import { Document, Page, View, Text, Image, StyleSheet } from '@react-pdf/renderer'
// import { formatToWeekdayMonthDay } from '../../utils/resumeUtils'
import { Order } from '../../models/User'
import { formatToWeekdayMonthDay } from '../../utils/resumeUtils'

interface InvoicePDFProps {
  order: Order
}

const styles = StyleSheet.create({
  page: {
    padding: 40,
    fontFamily: 'Helvetica',
    fontSize: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 30,
  },
  logo: {
    width: 85,
    height: 'auto',
  },
  date: {
    fontSize: 10,
  },
  title: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'medium',
    marginBottom: 8,
    color: '#000',
  },
  receipt: {
    textAlign: 'center',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 25,
    color: '#333',
  },
  section: {
    marginVertical: 30,
  },

  infoSection: {
    backgroundColor: '#f5f5f5',
    padding: 40,
    marginVertical: 30,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 14,
    color: '#333',
    width: 110,
    fontWeight: 'normal',
  },
  infoColon: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 15,
  },
  infoValue: {
    fontWeight: 'bold',
    fontSize: 14,
    color: '#000',
    flex: 1,
  },
  label: {
    fontSize: 11,
    marginBottom: 3,
    lineHeight: 1.4,
  },
  summary: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    marginTop: 20,
    color: '#000',
  },
  description: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  charge: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
    paddingVertical: 2,
  },
  chargeText: {
    fontSize: 12,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    paddingTop: 8,
    paddingBottom: 8,
    borderTopWidth: 1,
    borderTopColor: '#0000',
    borderBottomWidth: 1,
    borderBottomColor: '#0000',
  },

  totalText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  footer: {
    backgroundColor: '#B6E777',
    padding: 12,
    paddingHorizontal: 40,
    fontSize: 12,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    lineHeight: 1.6,
  },
})

const InvoicePDF = ({ order }: InvoicePDFProps) => {
  const currentDate = new Date().toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  })

  const fullName = [order.profile?.givenName, order.profile?.familyName].filter(Boolean).join(' ')

  const description =
    order.flow === 'SUBSCRIPTION'
      ? 'Lucres Career Launchpad Subscription'
      : order.flow === 'PROFILE' && fullName
        ? `Contact information of ${fullName}`
        : order.flow === 'PRO_RESUME'
          ? 'Pro Resume Download'
          : order.job
            ? `${order.job.title} at ${order.job.company}`
            : null

  const totalGst = order.amount.taxes.gst.amount
  const cgst = totalGst / 2
  const sgst = totalGst / 2

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.header}>
          <Image style={styles.logo} src="/common/logo.png" />
          <Text style={styles.date}>{currentDate}</Text>
        </View>
        <Text style={styles.title}>INVOICE</Text>
        <Text style={styles.receipt}>Receipt #{String(order.orderNo)}</Text>

        <View style={{ marginHorizontal: -40 }}>
          <View style={styles.infoSection}>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Amount</Text>
              <Text style={styles.infoColon}>:</Text>
              <Text style={styles.infoValue}>Rs. {order.amount.total}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Date Paid</Text>
              <Text style={styles.infoColon}>:</Text>
              <Text style={styles.infoValue}>{formatToWeekdayMonthDay(order.createdAt)}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Payment Method</Text>
              <Text style={styles.infoColon}>:</Text>
              <Text style={styles.infoValue}>
                {order.paymentMethod.trim().charAt(0).toUpperCase() +
                  order.paymentMethod.trim().slice(1)}
              </Text>
            </View>
          </View>
        </View>

        <View>
          <Text style={styles.summary}>Summary</Text>
          {description?.trim() && <Text style={styles.description}>{description}</Text>}

          <View style={styles.charge}>
            <Text style={styles.chargeText}>Amount</Text>
            <Text style={styles.chargeText}>Rs. {order.amount.subtotal}</Text>
          </View>
          <View style={styles.charge}>
            <Text style={styles.chargeText}>CGST (9%)</Text>
            <Text style={styles.chargeText}>Rs. {cgst}</Text>
          </View>
          <View style={styles.charge}>
            <Text style={styles.chargeText}>SGST (9%)</Text>
            <Text style={styles.chargeText}>Rs. {sgst}</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={styles.totalText}>Amount charged</Text>
            <Text style={styles.totalText}>Rs. {order.amount.total}</Text>
          </View>
        </View>
        <View style={styles.footer}>
          <Text style={{ fontWeight: 'bold' }}>Lucres Private Limited</Text>
          <Text>#11-14, 2nd floor, Shinde Complex, Neeligin Road, Hubli, Karnataka - 580029</Text>
          <Text style={{ fontWeight: 'bold' }}>GST IN : 29AACCF4125A1Z5</Text>
        </View>
      </Page>
    </Document>
  )
}

export default InvoicePDF
