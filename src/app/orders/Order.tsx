'use client'
import React, { useEffect, useState, useRef, useCallback } from 'react'
import { ArrowLeftIcon, FunnelIcon, MagnifyingGlassIcon } from '@phosphor-icons/react'
import OrderDetailsCard from './OrderDetailsCard'
import DateStatusFilter from './DateStatusFilter'
import Button from '../../components/Button'
import { UserService } from '../../services/UserService'
import { Order as orderType, Subscription } from '../../models/User'
import NoDataFound from '../../components/NoDataFound/NoDataFound'
import OrderCardSkeleton from './OrderCardSkelaton'
// import PostCardSkeleton from '../../components/PostCard/PostCardSketeton'
import useError from '../../context/ErrorContext'
import IconButton from '../../components/IconButton'
import Avatar from '../../components/Avatar/Avatar'
import { getFullName } from '../../utils/commonUtils'
import { useRouter } from 'next/navigation'
import ApplicationList from './ApplicationList'
import Credits from '../talent/Credits'
import Pricing from '../../components/Pricing'
import { useAuth } from '@/context/AuthContext'
import { useBodyScrollLock } from '@/utils/useBodyScrollLock'
interface ProfileImage {
  url: string
  name: string
}

export interface ApplicantDetail {
  id: string
  _id?: string
  username: string
  givenName: string
  familyName: string
  fullName: string
  profileImage?: ProfileImage
}

const Order: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState(false)
  const [expandedOrderId, setExpandedOrderId] = useState<string | null>(null)
  const [search, setSearch] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [status, setStatus] = useState<string>('ALL')
  const [fromDate, setFromDate] = useState<string>('')
  const [toDate, setToDate] = useState<string>('')
  const [showPricing, setShowPricing] = useState(false)
  const [page, setPage] = useState<number>(1)
  const [hasNextPage, setHasNextPage] = useState(false)
  const [orderDetails, setOrderDetails] = useState<orderType[]>([])
  const [showApplications, setShowApplications] = useState<boolean>(false)
  const [applications, setApplications] = useState<ApplicantDetail[]>([])
  const { handleError } = useError()
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [subscriptionId, setSubscriptionId] = useState('')
  const observer = useRef<IntersectionObserver | null>(null)
  const router = useRouter()
  const { authUserProfile, setAuthUserProfile } = useAuth()
  const lastPostElementRef = useCallback(
    (node: HTMLDivElement) => {
      if (loading) return
      if (observer.current) observer.current.disconnect()
      observer.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasNextPage) {
            setPage((prevPage) => prevPage + 1)
          }
        },
        { threshold: 0.5 },
      )
      if (node) observer.current.observe(node)
    },
    [loading, hasNextPage],
  )

  const toggleAccordion = (orderId: string) => {
    setExpandedOrderId((prev) => (prev === orderId ? null : orderId))
  }

  // ✅ Common function to load orders
  const loadOrders = async (
    pageNum: number,
    override: boolean = false,
    opts?: {
      searchOverride?: string
      statusOverride?: string
      fromDateOverride?: string
      toDateOverride?: string
    },
  ) => {
    setLoading(true)
    try {
      const res = await UserService.getOrderDetails(
        pageNum,
        opts?.searchOverride ?? search,
        (opts?.statusOverride ?? status) === 'ALL' ? '' : (opts?.statusOverride ?? status),
        opts?.fromDateOverride ?? fromDate,
        opts?.toDateOverride ?? toDate,
      )
      if (pageNum === 1 || override) {
        setOrderDetails(res.data.items)
      } else {
        setOrderDetails((prev) => [...prev, ...res.data.items])
      }
      setHasNextPage(res.data.paginator.hasNextPage)
    } catch (error: any) {
      handleError(error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearchClick = () => {
    setPage(1)
    loadOrders(1, true)
  }

  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setPage(1)
      loadOrders(1, true)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value)
    if (e.target.value === '') {
      loadOrders(1, true)
    }
  }

  useEffect(() => {
    if (page) {
      loadOrders(page, page === 1)
    }
  }, [page])

  const fetchOrdersByFilter = async (
    status: string,
    formattedFrom: string,
    formattedTo: string,
  ) => {
    // const statusToUse = status === 'ALL' ? '' : status
    const response = await UserService.getOrderDetails(
      1,
      search,
      status,
      formattedFrom,
      formattedTo,
    )
    setOrderDetails(response.data.items)
  }

  useEffect(() => {
    const fetchSubscriptions = async () => {
      try {
        const response = await UserService.getSubscriptions()
        setSubscriptions(response.data.items)
        response.data.items.map((item: any) => {
          // if (item.status === 'ACTIVE') {
          //   setHasActiveSubscription(true)
          // }
        })
      } catch (error: any) {
        if (error.data.message === 'SUBSCRIPTIONS_FIND_ERROR') {
          setSubscriptions([])
        }
      }
    }
    fetchSubscriptions()
  }, [])

  const handlePurchaseSuccess = async () => {
    if (authUserProfile) {
      const authUser = await UserService.getUserProfileByPermalink(authUserProfile.username)
      setAuthUserProfile(authUser.data)
    }
  }
  useBodyScrollLock(showPricing)
  return (
    <>
      {loading && orderDetails.length === 0 ? (
        <div className="flex flex-col gap-4">
          {[...Array(2)].map((_, idx) => (
            <OrderCardSkeleton key={idx} />
          ))}
        </div>
      ) : (
        <>
          {showApplications ? (
            <section className="font-inter flex h-full w-full items-center justify-center lg:py-0">
              <div className="flex w-full max-w-[1440px] flex-col gap-4 px-6 pt-6">
                <div
                  className="flex cursor-pointer items-center gap-4"
                  onClick={() => {
                    setApplications([])
                    setExpandedOrderId('')
                    setShowApplications(false)
                  }}
                >
                  <IconButton className="dark:bg-dark-lucres-black-500 w-fit bg-white">
                    <ArrowLeftIcon
                      size={24}
                      className="text-lucres-900 dark:text-dark-lucres-green-100"
                    />
                  </IconButton>
                  <h3 className="text-lucres-gray-700 hover:text-lucres-600 dark:text-lucres-300 dark:hover:text-lucres-500 font-medium">
                    Back
                  </h3>
                </div>
                <div className="text-lucres-black dark:text-lucres-green-100 font-semibold">
                  Applicants
                </div>
                <div className="flex  w-full gap-2">
                  {applications.length === 0 ? (
                    <div className="w-full">
                      <ApplicationList subscriptionId={subscriptionId} />
                    </div>
                  ) : (
                    <>
                      {applications.map((item) => (
                        <div
                          key={item._id}
                          className="dark:hover:bg-dark-lucres-black-400 flex w-1/2 items-center gap-2 rounded-lg p-2 hover:bg-gray-100"
                        >
                          <Avatar
                            src={item.profileImage?.url}
                            alt={item.profileImage?.name}
                            size={12}
                            className="min-w-10"
                          />
                          <div className="flex flex-col gap-0">
                            <span
                              onClick={() => router.push(`/${item.username}`)}
                              className="text-lucres-black dark:text-dark-lucres-green-100 cursor-pointer text-sm font-semibold hover:underline md:text-lg"
                            >
                              {getFullName(item.givenName, item.familyName)}
                            </span>
                            <span className="text-lucres-gray-700 dark:text-lucres-gray-400 -mt-1">
                              @{item.username}
                            </span>
                          </div>
                        </div>
                      ))}
                    </>
                  )}
                </div>
              </div>
            </section>
          ) : (
            <section className="font-inter flex h-full w-full items-center justify-center lg:py-0">
              <div className="flex w-full max-w-[1440px] flex-col gap-4 pt-6 lg:px-0">
                <div className="w-full">
                  <div className="mb-4 flex w-full items-center justify-between px-6">
                    <div className="text-lucres-black dark:text-lucres-green-100 font-semibold">
                      Order Details
                    </div>
                    {authUserProfile?.subscription?.status === 'ACTIVE' && (
                      <div className="flex items-center gap-2">
                        <Credits
                          totalcredits={authUserProfile?.subscription?.resumesTotal}
                          credits={authUserProfile?.subscription?.resumesRemaining}
                          sqSize={16}
                          strokeWidth={4}
                          creditsLeftRequired={false}
                          classes=" text-base !gap-1"
                        />
                        <Button
                          theme="transparent"
                          size="extraSmall"
                          isRectangle
                          className="!text-lucres-green-300 dark:!text-dark-lucres-green-600 !border-none "
                          onClick={() => {
                            setShowPricing(true)
                          }}
                        >
                          Upgrade
                        </Button>
                      </div>
                    )}
                    <div className="flex items-center justify-between gap-4">
                      <div className="dark:border-dark-lucres-black-200 flex w-40 items-center rounded-full border px-1 py-1 sm:w-52">
                        <input
                          type="text"
                          placeholder="Enter Title"
                          value={search}
                          onChange={handleInputChange}
                          onKeyDown={handleSearchKeyDown}
                          className="dark:bg-dark-lucres-black-500 !w-5 flex-grow px-6 text-sm outline-none"
                        />
                        <Button
                          theme="white"
                          size="small"
                          className="rounded-full !p-2"
                          onClick={handleSearchClick}
                        >
                          <MagnifyingGlassIcon size={20} />
                        </Button>
                      </div>
                      <button
                        className="dark:!border-dark-lucres-black-200 flex h-10 w-10 items-center justify-center !rounded-full border !border-gray-200"
                        onClick={() => setActiveFilter(!activeFilter)}
                      >
                        <FunnelIcon
                          size={20}
                          className="text-lucres-900 dark:text-dark-lucres-green-100"
                        />
                      </button>
                    </div>
                  </div>

                  {activeFilter && (
                    <DateStatusFilter
                      status={status}
                      setStatus={setStatus}
                      fromDate={fromDate}
                      toDate={toDate}
                      setFromDate={setFromDate}
                      setToDate={setToDate}
                      fetchOrdersByFilter={fetchOrdersByFilter}
                    />
                  )}

                  <div className="mt-4">
                    {orderDetails.length > 0 ? (
                      orderDetails.map((order) => (
                        <div key={order.id}>
                          <OrderDetailsCard
                            order={order}
                            isExpanded={expandedOrderId === order.id}
                            toggleAccordion={toggleAccordion}
                            showApplications={showApplications}
                            setShowApplications={setShowApplications}
                            setApplications={setApplications}
                            subscriptions={subscriptions}
                            setSubscriptionId={setSubscriptionId}
                            setShowPricing={setShowPricing}
                          />
                        </div>
                      ))
                    ) : (
                      <div className="max-w-9/12 mx-auto mb-8 py-4 text-center text-xl font-semibold">
                        <NoDataFound />
                      </div>
                    )}
                  </div>
                  {hasNextPage && (
                    <div
                      ref={lastPostElementRef}
                      className="flex w-full flex-col items-center justify-center pb-4"
                    >
                      <div className="flex w-full flex-col gap-4">
                        {[...Array(3)].map((_, idx) => (
                          <OrderCardSkeleton key={idx} />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
              {showPricing && (
                <div className="z-999! fixed left-0 top-0 h-screen w-screen">
                  <Pricing
                    handleClose={() => setShowPricing(false)}
                    flow="TALENT"
                    handleSuccess={handlePurchaseSuccess}
                  />
                </div>
              )}
            </section>
          )}
        </>
      )}
    </>
  )
}

export default Order
