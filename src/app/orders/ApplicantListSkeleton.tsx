const ApplicantTableSkeleton = () => {
  return (
    <table className="w-full table-fixed border-collapse text-left">
      <tbody>
        {[...Array(2)].map((_, index) => (
          <tr key={index} className="animate-pulse">
            <td className="mb-4 flex items-center gap-2 py-2">
              <div className="dark:bg-dark-lucres-black-200 h-10 w-10 rounded-full bg-gray-300" />
              <div className="flex flex-col gap-1">
                <div className="dark:bg-dark-lucres-black-200 h-4 w-32 rounded-sm bg-gray-300" />
                <div className="dark:bg-dark-lucres-black-200 block h-3 w-24 rounded-sm bg-gray-300 sm:hidden" />
              </div>
            </td>
            <td className="hidden py-2 md:table-cell">
              <div className="dark:bg-dark-lucres-black-200 h-4 w-40 rounded-sm bg-gray-300" />
            </td>
            <td className="py-2">
              <div className="dark:bg-dark-lucres-black-200 h-4 w-40 rounded-sm bg-gray-300" />
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  )
}

export default ApplicantTableSkeleton
