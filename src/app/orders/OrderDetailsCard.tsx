import React, { useState } from 'react'
import { Order, Subscription } from '../../models/User'
import { formatToReadableDate, truncateText } from '../../utils/commonUtils'
import Button from '../../components/Button'
import Credits from '../talent/Credits'
import { pdf } from '@react-pdf/renderer'
import InvoicePDF from './InvoicePdf'
import { ApplicantDetail } from './Order'
import {
  CaretDownIcon,
  CaretUpIcon,
  CurrencyInrIcon,
  DownloadSimpleIcon,
} from '@phosphor-icons/react'
// import { Order } from './Order'

interface OrderDetailsCardProps {
  order: Order
  isExpanded: boolean
  toggleAccordion: (orderId: string) => void
  showApplications: boolean
  setShowApplications: (show: boolean) => void
  setApplications: (apps: ApplicantDetail[]) => void
  subscriptions: Subscription[]
  setSubscriptionId: (id: string) => void
  setShowPricing: (value: boolean) => void
}

const OrderDetailsCard: React.FC<OrderDetailsCardProps> = ({
  order,
  isExpanded,
  toggleAccordion,
  showApplications,
  setShowApplications,
  setApplications,
  subscriptions,
  setSubscriptionId,
  setShowPricing,
}) => {
  // const [showApplications, setShowApplications] = useState<boolean>(false)
  const totalGst = order.amount.taxes.gst.amount
  const cgst = totalGst / 2
  const sgst = totalGst / 2

  const handleDownloadInvoice = async () => {
    try {
      const blob = await pdf(<InvoicePDF order={order} />).toBlob()

      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `invoice_${order.orderNo}.pdf` // ✅ Correct usage
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to download invoice:', error)
    }
  }
  return (
    <div>
      <div
        onClick={() => order.id && toggleAccordion(order.id)}
        className="text-lucres-gray-700 dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 dark:text-dark-lucres-green-100 dark:hover:bg-dark-lucres-black-400 flex h-20 w-full cursor-pointer items-center justify-between gap-2 border-b px-4 text-xs hover:bg-gray-100 md:text-sm dark:border"
      >
        <div className="flex  w-[25%] flex-col items-start">
          {/* <img src={user.profileImage} alt="profile image" className="h-auto w-8" /> */}
          <div className="text-lucres-gray-700 dark:text-dark-lucres-green-100 flex flex-col">
            {order.flow === 'SUBSCRIPTION' ? (
              <div>
                <span className="text-xs font-semibold sm:text-base">
                  {order.subscription?.plan === 'TRIAL_5'
                    ? 'Starter'
                    : order.subscription?.plan === 'ENTERPRISE'
                      ? 'Enterprise'
                      : 'Startup'}{' '}
                  Plan
                </span>
                {order.status === 'SUCCESS' && (
                  <div className="text-lucres-gray-500 dark:text-dark-lucres-green-100 mt-1 text-xs font-normal">
                    <Credits
                      totalcredits={order?.subscription?.resumesTotal}
                      credits={order?.subscription?.resumesRemaining}
                      sqSize={16}
                      strokeWidth={4}
                      classes="sm:flex-row-reverse !text-xs sm:text-base !items-start !sm:items-center !gap-0.5 sm:!gap-2"
                    />
                  </div>
                )}
              </div>
            ) : order.flow === 'JOB_DASHBOARD' ? (
              <span className="text-xs font-semibold sm:text-base">
                <span className="block">{truncateText(order?.job?.title ?? '', 40).text}</span>
              </span>
            ) : order.flow === 'PRO_RESUME' ? (
              <span className="text-xs font-semibold sm:text-base">
                <span className="block">Pro Resume</span>
              </span>
            ) : (
              <span className="text-sm font-semibold sm:text-base">
                {order.profile?.givenName}'s contact
              </span>
            )}
          </div>
          {order.flow === 'JOB_DASHBOARD' && (
            <div className="text-lucres-gray-500 text-xs">
              Applications: {order.job?.applicationsCount}
            </div>
          )}
        </div>
        <div
          className={`text-lucres-gray-700 dark:text-dark-lucres-green-100 flex w-[10%]  items-center`}
        >
          <CurrencyInrIcon weight="bold" className="text-sm" />
          {/* <CurrencyInr size={12} weight="bold" className="block sm:hidden" /> */}
          {order.amount.total}
        </div>
        <div
          className={`text-lucres-gray-700 dark:text-dark-lucres-green-100 flex w-[12%]  items-center`}
        >
          {order.status === 'SUCCESS' ? 'Processed' : 'Processing'}
        </div>
        <div className="flex w-[30%]  items-center justify-end gap-2 text-sm md:gap-5">
          <div dir="rtl">
            <div className="hidden sm:block">Order No. {order.orderNo}</div>
            <div className="flex text-xs sm:hidden"> {order.orderNo}</div>
            <div>
              <span className="inline text-xs sm:hidden sm:text-sm">
                {formatToReadableDate(order.createdAt, false)}
              </span>
              <span className="hidden text-sm sm:inline">
                {formatToReadableDate(order.createdAt, true)}
              </span>
            </div>
            <div>
              {/* Subscription Expiry Date */}
              {order.flow === 'SUBSCRIPTION' && order?.subscription?.status === 'ACTIVE' && (
                <div key={order.subscription?.id}>
                  <div className="text-lucres-gray-500 dark:text-dark-lucres-green-100 hidden whitespace-nowrap text-xs sm:flex">
                    Exp. {formatToReadableDate(order?.subscription?.expiresAt, true)}
                  </div>
                  <div
                    key={order.subscription?.id}
                    className="text-lucres-gray-500 dark:text-dark-lucres-green-100 flex whitespace-nowrap text-xs  sm:hidden"
                  >
                    Exp. {formatToReadableDate(order.subscription?.expiresAt, false)}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        <div>
          {isExpanded ? (
            <CaretUpIcon size={24} className="w-4 cursor-pointer md:w-6" />
          ) : (
            <CaretDownIcon
              size={24}
              className="w-4 cursor-pointer md:w-6"
              // onClick={() => order.id && toggleAccordion(order.id)}
            />
          )}
        </div>
      </div>
      {isExpanded && (
        <div className="dark:border-y-dark-lucres-black-300 w-full gap-8 border-y text-sm sm:flex">
          <div className="text-lucres-gray-700 dark:text-dark-lucres-green-100 flex w-full flex-col gap-2 p-4">
            <div
              className={`flex w-full ${
                (order.flow === 'JOB_DASHBOARD' || order.flow === 'SUBSCRIPTION') &&
                order.status === 'SUCCESS'
                  ? 'justify-between'
                  : 'justify-start'
              }`}
            >
              <div className={`flex flex-col items-center justify-start gap-2 text-xs`}>
                {order.flow === 'JOB_DASHBOARD' && order.status === 'SUCCESS' && (
                  <div className="flex w-full items-center">
                    <Button
                      theme="transparent"
                      size="extraSmall"
                      isRectangle
                      className={`${
                        order.status === 'SUCCESS' ? 'cursor-pointer' : ''
                      } text-lucres-green-300`}
                      onClick={() => {
                        if (order.status !== 'SUCCESS') return
                        const applicants = order.job?.applicantsDetails ?? []
                        setApplications(applicants)
                        setShowApplications(true)
                      }}
                    >
                      View Applications
                    </Button>
                  </div>
                )}
                {order.flow === 'SUBSCRIPTION' && order.status === 'SUCCESS' && (
                  <div className="flex w-full items-center gap-2">
                    <Button
                      theme="transparent"
                      size="extraSmall"
                      isRectangle
                      className={`${
                        order.status === 'SUCCESS' ? 'cursor-pointer' : ''
                      } text-lucres-green-300`}
                      onClick={() => {
                        if (order.subscription?.id) {
                          setSubscriptionId(order.subscription?.id)
                        }
                        setShowApplications(true)
                      }}
                    >
                      View Applications
                    </Button>
                    <Button
                      theme="transparent"
                      size="extraSmall"
                      isRectangle
                      onClick={() => {
                        setShowPricing(true)
                      }}
                    >
                      Upgrade
                    </Button>
                  </div>
                )}
              </div>
              <div className="text-lucres-gray-700 dark:text-dark-lucres-green-100 h-fit w-full max-w-xs rounded-lg p-2 text-xs sm:pb-0">
                <div className="mb-1 flex w-full items-center justify-between">
                  <div>Sub Total On Order Items</div>
                  <div>Rs. {order.amount.subtotal}</div>
                </div>
                <div className="my-1 flex w-full items-center justify-between">
                  <div>CGST (9%)</div>
                  <div>Rs. {cgst}</div>
                </div>
                <div className="my-1 flex w-full items-center justify-between">
                  <div>SGST (9%)</div>
                  <div>Rs. {sgst}</div>
                </div>
                <div className="dark:border-t-dark-lucres-black-300 flex w-full items-center justify-between border-t pb-2 pt-1 font-semibold">
                  <div>Total</div>
                  <div>Rs. {order.amount.total}</div>
                </div>
              </div>
            </div>
            {order.status === 'SUCCESS' && (
              <div className="dark:border-t-dark-lucres-black-300 flex w-full items-center justify-between border-t pt-4">
                <div className="text-lucres-gray-500 font-medium">
                  Payment Method{' '}
                  <span className="text-lucres-gray-700 ps-2">{order.paymentMethod}</span>
                </div>
                <div
                  className="hover:text-lucres-gray-500 flex cursor-pointer items-center gap-1"
                  onClick={handleDownloadInvoice}
                >
                  <DownloadSimpleIcon size={18} /> Download Invoice
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default OrderDetailsCard
