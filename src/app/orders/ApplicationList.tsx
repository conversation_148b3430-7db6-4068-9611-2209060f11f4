'use client'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { UserService } from '../../services/UserService'
import { ApplicationList as List } from '../../models/User'
import Avatar from '../../components/Avatar/Avatar'
import { formatToReadableDate, getFullName } from '../../utils/commonUtils'
import ApplicantListSkeleton from './ApplicantListSkeleton'
import { useRouter } from 'next/navigation'
const ApplicationList = ({ subscriptionId }: any) => {
  const [applicantLoading, setApplicantLoading] = useState<boolean>(true)
  const [page, setPage] = useState(1)
  const [hasNextPage, setHasNextPage] = useState(false)
  const observer = useRef<IntersectionObserver | null>(null)
  const [applicants, setApplicants] = useState<List[]>([])
  const router = useRouter()
  const fetchApplicants = async (page: number) => {
    try {
      const response = await UserService.getApplicantList(subscriptionId, page)
      setApplicants((prev) => [...prev, ...response.data.items])
      setHasNextPage(response.data.paginator.hasNextPage)
    } catch (error) {
      //  handleError(error)
    } finally {
      setApplicantLoading(false)
    }
  }
  useEffect(() => {
    fetchApplicants(1)
  }, [])

  useEffect(() => {
    if (page > 1) {
      fetchApplicants(page)
    }
  }, [page])
  const lastPostElementRef = useCallback(
    (node: HTMLDivElement) => {
      if (applicantLoading) return
      if (observer.current) observer.current.disconnect()
      observer.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasNextPage) {
            setPage((prevPage) => prevPage + 1)
          }
        },
        { threshold: 0.7 },
      )
      if (node) observer.current.observe(node)
    },
    [applicantLoading, hasNextPage],
  )
  return (
    <div className="text-lucres-black w-full">
      {applicantLoading && applicants.length === 0 ? (
        <div className="w-full">
          <ApplicantListSkeleton />
        </div>
      ) : (
        <div className="flex w-full flex-col items-center justify-center ">
          <div className="w-full  ">
            <div className="w-full">
              {applicants.map((item, index) => (
                <div
                  key={index}
                  className="dark:hover:bg-dark-lucres-black-400 w-full hover:bg-gray-100"
                >
                  <div className="flex w-full justify-between p-2">
                    <div className="w-[35%]">
                      <div className="flex gap-2">
                        <Avatar
                          src={item.profileAuthor.profileImage.url}
                          alt={item.profileAuthor.profileImage.name}
                          size={10}
                          className="min-w-10"
                        />

                        <div className="flex flex-col">
                          <span
                            className="text-lucres-black dark:text-dark-lucres-green-100 cursor-pointer text-sm font-semibold hover:underline md:text-lg"
                            onClick={() => router.push(`/${item.profileAuthor.username}`)}
                          >
                            {getFullName(
                              item.profileAuthor.givenName,
                              item.profileAuthor.familyName,
                            )}
                          </span>
                          <span className="text-lucres-gray-700 dark:text-lucres-gray-400 block text-xs sm:hidden">
                            {item.profileAuthor.headline}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="text-lucres-black dark:text-dark-lucres-green-100 hidden w-[30%] text-center md:table-cell">
                      {item.profileAuthor.headline}
                    </div>

                    <div className="text-lucres-black dark:text-dark-lucres-green-100 hidden w-[30%] text-right sm:block">
                      {formatToReadableDate(item.createdAt, true)}
                    </div>
                    <div className="text-lucres-black dark:text-dark-lucres-green-100 block w-[30%] text-right sm:hidden">
                      {formatToReadableDate(item.createdAt, false)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {hasNextPage && (
            <div
              ref={lastPostElementRef}
              className="flex w-full flex-col items-center justify-center"
            >
              <div className="flex w-full items-center">
                <ApplicantListSkeleton />
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default ApplicationList
