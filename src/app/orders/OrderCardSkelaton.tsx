const OrderCardSkeleton = () => {
  return (
    <div>
      <div className="bg-lucres-gray-100 dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 flex h-20 w-full animate-pulse items-center justify-between border-b text-xs sm:px-4 dark:border">
        <div className="flex w-2/5 flex-col gap-2">
          <div className="dark:bg-dark-lucres-black-300 h-4 w-3/4 rounded-sm bg-gray-300" />
          <div className="dark:bg-dark-lucres-black-300 h-3 w-1/2 rounded-sm bg-gray-300" />
        </div>
        <div className="dark:bg-dark-lucres-black-300 h-4 w-20 rounded-sm bg-gray-300" />
        <div className="flex w-48 flex-col items-end gap-2">
          <div className="dark:bg-dark-lucres-black-300 h-3 w-24 rounded-sm bg-gray-300" />
          <div className="dark:bg-dark-lucres-black-300 h-3 w-20 rounded-sm bg-gray-300" />
        </div>
        <div className="dark:bg-dark-lucres-black-300 h-6 w-6 rounded-full bg-gray-300" />
      </div>
    </div>
  )
}

export default OrderCardSkeleton
