'use client'
import React, { useRef, useState } from 'react'
import Input from '../../components/Input'
import { FormData } from './page'
import ImageCropModal from '../../components/ImageCropModal'
import { handleFileChange } from '../../utils/imageUtils'
import Toast, { ToastData, ToastProps } from '../../components/Toast'
import Avatar from '../../components/Avatar/Avatar'
import { useAuth } from '../../context/AuthContext'
import Button from '../../components/Button'

interface CreateComapnyDetailsProps {
  onContinue: () => void
  formData: FormData
  formErrors: Partial<Record<keyof FormData, string>>
  onInputChange: (name: keyof FormData, value: any) => void
  onBlur: (name: keyof FormData) => void
}
/* This was the last step before but now since we have added (Questions Comp) hence adding onContinue */
const CompanyDetails: React.FC<CreateComapnyDetailsProps> = ({
  formData,
  formErrors,
  onInputChange,
  onBlur,
  onContinue,
}) => {
  const { authUserProfile } = useAuth()
  const fileInputRef = useRef<HTMLInputElement | null>(null)

  const [isCropModalOpen, setIsCropModalOpen] = useState(false)
  const [imageSrc, setImageSrc] = useState<string | null>(null)
  const [isPhotoUpdated, setIsPhotoUpdated] = useState(false)
  const [toasts, setToasts] = useState<ToastData[]>([])
  const [toastPosition, setToastPosition] = useState<ToastProps['position']>('bottom-right')
  const [tempCroppedImage, setTempCroppedImage] = useState<File | null>(null)

  const handleShowToast = (
    type: 'success' | 'info' | 'error' | 'loading',
    message: string,
    timeout: number = 3000,
  ): number => {
    const id = Date.now()
    const newToast = { id, type, message, timeout }
    setToasts((prev) => [...prev, newToast])

    if (type !== 'loading') {
      setTimeout(() => {
        removeToast(id)
      }, timeout)
    }

    return id
  }

  const removeToast = (id: number) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id))
  }

  const handlePhotoUpdate = () => {
    fileInputRef.current?.click()
  }

  const onFileSelected = (image: string) => {
    setImageSrc(image)
    setIsPhotoUpdated(true)
    setIsCropModalOpen(true)
  }

  const onCropSuccess = (croppedFile: File) => {
    // Create a temporary URL for the cropped image to display in Avatar
    const imageUrl = URL.createObjectURL(croppedFile)
    // Store both the file and URL
    onInputChange('companyLogo', {
      file: croppedFile,
      url: imageUrl,
    })
    setIsPhotoUpdated(false)
    setIsCropModalOpen(false)
  }

  const onCropError = (error: string) => {
    console.error('Error:', error)
    handleShowToast('error', error)
    setIsPhotoUpdated(false)
  }

  // Get the image URL to display in Avatar - either the temporary cropped image or the existing profile image
  const getDisplayImage = () => {
    if (formData.companyLogo?.url) {
      return formData.companyLogo.url
    }
    return authUserProfile?.profileImage.url
  }

  return (
    <>
      {isCropModalOpen && imageSrc && (
        <ImageCropModal
          type="profile"
          image={imageSrc}
          onCropComplete={onCropSuccess}
          onClose={() => (setIsCropModalOpen(false), setIsPhotoUpdated(false))}
        />
      )}
      <Toast toasts={toasts} removeToast={removeToast} position={toastPosition} />

      <div className="mx-auto w-full max-w-2xl space-y-8 py-4">
        <div className="mb-10 flex items-start gap-5">
          <div className="flex items-center gap-4 sm:w-1/2">
            <Avatar src={getDisplayImage()} alt="profile" size={20} className="cursor-pointer" />
            <Button
              size="small"
              isRectangle
              className="bg-lucres-gray-100! dark:bg-dark-lucres-black-300! flex-1"
              onClick={handlePhotoUpdate}
              disabled={isPhotoUpdated}
              type="button"
            >
              Upload Photo
            </Button>
            <input
              type="file"
              ref={fileInputRef}
              style={{ display: 'none' }}
              onChange={(e) => handleFileChange(e, onFileSelected, handleShowToast)}
              accept="image/*"
            />
          </div>
        </div>
        {/* Company name */}
        <div>
          <Input
            placeholder="Company name"
            label="Company Name"
            name="companyName"
            autoComplete="off"
            value={formData.companyName}
            onChange={(e) => onInputChange('companyName', e.target.value)}
            onBlur={() => onBlur('companyName')}
            error={formErrors.companyName}
            theme="gray"
            className="w-fit text-base tracking-wider"
          />
        </div>
        {/* Website */}
        <div>
          <Input
            placeholder="https://example.com"
            label="Company Website"
            name="website"
            autoComplete="off"
            value={formData.website}
            onChange={(e) => onInputChange('website', e.target.value)}
            onBlur={() => onBlur('website')}
            error={formErrors.website}
            theme="gray"
            className="w-fit text-base tracking-wider"
          />
        </div>
      </div>
    </>
  )
}

export default CompanyDetails
