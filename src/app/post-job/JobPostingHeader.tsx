import { useRouter } from 'next/navigation'
import Stepper from '../../components/Stepper'
import Button from '../../components/Button'
import { ArrowLeftIcon } from '@phosphor-icons/react'
interface Step {
  label: string
  isNew?: boolean
}

interface JobPostingHeaderProps {
  steps: Step[]
  currentStep: number
  onStepChange: (step: number) => void
  onPublish: () => void
  isEditMode?: boolean
}

const JobPostingHeader: React.FC<JobPostingHeaderProps> = ({
  steps,
  currentStep,
  onStepChange,
  onPublish,
  isEditMode = false,
}) => {
  const router = useRouter()
  const isLastStep = currentStep === steps.length - 1

  return (
    <header className="border-b-lucres-300 dark:border-dark-lucres-black-200 flex min-w-full flex-col items-center border-b px-4 py-4 sm:py-6 sm:pb-3">
      <div className="m-auto mt-2 flex w-full max-w-2xl flex-col gap-4 sm:mt-0 sm:gap-6">
        {/* Top Section: Back Button and Actions */}
        <div className="flex w-full items-center justify-between">
          {/* Back Button */}
          <button
            className="flex items-center gap-2 transition-colors hover:opacity-70"
            onClick={() => router.back()}
          >
            <ArrowLeftIcon size={18} />
            <h3 className="flex items-center text-sm font-medium sm:text-[15px]">Back</h3>
          </button>
          {/* Action Buttons */}
          <div className="flex gap-3 sm:gap-4">
            {/* <Button
              // className="font-medium rounded-lg opacity-20!  dark:opacity-70! px-3! py-2! md:px-4! md:py-3! "
              size="small"
              theme="transparent"
              isRectangle
              disabled={isLastStep}
            >
              Save Draft
            </Button> */}

            <Button
              type="submit"
              isRectangle
              className={`border-none hover:opacity-100`}
              size="small"
              disabled={!isLastStep}
              onClick={onPublish}
            >
              {isEditMode ? 'Update' : 'Publish'}
            </Button>
          </div>
        </div>
        {/* Bottom Section: Title and Stepper */}
        <div className="grid gap-3 sm:gap-4">
          <h2 className="text-lucres-900 dark:text-dark-lucres-green-100 text-center text-2xl font-medium sm:text-start">
            {isEditMode ? 'Edit Job Posting' : 'New Job Posting'}
          </h2>
          {/* Stepper Component */}
          <div className="flex gap-4">
            <Stepper steps={steps} currentStep={currentStep} onStepChange={onStepChange} />
          </div>
        </div>
      </div>
    </header>
  )
}

export default JobPostingHeader
