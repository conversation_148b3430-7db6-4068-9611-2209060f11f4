'use client'
import { useEffect, useMemo, useState } from 'react'
import Editor, {
  Toolbar,
  BtnBold,
  BtnItalic,
  BtnBulletList,
  BtnNumberedList,
  BtnUnderline,
} from 'react-simple-wysiwyg'
import Input from '../../components/Input'
import Modal from '../../components/Modal'
import AISuggestionsButton from '../../components/AISuggestionsButton'
import {
  ArrowsClockwiseIcon,
  CheckCircleIcon,
  CircleDashedIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  XIcon,
} from '@phosphor-icons/react'
import { FormData } from './page'
import { truncateText } from '../../utils/commonUtils'
import { AiService } from '../../services/AiService'
import { useToast } from '../../components/ToastX'
import SafeHtml from '../../components/SafeHtml'

interface JobReqProps {
  formData: FormData
  formErrors: Partial<Record<keyof FormData, string>>
  onInputChange: (name: keyof FormData, value: any) => void
  onBlur: (name: keyof FormData) => void
  onContinue: () => void
  selectedSuggestions: string[]
  onSuggestionSelect: (suggestion: string) => void
  suggestedDescriptions: string[]
  skillsSuggestions: string[]
  isFetchingSkillsSuggestions: boolean
  isFetchingJobDescSuggestions: boolean
  fetchSuggestions: (section?: 'jobDesc' | 'jobSkills') => Promise<void>
}

const JobRequirement: React.FC<JobReqProps> = ({
  onContinue,
  formData,
  formErrors,
  onInputChange,
  onBlur,
  selectedSuggestions,
  onSuggestionSelect,
  suggestedDescriptions,
  skillsSuggestions,
  isFetchingSkillsSuggestions,
  isFetchingJobDescSuggestions,
  fetchSuggestions,
}) => {
  const [currentSkill, setCurrentSkill] = useState('')
  const [isJobDescriptionModalOpen, setIsJobDescriptionModalOpen] = useState(false)
  const [isScreenSmall, setIsScreenSmall] = useState(false)
  const toast = useToast()

  const requiredSkills = formData.requiredSkills

  useEffect(() => {
    const mediaQuery = window.matchMedia('(max-width: 768px)')
    setIsScreenSmall(mediaQuery.matches)

    const handleResize = () => setIsScreenSmall(mediaQuery.matches)
    mediaQuery.addEventListener('change', handleResize)

    return () => {
      mediaQuery.removeEventListener('change', handleResize)
    }
  }, [])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onContinue()
  }
  const handleAddSkill = (skill: string) => {
    if (!formData.requiredSkills.includes(skill)) {
      onInputChange('requiredSkills', [...formData.requiredSkills, skill])
    }
  }

  const handleDeleteSkill = (skill: string) => {
    const updated = formData.requiredSkills.filter((s: string) => s !== skill)
    onInputChange('requiredSkills', updated)
  }

  const handleJobDescriptionChange = (e: any) => {
    onInputChange('jobDescription', e.target.value)
  }

  const handleJobDescriptionBlur = () => {
    onBlur('jobDescription')
  }

  const handleSuggestionClick = (suggestion: string) => {
    // Only add if not already selected
    if (!selectedSuggestions.includes(suggestion)) {
      // Append text to editor
      const currentText = formData.jobDescription
      const newText = currentText ? `${currentText} ${suggestion}` : suggestion
      onInputChange('jobDescription', newText)
      onSuggestionSelect(suggestion)
    }
  }

  const handleRegenerateSuggestions = () => {
    if (formData.jobTitle) {
      fetchSuggestions('jobDesc')
    } else {
      toast.error('Please enter a job title first')
    }
  }

  const getTextFromHtml = (html: string) => {
    const parser = new DOMParser()
    const doc = parser.parseFromString(html, 'text/html')
    return doc.body.textContent || ''
  }

  const wordCount = useMemo(() => {
    const text = getTextFromHtml(formData.jobDescription)
    return text.trim().split(/\s+/).filter(Boolean).length
  }, [formData.jobDescription])

  return (
    <form className="mx-auto w-full max-w-full space-y-8 py-4 md:max-w-2xl" onSubmit={handleSubmit}>
      {/* Required Skills Input */}
      <div className="space-y-4">
        <div className="relative md:w-1/2">
          <div className="absolute right-3 top-11 z-10 flex items-center">
            <MagnifyingGlassIcon size={18} />
          </div>
          <Input
            label="Required Skills"
            placeholder="UI/UX Designer"
            name="requiredskills"
            autoComplete="off"
            className="pr-5 text-base tracking-wider"
            value={currentSkill}
            onChange={(e) => setCurrentSkill(e.target.value)}
            theme="gray"
            error={formErrors.requiredSkills}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault()
                if (currentSkill.trim()) {
                  handleAddSkill(currentSkill.trim())
                  setCurrentSkill('')
                }
              }
            }}
          />
        </div>

        {requiredSkills.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {requiredSkills.map((skill: string, index: number) => (
              <div
                key={index}
                className="bg-lucres-200 dark:bg-dark-lucres-black-200 flex items-center gap-2 rounded-md p-2 px-4"
              >
                <span className="whitespace-nowrap text-sm">{truncateText(skill, 15).text}</span>
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleDeleteSkill(skill)
                  }}
                >
                  <XIcon size={14} />
                </button>
              </div>
            ))}
          </div>
        )}

        <div className="grid gap-2">
          <label className="dark:text-dark-lucres-green-300 block text-sm font-medium">
            Suggested by AI:
          </label>
          <div className="flex flex-wrap gap-4">
            {isFetchingSkillsSuggestions ? (
              <div className="flex items-center justify-center py-4">
                <div className="border-lucres-600 h-6 w-6 animate-spin rounded-full border-2 border-t-transparent"></div>
              </div>
            ) : skillsSuggestions.length > 0 ? (
              skillsSuggestions.map((skill) => (
                <AISuggestionsButton
                  key={skill}
                  icon={<PlusIcon className="h-4 w-4" />}
                  theme="primary"
                  size="medium"
                  onClick={() => handleAddSkill(skill)}
                >
                  {skill}
                </AISuggestionsButton>
              ))
            ) : (
              <p className="text-sm text-gray-500">No skill suggestions available</p>
            )}
          </div>
        </div>
      </div>

      {/* Job Description Section */}
      <div className="relative mt-3 grid max-w-2xl gap-2">
        <label className="dark:text-dark-lucres-green-300 block text-sm font-medium">
          Job Description:
        </label>

        {/* Job Description - Desktop */}
        {!isScreenSmall && (
          <>
            <div className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 relative h-52 max-w-2xl rounded-lg border border-gray-200 px-4 pb-4">
              <Editor
                value={formData.jobDescription}
                onChange={handleJobDescriptionChange}
                onBlur={handleJobDescriptionBlur}
                placeholder="Write a compelling job description..."
                containerProps={{
                  style: {
                    border: 'none',
                    background: 'transparent',
                  },
                }}
                className="scrollbar-none placeholder-lucres-300 dark:bg-dark-lucres-black-500 dark:text-dark-lucres-green-100 dark:placeholder-lucres-800 overflow-scroll! focus:outline-hidden h-32 w-full max-w-2xl resize-none whitespace-pre-wrap text-sm placeholder:text-sm [&_ol]:list-decimal [&_ol]:pl-5 [&_ul]:list-disc [&_ul]:pl-5"
              >
                <Toolbar className="my-2 flex gap-2 bg-transparent">
                  <BtnBold className="text-lucres-900 hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-8 w-8 items-center justify-center rounded-lg" />
                  <BtnItalic className="text-lucres-900 hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-8 w-8 items-center justify-center rounded-lg" />
                  <BtnBulletList className="text-lucres-900 hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-8 w-8 items-center justify-center rounded-lg" />
                  <BtnNumberedList className="text-lucres-900 hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-8 w-8 items-center justify-center rounded-lg" />
                  <BtnUnderline className="text-lucres-900 hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-8 w-8 items-center justify-center rounded-lg" />
                </Toolbar>
              </Editor>
              <div className="flex items-center justify-between">
                <div className="text-lucres-800 text-sm">{wordCount}/450 words</div>
                <button
                  type="button"
                  onClick={handleRegenerateSuggestions}
                  disabled={isFetchingJobDescSuggestions}
                  className="text-lucres-800 dark:hover:bg-dark-lucres-black-300 flex items-center gap-1 rounded-full p-1 text-sm hover:bg-gray-100"
                >
                  <ArrowsClockwiseIcon
                    size={20}
                    className={isFetchingJobDescSuggestions ? 'animate-spin' : ''}
                  />
                </button>
              </div>
            </div>
          </>
        )}

        {/* Job Description - Mobile (Click to open modal) */}
        {isScreenSmall && (
          <div
            className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 relative overflow-scroll rounded-lg border border-gray-200 p-4"
            onClick={(e) => {
              e.preventDefault()
              setIsJobDescriptionModalOpen(true)
            }}
          >
            {formData.jobDescription ? (
              <SafeHtml
                html={formData.jobDescription}
                className="scrollbar-none !overflow:scroll dark:bg-dark-lucres-black-500 dark:text-dark-lucres-green-100 focus:outline-hidden h-20 w-full resize-none whitespace-pre-wrap text-sm"
              />
            ) : (
              <div className="scrollbar-none text-lucres-300 dark:bg-dark-lucres-black-500 dark:text-lucres-800 focus:outline-hidden h-20 w-full resize-none whitespace-pre-wrap text-sm">
                Write a compelling job description...
              </div>
            )}
            <div className="mt-2 flex items-center justify-between">
              <div className="text-sm text-gray-500">{wordCount}/450 words</div>
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation()
                  handleRegenerateSuggestions()
                }}
                disabled={isFetchingJobDescSuggestions}
                className="dark:hover:bg-dark-lucres-black-300 flex items-center gap-1 rounded-lg p-1 text-sm text-gray-500 hover:bg-gray-100"
              >
                <ArrowsClockwiseIcon
                  size={16}
                  className={isFetchingJobDescSuggestions ? 'animate-spin' : ''}
                />
                <span>Regenerate</span>
              </button>
            </div>
          </div>
        )}

        {formErrors.jobDescription && (
          <p className="mt-1 pl-4 text-xs text-red-600 dark:text-red-400">
            {formErrors.jobDescription}
          </p>
        )}
      </div>

      {/* Job Description Modal on Small Screens */}
      {isJobDescriptionModalOpen && (
        <Modal
          onClose={() => setIsJobDescriptionModalOpen(false)}
          crossRequired
          classes="relative w-full min-h-screen rounded-none"
          crossClass="mr-4!"
        >
          <div className="flex h-full w-full flex-col gap-4 p-2 pt-8">
            <label className="dark:text-dark-lucres-green-300 block font-medium">
              Job Description:
            </label>
            <Editor
              value={formData.jobDescription}
              onChange={handleJobDescriptionChange}
              onBlur={handleJobDescriptionBlur}
              placeholder="Write a compelling job description..."
              containerProps={{
                style: {
                  border: 'none',
                  background: 'transparent',
                },
              }}
              className="scrollbar-none border-lucres-200 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 dark:text-dark-lucres-green-100 dark:placeholder-lucres-800 focus:outline-hidden h-[300px] w-full max-w-full resize-none overflow-scroll rounded-lg border bg-white p-4 text-sm placeholder:text-sm [&_ol]:list-decimal [&_ol]:pl-5 [&_ul]:list-disc [&_ul]:pl-5"
            >
              <Toolbar className="mb-2 flex gap-2 bg-transparent">
                <BtnBold className="text-lucres-900 hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-8 w-8 items-center justify-center rounded-lg" />
                <BtnItalic className="text-lucres-900 hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-8 w-8 items-center justify-center rounded-lg" />
                <BtnUnderline className="text-lucres-900 hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-8 w-8 items-center justify-center rounded-lg" />
                <BtnBulletList className="text-lucres-900 hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-8 w-8 items-center justify-center rounded-lg" />
                <BtnNumberedList className="text-lucres-900 hover:bg-lucres-200 data-[active=true]:bg-lucres-400 dark:bg-dark-lucres-black-300 dark:text-lucres-100 dark:hover:bg-dark-lucres-black-200 dark:data-[active=true]:bg-lucres-600 flex h-8 w-8 items-center justify-center rounded-lg" />
              </Toolbar>
            </Editor>
            <div className="mt-2 flex items-center justify-between">
              {formErrors.jobDescription && (
                <p className="text-xs text-red-600 dark:text-red-400">
                  {formErrors.jobDescription}
                </p>
              )}
              <div className="text-sm text-gray-500">{wordCount}/450 words</div>
              <button
                type="button"
                onClick={handleRegenerateSuggestions}
                disabled={isFetchingJobDescSuggestions}
                className="dark:hover:bg-dark-lucres-black-300 flex items-center gap-1 rounded-lg p-1 text-sm text-gray-500 hover:bg-gray-100"
              >
                <ArrowsClockwiseIcon
                  size={16}
                  className={isFetchingJobDescSuggestions ? 'animate-spin' : ''}
                />
                <span>Regenerate</span>
              </button>
            </div>
          </div>
        </Modal>
      )}

      {/* More Suggestions */}
      <div className="w-full">
        <h3 className="mb-2 font-medium">More Suggestions</h3>
        {isFetchingJobDescSuggestions ? (
          <div className="flex items-center justify-center py-4">
            <div className="border-lucres-600 h-6 w-6 animate-spin rounded-full border-2 border-t-transparent"></div>
          </div>
        ) : suggestedDescriptions.length > 0 ? (
          suggestedDescriptions.map((suggestion, index) => (
            <button
              key={index}
              type="button"
              onClick={() => handleSuggestionClick(suggestion)}
              disabled={selectedSuggestions.includes(suggestion)}
              className={`hover:bg-lucres-100 dark:border-dark-lucres-black-200 dark:hover:bg-dark-lucres-black-300 mb-6 flex w-full items-start space-x-4 rounded-lg border bg-transparent p-4 text-left transition-colors ${
                selectedSuggestions.includes(suggestion)
                  ? 'cursor-default opacity-60'
                  : 'cursor-pointer'
              }`}
            >
              <div className="shrink-0">
                {selectedSuggestions.includes(suggestion) ? (
                  <CheckCircleIcon size={20} />
                ) : (
                  <CircleDashedIcon size={20} />
                )}
              </div>
              <div className="grow">
                <p className="text-lucres-800">{suggestion}</p>
              </div>
            </button>
          ))
        ) : (
          <p className="text-center text-gray-500">No suggestions available</p>
        )}
      </div>
    </form>
  )
}

export default JobRequirement
