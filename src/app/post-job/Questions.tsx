'use client'
import { useState } from 'react'
import Input from '../../components/Input'
import Select from '../../components/Select'
import {
  CheckCircleIcon,
  PlusIcon,
  SquaresFourIcon,
  TextAlignLeftIcon,
  TrashIcon,
} from '@phosphor-icons/react'
import AISuggestionsButton from '../../components/AISuggestionsButton'
import ToggleButton from '../../components/ToggleButton'

import { FormData } from './page'
import { Question } from '@/models/PostJobs'
import { useEffect } from 'react'

interface QuestionsProps {
  formData: FormData
  formErrors: Partial<Record<keyof FormData, string>>
  onInputChange: (name: keyof FormData, value: any) => void
  onBlur: (name: keyof FormData) => void
  onContinue: () => void
}

function Questions({ formData, formErrors, onInputChange, onBlur, onContinue }: QuestionsProps) {

  console.log("WE ARE IN QUESTIONS SECTION");
  console.log(formData);
  console.log(formErrors);

  const questionTypeOptions = [
    {
      value: 'MULTIPLE_CHOICE',
      label: 'Multiple Choice',
      icon: <SquaresFourIcon size={24} />,
    },
    {
      value: 'PARAGRAPH',
      label: 'Paragraph',
      icon: <TextAlignLeftIcon size={24} />,
    },
    { value: 'CHECKBOX', label: 'Check Box', icon: <CheckCircleIcon size={24} /> },
  ]

  const defaultOptions = {
    'MULTIPLE_CHOICE': ['Option 1'],
    'CHECKBOX': ['Option 1'],
    'PARAGRAPH': [],
  }

  // Initialize questions from formData or with default question
  const [questions, setQuestions] = useState(() => {
    if (formData.requiredQuestions && formData.requiredQuestions.length > 0) {
      return formData.requiredQuestions.map((q, index) => ({
        id: index + 1,
        text: q.text,
        options: q.choices || [],
        selectedOption: q.questionType,
        isRequired: q.isRequired,
      }))
    }
    return [
      {
        id: 1,
        text: 'Untitled Question',
        options: defaultOptions['MULTIPLE_CHOICE'],
        selectedOption: 'MULTIPLE_CHOICE',
        isRequired: false,
      },
    ]
  })

  // Sync questions with formData whenever questions change
  useEffect(() => {
    const formattedQuestions: Question[] = questions.map((q) => ({
      questionType: q.selectedOption as 'MULTIPLE_CHOICE' | 'PARAGRAPH' | 'CHECKBOX',
      text: q.text,
      isRequired: q.isRequired,
      choices: q.selectedOption !== 'PARAGRAPH' ? q.options : undefined,
    }))
    onInputChange('requiredQuestions', formattedQuestions)
  }, [questions, onInputChange])

  const handleAddQuestion = () => {
    setQuestions([
      ...questions,
      {
        id: questions.length + 1,
        text: 'Untitled Question',
        options: defaultOptions['MULTIPLE_CHOICE'],
        selectedOption: 'MULTIPLE_CHOICE',
        isRequired: false,
      },
    ])
  }

  const handleDeleteQuestion = (id: number) => {
    setQuestions(questions.filter((question) => question.id !== id))
  }

  const handleToggleRequired = (questionId: number) => {
    setQuestions(
      questions.map((question) =>
        question.id === questionId ? { ...question, isRequired: !question.isRequired } : question,
      ),
    )
  }

  // Handle Option Button
  const handleAddOption = (questionId: number) => {
    setQuestions(
      questions.map((question) => {
        if (question.id === questionId) {
          return {
            ...question,
            options: [...question.options, `Option ${question.options.length + 1}`],
          }
        }
        return question
      }),
    )
  }

  // (Option Type Handler -> paragraph, multiple choice, checkbox)
  const handleOptionChange = (questionId: number, value: string) => {
    setQuestions(
      questions.map((question) => {
        if (question.id === questionId) {
          const optionKey = value as keyof typeof defaultOptions
          return {
            ...question,
            selectedOption: value,
            options: defaultOptions[optionKey],
          }
        }
        return question
      }),
    )
  }

  // Options Handler (Question Options)
  const handleOptionTextChange = (questionId: number, optionIndex: number, value: string) => {
    setQuestions(
      questions.map((question) => {
        if (question.id === questionId) {
          const newOptions = [...question.options]
          newOptions[optionIndex] = value
          return { ...question, options: newOptions }
        }
        return question
      }),
    )
  }

  const handleQuestionTextChange = (questionId: number, value: string) => {
    setQuestions(
      questions.map((question) => {
        if (question.id === questionId) {
          return { ...question, text: value }
        }
        return question
      }),
    )
  }

  return (
    <div className="flex w-full flex-col items-center justify-center">
      <div className="flex w-full max-w-2xl flex-col items-center justify-center">
        {questions.map((question, index) => (
          <div key={question.id} className="m-4 w-full gap-4 rounded-lg border p-4 sm:flex-row">
            <span className="block sm:inline-block">Question {index + 1}</span>
            <div className="w-full items-center gap-4 sm:flex">
              <Input
                type="text"
                id={`Question-${question.id}`}
                name={`Question-${question.id}`}
                value={question.text}
                className="my-3 w-full"
                theme="gray"
                onChange={(e) => handleQuestionTextChange(question.id, e.target.value)}
                placeholder=""
              />
              <Select
                classes="p-4! w-full sm:w-auto mb-4 md:mb-0"
                options={questionTypeOptions}
                value={question.selectedOption}
                onChange={(value) => handleOptionChange(question.id, value)}
              />
            </div>
            {question.selectedOption === 'MULTIPLE_CHOICE' && (
              <div>
                {question.options.map((option, optionIndex) => (
                  <div key={optionIndex} className="mb-2 flex items-center">
                    <div className="relative flex items-center">
                      <span className="absolute left-2 h-3 w-3 rounded-full border-2"></span>
                      <Input
                        type="text"
                        value={option}
                        onChange={(e) =>
                          handleOptionTextChange(question.id, optionIndex, e.target.value)
                        }
                        className="w-full pl-8"
                      />
                    </div>
                  </div>
                ))}
                <div className="mt-4">
                  <AISuggestionsButton
                    onClick={() => handleAddOption(question.id)}
                    icon={<PlusIcon />}
                    className="dark:bg-dark-lucres-black-300! bg-lime-300! dark:hover:text-lime-300! !bg-opacity-30"
                    theme="primary"
                    size="medium"
                  >
                    Add Another Option
                  </AISuggestionsButton>
                </div>

                <div className="mt-4 flex w-full justify-end">
                  {/* Required toggle button */}
                  <ToggleButton
                    label="Required"
                    isRequired={question.isRequired}
                    onToggle={() => handleToggleRequired(question.id)}
                  />
                  <TrashIcon
                    size={20}
                    className="cursor-pointer"
                    onClick={() => handleDeleteQuestion(question.id)}
                  />
                </div>
              </div>
            )}
            {question.selectedOption === 'PARAGRAPH' && (
              <div>
                <div className="mt-4 w-full pe-2 lg:w-1/2">
                  <textarea
                    placeholder="Candidate's Answer"
                    className="focus:outline-hidden h-20 w-full resize-none rounded-lg border p-2 text-black"
                  ></textarea>
                </div>
                <div className="mt-4 flex w-full justify-end">
                  {/* Required toggle button */}
                  <ToggleButton
                    label="Required"
                    isRequired={question.isRequired}
                    onToggle={() => handleToggleRequired(question.id)}
                  />
                  <TrashIcon
                    size={20}
                    className="cursor-pointer"
                    onClick={() => handleDeleteQuestion(question.id)}
                  />
                </div>
              </div>
            )}
            {question.selectedOption === 'CHECKBOX' && (
              <div>
                {question.options.map((option, optionIndex) => (
                  <div key={optionIndex} className="mb-2 flex items-center">
                    <div className="relative flex items-center">
                      <span className="absolute left-2">
                        <CheckCircleIcon size={16} />
                      </span>
                      <Input
                        type="text"
                        value={option}
                        onChange={(e) =>
                          handleOptionTextChange(question.id, optionIndex, e.target.value)
                        }
                        className="w-full pl-8"
                      />
                    </div>
                  </div>
                ))}
                <div className="mt-4">
                  <AISuggestionsButton
                    onClick={() => handleAddOption(question.id)}
                    icon={<PlusIcon />}
                    className="dark:bg-dark-lucres-black-300! bg-lime-300! dark:hover:text-lime-300! !bg-opacity-30"
                    theme="primary"
                    size="medium"
                  >
                    Add Another Option
                  </AISuggestionsButton>
                </div>
                <div className="mt-4 flex w-full justify-end">
                  {/* Required toggle button */}
                  <ToggleButton
                    label="Required"
                    isRequired={question.isRequired}
                    onToggle={() => handleToggleRequired(question.id)}
                  />
                  <TrashIcon
                    size={20}
                    className="cursor-pointer"
                    onClick={() => handleDeleteQuestion(question.id)}
                  />
                </div>
              </div>
            )}
          </div>
        ))}
        <AISuggestionsButton
          onClick={handleAddQuestion}
          icon={<PlusIcon />}
          theme="secondary"
          size="medium"
          className="dark:hover:text-lime-300!"
        >
          Add Another Question
        </AISuggestionsButton>
      </div>
    </div>
  )
}

export default Questions
