'use client'
import { useEffect, useRef, useState } from 'react'
import Input from '../../components/Input'
import Radio from '../../components/Radio'
import { UserIcon, MagnifyingGlassIcon, CurrencyInrIcon, MapPinIcon } from '@phosphor-icons/react'
import { FormData } from './page'
import { LocationService } from '../../services/LocationService'
import { capitalizeWords, parseLocationInput } from '../../utils/commonUtils'

interface CreateJobProps {
  onContinue: () => void
  formData: FormData
  formErrors: Partial<Record<keyof FormData, string>>
  onInputChange: (name: keyof FormData, value: any) => void
  onBlur: (name: keyof FormData) => void
  onSelectionChange: (name: keyof FormData, value: any) => void
}

// Custom hook for Google Places AutocompleteService
function usePlacesAutocomplete() {
  const [suggestions, setSuggestions] = useState<any[]>([])
  const serviceRef = useRef<any>(null)

  const fetchSuggestions = (input: string) => {
    if (!window.google || !window.google.maps || !window.google.maps.places) {
      return
    }
    if (!serviceRef.current) {
      serviceRef.current = new window.google.maps.places.AutocompleteService()
    }
    if (input.length < 3) {
      setSuggestions([])
      return
    }
    serviceRef.current.getPlacePredictions({ input }, (predictions: any[] | null) => {
      setSuggestions(predictions || [])
    })
  }

  return { suggestions, fetchSuggestions, setSuggestions }
}

// Helper to format Location object as a string for input display
function formatLocationString(location: any) {
  if (!location || !location.address) return ''
  const { area, city, state, country } = location.address
  return [area, city, state, country].filter(Boolean).join(', ')
}

const CreateJob: React.FC<CreateJobProps> = ({
  onContinue,
  formData,
  formErrors,
  onInputChange,
  onBlur,
  onSelectionChange,
}) => {
  const debounceTimeout = useRef<ReturnType<typeof setTimeout> | null>(null)
  const locationDebounceTimeout = useRef<ReturnType<typeof setTimeout> | null>(null)
  const abortController = useRef<AbortController | null>(null)
  const [salaryType, setSalaryType] = useState<'MONTHLY' | 'YEARLY'>(
    formData.salaryPeriod || 'MONTHLY',
  )
  const [isGettingLocation, setIsGettingLocation] = useState(false)
  const [showLocationSuggestions, setShowLocationSuggestions] = useState(false)
  const {
    suggestions: locationSuggestions,
    fetchSuggestions,
    setSuggestions,
  } = usePlacesAutocomplete()
  const [locationInput, setLocationInput] = useState('')
  const isUserTypingRef = useRef(false)

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser.')
      return
    }

    setIsGettingLocation(true)

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords

        try {
          const location = await LocationService.getLocationFromCoords(latitude, longitude)
          if (location) {
            onInputChange('location', location)
            setLocationInput(formatLocationString(location))
            isUserTypingRef.current = false
          } else {
            alert('Could not get location details')
          }
        } catch (error) {
          console.error('Error getting location details:', error)
          alert('Error getting location details')
        } finally {
          setIsGettingLocation(false)
        }
      },
      (error) => {
        setIsGettingLocation(false)
        switch (error.code) {
          case error.PERMISSION_DENIED:
            alert('Location access denied by user.')
            break
          case error.POSITION_UNAVAILABLE:
            alert('Location information is unavailable.')
            break
          case error.TIMEOUT:
            alert('Location request timed out.')
            break
          default:
            alert('An unknown error occurred while getting location.')
            break
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000,
      },
    )
  }

  const handleToggle = () => {
    setSalaryType((prev) => (prev === 'MONTHLY' ? 'YEARLY' : 'MONTHLY'))
  }

  const handleUseCurrentLocation = () => {
    getCurrentLocation()
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onContinue()
  }

  useEffect(() => {
    onInputChange('salaryPeriod', salaryType)
  }, [salaryType])

  useEffect(() => {
    setLocationInput(formatLocationString(formData.location))
  }, [formData.location, locationInput])

  return (
    <form
      className="mx-auto w-full max-w-2xl py-4"
      onSubmit={handleSubmit}
      onKeyDown={(e) => {
        if (e.key === 'Enter') {
          e.preventDefault()
        }
      }}
    >
      {/* Job Title */}
      <div className="mb-8">
        <Input
          placeholder="e.g. Software Engineer, Product Designer"
          label="Job Title"
          name="jobTitle"
          autoComplete="off"
          value={formData.jobTitle}
          onChange={(e) => onInputChange('jobTitle', e.target.value)}
          onBlur={() => onBlur('jobTitle')}
          error={formErrors.jobTitle}
          theme="gray"
          className="w-fit text-base tracking-wider"
        />
      </div>

      {/* Required Job Experience */}
      <div className="mb-8">
        <p className="text-lucres-900 dark:text-dark-lucres-green-300 my-2 whitespace-nowrap text-sm font-semibold text-opacity-75">
          Required Job Experience
        </p>
        <div className="mb-5 flex flex-wrap gap-3 md:gap-5">
          {[
            'Fresher',
            '1 - 3 Years',
            '3 - 6 Years',
            '6 - 9 Years',
            '9 - 12 Years',
            '12+ Years',
          ].map((experience) => (
            <Radio
              key={experience}
              value={experience}
              label={experience}
              selected={formData.experience}
              setSelected={(value) => onSelectionChange('experience', value)}
              name="experience"
              classname="font-medium! text-sm! ml-0! min-w-32"
            />
          ))}
        </div>
      </div>

      <div className="mb-3">
        {/* Salary Range */}
        <div className="relative flex flex-col gap-4 sm:flex-row">
          {/* Minimum Salary */}
          <div className="relative w-full md:w-1/2">
            <div className="absolute left-3 top-11 z-10 flex items-center">
              <CurrencyInrIcon
                size={18}
                weight="bold"
                className="border-lucres-300 dark:border-x-dark-lucres-black-100 dark:text-lucres-200 mr-8 border-r pr-1"
              />
            </div>
            <Input
              label="Minimum Salary"
              placeholder="00,000"
              name="minSalary"
              type="text"
              onChange={(e) => {
                const value = e.target.value
                if (/^[\d,]*$/.test(value)) {
                  onInputChange('minSalary', value)
                }
              }}
              onBlur={() => onBlur('minSalary')}
              maxLength={11}
              autoComplete="off"
              value={formData.minSalary || ''}
              theme="gray"
              error={formErrors.minSalary}
              className="placeholder-lucres-300 mb-0! pl-9"
            />
          </div>

          {/* Maximum Salary */}
          <div className="relative w-full md:w-1/2">
            <div className="absolute left-3 top-11 z-10 flex items-center">
              <CurrencyInrIcon
                size={18}
                weight="bold"
                className="border-lucres-300 dark:border-lucres-200 dark:border-x-dark-lucres-black-100 border-r pr-1"
              />
            </div>
            <Input
              label="Maximum Salary"
              placeholder="00,000"
              name="maxSalary"
              type="text"
              onChange={(e) => {
                const value = e.target.value
                if (/^[\d,]*$/.test(value)) {
                  onInputChange('maxSalary', value)
                }
              }}
              onBlur={() => onBlur('maxSalary')}
              maxLength={11}
              autoComplete="off"
              value={formData.maxSalary || ''}
              theme="gray"
              error={formErrors.maxSalary}
              className="placeholder-lucres-300 mb-0! pl-9"
            />

            {/* Custom Toggle */}
            <div className="my-2 flex items-center justify-end gap-2">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Monthly</span>
                <div
                  className={`relative flex h-6 w-12 cursor-pointer items-center rounded-full transition-colors ${
                    salaryType === 'MONTHLY'
                      ? 'bg-dark-lucres-green-200 dark:bg-dark-lucres-black-200'
                      : 'bg-lucres-400 dark:bg-lucres-500'
                  }`}
                  onClick={handleToggle}
                  role="switch"
                  aria-checked={salaryType === 'MONTHLY'}
                  aria-label="Switch salary type"
                >
                  <div
                    className={`absolute inset-y-0 left-0 top-0.5 h-5 w-5 rounded-full bg-white transition-transform ${salaryType === 'MONTHLY' ? '' : 'translate-x-7'}`}
                  />
                </div>
                <span className="text-sm font-medium">Yearly</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Job Type */}
      <div className="mb-8">
        <p className="text-lucres-900 dark:text-dark-lucres-green-300 my-2 whitespace-nowrap text-sm font-semibold text-opacity-75">
          Job Type
        </p>
        <div className="mb-5 flex flex-wrap gap-3 md:gap-5">
          {['Full - Time', 'Part - Time', 'Contract', 'Internship'].map((jobType) => (
            <Radio
              key={jobType}
              value={jobType}
              label={jobType}
              selected={formData.jobType}
              setSelected={(value) => onSelectionChange('jobType', value)}
              name="jobType"
              classname="text-sm! font-medium! ml-0! min-w-32 text-center"
            />
          ))}
        </div>
      </div>

      {/* Number of Openings */}
      <div className="mb-2">
        <div className="relative flex flex-col gap-4 sm:flex-row">
          <div className="relative w-full md:w-1/2">
            <div className="absolute left-3 top-11 z-10 flex items-center">
              <UserIcon
                size={18}
                weight="bold"
                className="border-lucres-300 dark:border-lucres-200 dark:border-x-dark-lucres-black-100 border-r pr-1"
              />
            </div>
            <Input
              label="Number of Openings"
              name="numOpenings"
              autoComplete="off"
              value={formData.numOpenings?.toString() || ''}
              placeholder="101"
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              onChange={(e) => {
                const value = e.target.value
                if (/^\d*$/.test(value)) {
                  onInputChange('numOpenings', value)
                }
              }}
              onBlur={() => onBlur('numOpenings')}
              maxLength={4}
              error={formErrors.numOpenings}
              theme="gray"
              className="pl-10"
            />
          </div>
        </div>
      </div>

      {/* Work Policy */}
      <div className="mb-8">
        <p className="text-lucres-900 dark:text-dark-lucres-green-300 my-2 whitespace-nowrap text-sm font-semibold text-opacity-75">
          What's your remote work policy?
        </p>
        <p className="text-lucres-800 dark:text-dark-lucres-green-100 mb-2 mt-3 pr-12 text-sm">
          Lucres helps you find the best remote hires. This section ensures you are matched with
          candidates that fit your needs.
        </p>
        <div className="flex flex-col gap-4 sm:flex-row">
          {['ONSITE', 'HYBRID', 'REMOTE'].map((mode) => (
            <label className="flex cursor-pointer items-center" key={mode}>
              <input
                type="radio"
                name="remotePolicy"
                value={mode}
                checked={formData.remotePolicy === mode}
                onChange={(e) => onInputChange('remotePolicy', e.target.value)}
                className="accent-lucres-500 h-4 w-4 cursor-pointer"
              />
              <span className="text-lucres-900 ml-2 text-base opacity-90 dark:text-white">
                {mode === 'ONSITE' ? 'On Site' : capitalizeWords(mode)}
              </span>
            </label>
          ))}
        </div>
      </div>

      {/* Location */}
      {formData.remotePolicy !== 'REMOTE' && (
        <div className="relative">
          <div className="absolute left-3 top-11 z-10 flex items-center">
            <MagnifyingGlassIcon
              size={18}
              weight="bold"
              className="border-lucres-300 dark:border-lucres-200 dark:border-x-dark-lucres-black-100 border-r pr-1"
            />
          </div>

          <button
            type="button"
            onClick={handleUseCurrentLocation}
            disabled={isGettingLocation}
            className={`absolute right-3 top-[52%] z-10 flex items-center gap-1 rounded px-2 py-1 text-xs font-medium transition-colors ${
              isGettingLocation
                ? 'cursor-not-allowed bg-gray-200 text-gray-500'
                : 'bg-lucres-100 text-lucres-700 hover:bg-lucres-200 dark:bg-dark-lucres-black-300 dark:text-dark-lucres-green-200 dark:hover:bg-dark-lucres-black-200'
            }`}
          >
            <MapPinIcon size={12} weight={isGettingLocation ? 'regular' : 'bold'} />
            {isGettingLocation ? 'Getting...' : 'Use Current Location'}
          </button>

          <Input
            label="Location"
            placeholder="e.g. Area, City, State, Country"
            name="location"
            autoComplete="off"
            value={locationInput}
            onChange={(e) => {
              isUserTypingRef.current = true
              const query = e.target.value
              setLocationInput(query)
              if (locationDebounceTimeout.current) {
                clearTimeout(locationDebounceTimeout.current)
              }
              if (query.length > 2) {
                locationDebounceTimeout.current = setTimeout(() => {
                  if (isUserTypingRef.current) {
                    fetchSuggestions(query)
                    setShowLocationSuggestions(true)
                  }
                }, 300)
              } else {
                setSuggestions([])
                setShowLocationSuggestions(false)
              }
            }}
            onBlur={() => {
              setTimeout(() => setShowLocationSuggestions(false), 200)
              onBlur('location')
            }}
            onFocus={() => {
              if (locationInput.length > 2 && locationSuggestions.length > 0) {
                setShowLocationSuggestions(true)
              }
            }}
            error={formErrors.location}
            theme="gray"
            className="pl-10 pr-36"
          />
          {showLocationSuggestions && locationSuggestions.length > 0 && (
            <ul className="border-lucres-300 dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 absolute z-20 -mt-5 max-h-48 w-full overflow-auto rounded-md border bg-white shadow-lg">
              {locationSuggestions.map((suggestion) => (
                <li
                  key={suggestion.place_id}
                  className="hover:bg-lucres-green-200 dark:hover:bg-dark-lucres-black-400 cursor-pointer px-4 py-3 hover:bg-opacity-30"
                  onClick={async () => {
                    // Fetch place details using Google Maps JS API PlacesService
                    if (window.google && window.google.maps && window.google.maps.places) {
                      const service = new window.google.maps.places.PlacesService(
                        document.createElement('div'),
                      )
                      service.getDetails(
                        { placeId: suggestion.place_id },
                        (place: any, status: any) => {
                          if (
                            status === window.google.maps.places.PlacesServiceStatus.OK &&
                            place
                          ) {
                            // Parse address components
                            let country = ''
                            let state = ''
                            let city = ''
                            let area = ''
                            if (place.address_components) {
                              for (const component of place.address_components) {
                                const types = component.types
                                if (types.includes('country')) country = component.long_name
                                else if (types.includes('administrative_area_level_1'))
                                  state = component.long_name
                                else if (
                                  types.includes('locality') ||
                                  types.includes('administrative_area_level_2')
                                )
                                  city = component.long_name
                                else if (
                                  types.includes('sublocality') ||
                                  types.includes('neighborhood')
                                )
                                  area = component.long_name
                              }
                            }
                            const locationObj = {
                              address: { country, state, city, area },
                              latitude: place.geometry?.location?.lat() || 0,
                              longitude: place.geometry?.location?.lng() || 0,
                            }
                            onInputChange('location', locationObj)
                            setLocationInput(formatLocationString(locationObj))
                            isUserTypingRef.current = false
                          } else {
                            // fallback: just set the description as city
                            const locationObj = {
                              address: {
                                country: '',
                                state: '',
                                city: suggestion.description,
                                area: '',
                              },
                              latitude: 0,
                              longitude: 0,
                            }
                            onInputChange('location', locationObj)
                            setLocationInput(formatLocationString(locationObj))
                            isUserTypingRef.current = false
                          }
                        },
                      )
                    } else {
                      // fallback: just set the description as city
                      const locationObj = {
                        address: {
                          country: '',
                          state: '',
                          city: suggestion.description,
                          area: '',
                        },
                        latitude: 0,
                        longitude: 0,
                      }
                      onInputChange('location', locationObj)
                      setLocationInput(formatLocationString(locationObj))
                      isUserTypingRef.current = false
                    }
                    setShowLocationSuggestions(false)
                  }}
                >
                  <div className="flex items-center gap-2">
                    <MapPinIcon
                      size={14}
                      className="text-lucres-500 dark:text-dark-lucres-green-200"
                    />
                    <div>
                      <div className="text-lucres-900 font-medium dark:text-white">
                        {suggestion.structured_formatting?.main_text ||
                          suggestion.structured_formatting?.mainText ||
                          suggestion.description}
                      </div>
                      <div className="text-lucres-600 dark:text-dark-lucres-green-100 text-xs">
                        {suggestion.structured_formatting?.secondary_text ||
                          suggestion.structured_formatting?.secondaryText ||
                          ''}
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </form>
  )
}

export default CreateJob
