'use client'
import React, { useState, useEffect } from 'react'
import JobPostingHeader from './JobPostingHeader'
import C<PERSON><PERSON>ob from './Create<PERSON>ob'
import JobRequirement from './JobRequirement'
// import Questions from './Questions' // Applicant Questions (commented out)
import CompanyDetails from './CompanyDetails'
import JobPreview from './JobPreview'
import Button from '../../components/Button'
import { ArrowRightIcon } from '@phosphor-icons/react'
import { PostJobRequest } from '../../models/PostJobs'
import { useAuth } from '../../context/AuthContext'
import { PostService } from '../../services/PostService'
import { useToast } from '../../components/ToastX'
import useError from '../../context/ErrorContext'
import { useRouter, useSearchParams } from 'next/navigation'
import { JobService } from '../../services/JobService'
import { Location } from '../../services/LocationService'
import { CompanyService } from '../../services/CompanyService'
import { AiService } from '../../services/AiService'
import Questions from './Questions'
import { Question } from '@/models/PostJobs'
export interface FormData {
  jobTitle: string
  minSalary: string
  maxSalary: string
  salaryPeriod: 'MONTHLY' | 'YEARLY'
  numOpenings: number
  location: Location
  locationDetails?: string
  remotePolicy: string
  experience: string
  jobType: string
  requiredSkills: string[]
  jobDescription: string
  companyName: string
  website: string
  companyLogo?: {
    file: File
    url: string
  }
  selectedSuggestions?: string[]
  requiredQuestions: Question[]
}

type FormErrors = Partial<Record<keyof FormData, string>>
type TouchedFields = Partial<Record<keyof FormData, boolean>>

//! This is the entry point to the create job page component
const JobPosting: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0)
  const searchParams = useSearchParams()
  const isEditMode = searchParams.get('editMode') === 'true'
  const editJobDetails = searchParams.get('jobDetails')
    ? JSON.parse(searchParams.get('jobDetails')!)
    : null
  const [isFetchingSkillsSuggestions, setIsFetchingSkillsSuggestions] = useState(false)
  const [isFetchingJobDescSuggestions, setIsFetchingJobDescSuggestions] = useState(false)
  const [suggestedDescriptions, setSuggestedDescriptions] = useState<string[]>([])
  const [skillsSuggestions, setSkillsSuggestions] = useState<string[]>([])
  const [hasInitialLoad, setHasInitialLoad] = useState(false)
  console.log('This is the entry POINT')
  const [formData, setFormData] = useState<FormData>({
    jobTitle: '',
    minSalary: '',
    maxSalary: '',
    salaryPeriod: 'MONTHLY',
    numOpenings: 0,
    location: {
      address: {
        country: '',
        state: '',
        city: '',
        area: '',
      },
      latitude: 0,
      longitude: 0,
    },
    locationDetails: '',
    remotePolicy: 'in-office',
    experience: '',
    jobType: '',
    requiredSkills: [],
    jobDescription: '',
    companyName: '',
    website: '',
    companyLogo: undefined,
    selectedSuggestions: [],
    requiredQuestions: [],
  })

  const { authUserProfile } = useAuth()
  const toast = useToast()
  const { handleError } = useError()
  const router = useRouter()
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [touched, setTouched] = useState<TouchedFields>({})

  // Initialize form data with edit job details if in edit mode
  useEffect(() => {
    if (isEditMode && editJobDetails) {
      setFormData(editJobDetails)
    }
  }, [isEditMode, editJobDetails])

  useEffect(() => {
    console.log('Effect runs. Step:', currentStep, 'Has initial load:', hasInitialLoad)
  }, [currentStep, hasInitialLoad])
    

  // Update the useEffect for fetching suggestions
  // useEffect(() => {
  //   if (currentStep === 1 && !hasInitialLoad) {
  //     setHasInitialLoad(true)
  //     fetchSuggestions()
  //   }
  // }, [currentStep, hasInitialLoad, formData.jobTitle])

  const fetchSuggestions = async (section?: 'jobDesc' | 'jobSkills') => {
    if (!formData.jobTitle) return

    console.log('Fetching suggestions for:', formData.jobTitle)

    if (section === 'jobSkills') {
      setIsFetchingSkillsSuggestions(true)
    } else if (section === 'jobDesc') {
      setIsFetchingJobDescSuggestions(true)
    } else {
      setIsFetchingSkillsSuggestions(true)
      setIsFetchingJobDescSuggestions(true)
    }

    try {
      const response = section
        ? await AiService.getSuggestions(formData.jobTitle, section)
        : await AiService.getSuggestions(formData.jobTitle)
      if (response.status === 'success') {
        if (response.data.jobDesc || section === 'jobDesc')
          setSuggestedDescriptions(response.data.jobDesc || response.data.content)
        if (response.data.jobSkills || section === 'jobSkills')
          setSkillsSuggestions(response.data.jobSkills || response.data.content)
      } else {
        toast.error('Failed to fetch suggestions.')
      }
    } catch (error: any) {
      toast.error(error.message || 'An error occurred while fetching suggestions.')
    } finally {
      if (section === 'jobSkills') {
        setIsFetchingSkillsSuggestions(false)
      } else if (section === 'jobDesc') {
        setIsFetchingJobDescSuggestions(false)
      } else {
        setIsFetchingSkillsSuggestions(false)
        setIsFetchingJobDescSuggestions(false)
      }
    }
  }

  const steps = [
    { label: 'Create Job' },
    { label: 'Job Requirement' },
    { label: 'Company Details' },
    { label: 'Applicant Questions', isNew: true },
    { label: 'Preview' },
  ]
  const stepFields: Record<number, (keyof FormData)[]> = {
    0: [
      'jobTitle',
      'minSalary',
      'maxSalary',
      'numOpenings',
      'location',
      'remotePolicy',
      'experience',
      'jobType',
    ],
    1: ['requiredSkills', 'jobDescription'],
    2: ['companyLogo', 'companyName', 'website'],
    // 3: ['requiredQuestions'],
  }

  const handlePublish = async () => {
    const formatEmploymentType = (type: string) =>
      type
        .replace(/\s*-\s*/g, '_')
        .replace(/\s+/g, '_')
        .toUpperCase()

    // const getExperienceCount = (experience: string): number => {
    //   if (!experience) return 0

    //   experience = experience.toLowerCase().trim()

    //   if (experience.includes('fresher')) return 0

    //   if (experience.includes('-')) {
    //     const parts = experience.split('-')
    //     return parseInt(parts[1]) || 0
    //   }

    //   if (experience.includes('+')) {
    //     return parseInt(experience) || 0
    //   }

    //   const num = parseInt(experience)
    //   return isNaN(num) ? 0 : num
    // }
    const getExperienceCount = (experience: string): number[] => {
      if (!experience) return [0, 0]

      experience = experience.toLowerCase().trim()

      if (experience.includes('fresher')) {
        return [0, 1]
      }

      if (experience.includes('-')) {
        const parts = experience.split('-').map((p) => parseInt(p))
        const start = parts[0] || 0
        const end = parts[1] || 0
        return [start, end]
      }

      if (experience.includes('+')) {
        const num = parseInt(experience)
        return isNaN(num) ? [0, 0] : [num, 50]
      }

      const num = parseInt(experience)
      return isNaN(num) ? [0, 0] : [num, num]
    }
    //! The API Call for create Job post is done here.
    const postJobPayload: PostJobRequest = {
      ...(isEditMode && { id: editJobDetails.id }),
      title: formData.jobTitle || '',
      description: formData.jobDescription || '',
      employmentType: formatEmploymentType(formData.jobType || ''),
      // experienceLevel: 'INTERMEDIATE',
      workExperience: {
        unit: 'YEARS',
        min: getExperienceCount(formData.experience)[0],
        max: getExperienceCount(formData.experience)[1],
      },
      workMode: formData.remotePolicy.replace(/\s+/g, '').toUpperCase(),
      skills: formData.requiredSkills.map((skill) => ({
        name: skill,
        level: 50,
      })),
      salary: {
        min: Number(formData.minSalary.replace(/,/g, '')) || 0,
        max: Number(formData.maxSalary.replace(/,/g, '')) || 0,
        period: formData.salaryPeriod || 'MONTHLY',
      },
      openings: Number(formData.numOpenings) || 1,
      company: {
        name: formData.companyName || '',
        websiteUrl: formData.website || '',
      },
      location: {
        address: {
          area: '',
          city: formData.location.address.city,
          state: formData.location.address.state,
          country: formData.location.address.country,
        },
        longitude: formData.location.longitude,
        latitude: formData.location.latitude,
      },
      questions: formData.requiredQuestions.map((que): any => ({
          text: que.text,
          isRequired: que.isRequired,
          choices: que.choices,
          questionType: que.questionType
        })),
    }

    if (!authUserProfile?.id) return
    const userId = authUserProfile.id

    try {
      console.log("PAYLOADISHERE",postJobPayload);
      let response
      if (isEditMode) {
        // Update existing job
        response = await JobService.updateJob(postJobPayload, userId)
      } else {
        // Create new job
        response = await JobService.postJob(postJobPayload, userId)
      }

      if (response.data.status === 'success') {
        // Handle company logo upload for both create and edit
        if (formData.companyLogo?.file) {
          const logoFormData = new FormData()
          logoFormData.append('images', formData.companyLogo.file)
          logoFormData.append('type', 'companyLogo')
          logoFormData.append('id', response.data.data.id)

          try {
            await CompanyService.uploadPhoto(logoFormData)
          } catch (error: any) {
            console.error('Error uploading company logo:', error)
            toast.error('Job updated but failed to upload company logo')
          }
        }
        console.log("JOB IS POSTED RESPONSE", response);
        toast.success(isEditMode ? 'Job updated successfully' : 'Job created successfully')
        router.push(`/${authUserProfile.permalink}`)
      } else {
        throw new Error(
          response.data.message || `Cannot ${isEditMode ? 'update' : 'create'} job at this moment`,
        )
      }
    } catch (error: any) {
      toast.error(
        error.message || `Failed to ${isEditMode ? 'update' : 'post'} the job. Please try again.`,
      )
      handleError(error)
    }
  }

  const formatIndianNumber = (value: string | number): string => {
    const num = value.toString().replace(/,/g, '') // remove existing commas
    if (isNaN(Number(num))) return ''
    return Number(num).toLocaleString('en-IN')
  }

  const validators: Record<
    keyof FormData,
    (value: any, allValues?: FormData) => string | undefined
  > = {
    // create job
    jobTitle: (value) => {
      if (!value.trim()) return 'Job title is required'
      if (!/^[A-Za-z0-9 ,.-]{3,100}$/.test(value)) return 'Invalid job title format'
    },
    // minSalary: (value) => {
    //   if (!value) return 'Minimum salary is required'
    //   if (!/^\d+$/.test(value)) return 'Only digits are allowed'
    //   if (Number(value) < 0) return 'Salary must be positive'
    // },
    // maxSalary: (value, all) => {
    //   if (!value) return 'Minimum salary is required'
    //   if (!/^\d+$/.test(value)) return 'Only digits are allowed'
    //   if (Number(value) < 0) return 'Salary must be positive'
    //   if (all && Number(value) < Number(all.minSalary)) {
    //     return 'Max salary must be greater than min salary'
    //   }
    // },
    minSalary: (value) => {
      if (!value) return 'Minimum salary is required'
      if (!/^[\d,]+$/.test(value)) return 'Only digits and commas are allowed'

      const numericValue = Number(value.replace(/,/g, ''))
      if (isNaN(numericValue)) return 'Invalid salary amount'
      if (numericValue < 0) return 'Salary must be positive'
    },

    maxSalary: (value, all) => {
      if (!value) return 'Maximum salary is required'
      if (!/^[\d,]+$/.test(value)) return 'Only digits and commas are allowed'

      const numericValue = Number(value.replace(/,/g, ''))
      const minValue = all?.minSalary ? Number(all.minSalary.replace(/,/g, '')) : 0

      if (isNaN(numericValue)) return 'Invalid salary amount'
      if (numericValue < 0) return 'Salary must be positive'
      if (numericValue < minValue) {
        return 'Max salary must be greater than min salary'
      }
    },
    salaryPeriod: () => undefined,
    location: () => undefined,
    numOpenings: (value) => {
      if (!value || value <= 0) return 'Must be at least 1 opening'
    },
    // location: (value, all) => {
    //   if (all && all.remotePolicy !== 'remote') {
    //     if (!value.trim()) return 'Location is required'
    //     // if (!/^[A-Za-z\s,-]{2,100}$/.test(value)) return 'Invalid location format'
    //   }
    // },
    experience: (value) => (!value ? 'Select experience level' : undefined),
    jobType: (value) => (!value ? 'Select job type' : undefined),
    remotePolicy: () => undefined,
    /* industry: (value) => {
      if (!value.trim()) return 'Industry is required'
    }, */

    // Job Requirements
    requiredSkills: (value) => {
      if (!value || value.length === 0) return 'At least one skill is required'
    },
    // TODO: Remove this later
    requiredQuestions: (value) => {
      if(!value.isRequired) return "You need to Make this Required (DEMO PURPOSE)"
    },
    jobDescription: (value) => {
      if (!value.trim()) return 'Job description is required'

      const wordCount = value.trim().split(/\s+/).length

      if (wordCount < 100) return 'Description must be at least 100 words'
      if (wordCount > 450) return 'Description must not exceed 450 words'
    },

    // Company Details
    companyName: (value) => {
      if (!value.trim()) return 'Company name is required'
      if (!/^[A-Za-z0-9&.,'’\-\s]{3,100}$/.test(value)) return 'Invalid company name format'
    },

    website: (value) => {
      if (!value.trim()) return
      if (!/^(https?:\/\/)(?!-)(?!.*--)(?!.*\.\.)([a-zA-Z0-9-]{1,63}\.)+[a-zA-Z]{2,}$/.test(value))
        return 'Invalid website format'
    },

    companyLogo: () => undefined, // No validation needed for company logo as it's optional
    locationDetails: () => undefined,

    // Add validator for selectedSuggestions
    selectedSuggestions: () => undefined, // No validation needed as it's managed internally
  }

  const validateField = (name: keyof FormData, value: any): string | undefined => {
    return validators[name]?.(value, formData)
  }

  const handleInputChange = (name: keyof FormData, value: any) => {
    let formattedValue = value

    if ((name === 'minSalary' || name === 'maxSalary') && typeof value === 'string') {
      // Remove all non-digit characters (e.g., commas, letters)
      const numericValue = value.replace(/\D/g, '')

      // Only format if there's actually a numeric value
      if (numericValue) {
        formattedValue = formatIndianNumber(numericValue)
      } else {
        formattedValue = ''
      }
    }

    // If this is a manual skill addition, trigger suggestion fetch
    // if (name === 'requiredSkills' && Array.isArray(value)) {
    //   const newSkill = value[value.length - 1]
    //   if (newSkill && !skillsSuggestions.some((s) => s === newSkill)) {
    //     setFormData((prev) => ({
    //       ...prev,
    //       selectedSuggestions: [...(prev.selectedSuggestions || []), newSkill],
    //     }))
    //   }
    // }
    if (name === 'requiredSkills' && Array.isArray(value)) {
      const newSkill = value[value.length - 1]
      if (newSkill && !skillsSuggestions.some((s) => s === newSkill)) {
        // Fetch new skill suggestions when a skill is manually added
        fetchSuggestions('jobSkills')
      }
    }

    setFormData((prev) => {
      const newData = { ...prev, [name]: formattedValue }
      if (touched[name]) {
        const error = validateField(name, formattedValue)
        setFormErrors((prevErrors) => ({
          ...prevErrors,
          [name]: error,
        }))
      }
      return newData
    })
  }

  const handleSelectionChange = (name: keyof FormData, value: any) => {
    setFormData((prev) => {
      const newData = { ...prev, [name]: value }
      if (touched[name]) {
        const error = validateField(name, value)
        setFormErrors((prevErrors) => ({
          ...prevErrors,
          [name]: error,
        }))
      }
      return newData
    })
  }

  const handleBlur = (name: keyof FormData) => {
    setTouched((prev) => ({ ...prev, [name]: true }))
    const error = validateField(name, formData[name])
    setFormErrors((prev) => ({ ...prev, [name]: error }))
  }

  const handleStepChange = (targetStep: number) => {
    let firstInvalidStep: number | null = null

    for (let i = 0; i < targetStep; i++) {
      const errors: FormErrors = {}
      const fieldsToValidate = stepFields[i] || []

      fieldsToValidate.forEach((key) => {
        const error = validateField(key, formData[key])
        if (error) {
          errors[key] = error
        }
      })

      if (Object.keys(errors).length > 0) {
        // Mark fields as touched so errors show in UI
        setTouched((prev) => ({
          ...prev,
          ...fieldsToValidate.reduce((acc, key) => {
            acc[key] = true
            return acc
          }, {} as TouchedFields),
        }))

        setFormErrors((prevErrors) => ({ ...prevErrors, ...errors }))

        if (firstInvalidStep === null) {
          firstInvalidStep = i
        }
      }
    }

    // If any step was invalid, go to that step
    if (firstInvalidStep !== null) {
      setCurrentStep(firstInvalidStep)
      return
    }

    setCurrentStep(targetStep)
  }

  return (
    <section className="mb-16 flex min-h-screen w-full flex-col items-center justify-start">
      <JobPostingHeader
        steps={steps}
        currentStep={currentStep}
        onStepChange={handleStepChange}
        onPublish={handlePublish}
        isEditMode={isEditMode}
      />
      <div className="w-full max-w-[1440px] border-none md:w-11/12 xl:w-9/12">
        <main className="p-6">
          {currentStep === 0 && (
            <CreateJob
              formData={formData}
              formErrors={formErrors}
              onInputChange={handleInputChange}
              onBlur={handleBlur}
              onContinue={() => handleStepChange(1)}
              onSelectionChange={handleSelectionChange}
            />
          )}
          {currentStep === 1 && (
            <JobRequirement
              formData={formData}
              formErrors={formErrors}
              onInputChange={handleInputChange}
              onBlur={handleBlur}
              onContinue={() => handleStepChange(2)}
              selectedSuggestions={formData.selectedSuggestions || []}
              onSuggestionSelect={(suggestion) => {
                setFormData((prev) => ({
                  ...prev,
                  selectedSuggestions: [...(prev.selectedSuggestions || []), suggestion],
                }))
              }}
              suggestedDescriptions={suggestedDescriptions}
              skillsSuggestions={skillsSuggestions}
              isFetchingSkillsSuggestions={isFetchingSkillsSuggestions}
              isFetchingJobDescSuggestions={isFetchingJobDescSuggestions}
              fetchSuggestions={fetchSuggestions}
            />
          )}
          {currentStep === 2 && (
            <CompanyDetails
              formData={formData}
              formErrors={formErrors}
              onInputChange={handleInputChange}
              onBlur={handleBlur}
              onContinue={() => handleStepChange(3)}
            />
          )}
          {currentStep === 3 && (
            // expression
            <Questions
              formData={formData}
              formErrors={formErrors}
              onInputChange={handleInputChange}
              onBlur={handleBlur}
              onContinue={() => handleStepChange(4)}
            />
          )}

          {currentStep === 4 ? (
            <JobPreview formData={formData} />
          ) : (
            <div
              className={`flex w-full justify-end px-4 sm:px-5 md:w-5/6 ${
                currentStep === steps.length - 2 ? 'mt-2 md:ml-8 md:mt-0' : 'sm:ml-6 md:ml-8'
              }`}
            >
              <Button
                theme="dark"
                size={'medium'}
                type="submit"
                onClick={() => {
                  handleStepChange(currentStep + 1)
                }}
                disabled={stepFields[currentStep]?.some((key) => {
                  const value = formData[key]
                  const error = formErrors[key]

                  // Special case: location is not required if remotePolicy is 'remote'
                  if (key === 'location' && formData.remotePolicy === 'remote') return false
                  if (key === 'website') return
                  return value === '' || value === null || !!error
                })}
                className="px-2! py-3! md:px-7! md:py-4! mt-4 flex w-full items-center gap-2 rounded-lg sm:w-auto"
              >
                {currentStep === steps.length - 2 ? (
                  'Preview'
                ) : (
                  <>
                    Continue
                    <ArrowRightIcon size={18} />
                  </>
                )}
              </Button>
            </div>
          )}
        </main>
      </div>
    </section>
  )
}

export default JobPosting
