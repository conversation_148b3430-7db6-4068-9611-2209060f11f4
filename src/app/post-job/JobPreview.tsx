import {
  BriefcaseIcon,
  DotIcon,
  EyeIcon,
  IdentificationCardIcon,
  MoneyIcon,
  UsersIcon,
} from '@phosphor-icons/react'
import { FormData } from './page'
import {
  calculateSalaryRange,
  capitalizeWords,
  formatSalaryAbbreviation,
  truncateText,
} from '../../utils/commonUtils'
import Avatar from '../../components/Avatar/Avatar'
import { useAuth } from '../../context/AuthContext'
import SafeHtml from '../../components/SafeHtml'

interface JobPreviewProps {
  formData: FormData
}

const JobPreview: React.FC<JobPreviewProps> = ({ formData }) => {
  console.log("WE HAVE REACHED HERE");
  console.log("THIS IS THE FORM DATA",formData);
  const formatLocation = (location: string, remotePolicy: string) => {
    if (remotePolicy === 'remote') return 'Remote'
    if (remotePolicy === 'hybrid') return `${location} (Hybrid)`
    return location
  }

  const { authUserProfile } = useAuth()

  return (
    <div className="mx-auto flex w-full max-w-2xl flex-col items-center justify-center">
      <div className="dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500 relative mt-4 flex w-full flex-col justify-center gap-y-4 rounded-xl border border-[#F5F5F5]">
        <div className="dark:border-b-dark-lucres-black-300 flex w-full items-center gap-2 border-b p-4">
          <EyeIcon size={20} />
          <span>Job Preview</span>
        </div>
        <div className="flex flex-col gap-y-4 p-6">
          <div className="flex w-full items-start gap-x-4">
            <Avatar
              src={formData.companyLogo}
              alt="company logo"
              className="h-14! w-14! z-20 cursor-pointer rounded-full"
            />
            <div className="flex flex-col">
              <div className="flex flex-col sm:flex-row sm:items-center">
                <span className="flex items-center text-lg font-bold">
                  {truncateText(formData.jobTitle, 30).text}{' '}
                  <DotIcon weight="bold" size={14} className="hidden sm:block" />
                </span>
                <span className="text-lucres-900 dark:text-dark-lucres-green-100 cursor-pointer text-base">
                  {truncateText(formData.companyName, 15).text}
                </span>
              </div>
              <span className="dark:text-dark-lucres-green-100">
                {/* {formatLocation(
                  formData.location.address.state + ', ' + formData.location.address.country,
                  formData.remotePolicy,
                )} */}
                {formData.remotePolicy === 'REMOTE'
                  ? 'Remote'
                  : truncateText(
                      formData?.location?.address?.state +
                        ', ' +
                        formData?.location?.address?.country,
                      20,
                    ).text}{' '}
                {formData.remotePolicy === 'HYBRID' && '(Hybrid)'}
              </span>
            </div>
          </div>
          {/* <div className="flex flex-wrap items-center gap-4 text-sm">
            <div className="flex w-fit items-center gap-x-1 rounded-lg bg-lucres-400 bg-opacity-40 px-2 py-1 dark:border dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500">
              <CurrencyInr weight="bold" /> {(formData.minSalary, formData.maxSalary)}
            </div>
            <div className="flex w-fit items-center gap-x-1 rounded-lg bg-lucres-400 bg-opacity-40 px-2 py-1 dark:border dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500">
              {formData.jobType}
            </div>
            <div className="flex w-fit items-center gap-x-1 rounded-lg bg-lucres-400 bg-opacity-40 px-2 py-1 dark:border dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500">
              {formatLocation(formData.location, formData.remotePolicy)}
            </div>
            <div className="flex w-fit items-center gap-x-1 rounded-lg bg-lucres-400 bg-opacity-40 px-2 py-1 dark:border dark:border-dark-lucres-black-300 dark:bg-dark-lucres-black-500">
              {formData.experience} Experience
            </div>
          </div> */}
          <div className="text-lucres-900 dark:text-dark-lucres-green-100 mb-2 flex w-full flex-wrap items-start gap-4 ps-1 text-sm font-medium md:flex-row md:items-center md:gap-4 lg:gap-6">
            <div className="flex items-center gap-x-2">
              <UsersIcon size={24} className="text-lucres-900 dark:text-dark-lucres-green-100" />
              <span className="whitespace-nowrap text-sm font-medium">
                {formData?.numOpenings} Openings
              </span>
            </div>
            <div className="flex items-center gap-x-2">
              <BriefcaseIcon
                size={24}
                className="text-lucres-900 dark:text-dark-lucres-green-100"
              />
              {/* <span>{`${job?.workExperience?.count} ${job?.workExperience?.unit && convertToTitleCase(job?.workExperience?.unit)}`}</span> */}
              <span>{`${capitalizeWords(formData?.experience)}`}</span>
            </div>
            <div className="flex items-center gap-x-2">
              <MoneyIcon size={24} className="text-lucres-900 dark:text-dark-lucres-green-100" />
              <span>
                {formData.minSalary && formData.maxSalary
                  ? `${formatSalaryAbbreviation(
                      Number(formData.minSalary.replace(/,/g, '')),
                    )} - ${formatSalaryAbbreviation(Number(formData.maxSalary.replace(/,/g, '')))}`
                  : 'Not specified'}
              </span>
              <span className="text-lucres-gray-700 dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-300 dark:text-dark-lucres-green-100 inline-block rounded-[4px] border px-3 py-1 text-xs font-normal dark:border">
                {capitalizeWords(formData?.salaryPeriod || '')}
              </span>
            </div>
            <div className="flex items-center gap-x-2">
              <IdentificationCardIcon
                size={24}
                className="text-lucres-900 dark:text-dark-lucres-green-100"
              />
              <span>
                {capitalizeWords(
                  formData.jobType
                    .replace(/[^a-zA-Z0-9]/g, ' ')
                    .replace(/\s+/g, ' ')
                    .trim(),
                )}
              </span>
            </div>
          </div>
          <div className="flex flex-col gap-y-2">
            <span className="font-semibold">Required Skills</span>
            <div className="flex w-10/12 flex-wrap items-start justify-start gap-2 ps-1">
              {formData.requiredSkills.map((skill: string, i: number) => (
                <p
                  key={i}
                  className="text-lucres-900 dark:bg-dark-lucres-black-400 dark:text-dark-lucres-green-100 rounded-lg bg-[#EEEEEE] px-4 py-1 text-justify text-sm font-normal"
                >
                  {skill}
                </p>
              ))}
            </div>
          </div>
          <div className="flex flex-col gap-y-2">
            <span className="font-semibold">Job Description</span>

            {formData.jobDescription && (
              <SafeHtml
                html={formData.jobDescription}
                className="prose prose-sm dark:prose-invert max-w-none [&_ol]:list-decimal [&_ol]:pl-5 [&_ul]:list-disc [&_ul]:pl-5"
              />
            )}
          </div>

          {/* Questions Section */}
          {formData.requiredQuestions && formData.requiredQuestions.length > 0 && (
            <div className="mt-6 border-t pt-6">
              <h3 className="mb-4 text-lg font-semibold">Application Questions</h3>
              <div className="space-y-4">
                {formData.requiredQuestions.map((question, index) => (
                  <div key={index} className="rounded-lg border p-4">
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium">Question {index + 1}</span>
                      {question.isRequired && (
                        <span className="text-sm text-red-500">Required</span>
                      )}
                    </div>
                    <p className="mb-3 text-gray-700 dark:text-gray-300">{question.text}</p>

                    {question.questionType === 'MULTIPLE_CHOICE' && question.choices && (
                      <div className="space-y-2">
                        {question.choices.map((choice, choiceIndex) => (
                          <div key={choiceIndex} className="flex items-center">
                            <div className="mr-2 h-3 w-3 rounded-full border-2"></div>
                            <span className="text-sm text-gray-600 dark:text-gray-400">{choice}</span>
                          </div>
                        ))}
                      </div>
                    )}

                    {question.questionType === 'CHECKBOX' && question.choices && (
                      <div className="space-y-2">
                        {question.choices.map((choice, choiceIndex) => (
                          <div key={choiceIndex} className="flex items-center">
                            <div className="mr-2 h-3 w-3 border-2"></div>
                            <span className="text-sm text-gray-600 dark:text-gray-400">{choice}</span>
                          </div>
                        ))}
                      </div>
                    )}

                    {question.questionType === 'PARAGRAPH' && (
                      <div className="rounded border p-3 text-sm text-gray-500 dark:text-gray-400">
                        Candidate will provide a written response here...
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
      <div className="mt-8 flex flex-col items-center">
        <span className="text-light">This Job is posted by</span>
        <Avatar
          src={authUserProfile?.profileImage.url}
          alt={authUserProfile?.givenName + '`s Pic'}
          className="mt-4"
        />
        {/* <img src={formData?.companyLogo?.file} alt="nikita" className="mt-4" /> */}
        <span className="mt-4 text-lg font-semibold">
          {authUserProfile?.givenName + ' ' + authUserProfile?.familyName}
        </span>
        {/* <span>{formData.}</span> */}
      </div>
      <div className="text-lucres-300 dark:text-dark-lucres-black-200 mt-10 text-xs">
        Recruitment is both an art and a science that requires persistence, empathy, and resilience.
      </div>
    </div>
  )
}

export default JobPreview
