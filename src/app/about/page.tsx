import { PhosphorHeartIcon } from '@/components/IconsProvider'
import Tag from '../../components/Tag'
import Footer from '@/components/Footer'

const About = () => {
  return (
    <div className="dark:bg-dark-lucres-black-500 h-full">
      <div className="bg-lucres-100 dark:bg-dark-lucres-black-400 relative w-full pb-20 pt-20 lg:pt-40">
        <img
          src={'/aboutus/left-bottom.png'}
          alt=""
          width={239}
          height={293}
          className="absolute bottom-0 left-0 z-10 hidden lg:block"
        />
        <img
          src={'/aboutus/right-bottom.png'}
          alt=""
          width={180}
          height={263}
          className="absolute bottom-0 right-0 z-10 hidden lg:block"
        />
        <div className="w-769px flex flex-col gap-5 text-center">
          <div className="flex w-full items-center justify-center">
            <Tag theme="tertiary" TagText="About Our Company" />
          </div>
          <h1 className="text-3xl font-medium">Made with love, Right here in Bangalore</h1>
          <div className="dark:text-dark-lucres-green-200 text-gray-500">
            We exist to help job seekers find jobs with one tweet.
          </div>
        </div>
      </div>
      <div className="p-8 pb-24 lg:p-28">
        <div className="m-auto w-full xl:w-1/2">
          At Lucres,{' '}
          <span className="dark:text-dark-lucres-green-100 font-semibold text-black">
            our mission is to connect the world's professionals and make them more productive and
            successful.
          </span>
           We believe that every individual has the potential to succeed and advance in their
          career, and we strive to provide the tools and resources to make that possible. <br />
          <br />
          Our platform allows professionals to create and maintain their online profile and showcase
          their skills, experience, and education. Through our job search, members can find and
          apply to job openings that match their qualifications and career goals. we also reward
          users who connect their community to the right opportunities, by incentivising them for
          every opportunity they repost.
          <br />
          <br />
          At Lucres, we value diversity, inclusivity, and belonging, and we work to ensure that our
          platform is a safe and welcoming space for all members. We prioritise the privacy and
          security of our members' information and have strict policies and procedures in place to
          protect their data.
          <br />
          <br />
          Whether you're a recent graduate looking for your first job or an experienced professional
          seeking new opportunities, Lucres is the perfect place to grow your network, advance your
          career, and achieve your goals.
        </div>
        <div className="m-auto mt-8 w-72 text-center lg:mt-24">
          <PhosphorHeartIcon className="m-auto mb-3 text-lime-300" weight="fill" color="#e00606" />
          <div className="from-lucres-900 to-lucres-600 dark:from-dark-lucres-green-100 dark:to-dark-lucres-green-300 bg-linear-to-r inline-block bg-clip-text text-transparent">
            Made with love, Right here in Bangalore
          </div>
        </div>
      </div>
      <Footer />
    </div>
  )
}

export default About
