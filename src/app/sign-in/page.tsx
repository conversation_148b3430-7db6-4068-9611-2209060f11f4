'use client'

import { useEffect, useState } from 'react'
import { CheckIcon } from '@phosphor-icons/react'
import { useAuth } from '../../context/AuthContext'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Button from '../../components/Button'
import Input from '../../components/Input'
import FormHeader from '../../components/FormHeader'
import { useGoogleLogin } from '@react-oauth/google'
import useError from '../../context/ErrorContext'

const SignIn = () => {
  const router = useRouter()
  const { login, googleLogin } = useAuth()
  const { handleError } = useError()

  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [errors, setErrors] = useState({
    email: '',
    password: '',
    credentials: '',
  })

  const handleUserLogin = async () => {
    try {
      const response = await login(email.toLowerCase(), password)
      if (response.status === 'success') {
        router.push('/feed')
      } else {
        throw new Error(response.message || 'SignIn failed. Try again.')
      }
    } catch (error: any) {
      if (error.message) {
        setError(error.message)
      } else if (error.data?.message) {
        setError(error.data.message)
      } else if (
        error.message ===
        'Check Username. Unable to fetch account for the username provided, contact support'
      ) {
        setError('Invalid Username')
      } else if (
        error.message ===
          "Oops, looks like you've entered the incorrect username or password. Try again!" ||
        'Password does not meet minimum length.'
      ) {
        setError('Incorrect Password')
      } else {
        setError('SignIn failed. Try again.')
      }
    }
  }

  const handleGoogleSignIn = useGoogleLogin({
    onSuccess: async (response) => {
      try {
        const googleResponse = await googleLogin(response.code)
        if (googleResponse.status === 'success') {
          router.push('/feed')
        } else {
          throw new Error(googleResponse.message || 'Google Sign-in failed. Try again.')
        }
      } catch (error: any) {
        if (error.message) {
          setError(error.message)
        } else if (error.data?.message) {
          setError(error.data.message)
        } else {
          setError('Google Sign-in failed')
        }
      }
    },
    onError: (error) => {},
    flow: 'auth-code',
  })

  const handleRegisterUser = () => {
    router.push('/sign-up')
  }

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value)
    if (error) setError('')
  }

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value)
    if (error) setError('')
  }

  return (
    <>
      <header className="bg-lucres-900 dark:bg-dark-lucres-black-500 fixed left-0 top-0 z-10 hidden w-full xl:block">
        <div className="mx-auto flex max-w-7xl items-center justify-between px-4 pt-4">
          <FormHeader />
        </div>
      </header>

      <div className="bg-lucres-900 dark:bg-dark-lucres-black-500 relative flex h-screen w-full items-center justify-center py-3">
        <div className="flex h-full w-full max-w-7xl flex-1 items-center gap-x-10 p-4 lg:items-start lg:ps-16 lg:pt-16">
          {/* Left Form Content */}
          <div className="mx-auto w-full max-w-md flex-1">
            <div className="dark:border-dark-lucres-black-200 dark:bg-dark-lucres-black-500 mx-auto my-4 w-full max-w-sm rounded-xl bg-white px-8 py-6 dark:border">
              <h3 className="text-dark-lucres-black-100 mb-2 text-sm font-medium">LOGIN</h3>
              <h2 className="mb-4 text-2xl font-medium">Welcome back to Lucres</h2>

              <form className="flex flex-col">
                <Input
                  type="text"
                  id="email"
                  value={email}
                  onChange={handleEmailChange}
                  placeholder="Enter your Email / Username"
                  label="Email / Username"
                  required
                  error={errors.email}
                />

                <Input
                  type="password"
                  id="password"
                  value={password}
                  onChange={handlePasswordChange}
                  placeholder="Enter Password"
                  label="Password"
                  required
                  error={errors.password}
                  maxLength={30}
                />

                <div className="mb-1">
                  <div className="-mt-3 mb-8 flex justify-end">
                    <Link href="/forgot-password" className="text-sm text-blue-500">
                      Forgot Password?
                    </Link>
                  </div>
                  {error && <p className="-mt-3 mb-3 text-sm text-red-500">{error}</p>}

                  <Button
                    theme="opaque"
                    className="gap-0! w-full rounded-lg"
                    onClick={handleUserLogin}
                  >
                    Continue
                  </Button>
                </div>
              </form>

              <div className="mt-3 text-center">
                <p className="text-lucres-800 leading-4">or </p>
                <div className="mt-4">
                  <Button
                    type="button"
                    onClick={handleGoogleSignIn}
                    theme="transparent"
                    size="medium"
                    className="border-lucres-300! w-full rounded-lg"
                  >
                    <img src="/signin/Avatar.svg" alt="google-icon" className="me-2" /> Sign in with
                    Google
                  </Button>
                </div>
              </div>

              <div className="mt-4 text-center">
                <p className="text-lucres-800 dark:text-dark-lucres-green-300 mb-2 text-xs">
                  New to Lucres? Join our free platform{' '}
                </p>
                <Button
                  theme="transparent"
                  type="button"
                  size="small"
                  onClick={handleRegisterUser}
                  className="border-lucres-700! text-lucres-700! dark:border-dark-lucres-green-400! dark:text-dark-lucres-green-400! dark:hover:text-lucres-500 duration-400 mb-2 rounded-full text-sm tracking-wider transition-colors ease-in-out"
                >
                  Register for free
                </Button>
                <p className="dark:text-dark-lucres-green-300 -mx-4 pt-3 text-xs text-gray-500">
                  By continuing you agree to our{' '}
                  <Link href="#" className="text-blue-500 hover:underline">
                    privacy policy
                  </Link>{' '}
                  and{' '}
                  <Link href="#" className="text-blue-500 hover:underline">
                    terms of use
                  </Link>
                </p>
              </div>
            </div>
          </div>

          {/* Right Content */}
          <div className="hidden flex-col justify-start gap-y-4 px-1 pt-8 text-left text-white lg:flex">
            <h1 className="w-80 text-4xl leading-[45px] xl:w-96">
              Making Job Hunting as{' '}
              <span className="font-medium tracking-tighter text-lime-300">Common as Tweet</span>
            </h1>
            <ul className="flex flex-col gap-y-4 ps-2 text-base">
              <li className="flex items-center justify-start gap-x-1">
                <CheckIcon size={24} /> Join the fastest growing networking website today
              </li>
              <li className="flex items-center justify-start gap-x-1">
                <CheckIcon size={24} /> Build AI Resume for free
              </li>
              <li className="flex items-center justify-start gap-x-1">
                <CheckIcon size={24} /> Post Unlimited Jobs for free
              </li>
            </ul>
          </div>
        </div>

        <div className="relative hidden h-full w-96 xl:block">
          <span className="dark:bg-dark-lucres-black-100 fixed bottom-[30%] right-[3%] z-0 h-96 w-96 overflow-hidden rounded-full bg-lime-400 bg-opacity-30 opacity-30 blur-2xl dark:blur-3xl"></span>
          <img
            src="/signin/randomimg1.svg"
            alt="SignIn User Image"
            className="fixed bottom-0 right-6 z-10 w-96"
          />
        </div>
      </div>
    </>
  )
}

export default SignIn
