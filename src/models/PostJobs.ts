interface WorkExperience {
  unit?: 'YEARS' | 'MONTHS' | string
  min: number
  max: number
  count?: number
}

interface Skill {
  name: string
  level: number
}

interface Salary {
  min: number
  max: number
  period: string
}

interface Company {
  name: string
  websiteUrl: string
}
interface Degree {
  name: string
  other: boolean
}

interface Address {
  country: string
  state: string
  city: string
  area: string
}

interface Location {
  latitude: number
  longitude: number
  address: Address
}

export interface Question {
  questionType: 'MULTIPLE_CHOICE' | 'PARAGRAPH' | 'CHECKBOX';
  text: string;
  isRequired: boolean;
  choices?: string[]; // optional because PARAGRAPH questions don't have choices
}

export interface PostJobRequest {
  title: string
  description: string
  workMode?: string
  employmentType: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERN' | string
  experienceLevel?: 'BEGINNER' | 'INTERMEDIATE' | 'EXPERT' | string
  workExperience: WorkExperience
  skills: Skill[]
  degree?: Degree
  salary: Salary
  openings: number
  designation?: string
  company: Company
  industry?: string
  location: Location
  questions: Question[]
}
