import { Image, Location } from './User'

interface Degree {
  other: boolean
  name: string
}

export interface Skill {
  name: string
  level: number
}

interface Company {
  name: string
  websiteUrl: string
}
interface User {
  id: string
  givenName: string
  familyName: string
  profileImage: Image
  permalink: string
  headline: string
  username: string
  followersCount: Number
}

export interface Salary {
  raw?: any
  value: string
  min: number
  max: number
  period: 'MONTHLY' | 'YEARLY' | 'HOURLY'
}

interface JobDetails {
  id: string
  name: string // Job title
  title: string
  permalink: string
  workMode: string
  author: string // For simplicity, used as company name
  degree: string
  openings: number
  likesCount: number
  description: string
  rePostCount: number
  img?: string
  location?: string | Record<string, any>
  headline?: string
  company?: string | Record<string, any>
  employmentType: string
  skills: string[]
  workExperience: { value: string; min: number; max: number; unit: string; numeric: number }
  salary: Salary
  createdAt: string
}

export interface SuggestedJob {
  id: string
  permalink: string
  company: Company
  location?: Location
  experienceLevel: string
}

export interface JobPreviewDetails {
  id: string
  isApplied: boolean
  permalink: string
  // user: User // For simplicity, used as company name
  jobPoster: User // For simplicity, used as company name
  degree: Degree
  jobCode: string
  title: string
  description: string
  openings: number
  company: Company
  companyLogo: Image
  industry: string
  designation: string
  employmentType: string
  workExperience: {
    min: number
    max: number
    unit: string
  }
  workMode: string
  experienceLevel: string
  skills: Record<string, unknown>[]
  location?: Location
  createdAt: string
  updatedAt: string
  salary: Salary
  likesCount: number
  rePostCount: number
  applicationsCount: number
  jobStatus: string
  isBookmark?: boolean
}

export interface Job {
  id?: string
  status?: string
  job: JobDetails
  isApplied?: boolean
  isRePosted?: boolean
  isLiked?: boolean
  isBookmark?: boolean
  ownJob?: boolean
}

export interface TrendingJob {
  id: string
  title: string
  company: {
    name: string
    websiteUrl: string
  }
  companyLogo?: {
    url: string
    name: string
  }
  location: {
    address: {
      country: string
      state: string
      city: string
      area: string
      citySlug: string
    }
    latitude: number
    longitude: number
  }
  employmentType: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | string
  permalink: string
  jobStatus: 'APPROVED' | 'REJECTED' | 'PENDING' | string
}
