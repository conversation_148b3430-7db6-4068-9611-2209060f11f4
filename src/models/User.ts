import { Pagin<PERSON> } from './Paginator'

export interface Image {
  name: string
  url: string
}

interface Address {
  line1: string
  line2?: string
}

interface LocationAddress {
  country: string
  state: string
  city: string
  area?: string
  citySlug: string
}

export interface Location {
  address: LocationAddress
  latitude: number
  longitude: number
}

interface ProfileMetrics {
  aboutMe: boolean
  achievement: boolean
  experience: boolean
  education: boolean
  skills: boolean
  languages: boolean
  location: boolean
  pfp: boolean
}

export interface UserContact {
  primaryEmail: string
  primaryPhone: string
  secondaryPhone: string
  secondaryEmail: string
  countryCode: string
}

export interface UserProfile {
  id: string
  username: string
  givenName: string
  familyName: string
  headline: string
  title: string
  address: Address
  location: Location
  birthDate: string
  gender: string
  nationality: string
  permalink: string
  profileImage: Image
  coverImage: Record<string, unknown>
  profileCompletion: number
  profileMetrics: ProfileMetrics
  isActive: boolean
  hasNewMessage: boolean
  isUserVerified: boolean
  subscription: Record<string, unknown>
  recruiterTrial: boolean
}

export interface Preferances {
  allowRecruiterContact: boolean
  visibleInSearch: boolean
}

export interface Preferences {
  allowRecruiterContact: boolean
  visibleInSearch: boolean
}

export interface PermalinkUser {
  id: string
  username: string
  givenName?: string
  familyName?: string
  isActive?: boolean
  profileImage: Image
  nationality?: string
  coverImage: Record<string, unknown>
  permalink: string
  headline: string
  title: string
  address: Address
  gender?: string
  location: Location
  companyName: string
  isSearchActive: boolean
  followersCount: number
  followingCount: number
  birthDate?: string
  profileCompletion: number
  profileMetrics: ProfileMetrics
  preferences: Preferences
  postsCount: number
  isContactRestricted: boolean
  isUserVerified: boolean
  isFollowing: boolean
  isFollower: boolean
  isContactPurchased: boolean
  hasNewMessage: boolean
  subscription: Record<string, unknown>
  recruiterTrial: boolean
}

export interface TalentUser {
  id: string
  name: string
  username: string
  img: string
  aboutMe: string
  location: string
  headline: string
  skills: string[]
  workExperience: string
  downloadsCount: number
  permalink: string
  createdAt: string // ISO date string
  isBookmark?: boolean
}

export interface SuggestedTalent {
  id: string
  givenName: string
  familyName?: string
  username: string
  profileImage: Image
  permalink: string
  headline: string
  location: Location
}
// Define the TalentCard interface that wraps the user info.
// The additional fields are optional to accommodate both logged-in and not logged-in states.
export interface TalentCard {
  user: TalentUser
  isFollowing?: boolean
  isBookmark?: boolean
  isContactRestricted?: boolean
}

export interface FollowUser {
  id: string
  permalink: string
  givenName: string
  familyName: string
  headline: string
  username: string
  profileImage: Image
}

export interface FollowItem {
  id: string
  createdAt: string // ISO date string
  isFollower: boolean
  isFollowing: boolean
  entity: FollowUser
}

export interface FollowingsList {
  data: FollowItem[]
}

export interface FollowersList {
  data: FollowItem[]
}

export interface Order {
  id: string
  flow: 'SUBSCRIPTION' | 'JOB_DASHBOARD' | 'PROFILE' | 'PRO_RESUME'
  amount: {
    taxes: {
      gst: {
        scale: string
        rate: number
        amount: number
      }
    }
    subtotal: number
    total: number
  }
  status: 'SUCCESS' | string
  orderNo: number
  createdAt: string
  paymentMethod: string
  subscription?: {
    id: string
    plan: string // e.g., "CLS_100"
    status: 'ACTIVE' | 'INACTIVE' | 'EXPIRED'
    createdAt: string // ISO date string
    expiresAt: string // ISO date string
    daysRemaining: number
    resumesRemaining: number
    resumesTotal: number
  }
  job?: {
    id: string
    title: string
    company: string
    address: {
      country: string
      state: string
      city: string
      area: string
      citySlug: string
    }
    permalink: string
    applicationsCount: number
    applicantsDetails: Applicant[]
  }
  profile?: {
    _id: string
    username: string
    givenName: string
    familyName: string
    fullName: string
    profileImage?: Image
  }
}

export interface Applicant {
  id: string
  username: string
  givenName: string
  familyName: string
  fullName: string
  profileImage?: Image
}

export interface Wallet {
  wallet: {
    id: string
    amount: number
    currency: string
  }
  transactions: {
    items: TransactionItem[]
    paginator: Paginator
  }
}

export interface TransactionItem {
  id: string
  amount: number
  date: string
}

export interface Subscription {
  id: string
  plan: string // e.g., "CLS_100"
  status: 'ACTIVE' | 'INACTIVE' | 'EXPIRED'
  createdAt: string // ISO date string
  expiresAt: string // ISO date string
  daysRemaining: number
  resumesRemaining: number
  resumesTotal: number
}

export interface ProfileImage {
  url?: string
  name?: string
}

export interface ProfileAuthor {
  id: string
  username: string
  givenName: string
  familyName: string
  permalink: string
  profileImage: ProfileImage
  headline: string
}

export interface ApplicationList {
  id: string
  author: string
  createdAt: string
  totalResults?: number
  profileAuthor: ProfileAuthor
}
