export interface PersonalDetails {
  name: string
  profile?: string
  profilePicture?: string
  contactNumber: string
  email: string
  location: string
  nationality: string
  dateOfBirth?: string
}
export interface NamedEntity {
  other: boolean
  name: string
}

export interface Address {
  country: string
  state: string
  city: string
  area: string
}

export interface Location {
  address: Address
  latitude: number
  longitude: number
}

export interface Employment {
  location: Location
  employmentType: string
  isActive: boolean
  company: NamedEntity
  designation: NamedEntity
  startDate: string
  endDate?: string
  description?: string
  isCurrent: boolean
}

export interface Education {
  location: Location
  institution: NamedEntity
  startYear: number
  endYear: number
  degree: NamedEntity
  description?: string
  fieldOfStudy?: NamedEntity
  isCurrent?: boolean
}

export interface Achievement {
  title: string
  awardedBy?: string
  date: string
}

export interface Skill {
  name: string
  level?: number
}

export interface Language {
  name: string
  level?: string
  other: boolean
}

export interface SocialLink {
  title: string
  url: string
}

export interface Reference {
  name: string
  email: string
}

export interface Resume {
  personalDetails: PersonalDetails
  aboutMe: string
  experiences: Employment[]
  educations: Education[]
  achievements: Achievement[]
  skills: Skill[]
  languages: Language[]
  socialLinks: SocialLink[]
  references: Reference[]
}

export type ClearedSections = {
  aboutMe: boolean
  experiences: boolean
  educations: boolean
  achievements: boolean
  skills: boolean
  languages: boolean
  socialLinks: boolean
  references: boolean
}

export const DummyResumeData: Resume = {
  personalDetails: {
    name: 'Firstname Lastname',
    profilePicture: '/dashboard/hero1.png',
    contactNumber: '9893120961',
    email: '<EMAIL>',
    location: 'Bengaluru',
    nationality: 'Indian',
    profile: 'Software Engineer',
    dateOfBirth: '29/10/1994',
  },
  aboutMe:
    'Beginning with your most recent position, describe your experience, skills, and resulting outcomes in bullet or paragraph form. [Note: Begin each line with an action verb and include details that will help the reader understand your accomplishments, skills, knowledge, abilities, or achievements. Quantify where possible. Do not use personal pronouns; each line should be a phrase rather than a full sentence.]',
  experiences: [
    {
      location: {
        address: {
          country: 'India',
          state: 'Karnataka',
          city: 'Bengaluru',
          area: 'Bengaluru',
        },
        latitude: 12.9716,
        longitude: 77.5946,
      },
      employmentType: 'FULL_TIME',
      isActive: true,
      company: { other: false, name: 'ORGANIZATION' },
      designation: { other: false, name: 'Position Title' },
      startDate: '08/2024',
      endDate: '07/2026',
      description:
        'Beginning with your most recent position, describe your experience, skills, and resulting outcomes in bullet or paragraph form. [Note: Begin each line with an action verb and include details that will help the reader understand your accomplishments, skills, knowledge, abilities, or achievements. Quantify where possible. Do not use personal pronouns; each line should be a phrase rather than a full sentence.]',
      isCurrent: false,
    },
  ],
  educations: [
    {
      location: {
        address: {
          country: 'India',
          state: 'Karnataka',
          city: 'Bengaluru',
          area: 'Bengaluru',
        },
        latitude: 12.9716,
        longitude: 77.5946,
      },
      institution: { other: false, name: 'COLLEGE/UNIVERSITY' },
      description:
        '[Note: This section can be formatted similarly to the Experience section, or you can omit descriptions for activities. If this section is more relevant to the opportunity you are applying for, consider moving this above your Experience section.]',
      startYear: 2015,
      endYear: 2024,
      degree: { other: false, name: 'Degree' },
      isCurrent: false,
    },
  ],
  achievements: [
    {
      title: 'Title',
      awardedBy: 'Awarded By',
      date: 'Aug 2022',
    },
  ],
  skills: [
    { name: 'Skill 1', level: 50 },
    { name: 'Skill 2', level: 50 },
    { name: 'Skill 3', level: 50 },
    { name: 'Skill 4', level: 50 },
  ],
  languages: [
    { name: 'English', level: 'FLUENT', other: false },
    { name: 'French', level: 'FLUENT', other: false },
    { name: 'Hindi', level: 'FLUENT', other: false },
  ],
  socialLinks: [
    { title: 'GitHub', url: 'https://github.com/' },
    { title: 'Website', url: 'https://example.com' },
  ],
  references: [{ name: 'Prashant Bhushan', email: '<EMAIL>' }],
}
