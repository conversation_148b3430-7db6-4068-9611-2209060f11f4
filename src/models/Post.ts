import { Job, Sal<PERSON> } from './Job'

interface ProfileImage {
  name?: string
  url?: string
}

export interface Image {
  mimetype: string
  publicId: string
  width: number
  height: number
  secureUrl: string
}

export interface PostUser {
  id: string
  givenName: string
  familyName: string
  profileImage: ProfileImage
  permalink: string
  headline: string
  username: string
  followersCount: number
  companyLogo: ProfileImage
}

interface Article {
  id: string
  user: PostUser
  articleCode: number
  quoteCode: string
  content: string
  updatedAt: string
  createdAt: string
  images: Image[]
  likesCount: number
  commentsCount: number
  quoteCount: number
  rePostCount: number
}

interface RepostedItem {
  id: string
  quoteCode: string
  content: string
  createdAt: string
  updatedAt: string
  type: 'ARTICLE' | 'JOB' | 'QUOTE'
  quotedBy: PostUser
  quotedFrom: Article
  likesCount: number
  commentsCount: number
  quoteCount: number
  rePostCount: number
  user: PostUser
  articleCode: number
  images: Image[]
  title: string
  description: string
  designation: string
  openings: number
  degree: {
    name: string
  }
  permalink: string
  location: {
    address: {
      city: string
      country: string
    }
  }
  headline?: string
  employmentType: string
  skills: { name: string; level: number }[]
  company: { name: string; level: number }

  workExperience: { value: string; min: number; max: number; unit: string; numeric: number }
  workMode: string
  salary: Salary
}

interface Repost {
  id: string
  createdAt: string
  updatedAt: string
  type: 'ARTICLE' | 'QUOTE' | 'JOB'
  repostedBy: PostUser
  repostedItem: RepostedItem
}

interface Quote {
  id: string
  quoteCode: string
  content: string
  createdAt: string
  updatedAt: string
  likesCount: number
  commentsCount: number
  quoteCount: number
  rePostCount: number
  type: 'ARTICLE' | 'QUOTE'
  quotedBy: PostUser
  quotedFrom: Article
}

export interface PostCard {
  id: string
  type: 'ARTICLE' | 'REPOST' | 'QUOTE' | 'JOB'
  isLiked: boolean
  isReposted: boolean
  isApplied?: boolean
  article?: Article
  repost?: Repost
  quote?: Quote
  job?: any
}

export interface CommentUser {
  id: string
  username: string
  givenName: string
  familyName: string
  headline: string
  permalink: string
  followersCount: number
  profileImage: ProfileImage
}

export interface Comment {
  id: string
  text: string
  createdAt: string
  updatedAt: string
  user: CommentUser
}

// {
//   "id": "6811eaa54e36275b36edd1ca",
//   "quoteCode": "94719226429965707424",
//   "content": "checking",
//   "createdAt": "2025-04-30T09:17:25.778Z",
//   "updatedAt": "2025-04-30T09:17:25.778Z",
//   "likesCount": 0,
//   "commentsCount": 0,
//   "quoteCount": 0,
//   "rePostCount": 0,
//   "type": "ARTICLE",
//   "quotedBy": {
//       "id": "",
//       "givenName": "john",
//       "familyName": "doe",
//       "profileImage": {
//           "name": "john doe Profile photo",
//           "url": "https://res.cloudinary.com/lucres/image/upload/v1744785063/lucres/n4uvYs6uuK--cropped.png.png"
//       },
//       "permalink": "johndoe",
//       "headline": "",
//       "username": "johndoe",
//       "followersCount": 0
//   },
//   "quotedFrom": {
//       "id": "680f5d2aeec26144084ee0b4",
//       "user": {
//           "id": "680b112657fd8a0d5e6d27d2",
//           "givenName": "rakesh",
//           "familyName": "singh",
//           "profileImage": {},
//           "permalink": "spiderman",
//           "headline": "",
//           "username": "spiderman",
//           "followersCount": 0
//       },
//       "articleCode": 62383353,
//       "content": "testing",
//       "updatedAt": "2025-04-28T16:42:19.372Z",
//       "createdAt": "2025-04-28T10:49:14.645Z",
//       "images": [],
//       "likesCount": 1,
//       "commentsCount": 0,
//       "quoteCount": 0,
//       "rePostCount": 3
//   }
// }
