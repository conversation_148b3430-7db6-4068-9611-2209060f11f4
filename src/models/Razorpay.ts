// Razorpay Checkout types for client-side integration

export interface RazorpayOptions {
  key: string
  amount?: number
  currency?: string
  name?: string
  description?: string
  image?: string
  order_id?: string
  handler: (response: RazorpayResponse) => void | Promise<void>
  prefill?: {
    name?: string
    email?: string
    contact?: string
    method?: string
  }
  notes?: Record<string, any>
  theme?: {
    color?: string
  }
  modal?: {
    ondismiss?: () => void
    escape?: boolean
    backdropclose?: boolean
  }
  [key: string]: any
}

export interface RazorpayResponse {
  razorpay_payment_id: string
  razorpay_order_id: string
  razorpay_signature: string
}

export interface RazorpayPaymentFailedResponse {
  error: {
    code: string
    description: string
    source: string
    step: string
    reason: string
    metadata: {
      order_id: string
      payment_id: string
    }
  }
}

// Extend the Window interface for Razorpay
export interface RazorpayConstructor {
  new (options: RazorpayOptions): RazorpayInstance
}

export interface RazorpayInstance {
  open(): void
  on(event: 'payment.failed', handler: (response: RazorpayPaymentFailedResponse) => void): void
  // Add more event types as needed
}
