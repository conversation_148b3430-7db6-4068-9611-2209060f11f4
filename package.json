{"name": "lucres-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "npm run format && next build", "start": "next start", "lint": "next lint", "format": "prettier --config prettier.config.js --ignore-path .gitignore \"**/*.{ts,tsx,js,jsx,json,css,scss,md}\" --write", "format:check": "prettier --config prettier.config.js --ignore-path .gitignore \"**/*.{ts,tsx,js,jsx,json,css,scss,md}\" --check", "release": "standard-version", "release:minor": "standard-version --release-as minor", "release:major": "standard-version --release-as major"}, "dependencies": {"@phosphor-icons/react": "^2.1.10", "@react-oauth/google": "^0.12.2", "@react-pdf/renderer": "^4.3.0", "@types/swiper": "^5.4.3", "axios": "^1.9.0", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "emoji-picker-react": "^4.12.2", "google-fonts": "^1.0.0", "isomorphic-dompurify": "^2.25.0", "next": "15.3.3", "react": "^19.0.0", "react-confetti-boom": "^2.0.1", "react-dom": "^19.0.0", "react-easy-crop": "^5.4.2", "react-icons": "^5.5.0", "react-simple-wysiwyg": "^3.2.2", "swiper": "^11.2.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/google.maps": "^3.58.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.5.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.5.14", "standard-version": "^9.5.0", "tailwindcss": "^4", "typescript": "^5"}}