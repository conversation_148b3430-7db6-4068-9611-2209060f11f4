<svg width="224" height="120" viewBox="0 0 224 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <style>
    /* Animation for the laser effect */
    .laser {
      stroke-dasharray: 30, 750;
      stroke-dashoffset: -300;
      animation: dash 3s linear infinite;
    }

    @keyframes dash {
      from{
          stroke-dashoffset: -300;
      }
      to {
        stroke-dashoffset: 300;
      }
    }
  </style>

  <!-- Constant Green Path -->
  <path d="M10 1H33.7788C58.7828 1 82.7274 11.0966 100.182 29V29C117.637 46.9034 141.582 57 166.586 57H224" stroke="url(#paint0_linear_2230_6870)"/>
  <path d="M0 119H51.2752C78.2076 119 103.978 108.027 122.643 88.6121L123.82 87.3879C142.486 67.9727 168.256 57 195.188 57H222" stroke="url(#paint1_linear_2230_6870)"/>
  <path d="M70 68H99.7296C109.415 68 119.049 66.5786 128.322 63.7812L136.816 61.2188C146.089 58.4214 155.723 57 165.409 57H220" stroke="url(#paint2_linear_2230_6870)"/>

  <!-- Animated DarkGreen Laser Path -->
  <path class="laser" d="M0 1H33.7788C58.7828 1 82.7274 11.0966 100.182 29V29C117.637 46.9034 141.582 57 166.586 57H224" stroke="#22c55e" stroke-width="1"/>
  <path class="laser" d="M0 119H51.2752C78.2076 119 103.978 108.027 122.643 88.6121L123.82 87.3879C142.486 67.9727 168.256 57 195.188 57H222" stroke="#22c55e" stroke-width="1"/>
  <path class="laser" d="M0 68H99.7296C109.415 68 119.049 66.5786 128.322 63.7812L136.816 61.2188C146.089 58.4214 155.723 57 165.409 57H220" stroke="#22c55e" stroke-width="1"/>

  <defs>
    <linearGradient id="paint0_linear_2230_6870" x1="39.2482" y1="1.50009" x2="224.523" y2="65.0558" gradientUnits="userSpaceOnUse">
      <stop stop-color="#B6E777"/>
      <stop offset="1" stop-color="#DEFFB3"/>
    </linearGradient>
    <linearGradient id="paint1_linear_2230_6870" x1="157.048" y1="57" x2="69.0375" y2="116.437" gradientUnits="userSpaceOnUse">
      <stop stop-color="#B3E86F"/>
      <stop offset="1" stop-color="#D5F5AB"/>
    </linearGradient>
    <linearGradient id="paint2_linear_2230_6870" x1="191.409" y1="68" x2="145" y2="68" gradientUnits="userSpaceOnUse">
      <stop stop-color="#B3E86F"/>
      <stop offset="1" stop-color="#C9EB9D"/>
    </linearGradient>
  </defs>
</svg>
