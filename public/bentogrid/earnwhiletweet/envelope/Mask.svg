<svg width="full" height="full" viewBox="0 0 152 115" fill="none" xmlns="http://www.w3.org/2000/svg">
    <mask id="mask0_52_5441" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="-34" width="152" height="149">
    <path d="M0.687505 26.282C0.469218 23.787 0.360074 22.5395 0.604897 21.377C0.821636 20.3479 1.23917 19.3716 1.8336 18.504C2.50505 17.524 3.48251 16.7411 5.43742 15.1755L60.9724 -29.3006C63.3796 -31.2284 64.5832 -32.1923 65.9357 -32.6443C67.131 -33.0436 68.4037 -33.155 69.6502 -32.9692C71.0606 -32.759 72.4133 -32.0188 75.1187 -30.5382L137.533 3.61864C139.73 4.82101 140.829 5.4222 141.66 6.27073C142.396 7.02192 142.977 7.91088 143.369 8.88673C143.812 9.98903 143.921 11.2365 144.14 13.7316L150.794 89.7907C151.184 94.2541 151.38 96.4858 150.66 98.2665C150.027 99.833 148.915 101.159 147.482 102.054C145.853 103.072 143.622 103.267 139.158 103.658L21.2087 113.977C16.7453 114.367 14.5136 114.563 12.7329 113.843C11.1665 113.21 9.8406 112.098 8.94534 110.665C7.92756 109.036 7.73231 106.804 7.34182 102.341L0.687505 26.282Z" fill="black"/>
    </mask>
    <g mask="url(#mask0_52_5441)">
        <g filter="url(#filter0_di_52_5441)">
            <path d="M0.0904541 19.4578L8.4574 115.092L53.8966 73.0936C58.0311 69.2722 60.0983 67.3614 60.7134 65.2023C61.2525 63.3101 61.0753 61.2857 60.2159 59.5159C59.2352 57.4963 56.8676 55.9736 52.1323 52.9281L0.0904541 19.4578Z" fill="url(#paint0_linear_52_5441)"/>
        </g>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M-1.0835 17.5139L7.63885 117.211L59.1283 69.6198C63.4505 65.6248 62.8381 58.6246 57.8878 55.4409L-1.0835 17.5139ZM0.0903967 19.4579L57.3469 56.282C61.7471 59.1119 62.2915 65.3343 58.4496 68.8854L8.45734 115.093L0.0903967 19.4579Z" fill="#475569" fill-opacity="0.06"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M144.293 4.79517L153.015 104.492L94.0441 66.5651C89.0939 63.3814 88.4814 56.3812 92.8036 52.3862L144.293 4.79517ZM143.475 6.91342L93.4824 53.1206C89.6404 56.6716 90.1848 62.894 94.585 65.724L151.842 102.548L143.475 6.91342Z" fill="#475569" fill-opacity="0.06"/>
        <g filter="url(#filter1_di_52_5441)">
            <path d="M143.542 6.90723L151.909 102.542L99.8676 69.0716C95.1323 66.0261 92.7647 64.5034 91.784 62.4838C90.9245 60.714 90.7474 58.6896 91.2865 56.7974C91.9016 54.6383 93.9688 52.7275 98.1033 48.9061L143.542 6.90723Z" fill="url(#paint1_linear_52_5441)"/>
        </g>
        <g style="mix-blend-mode:multiply" opacity="0.4">
            <g filter="url(#filter2_f_52_5441)">
                <path d="M11.446 114.831L148.921 102.804L83.9724 53.9656C80.7228 51.522 79.098 50.3003 77.35 49.9358C75.8102 49.6148 74.2099 49.7548 72.7493 50.3384C71.0911 51.0008 69.7031 52.4861 66.9272 55.4568L11.446 114.831Z" fill="#334155" fill-opacity="0.4"/>
            </g>
            <g filter="url(#filter3_f_52_5441)">
                <path d="M19.4155 114.134L140.951 103.501L84.6744 50.8267C81.0679 47.4511 79.2647 45.7633 77.2692 45.2568C75.5166 44.8119 73.6649 44.9739 72.0162 45.7164C70.139 46.5617 68.6562 48.537 65.6907 52.4875L19.4155 114.134Z" fill="#334155" fill-opacity="0.2"/>
            </g>
            <g filter="url(#filter4_f_52_5441)">
                <path d="M11.446 114.831L148.921 102.804L83.9351 44.2849C80.3893 41.0919 78.6163 39.4954 76.6648 39.016C74.9498 38.5947 73.1436 38.7528 71.5279 39.4654C69.6892 40.2764 68.2204 42.1565 65.2829 45.9167L11.446 114.831Z" fill="#334155" fill-opacity="0.1"/>
            </g>
        </g>
        <g filter="url(#filter5_i_52_5441)">
            <path d="M8.45752 115.093L151.91 102.542L83.7273 53.4902C80.5528 51.2064 78.9656 50.0646 77.2666 49.7253C75.7692 49.4263 74.2176 49.562 72.7949 50.1165C71.1806 50.7457 69.8158 52.1458 67.0862 54.9461L8.45752 115.093Z" fill="url(#paint2_linear_52_5441)"/>
        </g>
        <path d="M9.77744 112.969L66.9266 54.5396C69.6979 51.7062 71.0836 50.2895 72.721 49.6532C74.1642 49.0925 75.7375 48.9557 77.2558 49.2591C78.9784 49.6033 80.5877 50.7597 83.8063 53.0725L151.324 101.589" stroke="#475569" stroke-opacity="0.06"/>
        <g filter="url(#filter6_f_52_5441)">
            <path d="M1.70288 37.8875L8.45745 115.093L151.909 102.542L145.155 25.337" stroke="url(#paint3_linear_52_5441)" stroke-width="16"/>
        </g>    
        <path d="M0.439087 23.4426L7.34182 102.341C7.73232 106.805 7.92756 109.036 8.94534 110.665C9.84061 112.098 11.1665 113.21 12.7329 113.843C14.5137 114.563 16.7453 114.367 21.2087 113.977L139.158 103.658C143.622 103.267 145.853 103.072 147.482 102.054C148.915 101.159 150.027 99.8331 150.66 98.2667C151.38 96.4859 151.184 94.2542 150.794 89.7908L143.891 10.8922" stroke="white" stroke-opacity="0.4" stroke-width="2" stroke-linecap="round"/>
        <defs>
            <filter id="filter0_di_52_5441" x="-1.90955" y="13.4578" width="80.9291" height="115.635" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                <feOffset dx="8" dy="4"/>
                <feGaussianBlur stdDeviation="5"/>
                <feComposite in2="hardAlpha" operator="out"/>
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_52_5441"/>
                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_52_5441" result="shape"/>
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                <feOffset dy="1"/>
                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
                <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
                <feBlend mode="normal" in2="shape" result="effect2_innerShadow_52_5441"/>
            </filter>
            <filter id="filter1_di_52_5441" x="72.9803" y="0.907227" width="80.9291" height="115.635" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                <feOffset dx="-8" dy="4"/>
                <feGaussianBlur stdDeviation="5"/>
                <feComposite in2="hardAlpha" operator="out"/>
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_52_5441"/>
                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_52_5441" result="shape"/>
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                <feOffset dy="1"/>
                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
                <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
                <feBlend mode="normal" in2="shape" result="effect2_innerShadow_52_5441"/>
            </filter>
            <filter id="filter2_f_52_5441" x="7.44604" y="45.7673" width="145.475" height="73.0637" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                <feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur_52_5441"/>
            </filter>
            <filter id="filter3_f_52_5441" x="7.41553" y="33.011" width="145.536" height="93.1228" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                <feGaussianBlur stdDeviation="6" result="effect1_foregroundBlur_52_5441"/>
            </filter>
            <filter id="filter4_f_52_5441" x="-12.554" y="14.7852" width="185.475" height="124.046" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                <feGaussianBlur stdDeviation="12" result="effect1_foregroundBlur_52_5441"/>
            </filter>
            <filter id="filter5_i_52_5441" x="8.45752" y="49.5703" width="143.452" height="65.5225" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                <feOffset dy="1"/>
                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
                <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
                <feBlend mode="normal" in2="shape" result="effect1_innerShadow_52_5441"/>
            </filter>
            <filter id="filter6_f_52_5441" x="-14.2667" y="16.6399" width="182.843" height="115.119" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                <feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_52_5441"/>
            </filter>
            <linearGradient id="paint0_linear_52_5441" x1="39.3394" y1="112.391" x2="30.9725" y2="16.7559" gradientUnits="userSpaceOnUse">
                <stop offset="0.28125" stop-color="#EEF0F1"/>
                <stop offset="0.78125" stop-color="#F9FAFA"/>
            </linearGradient>
            <linearGradient id="paint1_linear_52_5441" x1="121.027" y1="105.244" x2="112.66" y2="9.60905" gradientUnits="userSpaceOnUse">
                <stop offset="0.28125" stop-color="#EEF0F1"/>
                <stop offset="0.78125" stop-color="#F9FAFA"/>
            </linearGradient>
            <linearGradient id="paint2_linear_52_5441" x1="74.7799" y1="47.0532" x2="80.1835" y2="108.817" gradientUnits="userSpaceOnUse">
                <stop offset="0.322581" stop-color="#FAFAFB"/>
                <stop offset="1" stop-color="#EEF0F1"/>
            </linearGradient>
            <linearGradient id="paint3_linear_52_5441" x1="73.492" y1="32.3338" x2="80.1835" y2="108.817" gradientUnits="userSpaceOnUse">
                <stop stop-color="#F9FAFA"/>
                <stop offset="0.693396" stop-color="#EEF0F1"/>
            </linearGradient>
        </defs>
        </g>
</svg>  
