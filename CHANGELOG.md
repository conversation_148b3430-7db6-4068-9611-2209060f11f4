# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [0.0.6](https://github.com/Lucres-com/web-react/compare/v0.0.5...v0.0.6) (2025-05-24)

### Features

- add create quote ([8091a23](https://github.com/Lucres-com/web-react/commit/8091a2318d573874b05c177e6e57f31a6b3a6868))
- add emoji to article and comment ([121fee2](https://github.com/Lucres-com/web-react/commit/121fee2cc8f7ad2f95f477bd5db6ff2191a60cd3))
- add empty state to feed and board ([7dfbb4e](https://github.com/Lucres-com/web-react/commit/7dfbb4e9b908ffb8fd7a8d070259e41533f50848))
- add infinite scroll to user board ([21e4c2f](https://github.com/Lucres-com/web-react/commit/21e4c2f3afddd2e22a993bab0241b85923275b2e))
- add repost job ([2b5d152](https://github.com/Lucres-com/web-react/commit/2b5d152b5e6737ecc6ee1027e92b3b82cf7966b1))
- add tag feature to post and comment ([653cf9d](https://github.com/Lucres-com/web-react/commit/653cf9d93e7bf21c82296c8b29b6931e7c06e2b9))
- added NoDataFound component and conditional rendering in Resume ([6ba3dea](https://github.com/Lucres-com/web-react/commit/6ba3dea3d354da18a4ea942207fa6b0ed538e806))
- added validation for input fields in create resume form to prevent invalid submissions ([a2333ba](https://github.com/Lucres-com/web-react/commit/a2333ba5f9d6c7d88bbd911315f7143d514491f3))
- centralize the errors, fix ssr working ([adbe116](https://github.com/Lucres-com/web-react/commit/adbe116d4953379e1ab4f31f804a754f130ea501))
- complete get bookmark api, logout globally, refactor filters ([816a780](https://github.com/Lucres-com/web-react/commit/816a7804a622ec0fb4bbddaf944784a994680830))
- fetch order details ([a5b48d2](https://github.com/Lucres-com/web-react/commit/a5b48d28c774ff7d7a6afd07f45f19f01cad29b3))
- integrate backend API for resume in profile section ([73b9fd3](https://github.com/Lucres-com/web-react/commit/73b9fd3ee32e0f5a3e83f2ab47c8369e6cb34e1f))
- integrate bookmark apis, complete job and talent page ([60b54f3](https://github.com/Lucres-com/web-react/commit/60b54f3a5913ae7398230528c280c5cc3f47e45d))
- integrate create company api, refactor follow button, like button components ([ac663f3](https://github.com/Lucres-com/web-react/commit/ac663f3e1d588230bf5b9bcb8d5e99d4a205ee1c))
- integrate google signup, resolve the bugs ([3140f25](https://github.com/Lucres-com/web-react/commit/3140f25e765468f4f55f424c363150d597691ce1))
- integrate job apis, integrate geolocation ([40b571a](https://github.com/Lucres-com/web-react/commit/40b571a48594a60b8610ab34b93f8fa00ed57da3))
- integrate post jobs api, refactor text editor ([96a1ace](https://github.com/Lucres-com/web-react/commit/96a1ace6cbca97992d2ea48bb065f9c38b3cc157))
- integrate primary phone update api, refactor url patterns ([4b6e485](https://github.com/Lucres-com/web-react/commit/4b6e4851e198ad716cfd5f1ef03eea60e0a0c554))
- integrated month picker in resume form ([b95009b](https://github.com/Lucres-com/web-react/commit/b95009ba04e9e187b5edcc50eeebff4bf2d00d62))
- make comments section responsive ([c6fc35e](https://github.com/Lucres-com/web-react/commit/c6fc35ebce3171b5c6f149659f16074b1c1627f8))

### Bug Fixes

- about me validation message ([ae0b684](https://github.com/Lucres-com/web-react/commit/ae0b684ee6705b742b105c4b0b5d92998e55c25b))
- achievement date issue ([b7afec0](https://github.com/Lucres-com/web-react/commit/b7afec0fded42fda2293a1aa3a3a5d22e79d06c9))
- close dropdown on select ([ce206d1](https://github.com/Lucres-com/web-react/commit/ce206d1c38ef2774bad11d63d27efd219c8a572b))
- close dropdown on select ([da3a412](https://github.com/Lucres-com/web-react/commit/da3a4127b22586b36fb5bf13cf1cd5e7212a219c))
- conditionally render section titles in templates based on data presence ([16dd057](https://github.com/Lucres-com/web-react/commit/16dd05743f646290456395778f70dd1e2cd1e3d4))
- disabling save button on success ([1792528](https://github.com/Lucres-com/web-react/commit/17925284bd1007f734cb2f98f95e092f3c594d93))
- experience end date validation ([b89c34f](https://github.com/Lucres-com/web-react/commit/b89c34f3b87174e3f46d3ab8c98810d4ebbe43c2))
- feed content overflow ([e0944d4](https://github.com/Lucres-com/web-react/commit/e0944d4ae20eb75e5633021fa6a58daf625f1700))
- Personail details fetching from auth context ([35f7f25](https://github.com/Lucres-com/web-react/commit/35f7f254a13b52b41c175c61661f665da0f187ee))
- removed dummy data from resume form and replaced with placeholders ([6320ae2](https://github.com/Lucres-com/web-react/commit/6320ae2f78d8cdb75c686bb9dd5e351426e0ff49))
- resolve repost issue ([a34e5d7](https://github.com/Lucres-com/web-react/commit/a34e5d7b4c0727c883a9c49d38823bf2f4f5629d))
- resolved initial dummy resume data loading issue ([3b18c62](https://github.com/Lucres-com/web-react/commit/3b18c622eecc5a7e1d8709d1cac3f7fcaf19ca34))
- resume creation progress bar calculation ([59d36e0](https://github.com/Lucres-com/web-react/commit/59d36e097e38bb9f9d4f1c5857fb1ac47915e6f6))
- Social Link and refernces section ([770d190](https://github.com/Lucres-com/web-react/commit/770d190dee58cc44c11b5aa6b417ecb6f2ec70b1))
- updated resume templates to align with new resume interfaces ([3634e84](https://github.com/Lucres-com/web-react/commit/3634e84528368bd8b0d3724ce34818e6a68fe521))

### [0.0.5](https://github.com/Lucres-com/web-react/compare/v0.0.4...v0.0.5) (2025-05-09)

### Features

- add create quote ([e8a636d](https://github.com/Lucres-com/web-react/commit/e8a636d00c16e756ab4994e2f7e369fef3dd9803))
- add emoji to article and comment ([c15a34e](https://github.com/Lucres-com/web-react/commit/c15a34e1dea09e4c21b9ce1c98e70f9509d13c2f))
- add empty state to feed and board ([a056247](https://github.com/Lucres-com/web-react/commit/a0562472f8c4a93dee53e7d9c31da2eae804d35a))
- add infinite scroll to user board ([2adc126](https://github.com/Lucres-com/web-react/commit/2adc126844f88a42ebc64689d5ff2a8cbff2efd9))
- add repost job ([e4a6abf](https://github.com/Lucres-com/web-react/commit/e4a6abf8bcffecb9790d1e7bbf1ba1c80c7f5435))
- complete get bookmark api, logout globally, refactor filters ([5312be9](https://github.com/Lucres-com/web-react/commit/5312be9356c92d08c0e5a06ece8567bed72fa3ba))
- integrate bookmark apis, complete job and talent page ([3428e4f](https://github.com/Lucres-com/web-react/commit/3428e4f37b503e5d088f0125ce64a7e6ce111fec))
- integrate create company api, refactor follow button, like button components ([a80c9d0](https://github.com/Lucres-com/web-react/commit/a80c9d07659cc275df1c6a88bdd0fccf94e36974))
- integrate primary phone update api, refactor url patterns ([69714da](https://github.com/Lucres-com/web-react/commit/69714daf6fc285cd980ac59f9bea158c4e7ea3d1))
- make comments section responsive ([f489e36](https://github.com/Lucres-com/web-react/commit/f489e367868244a75de83a4a1fc60ede0fb07878))

### Bug Fixes

- repost and undo repost ([a74a37f](https://github.com/Lucres-com/web-react/commit/a74a37f8461bc6b48081dc98bf50d4d7fecbe58e))

### [0.0.4](https://github.com/Lucres-com/web-react/compare/v0.0.2...v0.0.4) (2025-04-23)

### Features

- add create post ([fda01a3](https://github.com/Lucres-com/web-react/commit/fda01a3d64906b6cedd7a41db20b5243b360491e))
- add create post with image upload and deletion ([5c19b6e](https://github.com/Lucres-com/web-react/commit/5c19b6ef75fe3e1a4f0f1cea33593d06623b6fa5))
- add create post with image upload and deletion ([b51417c](https://github.com/Lucres-com/web-react/commit/b51417c7811e92f0c22a6c75566dc9356b6f44ed))
- add edit and delete post ([8a5579c](https://github.com/Lucres-com/web-react/commit/8a5579c61754bfc89cc69c85a15a05cc3bee330d))
- add edit and delete post comment ([bf1309f](https://github.com/Lucres-com/web-react/commit/bf1309f18f4121963c15b8a5c3f7adc56d25039f))
- add fetch board data ([9df5ee4](https://github.com/Lucres-com/web-react/commit/9df5ee489d8bf86bf5ba6fb80c8b44267350f2d2))
- add like and dislike post ([7f14401](https://github.com/Lucres-com/web-react/commit/7f144011d3a92be7b48ea12dc1c855a4cbb5ff7c))
- add repost and undo repost ([4c1fbd6](https://github.com/Lucres-com/web-react/commit/4c1fbd6fa7e61308d26ebcffc9de4c908b91a158))
- Add resume templates features ([36a1785](https://github.com/Lucres-com/web-react/commit/36a1785fc00f1cf4667ed812c18905384e53d608))
- add skeleton for notifications, add unread notification count on alert icon ([c51e352](https://github.com/Lucres-com/web-react/commit/c51e3529e2a2cca3c39518aa86c88b5008ff8f95))
- added notification API integration to UI ([8c235b3](https://github.com/Lucres-com/web-react/commit/8c235b31ad82a28e9e9640260e0b133d728264ba))
- added pagination in free template ([6ae21df](https://github.com/Lucres-com/web-react/commit/6ae21dfc791ffcce1f4db1ccbfa01cf1cb90b66c))
- added pagination in free template preview ([958dfaf](https://github.com/Lucres-com/web-react/commit/958dfaf23d2eed426eba4b1f100ceb2582c68c86))
- added pagination in pro templateid 3 and 4 ([f44dc5f](https://github.com/Lucres-com/web-react/commit/f44dc5f343434719221c3738b89cb710b8a68ad9))
- fetch and display feed and board data ([e63d646](https://github.com/Lucres-com/web-react/commit/e63d646f4b07a808ae7193bcb8006bbe9b9dad79))
- fetch and store user board data ([b05a41c](https://github.com/Lucres-com/web-react/commit/b05a41cd34681aba189ce0682bff91b71613da00))
- integrate job apis, refactor talent ui ([2ff52cc](https://github.com/Lucres-com/web-react/commit/2ff52cc7a526626eeffa4721c59750a0a48d2b85))
- integrate jobs apis ([ced3b5d](https://github.com/Lucres-com/web-react/commit/ced3b5da1803c5db0d5ca37810b640f7fb6ff1cc))
- integrate user suggestion api, fix filters, design animation on like ([1927bbd](https://github.com/Lucres-com/web-react/commit/1927bbdcee43309c9c6ef6bb5fa40c0dc05f7521))
- mark all the notifcations as read ([05454af](https://github.com/Lucres-com/web-react/commit/05454af6f32a7727ac71a610a1645cc6eae47f4c))
- mark as all read notifications ([024b0db](https://github.com/Lucres-com/web-react/commit/024b0db03eff8d05ae173aaa56a67d715a1213a0))

### Bug Fixes

- Download pdf issue in mobile devices ([1fefa6a](https://github.com/Lucres-com/web-react/commit/1fefa6a5b84d2e4e874cd1a8cad8e2ebb9eec609))
- Pagination in Pro template 1 ([8b27e02](https://github.com/Lucres-com/web-react/commit/8b27e02b8be1cf5b49ad4ee615e4ed86504ab974))
- resolve scaling issue to maintain A4 aspect ratio in ResumePreview ([2bb2f76](https://github.com/Lucres-com/web-react/commit/2bb2f76b991e56f843b2c3d1ca97d1024d474191))
- Resolved responsive Resume Preview component with a4 size scaling ([dda385d](https://github.com/Lucres-com/web-react/commit/dda385d36b2e41eea09275efa1bdbd55bd391249))
- Resolved show templates height issue and Free template Preview component Responsiveness ([324f21c](https://github.com/Lucres-com/web-react/commit/324f21cc3758f6a153f37a0640b4655be7aae52b))

### [0.0.3](https://github.com/Lucres-com/web-react/compare/v0.0.2...v0.0.3) (2025-04-23)

### Features

- add create post ([fda01a3](https://github.com/Lucres-com/web-react/commit/fda01a3d64906b6cedd7a41db20b5243b360491e))
- add create post with image upload and deletion ([5c19b6e](https://github.com/Lucres-com/web-react/commit/5c19b6ef75fe3e1a4f0f1cea33593d06623b6fa5))
- add create post with image upload and deletion ([b51417c](https://github.com/Lucres-com/web-react/commit/b51417c7811e92f0c22a6c75566dc9356b6f44ed))
- add edit and delete post ([8a5579c](https://github.com/Lucres-com/web-react/commit/8a5579c61754bfc89cc69c85a15a05cc3bee330d))
- add edit and delete post comment ([bf1309f](https://github.com/Lucres-com/web-react/commit/bf1309f18f4121963c15b8a5c3f7adc56d25039f))
- add fetch board data ([9df5ee4](https://github.com/Lucres-com/web-react/commit/9df5ee489d8bf86bf5ba6fb80c8b44267350f2d2))
- add like and dislike post ([7f14401](https://github.com/Lucres-com/web-react/commit/7f144011d3a92be7b48ea12dc1c855a4cbb5ff7c))
- add repost and undo repost ([4c1fbd6](https://github.com/Lucres-com/web-react/commit/4c1fbd6fa7e61308d26ebcffc9de4c908b91a158))
- Add resume templates features ([36a1785](https://github.com/Lucres-com/web-react/commit/36a1785fc00f1cf4667ed812c18905384e53d608))
- add skeleton for notifications, add unread notification count on alert icon ([c51e352](https://github.com/Lucres-com/web-react/commit/c51e3529e2a2cca3c39518aa86c88b5008ff8f95))
- added notification API integration to UI ([8c235b3](https://github.com/Lucres-com/web-react/commit/8c235b31ad82a28e9e9640260e0b133d728264ba))
- added pagination in free template ([6ae21df](https://github.com/Lucres-com/web-react/commit/6ae21dfc791ffcce1f4db1ccbfa01cf1cb90b66c))
- added pagination in free template preview ([958dfaf](https://github.com/Lucres-com/web-react/commit/958dfaf23d2eed426eba4b1f100ceb2582c68c86))
- added pagination in pro templateid 3 and 4 ([f44dc5f](https://github.com/Lucres-com/web-react/commit/f44dc5f343434719221c3738b89cb710b8a68ad9))
- fetch and display feed and board data ([e63d646](https://github.com/Lucres-com/web-react/commit/e63d646f4b07a808ae7193bcb8006bbe9b9dad79))
- fetch and store user board data ([b05a41c](https://github.com/Lucres-com/web-react/commit/b05a41cd34681aba189ce0682bff91b71613da00))
- integrate job apis, refactor talent ui ([2ff52cc](https://github.com/Lucres-com/web-react/commit/2ff52cc7a526626eeffa4721c59750a0a48d2b85))
- integrate jobs apis ([ced3b5d](https://github.com/Lucres-com/web-react/commit/ced3b5da1803c5db0d5ca37810b640f7fb6ff1cc))
- integrate user suggestion api, fix filters, design animation on like ([1927bbd](https://github.com/Lucres-com/web-react/commit/1927bbdcee43309c9c6ef6bb5fa40c0dc05f7521))
- mark all the notifcations as read ([05454af](https://github.com/Lucres-com/web-react/commit/05454af6f32a7727ac71a610a1645cc6eae47f4c))
- mark as all read notifications ([024b0db](https://github.com/Lucres-com/web-react/commit/024b0db03eff8d05ae173aaa56a67d715a1213a0))

### Bug Fixes

- Download pdf issue in mobile devices ([1fefa6a](https://github.com/Lucres-com/web-react/commit/1fefa6a5b84d2e4e874cd1a8cad8e2ebb9eec609))
- Pagination in Pro template 1 ([8b27e02](https://github.com/Lucres-com/web-react/commit/8b27e02b8be1cf5b49ad4ee615e4ed86504ab974))
- resolve scaling issue to maintain A4 aspect ratio in ResumePreview ([2bb2f76](https://github.com/Lucres-com/web-react/commit/2bb2f76b991e56f843b2c3d1ca97d1024d474191))
- Resolved responsive Resume Preview component with a4 size scaling ([dda385d](https://github.com/Lucres-com/web-react/commit/dda385d36b2e41eea09275efa1bdbd55bd391249))
- Resolved show templates height issue and Free template Preview component Responsiveness ([324f21c](https://github.com/Lucres-com/web-react/commit/324f21cc3758f6a153f37a0640b4655be7aae52b))

### [0.0.2](https://github.com/Lucres-com/web-react/compare/v0.0.1...v0.0.2) (2025-04-03)

### Features

- add a separate token service, udpate user and talent interfaces and cleanup auth context ([ad7770f](https://github.com/Lucres-com/web-react/commit/ad7770f2ab0a6afe47ac9a4ab953d21643f32ab1))
- add avatar component and refactor code in navbars and job and talent cards ([96afc0e](https://github.com/Lucres-com/web-react/commit/96afc0e1318aeeb7e2cfc11f4b9dae7c44a133c1))
- add toast notifications ([1b09941](https://github.com/Lucres-com/web-react/commit/1b099411eb784aef3acdb30afa1c6eb04fc93bfc))
- add user service and integrate get user data by permalink, add user data in authcontext to get same data in whole application ([a693315](https://github.com/Lucres-com/web-react/commit/a693315bbc9429dd4cc4fc2c65fbbb1db8df67e4))
- add userProvider, sync auth and user context, integrate user profile endpoint ([7c3aa53](https://github.com/Lucres-com/web-react/commit/7c3aa532e4464507101aaafe3f04e314d8ceba92))
- complete talent api integration ([98516db](https://github.com/Lucres-com/web-react/commit/98516dbf11353ab9a8e1b11e33ed43187b146970))
- increase avatar size, improve ui and fix style problems in responsiveness in talent and job cards ([63cdcb1](https://github.com/Lucres-com/web-react/commit/63cdcb1650803c69a6e6cc0c23132dd84dfda3b4))
- integrate edit personal details and contact details ([1757c79](https://github.com/Lucres-com/web-react/commit/1757c79530412e6b075105321c083e738caf1f60))
- integrate upload photo apis, add validation to profile form ([5b111bd](https://github.com/Lucres-com/web-react/commit/5b111bd51e167fc59412e1d297f5d4cd1b5ce1d8))
- update avatar component to show user or company avatar in a modal on click ([0b5a1f5](https://github.com/Lucres-com/web-react/commit/0b5a1f5b322e95acfae1167658d112560bc80315))
- update profile data must update the data in navbars ([0cecf17](https://github.com/Lucres-com/web-react/commit/0cecf17e88c49b8dd909a6737b0a16186b039f8d))
- update salary format in job card component ([e2728d0](https://github.com/Lucres-com/web-react/commit/e2728d02cf9c3c4695fac8c0f35ece1a11a6ae43))

### Bug Fixes

- edit and update without making any changes to the profile form does not give an error ([156fa70](https://github.com/Lucres-com/web-react/commit/156fa700944295e3965e7dc17b134e164431bcb7))

### 0.0.1 (2025-03-26)

### Features

- add loader skeletons to talent and jobs, pagination, default avatar and company logos, refurbish components ([62429b1](https://github.com/Lucres-com/web-react/commit/62429b1c50658f74de83786a858964d33880a042))
- add toggle switch in questions & create job section ([5e19f24](https://github.com/Lucres-com/web-react/commit/5e19f2435e7d7d8bc096336cecc4b5d81275745b))
- implement authentication ([6fc7049](https://github.com/Lucres-com/web-react/commit/6fc7049b3e8072e90628a66aafce5501e1d97f21))
- add company signup and related pages ([5942504](https://github.com/Lucres-com/web-react/commit/5942504825c6b7ae8c3f260598003799978221b0))
- implement job requirement, application questions, and preview components ([b0ab3c0](https://github.com/Lucres-com/web-react/commit/b0ab3c080d7a417e4d07ae572489fe00ec4351c3))
- improvements in resume ([e7c9317](https://github.com/Lucres-com/web-react/commit/e7c9317cb8c217ba8c24d3ce54d087b39e449526))
- integrate jobs and talent ([8156be4](https://github.com/Lucres-com/web-react/commit/8156be4d6e04575369b63647a6e5b0f60fbac2a8))
- **job posting:** add create job sub-component for new job posting flow ([23088e3](https://github.com/Lucres-com/web-react/commit/23088e365bdd37e0b1775cec150203e6de4463d4))
- notes feature under company dashboard ([5b323af](https://github.com/Lucres-com/web-react/commit/5b323af946d2f5725ab24e439e6e26c64cee7087))
- schedule feature ([a4e07e9](https://github.com/Lucres-com/web-react/commit/a4e07e94eb58c63d025309a5f54831bdc84ea5c1))
- setup lucres application repository ([0b053a0](https://github.com/Lucres-com/web-react/commit/0b053a0c401f70f917de8e1b31d195f4a719e4a3))
- **user onboarding:** add signin/signup workflow ([177ad1d](https://github.com/Lucres-com/web-react/commit/177ad1d7256f87e19cd14092b8ce657b23abf71c))

### Bug Fixes

- application questions width ratio ([a46972f](https://github.com/Lucres-com/web-react/commit/a46972ffa00b64868cb736e0f50834cfc1381bb9))
- resolve responsiveness issues in jobposting and jobrequirement components ([1814523](https://github.com/Lucres-com/web-react/commit/181452306bd28706e3374240279ecf80e4285bea))
- responsive design of job posting feature ([086f1e8](https://github.com/Lucres-com/web-react/commit/086f1e832fbb0fa16820320ab0f679d48b298c30))
- user-onboarding responsiveness ([6f3d6ab](https://github.com/Lucres-com/web-react/commit/6f3d6ab897d08a3a92c05e927054038336beffaf))
