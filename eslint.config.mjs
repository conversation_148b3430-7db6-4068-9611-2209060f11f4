import { dirname } from 'path'
import { fileURLToPath } from 'url'
import { FlatCompat } from '@eslint/eslintrc'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const compat = new FlatCompat({
  baseDirectory: __dirname,
})

export default [
  // Next.js and TypeScript base rules
  ...compat.extends('next/core-web-vitals', 'next', 'next/typescript'),

  // General ESLint recommended rules
  {
    files: ['**/*.{js,ts,jsx,tsx}'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
    },
    rules: {
      // General best practices
      'no-unused-vars': 'off',
      'no-console': 'off',
      'no-debugger': 'error',
      eqeqeq: ['warn', 'always'],
      curly: ['warn', 'all'],
      'no-empty-function': 'warn',

      // TypeScript-specific
      '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/no-explicit-any': 'off',

      // React/Next improvements
      'react/jsx-uses-react': 'off',
      'react/react-in-jsx-scope': 'off', // Next.js doesn't need React in scope
    },
  },

  // Prettier integration for formatting
  {
    files: ['**/*.{js,ts,jsx,tsx}'],
    plugins: {
      prettier: require('eslint-plugin-prettier'),
    },
    rules: {
      'prettier/prettier': [
        'error',
        {
          semi: false,
          singleQuote: true,
          trailingComma: 'all',
          arrowParens: 'always',
          printWidth: 100,
          tabWidth: 2,
          useTabs: false,
          bracketSpacing: true,
          jsxBracketSameLine: false,
          endOfLine: 'lf',
        },
      ],
    },
  },

  // Prettier recommended config
  ...compat.extends('plugin:prettier/recommended'),
]
