# Contributing to Lucres React App

Thank you for considering contributing to the Lucres React App project! We welcome contributions from the community and appreciate your interest in enhancing our digital experience on lucres.com. By participating in this project, you agree to abide by the following guidelines.

## How Can I Contribute?

### Reporting Bugs

If you find a bug in the project, please create an issue with the following information:

- A clear and descriptive title.
- A detailed description of the problem.
- Steps to reproduce the issue.
- Any relevant logs or error messages.

### Suggesting Enhancements

If you have an idea for an enhancement or new feature, please create an issue with the following information:

- A clear and descriptive title.
- A detailed description of the proposed enhancement.
- The rationale for the enhancement and its potential benefits.

### Submitting Pull Requests

If you want to contribute code to the project, please follow these steps:

1. Fork the repository.
2. Create a new branch for your feature or bugfix:
   ```bash
   git checkout -b feature/your-feature-name
   ```
3. Make your changes in the new branch.
4. Ensure your code follows the project's coding standards and conventions. Here's a summary of the folder, file, and image naming conventions:

   📁 Folder & File Naming Conventions:

   - Component files and folders: Use **PascalCase**
     - Example: `UserProfile.tsx`
   - Custom hooks: Use **camelCase**
     - Example: `useAuth.ts`
   - Utility functions: Use **camelCase**
     - Example: `formatDate.ts`
   - Folder names: Use **kebab-case**
     - Example: `user-profile/`
   - Route names: Use **kebab-case**
     - Example: `/user-profile`, `/sign-up`

   📄 Page Organization:

   - Each route should have a dedicated folder under `pages/`, named using **kebab-case**
   - Inside that folder, place the corresponding component file using **PascalCase**
     - Example: `pages/user-profile/UserProfile.tsx` → renders the `/user-profile` route

   🖼️ Image Naming & Format Guidelines:

   - Use **kebab-case** for all image file names
     - Example: `user-avatar.png`, `empty-state-illustration.svg`
   - Use clear, descriptive names that reflect the content or purpose
   - Avoid spaces or special characters in file names
   - Use **.svg** for vector-based images (icons, logos, illustrations)
   - Use **.jpeg**, **.webp**, or **.png** for raster images (photos, UI
     elements, backgrounds)

   🗂️ Image Placement:

   - **Component Specific Images** → store inside the respective component's folder
     - Example: `components/UserProfile/user-avatar.png`
   - **Shared Images** (used across multiple components) → place in `src/assets/`
     - Example: `src/assets/logo.svg`

5. Write tests for your changes, if applicable.
6. Ensure all tests pass before submitting your pull request.
7. Commit your changes with a descriptive commit message.
8. Push your changes to your forked repository:
   ```bash
   git push origin feature/your-feature-name
   ```
9. Open a pull request against the `main` branch of the original repository.

### Commit Messages

Please follow these commit message conventions:

#### Examples

- **Feature**:
  ```markdown
  feat: add user authentication module
  ```
- **Bug Fix**:
  ```markdown
  fix: resolve login issue on mobile devices
  ```
- **Chore (task that doesn't change functionality)**:
  ```markdown
  chore: update npm dependencies
  ```
- **Documentation**:
  ```markdown
  docs: add API usage instructions
  ```
- **Style (code style improvements, not affecting functionality)**:
  ```markdown
  style: format code with prettier
  ```
- **Refactor (code changes that neither fix a bug nor add a feature)**:
  ```markdown
  refactor: restructure project directories
  ```
- **Performance**:
  ```markdown
  perf: improve data processing speed
  ```
- **Test**:
  ```markdown
  test: add unit tests for user module
  ```

### Coding Standards

- Use TypeScript for all code.
- Follow the project's existing code style and conventions.
- Write clear, concise, and self-documenting code.
- Include comments where necessary to explain complex logic.
- Use a consistent folder structure: organize files into `components/`, `pages/`, `hooks/`, `utils/`, and `assets/`.
- Follow the [Airbnb React/TypeScript Guide](https://github.com/airbnb/javascript) for best practices and code style.
- Follow the [Airbnb React/TypeScript Guide](https://github.com/airbnb/javascript) and use the provided Prettier configuration (`prettier.config.js`) for formatting.

### Running Tests

Before submitting a pull request, ensure all tests pass by running the following command:

```bash
npm test
```

### Code of Conduct

Please note that this project is governed by a [Code of Conduct](CODE_OF_CONDUCT.md). By participating, you are expected to uphold this code.

### License and NDA

This project is owned by Lucres Private Limited. All contributors must sign a Non-Disclosure Agreement (NDA) before contributing. Unauthorized use or distribution of this code is strictly prohibited. For more information regarding the NDA or licensing, please contact your project manager.

### Contact

If you have any questions or need further assistance, please reach out to the project maintainers.

Thank you for your contributions and for helping to make the Lucres React App even better!
