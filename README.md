# Lucres Next.js App

Lucres is an innovative networking platform that streamlines the hiring process by connecting job seekers and recruiters. Employers can post jobs for free, making it particularly beneficial for small businesses and startups. The platform features a unique "Repost" function, allowing users to share job postings within their network; if a candidate applies through a repost and is contacted by the employer, the user who shared the post earns 75% of <PERSON><PERSON>' revenue from that contact. Recruiters can access comprehensive contact details of applicants for a nominal fee, with the first five contacts per job post available at no cost. Job seekers can create detailed profiles to showcase their professional history, eliminating the need for traditional resumes. <PERSON>res fosters an open network where users can interact freely, follow recruiters, and access job postings without barriers, aiming to democratize and enhance the efficiency of the hiring process. ​

## Technology Stack

- **API Integration**: Consumes robust APIs developed in Node.js and Express by the backend team.
- **Next.js Frontend**: Built with Next.js, React, and TypeScript to ensure a scalable, maintainable, and high-performance web experience with server-side rendering. (This repository)
- **Language**: TypeScript is used across the codebase for consistency and reliability.

## Getting Started

Follow these instructions to set up the project on your local machine for development and testing.

### Prerequisites

- Node.js (version 22.x)
- npm

### Installation

Clone the repository and install the dependencies:

```bash
git clone https://github.com/Lucres-com/web-next
cd web-next
npm install
```

### Configuration

Obtain your environment configuration files (`.env.development` and `.env.production`) from your project manager and place them in the root of the project.

### Running the Application

#### Development

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

#### Production

```bash
npm run build
npm run start
# or
yarn build
yarn start
# or
pnpm build
pnpm start
# or
bun build
bun start
```

### Scripts

- `build`: Compiles the TypeScript and React code.
- `dev`: Runs the application in development mode with live reloading.
- `start`: Launches the production build.
- `lint`: Runs the linter to ensure code quality.
- `test`: Executes unit and integration tests.
- `release`: Uses `standard-version` to automate versioning and changelog generation.
- `release:minor`: Manually bumps the minor version using `standard-version`.
- `release:major`: Manually bumps the major version using `standard-version`.

## Versioning and Changelog

This project uses [standard-version](https://github.com/conventional-changelog/standard-version) for automated version management and changelog generation.

#### Release Types

- **Patch Release (`npm run release`)**: Increments the patch version (e.g., `1.0.0` to `1.0.1`).
- **Minor Release (`npm run release:minor`)**: Increments the minor version (e.g., `1.0.0` to `1.1.0`).
- **Major Release (`npm run release:major`)**: Increments the major version (e.g., `1.0.0` to `2.0.0`).

### Changelog

Detailed changes are maintained in the [CHANGELOG.md](CHANGELOG.md) file.

## Contributing

We welcome contributions! Please refer to the [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines on how to contribute effectively to this project.

## License

This codebase is the property of Lucres Private Limited. Unauthorized use or distribution is strictly prohibited. All contributors are required to sign a Non-Disclosure Agreement (NDA) and adhere to the confidentiality terms set forth by the company. For further details regarding the NDA or licensing, please contact your project manager.
